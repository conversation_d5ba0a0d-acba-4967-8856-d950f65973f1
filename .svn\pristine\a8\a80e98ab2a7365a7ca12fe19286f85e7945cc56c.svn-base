###
### Copyright (c) 2004 Keda Telecom, Inc.
###

#########################################################################
###
###  DESCRIPTION:
###    Common definitions for all Makefiles in OSP linux project.
###
#########################################################################

TOP := ..

COMM_DIR := ../../../common/

SRC_DIR := $(TOP)/source
CUR_DIR := ./

NVR_VOB_DIR := $(CUR_DIR)/../../../../

## Name and type of the target for this Makefile

APP_TARGET      := epapp

## Define debugging symbols
DEBUG = 0
LINUX_COMPILER = _HIS3531DV200_
PWLIB_SUPPORT = 0
CFLAGS +=  -funwind-tables

## Object files that compose the target(s)

OBJS := $(SRC_DIR)/nvrsgwepcore \
        $(SRC_DIR)/nvrsgwepmca \
        $(SRC_DIR)/nvrsgwsystem \


## Libraries to include in shared object file
LIB_PATH := $(NVR_VOB_DIR)/10-common/lib/release/his3531dv200 \
            $(NVR_VOB_DIR)/10-common/lib/release/his3531dv200/audlib \
            $(NVR_VOB_DIR)/10-common/lib/release/his3531dv200/sgw \

#dynamic libraries
LIBS += stdc++\
         sysdbg\
		pthread\
		rt\
         m\
		mca\
		drv\
		debuglog\
		malloc\
		charconv\
		freetype\
		osp\
		kdvencrypt\
		mediactrl_nvr\
		zmq\
	   equalizer_ex_armhi3531dv200_linux \
		mixer_armhi3531dv200_linux \
        spe_armhi3531dv200_linux \
		videomanage_armhi3531dv200_linux \
        audproc_plus_armhi3531dv200_linux \
		speechexcit_armhi3531dv200_linux \
		resample_armhi3531dv200_linux \
		extexp_armhi3531dv200_linux \
        voicechanger_armhi3531dv200_linux \
		multiaec16k_v201_armhi3531dv200_linux \
        agc_speechsense_armhi3531dv200_linux\
        audcodec_armhi3531dv200_linux \
		g7221c_armhi3531dv200_linux \
        adpcm_armhi3531dv200_linux \
		g711_armhi3531dv200_linux \
        g722_armhi3531dv200_linux \
		aaclcenc_armhi3531dv200_linux \
        aaclcdec_armhi3531dv200_linux \
		g726_armhi3531dv200_linux \
        aaclddec_armhi3531dv200_linux \
		aacldenc_armhi3531dv200_linux \
        amr_nb_armhi3531dv200_linux \
		g719_armhi3531dv200_linux \
        g728_armhi3531dv200_linux \
		g729_armhi3531dv200_linux \
        mp3dec_armhi3531dv200_linux \
		mp3enc_armhi3531dv200_linux \
        opus_armhi3531dv200_linux \
		mp2_armhi3531dv200_linux \
        stdg722_armhi3531dv200_linux \
		dlydct_armhi3531dv200_linux \
        videomanage_armhi3531dv200_linux\
        md5lib_armhisi3531dv200_linux\

#static libraries
SLIBS := 
     
ifneq ($(SLIBS),) 
LDFLAGS += -Wl,-static
LDFLAGS += $(foreach lib,$(SLIBS),-l$(lib))
endif
## Add driver-specific include directory to the search path

INC_PATH += ../include \
	../../common \
	$(NVR_VOB_DIR)/10-common/include/system\
	$(NVR_VOB_DIR)/10-common/include/cbb \
	$(NVR_VOB_DIR)/10-common/include/cbb/osp\
	$(NVR_VOB_DIR)/10-common/include/hal/drvlib_64\
	$(NVR_VOB_DIR)/10-common/include/hal/drvlib_64/audio\
	$(NVR_VOB_DIR)/10-common/include/hal/drvlib_64/common\
	$(NVR_VOB_DIR)/10-common/include/hal/drvlib_64/dspcci\
	$(NVR_VOB_DIR)/10-common/include/hal/drvlib_64/hardware\
	$(NVR_VOB_DIR)/10-common/include/hal/drvlib_64/system\
	$(NVR_VOB_DIR)/10-common/include/hal/drvlib_64/video\
	$(NVR_VOB_DIR)/10-common/include/cbb/mcav2.0 \
	$(NVR_VOB_DIR)/10-common/include/cbb/debuglog \
	$(NVR_VOB_DIR)/10-common/include/cbb/mediactrl \
    $(NVR_VOB_DIR)/10-common/include/cbb/mediaswitch\
    $(NVR_VOB_DIR)/10-common/include/cbb/protobuf \
	$(NVR_VOB_DIR)/10-common/include/hal/sysdbg \
	$(NVR_VOB_DIR)/10-common/include/service \

            
CFLAGS += -D_HIS3531DV200_

ifeq ($(PWLIB_SUPPORT),1)
   INC_PATH += $(PWLIBDIR)/include/ptlib/unix $(PWLIBDIR)/include
endif


INSTALL_APP_PATH := $(NVR_VOB_DIR)/10-common/version/release/his3531dv200/bin_sgw
LDFLAGS += -L$(INSTALL_LIB_PATH)
include $(COMM_DIR)/common.mk


