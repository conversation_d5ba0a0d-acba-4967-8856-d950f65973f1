

TOP := ..

COMM_DIR := ../../common/

SRC_DIR := $(TOP)/source
CURDIR := ./

## Name and type of the target for this Makefile

SO_TARGET	      := nvrrec


## Define debugging symbols
DEBUG = 1
LINUX_COMPILER = _HIS3516DV300_
PWLIB_SUPPORT = 0
CFLAGS += -funwind-tables
## Object files that compose the target(s)



OBJS := $(SRC_DIR)/nvrrec \
	$(SRC_DIR)/nvrrecservice \
	$(SRC_DIR)/nvrrecctl \
	$(SRC_DIR)/nvrrecanr \
	
## Libraries to include in shared object file
        
ifneq ($(SLIBS),) 
LDFLAGS += -Wl,-static
LDFLAGS += $(foreach lib,$(SLIBS),-l$(lib))
endif
## Add driver-specific include directory to the search path

INC_PATH += $(CURDIR)/../include \
		$(CURDIR)/../../common \
		$(CURDIR)/../../nvrcfg/include \
		$(CURDIR)/../../nvrusrmgr/include \
		$(CURDIR)/../../nvrcap/include \
		$(CURDIR)/../../nvrpui/include \
		$(CURDIR)/../../nvrmpu/include \
		$(CURDIR)/../../nvrvtductrl/include \
		$(CURDIR)/../../airp/include \
		$(CURDIR)/../../nvrsys/include \
		$(CURDIR)/../../../10-common/include/service \
		$(CURDIR)/../../../10-common/include/cbb/debuglog\
		$(CURDIR)/../../../10-common/include/system\
		$(CURDIR)/../../../10-common/include/cbb/protobuf \
		$(CURDIR)/../../../10-common/include/cbb/mxml \
		$(CURDIR)/../../../10-common/include/cbb/osp \
		$(CURDIR)/../../../10-common/include/cbb/charconversion \
		$(CURDIR)/../../../10-common/include/cbb/appclt \
		$(CURDIR)/../../../10-common/include/cbb/rp \
		$(CURDIR)/../../../10-common/include/cbb/mediaswitch \
		$(CURDIR)/../../../10-common/include/app\
		$(CURDIR)/../../../10-common/include/cbb/kdmfileinterface
		

CFLAGS += -D_HIS3516DV300_


ifeq ($(PWLIB_SUPPORT),1)
   INC_PATH += $(PWLIBDIR)/include/ptlib/unix $(PWLIBDIR)/include
endif


INSTALL_LIB_PATH = ../../../10-common/lib/release/his3516dv300

include $(COMM_DIR)/common.mk


