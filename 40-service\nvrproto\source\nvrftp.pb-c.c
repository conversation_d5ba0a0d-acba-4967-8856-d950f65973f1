/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: nvrftp.proto */

/* Do not generate deprecated warnings for self */
#ifndef PROTOBUF_C__NO_DEPRECATED
#define PROTOBUF_C__NO_DEPRECATED
#endif

#include "nvrftp.pb-c.h"
void   tpb_nvr_ftp_net_addr__init
                     (TPbNvrFtpNetAddr         *message)
{
  static TPbNvrFtpNetAddr init_value = TPB_NVR_FTP_NET_ADDR__INIT;
  *message = init_value;
}
size_t tpb_nvr_ftp_net_addr__get_packed_size
                     (const TPbNvrFtpNetAddr *message)
{
  assert(message->base.descriptor == &tpb_nvr_ftp_net_addr__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_ftp_net_addr__pack
                     (const TPbNvrFtpNetAddr *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_ftp_net_addr__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_ftp_net_addr__pack_to_buffer
                     (const TPbNvrFtpNetAddr *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_ftp_net_addr__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrFtpNetAddr *
       tpb_nvr_ftp_net_addr__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrFtpNetAddr *)
     protobuf_c_message_unpack (&tpb_nvr_ftp_net_addr__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_ftp_net_addr__free_unpacked
                     (TPbNvrFtpNetAddr *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_ftp_net_addr__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_ftp_server_param__init
                     (TPbNvrFtpServerParam         *message)
{
  static TPbNvrFtpServerParam init_value = TPB_NVR_FTP_SERVER_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_ftp_server_param__get_packed_size
                     (const TPbNvrFtpServerParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_ftp_server_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_ftp_server_param__pack
                     (const TPbNvrFtpServerParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_ftp_server_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_ftp_server_param__pack_to_buffer
                     (const TPbNvrFtpServerParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_ftp_server_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrFtpServerParam *
       tpb_nvr_ftp_server_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrFtpServerParam *)
     protobuf_c_message_unpack (&tpb_nvr_ftp_server_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_ftp_server_param__free_unpacked
                     (TPbNvrFtpServerParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_ftp_server_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_ftp_cfg__init
                     (TPbNvrFtpCfg         *message)
{
  static TPbNvrFtpCfg init_value = TPB_NVR_FTP_CFG__INIT;
  *message = init_value;
}
size_t tpb_nvr_ftp_cfg__get_packed_size
                     (const TPbNvrFtpCfg *message)
{
  assert(message->base.descriptor == &tpb_nvr_ftp_cfg__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_ftp_cfg__pack
                     (const TPbNvrFtpCfg *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_ftp_cfg__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_ftp_cfg__pack_to_buffer
                     (const TPbNvrFtpCfg *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_ftp_cfg__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrFtpCfg *
       tpb_nvr_ftp_cfg__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrFtpCfg *)
     protobuf_c_message_unpack (&tpb_nvr_ftp_cfg__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_ftp_cfg__free_unpacked
                     (TPbNvrFtpCfg *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_ftp_cfg__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
static const ProtobufCFieldDescriptor tpb_nvr_ftp_net_addr__field_descriptors[3] =
{
  {
    "ip_type",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrFtpNetAddr, has_ip_type),
    offsetof(TPbNvrFtpNetAddr, ip_type),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "server_addrv4",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrFtpNetAddr, has_server_addrv4),
    offsetof(TPbNvrFtpNetAddr, server_addrv4),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "server_addrv6",
    3,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrFtpNetAddr, n_server_addrv6),
    offsetof(TPbNvrFtpNetAddr, server_addrv6),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_ftp_net_addr__field_indices_by_name[] = {
  0,   /* field[0] = ip_type */
  1,   /* field[1] = server_addrv4 */
  2,   /* field[2] = server_addrv6 */
};
static const ProtobufCIntRange tpb_nvr_ftp_net_addr__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 3 }
};
const ProtobufCMessageDescriptor tpb_nvr_ftp_net_addr__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrFtpNetAddr",
  "TPbNvrFtpNetAddr",
  "TPbNvrFtpNetAddr",
  "",
  sizeof(TPbNvrFtpNetAddr),
  3,
  tpb_nvr_ftp_net_addr__field_descriptors,
  tpb_nvr_ftp_net_addr__field_indices_by_name,
  1,  tpb_nvr_ftp_net_addr__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_ftp_net_addr__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_ftp_server_param__field_descriptors[14] =
{
  {
    "server_addr",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrFtpServerParam, has_server_addr),
    offsetof(TPbNvrFtpServerParam, server_addr),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "server_port",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrFtpServerParam, has_server_port),
    offsetof(TPbNvrFtpServerParam, server_port),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "anonymous",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrFtpServerParam, has_anonymous),
    offsetof(TPbNvrFtpServerParam, anonymous),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "user_name",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BYTES,
    offsetof(TPbNvrFtpServerParam, has_user_name),
    offsetof(TPbNvrFtpServerParam, user_name),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "passwd",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BYTES,
    offsetof(TPbNvrFtpServerParam, has_passwd),
    offsetof(TPbNvrFtpServerParam, passwd),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "dir_struct",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrFtpServerParam, has_dir_struct),
    offsetof(TPbNvrFtpServerParam, dir_struct),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "l1_name",
    7,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrFtpServerParam, has_l1_name),
    offsetof(TPbNvrFtpServerParam, l1_name),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "l2_name",
    8,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrFtpServerParam, has_l2_name),
    offsetof(TPbNvrFtpServerParam, l2_name),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "dir_l1_custom_len",
    9,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrFtpServerParam, has_dir_l1_custom_len),
    offsetof(TPbNvrFtpServerParam, dir_l1_custom_len),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "dir_l1_custom",
    10,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BYTES,
    offsetof(TPbNvrFtpServerParam, has_dir_l1_custom),
    offsetof(TPbNvrFtpServerParam, dir_l1_custom),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "dir_l2_custom_len",
    11,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrFtpServerParam, has_dir_l2_custom_len),
    offsetof(TPbNvrFtpServerParam, dir_l2_custom_len),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "dir_l2_custom",
    12,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BYTES,
    offsetof(TPbNvrFtpServerParam, has_dir_l2_custom),
    offsetof(TPbNvrFtpServerParam, dir_l2_custom),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "upload_pic",
    13,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrFtpServerParam, has_upload_pic),
    offsetof(TPbNvrFtpServerParam, upload_pic),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "server_addrv4v6",
    14,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrFtpServerParam, server_addrv4v6),
    &tpb_nvr_ftp_net_addr__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_ftp_server_param__field_indices_by_name[] = {
  2,   /* field[2] = anonymous */
  9,   /* field[9] = dir_l1_custom */
  8,   /* field[8] = dir_l1_custom_len */
  11,   /* field[11] = dir_l2_custom */
  10,   /* field[10] = dir_l2_custom_len */
  5,   /* field[5] = dir_struct */
  6,   /* field[6] = l1_name */
  7,   /* field[7] = l2_name */
  4,   /* field[4] = passwd */
  0,   /* field[0] = server_addr */
  13,   /* field[13] = server_addrv4v6 */
  1,   /* field[1] = server_port */
  12,   /* field[12] = upload_pic */
  3,   /* field[3] = user_name */
};
static const ProtobufCIntRange tpb_nvr_ftp_server_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 14 }
};
const ProtobufCMessageDescriptor tpb_nvr_ftp_server_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrFtpServerParam",
  "TPbNvrFtpServerParam",
  "TPbNvrFtpServerParam",
  "",
  sizeof(TPbNvrFtpServerParam),
  14,
  tpb_nvr_ftp_server_param__field_descriptors,
  tpb_nvr_ftp_server_param__field_indices_by_name,
  1,  tpb_nvr_ftp_server_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_ftp_server_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_ftp_cfg__field_descriptors[1] =
{
  {
    "ftp_server_param",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrFtpCfg, ftp_server_param),
    &tpb_nvr_ftp_server_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_ftp_cfg__field_indices_by_name[] = {
  0,   /* field[0] = ftp_server_param */
};
static const ProtobufCIntRange tpb_nvr_ftp_cfg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor tpb_nvr_ftp_cfg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrFtpCfg",
  "TPbNvrFtpCfg",
  "TPbNvrFtpCfg",
  "",
  sizeof(TPbNvrFtpCfg),
  1,
  tpb_nvr_ftp_cfg__field_descriptors,
  tpb_nvr_ftp_cfg__field_indices_by_name,
  1,  tpb_nvr_ftp_cfg__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_ftp_cfg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
