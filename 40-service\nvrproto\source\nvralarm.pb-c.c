/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: nvralarm.proto */

/* Do not generate deprecated warnings for self */
#ifndef PROTOBUF_C__NO_DEPRECATED
#define PROTOBUF_C__NO_DEPRECATED
#endif

#include "nvralarm.pb-c.h"
void   tpb_nvr_alarm_in_basic_param__init
                     (TPbNvrAlarmInBasicParam         *message)
{
  static TPbNvrAlarmInBasicParam init_value = TPB_NVR_ALARM_IN_BASIC_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_alarm_in_basic_param__get_packed_size
                     (const TPbNvrAlarmInBasicParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_alarm_in_basic_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_alarm_in_basic_param__pack
                     (const TPbNvrAlarmInBasicParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_alarm_in_basic_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_alarm_in_basic_param__pack_to_buffer
                     (const TPbNvrAlarmInBasicParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_alarm_in_basic_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrAlarmInBasicParam *
       tpb_nvr_alarm_in_basic_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrAlarmInBasicParam *)
     protobuf_c_message_unpack (&tpb_nvr_alarm_in_basic_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_alarm_in_basic_param__free_unpacked
                     (TPbNvrAlarmInBasicParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_alarm_in_basic_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_alarm_time_seg__init
                     (TPbNvrAlarmTimeSeg         *message)
{
  static TPbNvrAlarmTimeSeg init_value = TPB_NVR_ALARM_TIME_SEG__INIT;
  *message = init_value;
}
size_t tpb_nvr_alarm_time_seg__get_packed_size
                     (const TPbNvrAlarmTimeSeg *message)
{
  assert(message->base.descriptor == &tpb_nvr_alarm_time_seg__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_alarm_time_seg__pack
                     (const TPbNvrAlarmTimeSeg *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_alarm_time_seg__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_alarm_time_seg__pack_to_buffer
                     (const TPbNvrAlarmTimeSeg *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_alarm_time_seg__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrAlarmTimeSeg *
       tpb_nvr_alarm_time_seg__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrAlarmTimeSeg *)
     protobuf_c_message_unpack (&tpb_nvr_alarm_time_seg__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_alarm_time_seg__free_unpacked
                     (TPbNvrAlarmTimeSeg *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_alarm_time_seg__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_alarm_guard_time_param__init
                     (TPbNvrAlarmGuardTimeParam         *message)
{
  static TPbNvrAlarmGuardTimeParam init_value = TPB_NVR_ALARM_GUARD_TIME_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_alarm_guard_time_param__get_packed_size
                     (const TPbNvrAlarmGuardTimeParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_alarm_guard_time_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_alarm_guard_time_param__pack
                     (const TPbNvrAlarmGuardTimeParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_alarm_guard_time_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_alarm_guard_time_param__pack_to_buffer
                     (const TPbNvrAlarmGuardTimeParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_alarm_guard_time_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrAlarmGuardTimeParam *
       tpb_nvr_alarm_guard_time_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrAlarmGuardTimeParam *)
     protobuf_c_message_unpack (&tpb_nvr_alarm_guard_time_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_alarm_guard_time_param__free_unpacked
                     (TPbNvrAlarmGuardTimeParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_alarm_guard_time_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_alarm_link_param__init
                     (TPbNvrAlarmLinkParam         *message)
{
  static TPbNvrAlarmLinkParam init_value = TPB_NVR_ALARM_LINK_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_alarm_link_param__get_packed_size
                     (const TPbNvrAlarmLinkParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_alarm_link_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_alarm_link_param__pack
                     (const TPbNvrAlarmLinkParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_alarm_link_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_alarm_link_param__pack_to_buffer
                     (const TPbNvrAlarmLinkParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_alarm_link_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrAlarmLinkParam *
       tpb_nvr_alarm_link_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrAlarmLinkParam *)
     protobuf_c_message_unpack (&tpb_nvr_alarm_link_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_alarm_link_param__free_unpacked
                     (TPbNvrAlarmLinkParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_alarm_link_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_alarm_sys_alarm_param__init
                     (TPbNvrAlarmSysAlarmParam         *message)
{
  static TPbNvrAlarmSysAlarmParam init_value = TPB_NVR_ALARM_SYS_ALARM_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_alarm_sys_alarm_param__get_packed_size
                     (const TPbNvrAlarmSysAlarmParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_alarm_sys_alarm_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_alarm_sys_alarm_param__pack
                     (const TPbNvrAlarmSysAlarmParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_alarm_sys_alarm_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_alarm_sys_alarm_param__pack_to_buffer
                     (const TPbNvrAlarmSysAlarmParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_alarm_sys_alarm_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrAlarmSysAlarmParam *
       tpb_nvr_alarm_sys_alarm_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrAlarmSysAlarmParam *)
     protobuf_c_message_unpack (&tpb_nvr_alarm_sys_alarm_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_alarm_sys_alarm_param__free_unpacked
                     (TPbNvrAlarmSysAlarmParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_alarm_sys_alarm_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_alarm_basic_cfg__init
                     (TPbNvrAlarmBasicCfg         *message)
{
  static TPbNvrAlarmBasicCfg init_value = TPB_NVR_ALARM_BASIC_CFG__INIT;
  *message = init_value;
}
size_t tpb_nvr_alarm_basic_cfg__get_packed_size
                     (const TPbNvrAlarmBasicCfg *message)
{
  assert(message->base.descriptor == &tpb_nvr_alarm_basic_cfg__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_alarm_basic_cfg__pack
                     (const TPbNvrAlarmBasicCfg *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_alarm_basic_cfg__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_alarm_basic_cfg__pack_to_buffer
                     (const TPbNvrAlarmBasicCfg *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_alarm_basic_cfg__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrAlarmBasicCfg *
       tpb_nvr_alarm_basic_cfg__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrAlarmBasicCfg *)
     protobuf_c_message_unpack (&tpb_nvr_alarm_basic_cfg__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_alarm_basic_cfg__free_unpacked
                     (TPbNvrAlarmBasicCfg *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_alarm_basic_cfg__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_alarm_guard_cfg__init
                     (TPbNvrAlarmGuardCfg         *message)
{
  static TPbNvrAlarmGuardCfg init_value = TPB_NVR_ALARM_GUARD_CFG__INIT;
  *message = init_value;
}
size_t tpb_nvr_alarm_guard_cfg__get_packed_size
                     (const TPbNvrAlarmGuardCfg *message)
{
  assert(message->base.descriptor == &tpb_nvr_alarm_guard_cfg__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_alarm_guard_cfg__pack
                     (const TPbNvrAlarmGuardCfg *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_alarm_guard_cfg__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_alarm_guard_cfg__pack_to_buffer
                     (const TPbNvrAlarmGuardCfg *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_alarm_guard_cfg__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrAlarmGuardCfg *
       tpb_nvr_alarm_guard_cfg__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrAlarmGuardCfg *)
     protobuf_c_message_unpack (&tpb_nvr_alarm_guard_cfg__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_alarm_guard_cfg__free_unpacked
                     (TPbNvrAlarmGuardCfg *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_alarm_guard_cfg__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_alarm_link_cfg__init
                     (TPbNvrAlarmLinkCfg         *message)
{
  static TPbNvrAlarmLinkCfg init_value = TPB_NVR_ALARM_LINK_CFG__INIT;
  *message = init_value;
}
size_t tpb_nvr_alarm_link_cfg__get_packed_size
                     (const TPbNvrAlarmLinkCfg *message)
{
  assert(message->base.descriptor == &tpb_nvr_alarm_link_cfg__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_alarm_link_cfg__pack
                     (const TPbNvrAlarmLinkCfg *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_alarm_link_cfg__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_alarm_link_cfg__pack_to_buffer
                     (const TPbNvrAlarmLinkCfg *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_alarm_link_cfg__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrAlarmLinkCfg *
       tpb_nvr_alarm_link_cfg__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrAlarmLinkCfg *)
     protobuf_c_message_unpack (&tpb_nvr_alarm_link_cfg__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_alarm_link_cfg__free_unpacked
                     (TPbNvrAlarmLinkCfg *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_alarm_link_cfg__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_alarm_out_dev_cfg__init
                     (TPbNvrAlarmOutDevCfg         *message)
{
  static TPbNvrAlarmOutDevCfg init_value = TPB_NVR_ALARM_OUT_DEV_CFG__INIT;
  *message = init_value;
}
size_t tpb_nvr_alarm_out_dev_cfg__get_packed_size
                     (const TPbNvrAlarmOutDevCfg *message)
{
  assert(message->base.descriptor == &tpb_nvr_alarm_out_dev_cfg__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_alarm_out_dev_cfg__pack
                     (const TPbNvrAlarmOutDevCfg *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_alarm_out_dev_cfg__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_alarm_out_dev_cfg__pack_to_buffer
                     (const TPbNvrAlarmOutDevCfg *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_alarm_out_dev_cfg__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrAlarmOutDevCfg *
       tpb_nvr_alarm_out_dev_cfg__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrAlarmOutDevCfg *)
     protobuf_c_message_unpack (&tpb_nvr_alarm_out_dev_cfg__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_alarm_out_dev_cfg__free_unpacked
                     (TPbNvrAlarmOutDevCfg *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_alarm_out_dev_cfg__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_alarm_out_cfg__init
                     (TPbNvrAlarmOutCfg         *message)
{
  static TPbNvrAlarmOutCfg init_value = TPB_NVR_ALARM_OUT_CFG__INIT;
  *message = init_value;
}
size_t tpb_nvr_alarm_out_cfg__get_packed_size
                     (const TPbNvrAlarmOutCfg *message)
{
  assert(message->base.descriptor == &tpb_nvr_alarm_out_cfg__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_alarm_out_cfg__pack
                     (const TPbNvrAlarmOutCfg *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_alarm_out_cfg__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_alarm_out_cfg__pack_to_buffer
                     (const TPbNvrAlarmOutCfg *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_alarm_out_cfg__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrAlarmOutCfg *
       tpb_nvr_alarm_out_cfg__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrAlarmOutCfg *)
     protobuf_c_message_unpack (&tpb_nvr_alarm_out_cfg__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_alarm_out_cfg__free_unpacked
                     (TPbNvrAlarmOutCfg *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_alarm_out_cfg__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
static const ProtobufCFieldDescriptor tpb_nvr_alarm_in_basic_param__field_descriptors[3] =
{
  {
    "alarm_type",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrAlarmInBasicParam, has_alarm_type),
    offsetof(TPbNvrAlarmInBasicParam, alarm_type),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "alarm_name_len",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrAlarmInBasicParam, has_alarm_name_len),
    offsetof(TPbNvrAlarmInBasicParam, alarm_name_len),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "alarm_name",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BYTES,
    offsetof(TPbNvrAlarmInBasicParam, has_alarm_name),
    offsetof(TPbNvrAlarmInBasicParam, alarm_name),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_alarm_in_basic_param__field_indices_by_name[] = {
  2,   /* field[2] = alarm_name */
  1,   /* field[1] = alarm_name_len */
  0,   /* field[0] = alarm_type */
};
static const ProtobufCIntRange tpb_nvr_alarm_in_basic_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 3 }
};
const ProtobufCMessageDescriptor tpb_nvr_alarm_in_basic_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrAlarmInBasicParam",
  "TPbNvrAlarmInBasicParam",
  "TPbNvrAlarmInBasicParam",
  "",
  sizeof(TPbNvrAlarmInBasicParam),
  3,
  tpb_nvr_alarm_in_basic_param__field_descriptors,
  tpb_nvr_alarm_in_basic_param__field_indices_by_name,
  1,  tpb_nvr_alarm_in_basic_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_alarm_in_basic_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_alarm_time_seg__field_descriptors[2] =
{
  {
    "start_time",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrAlarmTimeSeg, has_start_time),
    offsetof(TPbNvrAlarmTimeSeg, start_time),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "end_time",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrAlarmTimeSeg, has_end_time),
    offsetof(TPbNvrAlarmTimeSeg, end_time),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_alarm_time_seg__field_indices_by_name[] = {
  1,   /* field[1] = end_time */
  0,   /* field[0] = start_time */
};
static const ProtobufCIntRange tpb_nvr_alarm_time_seg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_alarm_time_seg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrAlarmTimeSeg",
  "TPbNvrAlarmTimeSeg",
  "TPbNvrAlarmTimeSeg",
  "",
  sizeof(TPbNvrAlarmTimeSeg),
  2,
  tpb_nvr_alarm_time_seg__field_descriptors,
  tpb_nvr_alarm_time_seg__field_indices_by_name,
  1,  tpb_nvr_alarm_time_seg__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_alarm_time_seg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_alarm_guard_time_param__field_descriptors[6] =
{
  {
    "dev_id",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrAlarmGuardTimeParam, has_dev_id),
    offsetof(TPbNvrAlarmGuardTimeParam, dev_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "alarm_type",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrAlarmGuardTimeParam, has_alarm_type),
    offsetof(TPbNvrAlarmGuardTimeParam, alarm_type),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "alarm_num",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrAlarmGuardTimeParam, has_alarm_num),
    offsetof(TPbNvrAlarmGuardTimeParam, alarm_num),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "week_id",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrAlarmGuardTimeParam, has_week_id),
    offsetof(TPbNvrAlarmGuardTimeParam, week_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "time_seg_id",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrAlarmGuardTimeParam, has_time_seg_id),
    offsetof(TPbNvrAlarmGuardTimeParam, time_seg_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "time_seg",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrAlarmGuardTimeParam, time_seg),
    &tpb_nvr_alarm_time_seg__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_alarm_guard_time_param__field_indices_by_name[] = {
  2,   /* field[2] = alarm_num */
  1,   /* field[1] = alarm_type */
  0,   /* field[0] = dev_id */
  5,   /* field[5] = time_seg */
  4,   /* field[4] = time_seg_id */
  3,   /* field[3] = week_id */
};
static const ProtobufCIntRange tpb_nvr_alarm_guard_time_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 6 }
};
const ProtobufCMessageDescriptor tpb_nvr_alarm_guard_time_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrAlarmGuardTimeParam",
  "TPbNvrAlarmGuardTimeParam",
  "TPbNvrAlarmGuardTimeParam",
  "",
  sizeof(TPbNvrAlarmGuardTimeParam),
  6,
  tpb_nvr_alarm_guard_time_param__field_descriptors,
  tpb_nvr_alarm_guard_time_param__field_indices_by_name,
  1,  tpb_nvr_alarm_guard_time_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_alarm_guard_time_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_alarm_link_param__field_descriptors[9] =
{
  {
    "dev_id",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrAlarmLinkParam, has_dev_id),
    offsetof(TPbNvrAlarmLinkParam, dev_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "alarm_type",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrAlarmLinkParam, has_alarm_type),
    offsetof(TPbNvrAlarmLinkParam, alarm_type),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "alarm_num",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrAlarmLinkParam, has_alarm_num),
    offsetof(TPbNvrAlarmLinkParam, alarm_num),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "link_dev_id",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrAlarmLinkParam, has_link_dev_id),
    offsetof(TPbNvrAlarmLinkParam, link_dev_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "link_param",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrAlarmLinkParam, has_link_param),
    offsetof(TPbNvrAlarmLinkParam, link_param),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "sound_type",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BYTES,
    offsetof(TPbNvrAlarmLinkParam, has_sound_type),
    offsetof(TPbNvrAlarmLinkParam, sound_type),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "sound_text",
    7,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BYTES,
    offsetof(TPbNvrAlarmLinkParam, has_sound_text),
    offsetof(TPbNvrAlarmLinkParam, sound_text),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "alarm_times",
    8,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrAlarmLinkParam, has_alarm_times),
    offsetof(TPbNvrAlarmLinkParam, alarm_times),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "alarm_time_interval",
    9,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrAlarmLinkParam, has_alarm_time_interval),
    offsetof(TPbNvrAlarmLinkParam, alarm_time_interval),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_alarm_link_param__field_indices_by_name[] = {
  2,   /* field[2] = alarm_num */
  8,   /* field[8] = alarm_time_interval */
  7,   /* field[7] = alarm_times */
  1,   /* field[1] = alarm_type */
  0,   /* field[0] = dev_id */
  3,   /* field[3] = link_dev_id */
  4,   /* field[4] = link_param */
  6,   /* field[6] = sound_text */
  5,   /* field[5] = sound_type */
};
static const ProtobufCIntRange tpb_nvr_alarm_link_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 9 }
};
const ProtobufCMessageDescriptor tpb_nvr_alarm_link_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrAlarmLinkParam",
  "TPbNvrAlarmLinkParam",
  "TPbNvrAlarmLinkParam",
  "",
  sizeof(TPbNvrAlarmLinkParam),
  9,
  tpb_nvr_alarm_link_param__field_descriptors,
  tpb_nvr_alarm_link_param__field_indices_by_name,
  1,  tpb_nvr_alarm_link_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_alarm_link_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_alarm_sys_alarm_param__field_descriptors[9] =
{
  {
    "sound_en",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrAlarmSysAlarmParam, has_sound_en),
    offsetof(TPbNvrAlarmSysAlarmParam, sound_en),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "mail_post_en",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrAlarmSysAlarmParam, has_mail_post_en),
    offsetof(TPbNvrAlarmSysAlarmParam, mail_post_en),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "light_flick",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrAlarmSysAlarmParam, has_light_flick),
    offsetof(TPbNvrAlarmSysAlarmParam, light_flick),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "sound_beep",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrAlarmSysAlarmParam, has_sound_beep),
    offsetof(TPbNvrAlarmSysAlarmParam, sound_beep),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "sound_out",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrAlarmSysAlarmParam, has_sound_out),
    offsetof(TPbNvrAlarmSysAlarmParam, sound_out),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "sound_type",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrAlarmSysAlarmParam, has_sound_type),
    offsetof(TPbNvrAlarmSysAlarmParam, sound_type),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "sound_text_id",
    7,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrAlarmSysAlarmParam, has_sound_text_id),
    offsetof(TPbNvrAlarmSysAlarmParam, sound_text_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "alarm_times",
    8,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrAlarmSysAlarmParam, has_alarm_times),
    offsetof(TPbNvrAlarmSysAlarmParam, alarm_times),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "alarm_time_interval",
    9,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrAlarmSysAlarmParam, has_alarm_time_interval),
    offsetof(TPbNvrAlarmSysAlarmParam, alarm_time_interval),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_alarm_sys_alarm_param__field_indices_by_name[] = {
  8,   /* field[8] = alarm_time_interval */
  7,   /* field[7] = alarm_times */
  2,   /* field[2] = light_flick */
  1,   /* field[1] = mail_post_en */
  3,   /* field[3] = sound_beep */
  0,   /* field[0] = sound_en */
  4,   /* field[4] = sound_out */
  6,   /* field[6] = sound_text_id */
  5,   /* field[5] = sound_type */
};
static const ProtobufCIntRange tpb_nvr_alarm_sys_alarm_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 9 }
};
const ProtobufCMessageDescriptor tpb_nvr_alarm_sys_alarm_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrAlarmSysAlarmParam",
  "TPbNvrAlarmSysAlarmParam",
  "TPbNvrAlarmSysAlarmParam",
  "",
  sizeof(TPbNvrAlarmSysAlarmParam),
  9,
  tpb_nvr_alarm_sys_alarm_param__field_descriptors,
  tpb_nvr_alarm_sys_alarm_param__field_indices_by_name,
  1,  tpb_nvr_alarm_sys_alarm_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_alarm_sys_alarm_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_alarm_basic_cfg__field_descriptors[2] =
{
  {
    "nvr_alarm_in_basic_param",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrAlarmBasicCfg, n_nvr_alarm_in_basic_param),
    offsetof(TPbNvrAlarmBasicCfg, nvr_alarm_in_basic_param),
    &tpb_nvr_alarm_in_basic_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "nvr_alarm_sys_alarm_param",
    2,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrAlarmBasicCfg, n_nvr_alarm_sys_alarm_param),
    offsetof(TPbNvrAlarmBasicCfg, nvr_alarm_sys_alarm_param),
    &tpb_nvr_alarm_sys_alarm_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_alarm_basic_cfg__field_indices_by_name[] = {
  0,   /* field[0] = nvr_alarm_in_basic_param */
  1,   /* field[1] = nvr_alarm_sys_alarm_param */
};
static const ProtobufCIntRange tpb_nvr_alarm_basic_cfg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_alarm_basic_cfg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrAlarmBasicCfg",
  "TPbNvrAlarmBasicCfg",
  "TPbNvrAlarmBasicCfg",
  "",
  sizeof(TPbNvrAlarmBasicCfg),
  2,
  tpb_nvr_alarm_basic_cfg__field_descriptors,
  tpb_nvr_alarm_basic_cfg__field_indices_by_name,
  1,  tpb_nvr_alarm_basic_cfg__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_alarm_basic_cfg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_alarm_guard_cfg__field_descriptors[1] =
{
  {
    "nvr_alarm_guard_time_param",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrAlarmGuardCfg, n_nvr_alarm_guard_time_param),
    offsetof(TPbNvrAlarmGuardCfg, nvr_alarm_guard_time_param),
    &tpb_nvr_alarm_guard_time_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_alarm_guard_cfg__field_indices_by_name[] = {
  0,   /* field[0] = nvr_alarm_guard_time_param */
};
static const ProtobufCIntRange tpb_nvr_alarm_guard_cfg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor tpb_nvr_alarm_guard_cfg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrAlarmGuardCfg",
  "TPbNvrAlarmGuardCfg",
  "TPbNvrAlarmGuardCfg",
  "",
  sizeof(TPbNvrAlarmGuardCfg),
  1,
  tpb_nvr_alarm_guard_cfg__field_descriptors,
  tpb_nvr_alarm_guard_cfg__field_indices_by_name,
  1,  tpb_nvr_alarm_guard_cfg__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_alarm_guard_cfg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_alarm_link_cfg__field_descriptors[1] =
{
  {
    "nvr_alarm_link_param",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrAlarmLinkCfg, n_nvr_alarm_link_param),
    offsetof(TPbNvrAlarmLinkCfg, nvr_alarm_link_param),
    &tpb_nvr_alarm_link_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_alarm_link_cfg__field_indices_by_name[] = {
  0,   /* field[0] = nvr_alarm_link_param */
};
static const ProtobufCIntRange tpb_nvr_alarm_link_cfg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor tpb_nvr_alarm_link_cfg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrAlarmLinkCfg",
  "TPbNvrAlarmLinkCfg",
  "TPbNvrAlarmLinkCfg",
  "",
  sizeof(TPbNvrAlarmLinkCfg),
  1,
  tpb_nvr_alarm_link_cfg__field_descriptors,
  tpb_nvr_alarm_link_cfg__field_indices_by_name,
  1,  tpb_nvr_alarm_link_cfg__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_alarm_link_cfg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_alarm_out_dev_cfg__field_descriptors[2] =
{
  {
    "dev_id",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrAlarmOutDevCfg, has_dev_id),
    offsetof(TPbNvrAlarmOutDevCfg, dev_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "delay_time",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrAlarmOutDevCfg, has_delay_time),
    offsetof(TPbNvrAlarmOutDevCfg, delay_time),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_alarm_out_dev_cfg__field_indices_by_name[] = {
  1,   /* field[1] = delay_time */
  0,   /* field[0] = dev_id */
};
static const ProtobufCIntRange tpb_nvr_alarm_out_dev_cfg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_alarm_out_dev_cfg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrAlarmOutDevCfg",
  "TPbNvrAlarmOutDevCfg",
  "TPbNvrAlarmOutDevCfg",
  "",
  sizeof(TPbNvrAlarmOutDevCfg),
  2,
  tpb_nvr_alarm_out_dev_cfg__field_descriptors,
  tpb_nvr_alarm_out_dev_cfg__field_indices_by_name,
  1,  tpb_nvr_alarm_out_dev_cfg__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_alarm_out_dev_cfg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_alarm_out_cfg__field_descriptors[1] =
{
  {
    "alarm_out_param",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrAlarmOutCfg, n_alarm_out_param),
    offsetof(TPbNvrAlarmOutCfg, alarm_out_param),
    &tpb_nvr_alarm_out_dev_cfg__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_alarm_out_cfg__field_indices_by_name[] = {
  0,   /* field[0] = alarm_out_param */
};
static const ProtobufCIntRange tpb_nvr_alarm_out_cfg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor tpb_nvr_alarm_out_cfg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrAlarmOutCfg",
  "TPbNvrAlarmOutCfg",
  "TPbNvrAlarmOutCfg",
  "",
  sizeof(TPbNvrAlarmOutCfg),
  1,
  tpb_nvr_alarm_out_cfg__field_descriptors,
  tpb_nvr_alarm_out_cfg__field_indices_by_name,
  1,  tpb_nvr_alarm_out_cfg__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_alarm_out_cfg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCEnumValue em_pb_nvr_alarm_enabel_switch__enum_values_by_number[2] =
{
  { "DISABLE", "EM_PB_NVR_ALARM_ENABEL_SWITCH__DISABLE", 0 },
  { "ENABLE", "EM_PB_NVR_ALARM_ENABEL_SWITCH__ENABLE", 1 },
};
static const ProtobufCIntRange em_pb_nvr_alarm_enabel_switch__value_ranges[] = {
{0, 0},{0, 2}
};
static const ProtobufCEnumValueIndex em_pb_nvr_alarm_enabel_switch__enum_values_by_name[2] =
{
  { "DISABLE", 0 },
  { "ENABLE", 1 },
};
const ProtobufCEnumDescriptor em_pb_nvr_alarm_enabel_switch__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "EmPbNvrAlarmEnabelSwitch",
  "EmPbNvrAlarmEnabelSwitch",
  "EmPbNvrAlarmEnabelSwitch",
  "",
  2,
  em_pb_nvr_alarm_enabel_switch__enum_values_by_number,
  2,
  em_pb_nvr_alarm_enabel_switch__enum_values_by_name,
  1,
  em_pb_nvr_alarm_enabel_switch__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
static const ProtobufCEnumValue em_pb_nvr_alarm_type__enum_values_by_number[2] =
{
  { "NORMALCLOSE", "EM_PB_NVR_ALARM_TYPE__NORMALCLOSE", 0 },
  { "NORMALOPEN", "EM_PB_NVR_ALARM_TYPE__NORMALOPEN", 1 },
};
static const ProtobufCIntRange em_pb_nvr_alarm_type__value_ranges[] = {
{0, 0},{0, 2}
};
static const ProtobufCEnumValueIndex em_pb_nvr_alarm_type__enum_values_by_name[2] =
{
  { "NORMALCLOSE", 0 },
  { "NORMALOPEN", 1 },
};
const ProtobufCEnumDescriptor em_pb_nvr_alarm_type__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "EmPbNvrAlarmType",
  "EmPbNvrAlarmType",
  "EmPbNvrAlarmType",
  "",
  2,
  em_pb_nvr_alarm_type__enum_values_by_number,
  2,
  em_pb_nvr_alarm_type__enum_values_by_name,
  1,
  em_pb_nvr_alarm_type__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
static const ProtobufCEnumValue en_pb_nvr_alarm_linkrec__enum_values_by_number[2] =
{
  { "MAINSTREAM", "EN_PB_NVR_ALARM_LINKREC__MAINSTREAM", 0 },
  { "SECSTREAM", "EN_PB_NVR_ALARM_LINKREC__SECSTREAM", 1 },
};
static const ProtobufCIntRange en_pb_nvr_alarm_linkrec__value_ranges[] = {
{0, 0},{0, 2}
};
static const ProtobufCEnumValueIndex en_pb_nvr_alarm_linkrec__enum_values_by_name[2] =
{
  { "MAINSTREAM", 0 },
  { "SECSTREAM", 1 },
};
const ProtobufCEnumDescriptor en_pb_nvr_alarm_linkrec__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "EnPbNvrAlarmLinkrec",
  "EnPbNvrAlarmLinkrec",
  "EnPbNvrAlarmLinkrec",
  "",
  2,
  en_pb_nvr_alarm_linkrec__enum_values_by_number,
  2,
  en_pb_nvr_alarm_linkrec__enum_values_by_name,
  1,
  en_pb_nvr_alarm_linkrec__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
static const ProtobufCEnumValue en_pb_nvr_alarm_week__enum_values_by_number[8] =
{
  { "MONDAY", "EN_PB_NVR_ALARM_WEEK__MONDAY", 0 },
  { "TUESDAY", "EN_PB_NVR_ALARM_WEEK__TUESDAY", 1 },
  { "WEDNESDAY", "EN_PB_NVR_ALARM_WEEK__WEDNESDAY", 2 },
  { "THURSDAY", "EN_PB_NVR_ALARM_WEEK__THURSDAY", 3 },
  { "FRIDAY", "EN_PB_NVR_ALARM_WEEK__FRIDAY", 4 },
  { "SATURDAY", "EN_PB_NVR_ALARM_WEEK__SATURDAY", 5 },
  { "SUNDAY", "EN_PB_NVR_ALARM_WEEK__SUNDAY", 6 },
  { "HOLIDAY", "EN_PB_NVR_ALARM_WEEK__HOLIDAY", 7 },
};
static const ProtobufCIntRange en_pb_nvr_alarm_week__value_ranges[] = {
{0, 0},{0, 8}
};
static const ProtobufCEnumValueIndex en_pb_nvr_alarm_week__enum_values_by_name[8] =
{
  { "FRIDAY", 4 },
  { "HOLIDAY", 7 },
  { "MONDAY", 0 },
  { "SATURDAY", 5 },
  { "SUNDAY", 6 },
  { "THURSDAY", 3 },
  { "TUESDAY", 1 },
  { "WEDNESDAY", 2 },
};
const ProtobufCEnumDescriptor en_pb_nvr_alarm_week__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "EnPbNvrAlarmWeek",
  "EnPbNvrAlarmWeek",
  "EnPbNvrAlarmWeek",
  "",
  8,
  en_pb_nvr_alarm_week__enum_values_by_number,
  8,
  en_pb_nvr_alarm_week__enum_values_by_name,
  1,
  en_pb_nvr_alarm_week__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
