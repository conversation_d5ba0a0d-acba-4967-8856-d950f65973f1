/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: nvrrec.proto */

/* Do not generate deprecated warnings for self */
#ifndef PROTOBUF_C__NO_DEPRECATED
#define PROTOBUF_C__NO_DEPRECATED
#endif

#include "nvrrec.pb-c.h"
void   tpb_nvr_rec_srv_no_rec_cfg__init
                     (TPbNvrRecSrvNoRecCfg         *message)
{
  static TPbNvrRecSrvNoRecCfg init_value = TPB_NVR_REC_SRV_NO_REC_CFG__INIT;
  *message = init_value;
}
size_t tpb_nvr_rec_srv_no_rec_cfg__get_packed_size
                     (const TPbNvrRecSrvNoRecCfg *message)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_no_rec_cfg__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_rec_srv_no_rec_cfg__pack
                     (const TPbNvrRecSrvNoRecCfg *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_no_rec_cfg__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_rec_srv_no_rec_cfg__pack_to_buffer
                     (const TPbNvrRecSrvNoRecCfg *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_no_rec_cfg__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrRecSrvNoRecCfg *
       tpb_nvr_rec_srv_no_rec_cfg__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrRecSrvNoRecCfg *)
     protobuf_c_message_unpack (&tpb_nvr_rec_srv_no_rec_cfg__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_rec_srv_no_rec_cfg__free_unpacked
                     (TPbNvrRecSrvNoRecCfg *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_no_rec_cfg__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_rec_srv_mgr_adv_cfg__init
                     (TPbNvrRecSrvMgrAdvCfg         *message)
{
  static TPbNvrRecSrvMgrAdvCfg init_value = TPB_NVR_REC_SRV_MGR_ADV_CFG__INIT;
  *message = init_value;
}
size_t tpb_nvr_rec_srv_mgr_adv_cfg__get_packed_size
                     (const TPbNvrRecSrvMgrAdvCfg *message)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_mgr_adv_cfg__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_rec_srv_mgr_adv_cfg__pack
                     (const TPbNvrRecSrvMgrAdvCfg *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_mgr_adv_cfg__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_rec_srv_mgr_adv_cfg__pack_to_buffer
                     (const TPbNvrRecSrvMgrAdvCfg *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_mgr_adv_cfg__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrRecSrvMgrAdvCfg *
       tpb_nvr_rec_srv_mgr_adv_cfg__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrRecSrvMgrAdvCfg *)
     protobuf_c_message_unpack (&tpb_nvr_rec_srv_mgr_adv_cfg__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_rec_srv_mgr_adv_cfg__free_unpacked
                     (TPbNvrRecSrvMgrAdvCfg *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_mgr_adv_cfg__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_rec_srv_snap_cfg__init
                     (TPbNvrRecSrvSnapCfg         *message)
{
  static TPbNvrRecSrvSnapCfg init_value = TPB_NVR_REC_SRV_SNAP_CFG__INIT;
  *message = init_value;
}
size_t tpb_nvr_rec_srv_snap_cfg__get_packed_size
                     (const TPbNvrRecSrvSnapCfg *message)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_snap_cfg__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_rec_srv_snap_cfg__pack
                     (const TPbNvrRecSrvSnapCfg *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_snap_cfg__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_rec_srv_snap_cfg__pack_to_buffer
                     (const TPbNvrRecSrvSnapCfg *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_snap_cfg__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrRecSrvSnapCfg *
       tpb_nvr_rec_srv_snap_cfg__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrRecSrvSnapCfg *)
     protobuf_c_message_unpack (&tpb_nvr_rec_srv_snap_cfg__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_rec_srv_snap_cfg__free_unpacked
                     (TPbNvrRecSrvSnapCfg *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_snap_cfg__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_rec_srv_whole_cfg__init
                     (TPbNvrRecSrvWholeCfg         *message)
{
  static TPbNvrRecSrvWholeCfg init_value = TPB_NVR_REC_SRV_WHOLE_CFG__INIT;
  *message = init_value;
}
size_t tpb_nvr_rec_srv_whole_cfg__get_packed_size
                     (const TPbNvrRecSrvWholeCfg *message)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_whole_cfg__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_rec_srv_whole_cfg__pack
                     (const TPbNvrRecSrvWholeCfg *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_whole_cfg__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_rec_srv_whole_cfg__pack_to_buffer
                     (const TPbNvrRecSrvWholeCfg *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_whole_cfg__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrRecSrvWholeCfg *
       tpb_nvr_rec_srv_whole_cfg__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrRecSrvWholeCfg *)
     protobuf_c_message_unpack (&tpb_nvr_rec_srv_whole_cfg__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_rec_srv_whole_cfg__free_unpacked
                     (TPbNvrRecSrvWholeCfg *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_whole_cfg__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_rec_srv_mode_cfg_item__init
                     (TPbNvrRecSrvModeCfgItem         *message)
{
  static TPbNvrRecSrvModeCfgItem init_value = TPB_NVR_REC_SRV_MODE_CFG_ITEM__INIT;
  *message = init_value;
}
size_t tpb_nvr_rec_srv_mode_cfg_item__get_packed_size
                     (const TPbNvrRecSrvModeCfgItem *message)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_mode_cfg_item__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_rec_srv_mode_cfg_item__pack
                     (const TPbNvrRecSrvModeCfgItem *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_mode_cfg_item__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_rec_srv_mode_cfg_item__pack_to_buffer
                     (const TPbNvrRecSrvModeCfgItem *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_mode_cfg_item__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrRecSrvModeCfgItem *
       tpb_nvr_rec_srv_mode_cfg_item__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrRecSrvModeCfgItem *)
     protobuf_c_message_unpack (&tpb_nvr_rec_srv_mode_cfg_item__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_rec_srv_mode_cfg_item__free_unpacked
                     (TPbNvrRecSrvModeCfgItem *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_mode_cfg_item__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_rec_srv_mode_cfg__init
                     (TPbNvrRecSrvModeCfg         *message)
{
  static TPbNvrRecSrvModeCfg init_value = TPB_NVR_REC_SRV_MODE_CFG__INIT;
  *message = init_value;
}
size_t tpb_nvr_rec_srv_mode_cfg__get_packed_size
                     (const TPbNvrRecSrvModeCfg *message)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_mode_cfg__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_rec_srv_mode_cfg__pack
                     (const TPbNvrRecSrvModeCfg *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_mode_cfg__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_rec_srv_mode_cfg__pack_to_buffer
                     (const TPbNvrRecSrvModeCfg *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_mode_cfg__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrRecSrvModeCfg *
       tpb_nvr_rec_srv_mode_cfg__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrRecSrvModeCfg *)
     protobuf_c_message_unpack (&tpb_nvr_rec_srv_mode_cfg__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_rec_srv_mode_cfg__free_unpacked
                     (TPbNvrRecSrvModeCfg *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_mode_cfg__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_rec_srv_rec_chn_day_plan_section__init
                     (TPbNvrRecSrvRecChnDayPlanSection         *message)
{
  static TPbNvrRecSrvRecChnDayPlanSection init_value = TPB_NVR_REC_SRV_REC_CHN_DAY_PLAN_SECTION__INIT;
  *message = init_value;
}
size_t tpb_nvr_rec_srv_rec_chn_day_plan_section__get_packed_size
                     (const TPbNvrRecSrvRecChnDayPlanSection *message)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_rec_chn_day_plan_section__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_rec_srv_rec_chn_day_plan_section__pack
                     (const TPbNvrRecSrvRecChnDayPlanSection *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_rec_chn_day_plan_section__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_rec_srv_rec_chn_day_plan_section__pack_to_buffer
                     (const TPbNvrRecSrvRecChnDayPlanSection *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_rec_chn_day_plan_section__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrRecSrvRecChnDayPlanSection *
       tpb_nvr_rec_srv_rec_chn_day_plan_section__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrRecSrvRecChnDayPlanSection *)
     protobuf_c_message_unpack (&tpb_nvr_rec_srv_rec_chn_day_plan_section__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_rec_srv_rec_chn_day_plan_section__free_unpacked
                     (TPbNvrRecSrvRecChnDayPlanSection *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_rec_chn_day_plan_section__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_rec_srv_rec_chn_day_plan_section_cfg__init
                     (TPbNvrRecSrvRecChnDayPlanSectionCfg         *message)
{
  static TPbNvrRecSrvRecChnDayPlanSectionCfg init_value = TPB_NVR_REC_SRV_REC_CHN_DAY_PLAN_SECTION_CFG__INIT;
  *message = init_value;
}
size_t tpb_nvr_rec_srv_rec_chn_day_plan_section_cfg__get_packed_size
                     (const TPbNvrRecSrvRecChnDayPlanSectionCfg *message)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_rec_chn_day_plan_section_cfg__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_rec_srv_rec_chn_day_plan_section_cfg__pack
                     (const TPbNvrRecSrvRecChnDayPlanSectionCfg *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_rec_chn_day_plan_section_cfg__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_rec_srv_rec_chn_day_plan_section_cfg__pack_to_buffer
                     (const TPbNvrRecSrvRecChnDayPlanSectionCfg *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_rec_chn_day_plan_section_cfg__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrRecSrvRecChnDayPlanSectionCfg *
       tpb_nvr_rec_srv_rec_chn_day_plan_section_cfg__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrRecSrvRecChnDayPlanSectionCfg *)
     protobuf_c_message_unpack (&tpb_nvr_rec_srv_rec_chn_day_plan_section_cfg__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_rec_srv_rec_chn_day_plan_section_cfg__free_unpacked
                     (TPbNvrRecSrvRecChnDayPlanSectionCfg *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_rec_chn_day_plan_section_cfg__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_rec_srv_rec_chn_snap_day_item_cfg__init
                     (TPbNvrRecSrvRecChnSnapDayItemCfg         *message)
{
  static TPbNvrRecSrvRecChnSnapDayItemCfg init_value = TPB_NVR_REC_SRV_REC_CHN_SNAP_DAY_ITEM_CFG__INIT;
  *message = init_value;
}
size_t tpb_nvr_rec_srv_rec_chn_snap_day_item_cfg__get_packed_size
                     (const TPbNvrRecSrvRecChnSnapDayItemCfg *message)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_rec_chn_snap_day_item_cfg__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_rec_srv_rec_chn_snap_day_item_cfg__pack
                     (const TPbNvrRecSrvRecChnSnapDayItemCfg *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_rec_chn_snap_day_item_cfg__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_rec_srv_rec_chn_snap_day_item_cfg__pack_to_buffer
                     (const TPbNvrRecSrvRecChnSnapDayItemCfg *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_rec_chn_snap_day_item_cfg__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrRecSrvRecChnSnapDayItemCfg *
       tpb_nvr_rec_srv_rec_chn_snap_day_item_cfg__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrRecSrvRecChnSnapDayItemCfg *)
     protobuf_c_message_unpack (&tpb_nvr_rec_srv_rec_chn_snap_day_item_cfg__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_rec_srv_rec_chn_snap_day_item_cfg__free_unpacked
                     (TPbNvrRecSrvRecChnSnapDayItemCfg *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_rec_chn_snap_day_item_cfg__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_rec_srv_rec_chn_day_plan__init
                     (TPbNvrRecSrvRecChnDayPlan         *message)
{
  static TPbNvrRecSrvRecChnDayPlan init_value = TPB_NVR_REC_SRV_REC_CHN_DAY_PLAN__INIT;
  *message = init_value;
}
size_t tpb_nvr_rec_srv_rec_chn_day_plan__get_packed_size
                     (const TPbNvrRecSrvRecChnDayPlan *message)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_rec_chn_day_plan__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_rec_srv_rec_chn_day_plan__pack
                     (const TPbNvrRecSrvRecChnDayPlan *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_rec_chn_day_plan__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_rec_srv_rec_chn_day_plan__pack_to_buffer
                     (const TPbNvrRecSrvRecChnDayPlan *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_rec_chn_day_plan__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrRecSrvRecChnDayPlan *
       tpb_nvr_rec_srv_rec_chn_day_plan__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrRecSrvRecChnDayPlan *)
     protobuf_c_message_unpack (&tpb_nvr_rec_srv_rec_chn_day_plan__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_rec_srv_rec_chn_day_plan__free_unpacked
                     (TPbNvrRecSrvRecChnDayPlan *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_rec_chn_day_plan__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_rec_srv_rec_chn_day_plan_cfg__init
                     (TPbNvrRecSrvRecChnDayPlanCfg         *message)
{
  static TPbNvrRecSrvRecChnDayPlanCfg init_value = TPB_NVR_REC_SRV_REC_CHN_DAY_PLAN_CFG__INIT;
  *message = init_value;
}
size_t tpb_nvr_rec_srv_rec_chn_day_plan_cfg__get_packed_size
                     (const TPbNvrRecSrvRecChnDayPlanCfg *message)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_rec_chn_day_plan_cfg__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_rec_srv_rec_chn_day_plan_cfg__pack
                     (const TPbNvrRecSrvRecChnDayPlanCfg *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_rec_chn_day_plan_cfg__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_rec_srv_rec_chn_day_plan_cfg__pack_to_buffer
                     (const TPbNvrRecSrvRecChnDayPlanCfg *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_rec_chn_day_plan_cfg__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrRecSrvRecChnDayPlanCfg *
       tpb_nvr_rec_srv_rec_chn_day_plan_cfg__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrRecSrvRecChnDayPlanCfg *)
     protobuf_c_message_unpack (&tpb_nvr_rec_srv_rec_chn_day_plan_cfg__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_rec_srv_rec_chn_day_plan_cfg__free_unpacked
                     (TPbNvrRecSrvRecChnDayPlanCfg *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_rec_chn_day_plan_cfg__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_rec_srv_rec_chn_snap_day_cfg__init
                     (TPbNvrRecSrvRecChnSnapDayCfg         *message)
{
  static TPbNvrRecSrvRecChnSnapDayCfg init_value = TPB_NVR_REC_SRV_REC_CHN_SNAP_DAY_CFG__INIT;
  *message = init_value;
}
size_t tpb_nvr_rec_srv_rec_chn_snap_day_cfg__get_packed_size
                     (const TPbNvrRecSrvRecChnSnapDayCfg *message)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_rec_chn_snap_day_cfg__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_rec_srv_rec_chn_snap_day_cfg__pack
                     (const TPbNvrRecSrvRecChnSnapDayCfg *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_rec_chn_snap_day_cfg__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_rec_srv_rec_chn_snap_day_cfg__pack_to_buffer
                     (const TPbNvrRecSrvRecChnSnapDayCfg *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_rec_chn_snap_day_cfg__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrRecSrvRecChnSnapDayCfg *
       tpb_nvr_rec_srv_rec_chn_snap_day_cfg__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrRecSrvRecChnSnapDayCfg *)
     protobuf_c_message_unpack (&tpb_nvr_rec_srv_rec_chn_snap_day_cfg__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_rec_srv_rec_chn_snap_day_cfg__free_unpacked
                     (TPbNvrRecSrvRecChnSnapDayCfg *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_rec_chn_snap_day_cfg__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_rec_srv_rec_chn_plan_cfg__init
                     (TPbNvrRecSrvRecChnPlanCfg         *message)
{
  static TPbNvrRecSrvRecChnPlanCfg init_value = TPB_NVR_REC_SRV_REC_CHN_PLAN_CFG__INIT;
  *message = init_value;
}
size_t tpb_nvr_rec_srv_rec_chn_plan_cfg__get_packed_size
                     (const TPbNvrRecSrvRecChnPlanCfg *message)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_rec_chn_plan_cfg__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_rec_srv_rec_chn_plan_cfg__pack
                     (const TPbNvrRecSrvRecChnPlanCfg *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_rec_chn_plan_cfg__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_rec_srv_rec_chn_plan_cfg__pack_to_buffer
                     (const TPbNvrRecSrvRecChnPlanCfg *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_rec_chn_plan_cfg__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrRecSrvRecChnPlanCfg *
       tpb_nvr_rec_srv_rec_chn_plan_cfg__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrRecSrvRecChnPlanCfg *)
     protobuf_c_message_unpack (&tpb_nvr_rec_srv_rec_chn_plan_cfg__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_rec_srv_rec_chn_plan_cfg__free_unpacked
                     (TPbNvrRecSrvRecChnPlanCfg *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_rec_chn_plan_cfg__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_rec_srv_chn_snap_cfg__init
                     (TPbNvrRecSrvChnSnapCfg         *message)
{
  static TPbNvrRecSrvChnSnapCfg init_value = TPB_NVR_REC_SRV_CHN_SNAP_CFG__INIT;
  *message = init_value;
}
size_t tpb_nvr_rec_srv_chn_snap_cfg__get_packed_size
                     (const TPbNvrRecSrvChnSnapCfg *message)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_chn_snap_cfg__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_rec_srv_chn_snap_cfg__pack
                     (const TPbNvrRecSrvChnSnapCfg *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_chn_snap_cfg__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_rec_srv_chn_snap_cfg__pack_to_buffer
                     (const TPbNvrRecSrvChnSnapCfg *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_chn_snap_cfg__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrRecSrvChnSnapCfg *
       tpb_nvr_rec_srv_chn_snap_cfg__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrRecSrvChnSnapCfg *)
     protobuf_c_message_unpack (&tpb_nvr_rec_srv_chn_snap_cfg__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_rec_srv_chn_snap_cfg__free_unpacked
                     (TPbNvrRecSrvChnSnapCfg *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_chn_snap_cfg__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_rec_srv_rec_chn_plan__init
                     (TPbNvrRecSrvRecChnPlan         *message)
{
  static TPbNvrRecSrvRecChnPlan init_value = TPB_NVR_REC_SRV_REC_CHN_PLAN__INIT;
  *message = init_value;
}
size_t tpb_nvr_rec_srv_rec_chn_plan__get_packed_size
                     (const TPbNvrRecSrvRecChnPlan *message)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_rec_chn_plan__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_rec_srv_rec_chn_plan__pack
                     (const TPbNvrRecSrvRecChnPlan *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_rec_chn_plan__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_rec_srv_rec_chn_plan__pack_to_buffer
                     (const TPbNvrRecSrvRecChnPlan *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_rec_chn_plan__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrRecSrvRecChnPlan *
       tpb_nvr_rec_srv_rec_chn_plan__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrRecSrvRecChnPlan *)
     protobuf_c_message_unpack (&tpb_nvr_rec_srv_rec_chn_plan__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_rec_srv_rec_chn_plan__free_unpacked
                     (TPbNvrRecSrvRecChnPlan *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_rec_chn_plan__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_rec_srv_rec_plan_cfg__init
                     (TPbNvrRecSrvRecPlanCfg         *message)
{
  static TPbNvrRecSrvRecPlanCfg init_value = TPB_NVR_REC_SRV_REC_PLAN_CFG__INIT;
  *message = init_value;
}
size_t tpb_nvr_rec_srv_rec_plan_cfg__get_packed_size
                     (const TPbNvrRecSrvRecPlanCfg *message)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_rec_plan_cfg__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_rec_srv_rec_plan_cfg__pack
                     (const TPbNvrRecSrvRecPlanCfg *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_rec_plan_cfg__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_rec_srv_rec_plan_cfg__pack_to_buffer
                     (const TPbNvrRecSrvRecPlanCfg *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_rec_plan_cfg__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrRecSrvRecPlanCfg *
       tpb_nvr_rec_srv_rec_plan_cfg__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrRecSrvRecPlanCfg *)
     protobuf_c_message_unpack (&tpb_nvr_rec_srv_rec_plan_cfg__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_rec_srv_rec_plan_cfg__free_unpacked
                     (TPbNvrRecSrvRecPlanCfg *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_rec_plan_cfg__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_rec_srv_snp_cfg__init
                     (TPbNvrRecSrvSnpCfg         *message)
{
  static TPbNvrRecSrvSnpCfg init_value = TPB_NVR_REC_SRV_SNP_CFG__INIT;
  *message = init_value;
}
size_t tpb_nvr_rec_srv_snp_cfg__get_packed_size
                     (const TPbNvrRecSrvSnpCfg *message)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_snp_cfg__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_rec_srv_snp_cfg__pack
                     (const TPbNvrRecSrvSnpCfg *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_snp_cfg__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_rec_srv_snp_cfg__pack_to_buffer
                     (const TPbNvrRecSrvSnpCfg *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_snp_cfg__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrRecSrvSnpCfg *
       tpb_nvr_rec_srv_snp_cfg__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrRecSrvSnpCfg *)
     protobuf_c_message_unpack (&tpb_nvr_rec_srv_snp_cfg__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_rec_srv_snp_cfg__free_unpacked
                     (TPbNvrRecSrvSnpCfg *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_snp_cfg__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_rec_srv_rec_plan__init
                     (TPbNvrRecSrvRecPlan         *message)
{
  static TPbNvrRecSrvRecPlan init_value = TPB_NVR_REC_SRV_REC_PLAN__INIT;
  *message = init_value;
}
size_t tpb_nvr_rec_srv_rec_plan__get_packed_size
                     (const TPbNvrRecSrvRecPlan *message)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_rec_plan__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_rec_srv_rec_plan__pack
                     (const TPbNvrRecSrvRecPlan *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_rec_plan__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_rec_srv_rec_plan__pack_to_buffer
                     (const TPbNvrRecSrvRecPlan *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_rec_plan__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrRecSrvRecPlan *
       tpb_nvr_rec_srv_rec_plan__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrRecSrvRecPlan *)
     protobuf_c_message_unpack (&tpb_nvr_rec_srv_rec_plan__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_rec_srv_rec_plan__free_unpacked
                     (TPbNvrRecSrvRecPlan *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_rec_plan__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_rec_srv_holiday_period__init
                     (TPbNvrRecSrvHolidayPeriod         *message)
{
  static TPbNvrRecSrvHolidayPeriod init_value = TPB_NVR_REC_SRV_HOLIDAY_PERIOD__INIT;
  *message = init_value;
}
size_t tpb_nvr_rec_srv_holiday_period__get_packed_size
                     (const TPbNvrRecSrvHolidayPeriod *message)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_holiday_period__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_rec_srv_holiday_period__pack
                     (const TPbNvrRecSrvHolidayPeriod *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_holiday_period__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_rec_srv_holiday_period__pack_to_buffer
                     (const TPbNvrRecSrvHolidayPeriod *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_holiday_period__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrRecSrvHolidayPeriod *
       tpb_nvr_rec_srv_holiday_period__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrRecSrvHolidayPeriod *)
     protobuf_c_message_unpack (&tpb_nvr_rec_srv_holiday_period__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_rec_srv_holiday_period__free_unpacked
                     (TPbNvrRecSrvHolidayPeriod *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_holiday_period__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_rec_srv_holiday__init
                     (TPbNvrRecSrvHoliday         *message)
{
  static TPbNvrRecSrvHoliday init_value = TPB_NVR_REC_SRV_HOLIDAY__INIT;
  *message = init_value;
}
size_t tpb_nvr_rec_srv_holiday__get_packed_size
                     (const TPbNvrRecSrvHoliday *message)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_holiday__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_rec_srv_holiday__pack
                     (const TPbNvrRecSrvHoliday *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_holiday__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_rec_srv_holiday__pack_to_buffer
                     (const TPbNvrRecSrvHoliday *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_holiday__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrRecSrvHoliday *
       tpb_nvr_rec_srv_holiday__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrRecSrvHoliday *)
     protobuf_c_message_unpack (&tpb_nvr_rec_srv_holiday__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_rec_srv_holiday__free_unpacked
                     (TPbNvrRecSrvHoliday *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_holiday__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_rec_srv_holiday_tab__init
                     (TPbNvrRecSrvHolidayTab         *message)
{
  static TPbNvrRecSrvHolidayTab init_value = TPB_NVR_REC_SRV_HOLIDAY_TAB__INIT;
  *message = init_value;
}
size_t tpb_nvr_rec_srv_holiday_tab__get_packed_size
                     (const TPbNvrRecSrvHolidayTab *message)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_holiday_tab__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_rec_srv_holiday_tab__pack
                     (const TPbNvrRecSrvHolidayTab *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_holiday_tab__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_rec_srv_holiday_tab__pack_to_buffer
                     (const TPbNvrRecSrvHolidayTab *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_holiday_tab__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrRecSrvHolidayTab *
       tpb_nvr_rec_srv_holiday_tab__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrRecSrvHolidayTab *)
     protobuf_c_message_unpack (&tpb_nvr_rec_srv_holiday_tab__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_rec_srv_holiday_tab__free_unpacked
                     (TPbNvrRecSrvHolidayTab *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_holiday_tab__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_rec_srv_time_param__init
                     (TPbNvrRecSrvTimeParam         *message)
{
  static TPbNvrRecSrvTimeParam init_value = TPB_NVR_REC_SRV_TIME_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_rec_srv_time_param__get_packed_size
                     (const TPbNvrRecSrvTimeParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_time_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_rec_srv_time_param__pack
                     (const TPbNvrRecSrvTimeParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_time_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_rec_srv_time_param__pack_to_buffer
                     (const TPbNvrRecSrvTimeParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_time_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrRecSrvTimeParam *
       tpb_nvr_rec_srv_time_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrRecSrvTimeParam *)
     protobuf_c_message_unpack (&tpb_nvr_rec_srv_time_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_rec_srv_time_param__free_unpacked
                     (TPbNvrRecSrvTimeParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_rec_srv_time_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_rec_anr_task_info__init
                     (TPbNvrRecAnrTaskInfo         *message)
{
  static TPbNvrRecAnrTaskInfo init_value = TPB_NVR_REC_ANR_TASK_INFO__INIT;
  *message = init_value;
}
size_t tpb_nvr_rec_anr_task_info__get_packed_size
                     (const TPbNvrRecAnrTaskInfo *message)
{
  assert(message->base.descriptor == &tpb_nvr_rec_anr_task_info__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_rec_anr_task_info__pack
                     (const TPbNvrRecAnrTaskInfo *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_rec_anr_task_info__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_rec_anr_task_info__pack_to_buffer
                     (const TPbNvrRecAnrTaskInfo *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_rec_anr_task_info__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrRecAnrTaskInfo *
       tpb_nvr_rec_anr_task_info__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrRecAnrTaskInfo *)
     protobuf_c_message_unpack (&tpb_nvr_rec_anr_task_info__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_rec_anr_task_info__free_unpacked
                     (TPbNvrRecAnrTaskInfo *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_rec_anr_task_info__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_rec_anr_task_list__init
                     (TPbNvrRecAnrTaskList         *message)
{
  static TPbNvrRecAnrTaskList init_value = TPB_NVR_REC_ANR_TASK_LIST__INIT;
  *message = init_value;
}
size_t tpb_nvr_rec_anr_task_list__get_packed_size
                     (const TPbNvrRecAnrTaskList *message)
{
  assert(message->base.descriptor == &tpb_nvr_rec_anr_task_list__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_rec_anr_task_list__pack
                     (const TPbNvrRecAnrTaskList *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_rec_anr_task_list__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_rec_anr_task_list__pack_to_buffer
                     (const TPbNvrRecAnrTaskList *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_rec_anr_task_list__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrRecAnrTaskList *
       tpb_nvr_rec_anr_task_list__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrRecAnrTaskList *)
     protobuf_c_message_unpack (&tpb_nvr_rec_anr_task_list__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_rec_anr_task_list__free_unpacked
                     (TPbNvrRecAnrTaskList *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_rec_anr_task_list__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_rec_label_name__init
                     (TPbNvrRecLabelName         *message)
{
  static TPbNvrRecLabelName init_value = TPB_NVR_REC_LABEL_NAME__INIT;
  *message = init_value;
}
size_t tpb_nvr_rec_label_name__get_packed_size
                     (const TPbNvrRecLabelName *message)
{
  assert(message->base.descriptor == &tpb_nvr_rec_label_name__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_rec_label_name__pack
                     (const TPbNvrRecLabelName *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_rec_label_name__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_rec_label_name__pack_to_buffer
                     (const TPbNvrRecLabelName *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_rec_label_name__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrRecLabelName *
       tpb_nvr_rec_label_name__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrRecLabelName *)
     protobuf_c_message_unpack (&tpb_nvr_rec_label_name__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_rec_label_name__free_unpacked
                     (TPbNvrRecLabelName *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_rec_label_name__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_rec_label_info__init
                     (TPbNvrRecLabelInfo         *message)
{
  static TPbNvrRecLabelInfo init_value = TPB_NVR_REC_LABEL_INFO__INIT;
  *message = init_value;
}
size_t tpb_nvr_rec_label_info__get_packed_size
                     (const TPbNvrRecLabelInfo *message)
{
  assert(message->base.descriptor == &tpb_nvr_rec_label_info__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_rec_label_info__pack
                     (const TPbNvrRecLabelInfo *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_rec_label_info__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_rec_label_info__pack_to_buffer
                     (const TPbNvrRecLabelInfo *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_rec_label_info__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrRecLabelInfo *
       tpb_nvr_rec_label_info__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrRecLabelInfo *)
     protobuf_c_message_unpack (&tpb_nvr_rec_label_info__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_rec_label_info__free_unpacked
                     (TPbNvrRecLabelInfo *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_rec_label_info__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
static const ProtobufCFieldDescriptor tpb_nvr_rec_srv_no_rec_cfg__field_descriptors[4] =
{
  {
    "no_rec_hour",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrRecSrvNoRecCfg, has_no_rec_hour),
    offsetof(TPbNvrRecSrvNoRecCfg, no_rec_hour),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "no_rec_min",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrRecSrvNoRecCfg, has_no_rec_min),
    offsetof(TPbNvrRecSrvNoRecCfg, no_rec_min),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "no_rec_sec",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrRecSrvNoRecCfg, has_no_rec_sec),
    offsetof(TPbNvrRecSrvNoRecCfg, no_rec_sec),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "no_rec_day",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrRecSrvNoRecCfg, has_no_rec_day),
    offsetof(TPbNvrRecSrvNoRecCfg, no_rec_day),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_rec_srv_no_rec_cfg__field_indices_by_name[] = {
  3,   /* field[3] = no_rec_day */
  0,   /* field[0] = no_rec_hour */
  1,   /* field[1] = no_rec_min */
  2,   /* field[2] = no_rec_sec */
};
static const ProtobufCIntRange tpb_nvr_rec_srv_no_rec_cfg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 4 }
};
const ProtobufCMessageDescriptor tpb_nvr_rec_srv_no_rec_cfg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrRecSrvNoRecCfg",
  "TPbNvrRecSrvNoRecCfg",
  "TPbNvrRecSrvNoRecCfg",
  "",
  sizeof(TPbNvrRecSrvNoRecCfg),
  4,
  tpb_nvr_rec_srv_no_rec_cfg__field_descriptors,
  tpb_nvr_rec_srv_no_rec_cfg__field_indices_by_name,
  1,  tpb_nvr_rec_srv_no_rec_cfg__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_rec_srv_no_rec_cfg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_rec_srv_mgr_adv_cfg__field_descriptors[14] =
{
  {
    "rec_cover_pollcy",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_ENUM,
    offsetof(TPbNvrRecSrvMgrAdvCfg, has_rec_cover_pollcy),
    offsetof(TPbNvrRecSrvMgrAdvCfg, rec_cover_pollcy),
    &em_pb_nvr_rec_srv_cover_pollcy__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "pic_cover_pollcy",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_ENUM,
    offsetof(TPbNvrRecSrvMgrAdvCfg, has_pic_cover_pollcy),
    offsetof(TPbNvrRecSrvMgrAdvCfg, pic_cover_pollcy),
    &em_pb_nvr_rec_srv_cover_pollcy__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "pre_rec_time",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_SINT32,
    offsetof(TPbNvrRecSrvMgrAdvCfg, has_pre_rec_time),
    offsetof(TPbNvrRecSrvMgrAdvCfg, pre_rec_time),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "rec_delay",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_SINT32,
    offsetof(TPbNvrRecSrvMgrAdvCfg, has_rec_delay),
    offsetof(TPbNvrRecSrvMgrAdvCfg, rec_delay),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "rec_download_speed",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_SINT32,
    offsetof(TPbNvrRecSrvMgrAdvCfg, has_rec_download_speed),
    offsetof(TPbNvrRecSrvMgrAdvCfg, rec_download_speed),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "start_anr",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrRecSrvMgrAdvCfg, has_start_anr),
    offsetof(TPbNvrRecSrvMgrAdvCfg, start_anr),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "mp4_file_max_size",
    7,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrRecSrvMgrAdvCfg, has_mp4_file_max_size),
    offsetof(TPbNvrRecSrvMgrAdvCfg, mp4_file_max_size),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "enable_over_rec_time_limit",
    8,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrRecSrvMgrAdvCfg, has_enable_over_rec_time_limit),
    offsetof(TPbNvrRecSrvMgrAdvCfg, enable_over_rec_time_limit),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "over_rec_time",
    9,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrRecSrvMgrAdvCfg, has_over_rec_time),
    offsetof(TPbNvrRecSrvMgrAdvCfg, over_rec_time),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "mp4_file_time_duration",
    10,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrRecSrvMgrAdvCfg, has_mp4_file_time_duration),
    offsetof(TPbNvrRecSrvMgrAdvCfg, mp4_file_time_duration),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "small_video_pretime",
    11,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrRecSrvMgrAdvCfg, has_small_video_pretime),
    offsetof(TPbNvrRecSrvMgrAdvCfg, small_video_pretime),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "small_video_overtime",
    12,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrRecSrvMgrAdvCfg, has_small_video_overtime),
    offsetof(TPbNvrRecSrvMgrAdvCfg, small_video_overtime),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "no_rec_record_cfg",
    13,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrRecSrvMgrAdvCfg, no_rec_record_cfg),
    &tpb_nvr_rec_srv_no_rec_cfg__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "upload_full_policy",
    14,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrRecSrvMgrAdvCfg, has_upload_full_policy),
    offsetof(TPbNvrRecSrvMgrAdvCfg, upload_full_policy),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_rec_srv_mgr_adv_cfg__field_indices_by_name[] = {
  7,   /* field[7] = enable_over_rec_time_limit */
  6,   /* field[6] = mp4_file_max_size */
  9,   /* field[9] = mp4_file_time_duration */
  12,   /* field[12] = no_rec_record_cfg */
  8,   /* field[8] = over_rec_time */
  1,   /* field[1] = pic_cover_pollcy */
  2,   /* field[2] = pre_rec_time */
  0,   /* field[0] = rec_cover_pollcy */
  3,   /* field[3] = rec_delay */
  4,   /* field[4] = rec_download_speed */
  11,   /* field[11] = small_video_overtime */
  10,   /* field[10] = small_video_pretime */
  5,   /* field[5] = start_anr */
  13,   /* field[13] = upload_full_policy */
};
static const ProtobufCIntRange tpb_nvr_rec_srv_mgr_adv_cfg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 14 }
};
const ProtobufCMessageDescriptor tpb_nvr_rec_srv_mgr_adv_cfg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrRecSrvMgrAdvCfg",
  "TPbNvrRecSrvMgrAdvCfg",
  "TPbNvrRecSrvMgrAdvCfg",
  "",
  sizeof(TPbNvrRecSrvMgrAdvCfg),
  14,
  tpb_nvr_rec_srv_mgr_adv_cfg__field_descriptors,
  tpb_nvr_rec_srv_mgr_adv_cfg__field_indices_by_name,
  1,  tpb_nvr_rec_srv_mgr_adv_cfg__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_rec_srv_mgr_adv_cfg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_rec_srv_snap_cfg__field_descriptors[2] =
{
  {
    "alarm_snap_num",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_SINT32,
    offsetof(TPbNvrRecSrvSnapCfg, has_alarm_snap_num),
    offsetof(TPbNvrRecSrvSnapCfg, alarm_snap_num),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "alarm_snap_interval",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_SINT32,
    offsetof(TPbNvrRecSrvSnapCfg, has_alarm_snap_interval),
    offsetof(TPbNvrRecSrvSnapCfg, alarm_snap_interval),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_rec_srv_snap_cfg__field_indices_by_name[] = {
  1,   /* field[1] = alarm_snap_interval */
  0,   /* field[0] = alarm_snap_num */
};
static const ProtobufCIntRange tpb_nvr_rec_srv_snap_cfg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_rec_srv_snap_cfg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrRecSrvSnapCfg",
  "TPbNvrRecSrvSnapCfg",
  "TPbNvrRecSrvSnapCfg",
  "",
  sizeof(TPbNvrRecSrvSnapCfg),
  2,
  tpb_nvr_rec_srv_snap_cfg__field_descriptors,
  tpb_nvr_rec_srv_snap_cfg__field_indices_by_name,
  1,  tpb_nvr_rec_srv_snap_cfg__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_rec_srv_snap_cfg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_rec_srv_whole_cfg__field_descriptors[2] =
{
  {
    "nvr_rec_adv_cfg",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrRecSrvWholeCfg, nvr_rec_adv_cfg),
    &tpb_nvr_rec_srv_mgr_adv_cfg__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "nvr_rec_snap_cfg",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrRecSrvWholeCfg, nvr_rec_snap_cfg),
    &tpb_nvr_rec_srv_snap_cfg__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_rec_srv_whole_cfg__field_indices_by_name[] = {
  0,   /* field[0] = nvr_rec_adv_cfg */
  1,   /* field[1] = nvr_rec_snap_cfg */
};
static const ProtobufCIntRange tpb_nvr_rec_srv_whole_cfg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_rec_srv_whole_cfg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrRecSrvWholeCfg",
  "TPbNvrRecSrvWholeCfg",
  "TPbNvrRecSrvWholeCfg",
  "",
  sizeof(TPbNvrRecSrvWholeCfg),
  2,
  tpb_nvr_rec_srv_whole_cfg__field_descriptors,
  tpb_nvr_rec_srv_whole_cfg__field_indices_by_name,
  1,  tpb_nvr_rec_srv_whole_cfg__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_rec_srv_whole_cfg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_rec_srv_mode_cfg_item__field_descriptors[4] =
{
  {
    "chn_id",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_SINT32,
    offsetof(TPbNvrRecSrvModeCfgItem, has_chn_id),
    offsetof(TPbNvrRecSrvModeCfgItem, chn_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "rec_mode",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_ENUM,
    offsetof(TPbNvrRecSrvModeCfgItem, has_rec_mode),
    offsetof(TPbNvrRecSrvModeCfgItem, rec_mode),
    &epb_nvr_rec_srv_mode__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "nvr_rec_stream",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_ENUM,
    offsetof(TPbNvrRecSrvModeCfgItem, has_nvr_rec_stream),
    offsetof(TPbNvrRecSrvModeCfgItem, nvr_rec_stream),
    &epb_nvr_rec_srv_stream__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "rec_audio",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrRecSrvModeCfgItem, has_rec_audio),
    offsetof(TPbNvrRecSrvModeCfgItem, rec_audio),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_rec_srv_mode_cfg_item__field_indices_by_name[] = {
  0,   /* field[0] = chn_id */
  2,   /* field[2] = nvr_rec_stream */
  3,   /* field[3] = rec_audio */
  1,   /* field[1] = rec_mode */
};
static const ProtobufCIntRange tpb_nvr_rec_srv_mode_cfg_item__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 4 }
};
const ProtobufCMessageDescriptor tpb_nvr_rec_srv_mode_cfg_item__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrRecSrvModeCfgItem",
  "TPbNvrRecSrvModeCfgItem",
  "TPbNvrRecSrvModeCfgItem",
  "",
  sizeof(TPbNvrRecSrvModeCfgItem),
  4,
  tpb_nvr_rec_srv_mode_cfg_item__field_descriptors,
  tpb_nvr_rec_srv_mode_cfg_item__field_indices_by_name,
  1,  tpb_nvr_rec_srv_mode_cfg_item__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_rec_srv_mode_cfg_item__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_rec_srv_mode_cfg__field_descriptors[1] =
{
  {
    "nvr_rec_srv_mode_cfg_list",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrRecSrvModeCfg, n_nvr_rec_srv_mode_cfg_list),
    offsetof(TPbNvrRecSrvModeCfg, nvr_rec_srv_mode_cfg_list),
    &tpb_nvr_rec_srv_mode_cfg_item__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_rec_srv_mode_cfg__field_indices_by_name[] = {
  0,   /* field[0] = nvr_rec_srv_mode_cfg_list */
};
static const ProtobufCIntRange tpb_nvr_rec_srv_mode_cfg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor tpb_nvr_rec_srv_mode_cfg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrRecSrvModeCfg",
  "TPbNvrRecSrvModeCfg",
  "TPbNvrRecSrvModeCfg",
  "",
  sizeof(TPbNvrRecSrvModeCfg),
  1,
  tpb_nvr_rec_srv_mode_cfg__field_descriptors,
  tpb_nvr_rec_srv_mode_cfg__field_indices_by_name,
  1,  tpb_nvr_rec_srv_mode_cfg__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_rec_srv_mode_cfg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_rec_srv_rec_chn_day_plan_section__field_descriptors[5] =
{
  {
    "section_id",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_SINT32,
    offsetof(TPbNvrRecSrvRecChnDayPlanSection, has_section_id),
    offsetof(TPbNvrRecSrvRecChnDayPlanSection, section_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "start_time",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrRecSrvRecChnDayPlanSection, has_start_time),
    offsetof(TPbNvrRecSrvRecChnDayPlanSection, start_time),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "end_time",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrRecSrvRecChnDayPlanSection, has_end_time),
    offsetof(TPbNvrRecSrvRecChnDayPlanSection, end_time),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "stream_type",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_ENUM,
    offsetof(TPbNvrRecSrvRecChnDayPlanSection, has_stream_type),
    offsetof(TPbNvrRecSrvRecChnDayPlanSection, stream_type),
    &epb_nvr_rec_srv_stream__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "rec_audio",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrRecSrvRecChnDayPlanSection, has_rec_audio),
    offsetof(TPbNvrRecSrvRecChnDayPlanSection, rec_audio),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_rec_srv_rec_chn_day_plan_section__field_indices_by_name[] = {
  2,   /* field[2] = end_time */
  4,   /* field[4] = rec_audio */
  0,   /* field[0] = section_id */
  1,   /* field[1] = start_time */
  3,   /* field[3] = stream_type */
};
static const ProtobufCIntRange tpb_nvr_rec_srv_rec_chn_day_plan_section__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 5 }
};
const ProtobufCMessageDescriptor tpb_nvr_rec_srv_rec_chn_day_plan_section__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrRecSrvRecChnDayPlanSection",
  "TPbNvrRecSrvRecChnDayPlanSection",
  "TPbNvrRecSrvRecChnDayPlanSection",
  "",
  sizeof(TPbNvrRecSrvRecChnDayPlanSection),
  5,
  tpb_nvr_rec_srv_rec_chn_day_plan_section__field_descriptors,
  tpb_nvr_rec_srv_rec_chn_day_plan_section__field_indices_by_name,
  1,  tpb_nvr_rec_srv_rec_chn_day_plan_section__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_rec_srv_rec_chn_day_plan_section__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_rec_srv_rec_chn_day_plan_section_cfg__field_descriptors[4] =
{
  {
    "section_id",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_SINT32,
    offsetof(TPbNvrRecSrvRecChnDayPlanSectionCfg, has_section_id),
    offsetof(TPbNvrRecSrvRecChnDayPlanSectionCfg, section_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "start_time",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrRecSrvRecChnDayPlanSectionCfg, has_start_time),
    offsetof(TPbNvrRecSrvRecChnDayPlanSectionCfg, start_time),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "end_time",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrRecSrvRecChnDayPlanSectionCfg, has_end_time),
    offsetof(TPbNvrRecSrvRecChnDayPlanSectionCfg, end_time),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "enable",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrRecSrvRecChnDayPlanSectionCfg, has_enable),
    offsetof(TPbNvrRecSrvRecChnDayPlanSectionCfg, enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_rec_srv_rec_chn_day_plan_section_cfg__field_indices_by_name[] = {
  3,   /* field[3] = enable */
  2,   /* field[2] = end_time */
  0,   /* field[0] = section_id */
  1,   /* field[1] = start_time */
};
static const ProtobufCIntRange tpb_nvr_rec_srv_rec_chn_day_plan_section_cfg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 4 }
};
const ProtobufCMessageDescriptor tpb_nvr_rec_srv_rec_chn_day_plan_section_cfg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrRecSrvRecChnDayPlanSectionCfg",
  "TPbNvrRecSrvRecChnDayPlanSectionCfg",
  "TPbNvrRecSrvRecChnDayPlanSectionCfg",
  "",
  sizeof(TPbNvrRecSrvRecChnDayPlanSectionCfg),
  4,
  tpb_nvr_rec_srv_rec_chn_day_plan_section_cfg__field_descriptors,
  tpb_nvr_rec_srv_rec_chn_day_plan_section_cfg__field_indices_by_name,
  1,  tpb_nvr_rec_srv_rec_chn_day_plan_section_cfg__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_rec_srv_rec_chn_day_plan_section_cfg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_rec_srv_rec_chn_snap_day_item_cfg__field_descriptors[2] =
{
  {
    "start_time",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrRecSrvRecChnSnapDayItemCfg, has_start_time),
    offsetof(TPbNvrRecSrvRecChnSnapDayItemCfg, start_time),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "end_time",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrRecSrvRecChnSnapDayItemCfg, has_end_time),
    offsetof(TPbNvrRecSrvRecChnSnapDayItemCfg, end_time),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_rec_srv_rec_chn_snap_day_item_cfg__field_indices_by_name[] = {
  1,   /* field[1] = end_time */
  0,   /* field[0] = start_time */
};
static const ProtobufCIntRange tpb_nvr_rec_srv_rec_chn_snap_day_item_cfg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_rec_srv_rec_chn_snap_day_item_cfg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrRecSrvRecChnSnapDayItemCfg",
  "TPbNvrRecSrvRecChnSnapDayItemCfg",
  "TPbNvrRecSrvRecChnSnapDayItemCfg",
  "",
  sizeof(TPbNvrRecSrvRecChnSnapDayItemCfg),
  2,
  tpb_nvr_rec_srv_rec_chn_snap_day_item_cfg__field_descriptors,
  tpb_nvr_rec_srv_rec_chn_snap_day_item_cfg__field_indices_by_name,
  1,  tpb_nvr_rec_srv_rec_chn_snap_day_item_cfg__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_rec_srv_rec_chn_snap_day_item_cfg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_rec_srv_rec_chn_day_plan__field_descriptors[2] =
{
  {
    "week_day_id",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_SINT32,
    offsetof(TPbNvrRecSrvRecChnDayPlan, has_week_day_id),
    offsetof(TPbNvrRecSrvRecChnDayPlan, week_day_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "nvr_rec_srv_chn_day_plan_sec_list",
    2,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrRecSrvRecChnDayPlan, n_nvr_rec_srv_chn_day_plan_sec_list),
    offsetof(TPbNvrRecSrvRecChnDayPlan, nvr_rec_srv_chn_day_plan_sec_list),
    &tpb_nvr_rec_srv_rec_chn_day_plan_section__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_rec_srv_rec_chn_day_plan__field_indices_by_name[] = {
  1,   /* field[1] = nvr_rec_srv_chn_day_plan_sec_list */
  0,   /* field[0] = week_day_id */
};
static const ProtobufCIntRange tpb_nvr_rec_srv_rec_chn_day_plan__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_rec_srv_rec_chn_day_plan__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrRecSrvRecChnDayPlan",
  "TPbNvrRecSrvRecChnDayPlan",
  "TPbNvrRecSrvRecChnDayPlan",
  "",
  sizeof(TPbNvrRecSrvRecChnDayPlan),
  2,
  tpb_nvr_rec_srv_rec_chn_day_plan__field_descriptors,
  tpb_nvr_rec_srv_rec_chn_day_plan__field_indices_by_name,
  1,  tpb_nvr_rec_srv_rec_chn_day_plan__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_rec_srv_rec_chn_day_plan__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_rec_srv_rec_chn_day_plan_cfg__field_descriptors[2] =
{
  {
    "week_day_id",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_SINT32,
    offsetof(TPbNvrRecSrvRecChnDayPlanCfg, has_week_day_id),
    offsetof(TPbNvrRecSrvRecChnDayPlanCfg, week_day_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "nvr_rec_srv_chn_day_plan_sec_list",
    2,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrRecSrvRecChnDayPlanCfg, n_nvr_rec_srv_chn_day_plan_sec_list),
    offsetof(TPbNvrRecSrvRecChnDayPlanCfg, nvr_rec_srv_chn_day_plan_sec_list),
    &tpb_nvr_rec_srv_rec_chn_day_plan_section_cfg__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_rec_srv_rec_chn_day_plan_cfg__field_indices_by_name[] = {
  1,   /* field[1] = nvr_rec_srv_chn_day_plan_sec_list */
  0,   /* field[0] = week_day_id */
};
static const ProtobufCIntRange tpb_nvr_rec_srv_rec_chn_day_plan_cfg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_rec_srv_rec_chn_day_plan_cfg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrRecSrvRecChnDayPlanCfg",
  "TPbNvrRecSrvRecChnDayPlanCfg",
  "TPbNvrRecSrvRecChnDayPlanCfg",
  "",
  sizeof(TPbNvrRecSrvRecChnDayPlanCfg),
  2,
  tpb_nvr_rec_srv_rec_chn_day_plan_cfg__field_descriptors,
  tpb_nvr_rec_srv_rec_chn_day_plan_cfg__field_indices_by_name,
  1,  tpb_nvr_rec_srv_rec_chn_day_plan_cfg__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_rec_srv_rec_chn_day_plan_cfg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_rec_srv_rec_chn_snap_day_cfg__field_descriptors[3] =
{
  {
    "week_day_id",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_SINT32,
    offsetof(TPbNvrRecSrvRecChnSnapDayCfg, has_week_day_id),
    offsetof(TPbNvrRecSrvRecChnSnapDayCfg, week_day_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "item_num",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_SINT32,
    offsetof(TPbNvrRecSrvRecChnSnapDayCfg, has_item_num),
    offsetof(TPbNvrRecSrvRecChnSnapDayCfg, item_num),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "chn_snap_day_item_list",
    3,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrRecSrvRecChnSnapDayCfg, n_chn_snap_day_item_list),
    offsetof(TPbNvrRecSrvRecChnSnapDayCfg, chn_snap_day_item_list),
    &tpb_nvr_rec_srv_rec_chn_snap_day_item_cfg__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_rec_srv_rec_chn_snap_day_cfg__field_indices_by_name[] = {
  2,   /* field[2] = chn_snap_day_item_list */
  1,   /* field[1] = item_num */
  0,   /* field[0] = week_day_id */
};
static const ProtobufCIntRange tpb_nvr_rec_srv_rec_chn_snap_day_cfg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 3 }
};
const ProtobufCMessageDescriptor tpb_nvr_rec_srv_rec_chn_snap_day_cfg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrRecSrvRecChnSnapDayCfg",
  "TPbNvrRecSrvRecChnSnapDayCfg",
  "TPbNvrRecSrvRecChnSnapDayCfg",
  "",
  sizeof(TPbNvrRecSrvRecChnSnapDayCfg),
  3,
  tpb_nvr_rec_srv_rec_chn_snap_day_cfg__field_descriptors,
  tpb_nvr_rec_srv_rec_chn_snap_day_cfg__field_indices_by_name,
  1,  tpb_nvr_rec_srv_rec_chn_snap_day_cfg__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_rec_srv_rec_chn_snap_day_cfg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_rec_srv_rec_chn_plan_cfg__field_descriptors[8] =
{
  {
    "chn_id",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_SINT32,
    offsetof(TPbNvrRecSrvRecChnPlanCfg, has_chn_id),
    offsetof(TPbNvrRecSrvRecChnPlanCfg, chn_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "over_time",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_SINT32,
    offsetof(TPbNvrRecSrvRecChnPlanCfg, has_over_time),
    offsetof(TPbNvrRecSrvRecChnPlanCfg, over_time),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "rec_audio",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrRecSrvRecChnPlanCfg, has_rec_audio),
    offsetof(TPbNvrRecSrvRecChnPlanCfg, rec_audio),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "stream_type",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_ENUM,
    offsetof(TPbNvrRecSrvRecChnPlanCfg, has_stream_type),
    offsetof(TPbNvrRecSrvRecChnPlanCfg, stream_type),
    &epb_nvr_rec_srv_stream__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "rec_mode",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_ENUM,
    offsetof(TPbNvrRecSrvRecChnPlanCfg, has_rec_mode),
    offsetof(TPbNvrRecSrvRecChnPlanCfg, rec_mode),
    &epb_nvr_rec_srv_mode__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "nvr_rec_srv_chn_day_plan_cfg_list",
    6,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrRecSrvRecChnPlanCfg, n_nvr_rec_srv_chn_day_plan_cfg_list),
    offsetof(TPbNvrRecSrvRecChnPlanCfg, nvr_rec_srv_chn_day_plan_cfg_list),
    &tpb_nvr_rec_srv_rec_chn_day_plan_cfg__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "mp4_dir_name",
    7,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BYTES,
    offsetof(TPbNvrRecSrvRecChnPlanCfg, has_mp4_dir_name),
    offsetof(TPbNvrRecSrvRecChnPlanCfg, mp4_dir_name),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "rec_audio_only",
    8,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrRecSrvRecChnPlanCfg, has_rec_audio_only),
    offsetof(TPbNvrRecSrvRecChnPlanCfg, rec_audio_only),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_rec_srv_rec_chn_plan_cfg__field_indices_by_name[] = {
  0,   /* field[0] = chn_id */
  6,   /* field[6] = mp4_dir_name */
  5,   /* field[5] = nvr_rec_srv_chn_day_plan_cfg_list */
  1,   /* field[1] = over_time */
  2,   /* field[2] = rec_audio */
  7,   /* field[7] = rec_audio_only */
  4,   /* field[4] = rec_mode */
  3,   /* field[3] = stream_type */
};
static const ProtobufCIntRange tpb_nvr_rec_srv_rec_chn_plan_cfg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 8 }
};
const ProtobufCMessageDescriptor tpb_nvr_rec_srv_rec_chn_plan_cfg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrRecSrvRecChnPlanCfg",
  "TPbNvrRecSrvRecChnPlanCfg",
  "TPbNvrRecSrvRecChnPlanCfg",
  "",
  sizeof(TPbNvrRecSrvRecChnPlanCfg),
  8,
  tpb_nvr_rec_srv_rec_chn_plan_cfg__field_descriptors,
  tpb_nvr_rec_srv_rec_chn_plan_cfg__field_indices_by_name,
  1,  tpb_nvr_rec_srv_rec_chn_plan_cfg__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_rec_srv_rec_chn_plan_cfg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_rec_srv_chn_snap_cfg__field_descriptors[5] =
{
  {
    "chn_id",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_SINT32,
    offsetof(TPbNvrRecSrvChnSnapCfg, has_chn_id),
    offsetof(TPbNvrRecSrvChnSnapCfg, chn_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "alarm_snap_interval",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_SINT32,
    offsetof(TPbNvrRecSrvChnSnapCfg, has_alarm_snap_interval),
    offsetof(TPbNvrRecSrvChnSnapCfg, alarm_snap_interval),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "timer_snap_interval",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_SINT32,
    offsetof(TPbNvrRecSrvChnSnapCfg, has_timer_snap_interval),
    offsetof(TPbNvrRecSrvChnSnapCfg, timer_snap_interval),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "snap_mode",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_ENUM,
    offsetof(TPbNvrRecSrvChnSnapCfg, has_snap_mode),
    offsetof(TPbNvrRecSrvChnSnapCfg, snap_mode),
    &epb_nvr_rec_srv_snap_mode__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "chn_snap_day_cfg_list",
    5,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrRecSrvChnSnapCfg, n_chn_snap_day_cfg_list),
    offsetof(TPbNvrRecSrvChnSnapCfg, chn_snap_day_cfg_list),
    &tpb_nvr_rec_srv_rec_chn_snap_day_cfg__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_rec_srv_chn_snap_cfg__field_indices_by_name[] = {
  1,   /* field[1] = alarm_snap_interval */
  0,   /* field[0] = chn_id */
  4,   /* field[4] = chn_snap_day_cfg_list */
  3,   /* field[3] = snap_mode */
  2,   /* field[2] = timer_snap_interval */
};
static const ProtobufCIntRange tpb_nvr_rec_srv_chn_snap_cfg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 5 }
};
const ProtobufCMessageDescriptor tpb_nvr_rec_srv_chn_snap_cfg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrRecSrvChnSnapCfg",
  "TPbNvrRecSrvChnSnapCfg",
  "TPbNvrRecSrvChnSnapCfg",
  "",
  sizeof(TPbNvrRecSrvChnSnapCfg),
  5,
  tpb_nvr_rec_srv_chn_snap_cfg__field_descriptors,
  tpb_nvr_rec_srv_chn_snap_cfg__field_indices_by_name,
  1,  tpb_nvr_rec_srv_chn_snap_cfg__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_rec_srv_chn_snap_cfg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_rec_srv_rec_chn_plan__field_descriptors[2] =
{
  {
    "chn_id",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_SINT32,
    offsetof(TPbNvrRecSrvRecChnPlan, has_chn_id),
    offsetof(TPbNvrRecSrvRecChnPlan, chn_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "nvr_rec_srv_chn_day_plan_list",
    2,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrRecSrvRecChnPlan, n_nvr_rec_srv_chn_day_plan_list),
    offsetof(TPbNvrRecSrvRecChnPlan, nvr_rec_srv_chn_day_plan_list),
    &tpb_nvr_rec_srv_rec_chn_day_plan__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_rec_srv_rec_chn_plan__field_indices_by_name[] = {
  0,   /* field[0] = chn_id */
  1,   /* field[1] = nvr_rec_srv_chn_day_plan_list */
};
static const ProtobufCIntRange tpb_nvr_rec_srv_rec_chn_plan__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_rec_srv_rec_chn_plan__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrRecSrvRecChnPlan",
  "TPbNvrRecSrvRecChnPlan",
  "TPbNvrRecSrvRecChnPlan",
  "",
  sizeof(TPbNvrRecSrvRecChnPlan),
  2,
  tpb_nvr_rec_srv_rec_chn_plan__field_descriptors,
  tpb_nvr_rec_srv_rec_chn_plan__field_indices_by_name,
  1,  tpb_nvr_rec_srv_rec_chn_plan__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_rec_srv_rec_chn_plan__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_rec_srv_rec_plan_cfg__field_descriptors[1] =
{
  {
    "nvr_rec_srv_chn_plan_cfg_list",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrRecSrvRecPlanCfg, n_nvr_rec_srv_chn_plan_cfg_list),
    offsetof(TPbNvrRecSrvRecPlanCfg, nvr_rec_srv_chn_plan_cfg_list),
    &tpb_nvr_rec_srv_rec_chn_plan_cfg__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_rec_srv_rec_plan_cfg__field_indices_by_name[] = {
  0,   /* field[0] = nvr_rec_srv_chn_plan_cfg_list */
};
static const ProtobufCIntRange tpb_nvr_rec_srv_rec_plan_cfg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor tpb_nvr_rec_srv_rec_plan_cfg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrRecSrvRecPlanCfg",
  "TPbNvrRecSrvRecPlanCfg",
  "TPbNvrRecSrvRecPlanCfg",
  "",
  sizeof(TPbNvrRecSrvRecPlanCfg),
  1,
  tpb_nvr_rec_srv_rec_plan_cfg__field_descriptors,
  tpb_nvr_rec_srv_rec_plan_cfg__field_indices_by_name,
  1,  tpb_nvr_rec_srv_rec_plan_cfg__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_rec_srv_rec_plan_cfg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_rec_srv_snp_cfg__field_descriptors[1] =
{
  {
    "chn_snap_cfg_list",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrRecSrvSnpCfg, n_chn_snap_cfg_list),
    offsetof(TPbNvrRecSrvSnpCfg, chn_snap_cfg_list),
    &tpb_nvr_rec_srv_chn_snap_cfg__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_rec_srv_snp_cfg__field_indices_by_name[] = {
  0,   /* field[0] = chn_snap_cfg_list */
};
static const ProtobufCIntRange tpb_nvr_rec_srv_snp_cfg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor tpb_nvr_rec_srv_snp_cfg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrRecSrvSnpCfg",
  "TPbNvrRecSrvSnpCfg",
  "TPbNvrRecSrvSnpCfg",
  "",
  sizeof(TPbNvrRecSrvSnpCfg),
  1,
  tpb_nvr_rec_srv_snp_cfg__field_descriptors,
  tpb_nvr_rec_srv_snp_cfg__field_indices_by_name,
  1,  tpb_nvr_rec_srv_snp_cfg__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_rec_srv_snp_cfg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_rec_srv_rec_plan__field_descriptors[1] =
{
  {
    "nvr_rec_srv_chn_plan_list",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrRecSrvRecPlan, n_nvr_rec_srv_chn_plan_list),
    offsetof(TPbNvrRecSrvRecPlan, nvr_rec_srv_chn_plan_list),
    &tpb_nvr_rec_srv_rec_chn_plan__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_rec_srv_rec_plan__field_indices_by_name[] = {
  0,   /* field[0] = nvr_rec_srv_chn_plan_list */
};
static const ProtobufCIntRange tpb_nvr_rec_srv_rec_plan__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor tpb_nvr_rec_srv_rec_plan__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrRecSrvRecPlan",
  "TPbNvrRecSrvRecPlan",
  "TPbNvrRecSrvRecPlan",
  "",
  sizeof(TPbNvrRecSrvRecPlan),
  1,
  tpb_nvr_rec_srv_rec_plan__field_descriptors,
  tpb_nvr_rec_srv_rec_plan__field_indices_by_name,
  1,  tpb_nvr_rec_srv_rec_plan__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_rec_srv_rec_plan__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_rec_srv_holiday_period__field_descriptors[5] =
{
  {
    "year_id",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_SINT32,
    offsetof(TPbNvrRecSrvHolidayPeriod, has_year_id),
    offsetof(TPbNvrRecSrvHolidayPeriod, year_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "month_id",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_SINT32,
    offsetof(TPbNvrRecSrvHolidayPeriod, has_month_id),
    offsetof(TPbNvrRecSrvHolidayPeriod, month_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "day_id",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_SINT32,
    offsetof(TPbNvrRecSrvHolidayPeriod, has_day_id),
    offsetof(TPbNvrRecSrvHolidayPeriod, day_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "week_id",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_SINT32,
    offsetof(TPbNvrRecSrvHolidayPeriod, has_week_id),
    offsetof(TPbNvrRecSrvHolidayPeriod, week_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "week_day_id",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_SINT32,
    offsetof(TPbNvrRecSrvHolidayPeriod, has_week_day_id),
    offsetof(TPbNvrRecSrvHolidayPeriod, week_day_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_rec_srv_holiday_period__field_indices_by_name[] = {
  2,   /* field[2] = day_id */
  1,   /* field[1] = month_id */
  4,   /* field[4] = week_day_id */
  3,   /* field[3] = week_id */
  0,   /* field[0] = year_id */
};
static const ProtobufCIntRange tpb_nvr_rec_srv_holiday_period__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 5 }
};
const ProtobufCMessageDescriptor tpb_nvr_rec_srv_holiday_period__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrRecSrvHolidayPeriod",
  "TPbNvrRecSrvHolidayPeriod",
  "TPbNvrRecSrvHolidayPeriod",
  "",
  sizeof(TPbNvrRecSrvHolidayPeriod),
  5,
  tpb_nvr_rec_srv_holiday_period__field_descriptors,
  tpb_nvr_rec_srv_holiday_period__field_indices_by_name,
  1,  tpb_nvr_rec_srv_holiday_period__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_rec_srv_holiday_period__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_rec_srv_holiday__field_descriptors[6] =
{
  {
    "holiday_id",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_SINT32,
    offsetof(TPbNvrRecSrvHoliday, has_holiday_id),
    offsetof(TPbNvrRecSrvHoliday, holiday_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "holiday_name",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BYTES,
    offsetof(TPbNvrRecSrvHoliday, has_holiday_name),
    offsetof(TPbNvrRecSrvHoliday, holiday_name),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "holiday_type",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_ENUM,
    offsetof(TPbNvrRecSrvHoliday, has_holiday_type),
    offsetof(TPbNvrRecSrvHoliday, holiday_type),
    &epb_nvr_rec_holiday_type__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "holiday_start",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrRecSrvHoliday, holiday_start),
    &tpb_nvr_rec_srv_holiday_period__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "holiday_end",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrRecSrvHoliday, holiday_end),
    &tpb_nvr_rec_srv_holiday_period__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "enable",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrRecSrvHoliday, has_enable),
    offsetof(TPbNvrRecSrvHoliday, enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_rec_srv_holiday__field_indices_by_name[] = {
  5,   /* field[5] = enable */
  4,   /* field[4] = holiday_end */
  0,   /* field[0] = holiday_id */
  1,   /* field[1] = holiday_name */
  3,   /* field[3] = holiday_start */
  2,   /* field[2] = holiday_type */
};
static const ProtobufCIntRange tpb_nvr_rec_srv_holiday__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 6 }
};
const ProtobufCMessageDescriptor tpb_nvr_rec_srv_holiday__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrRecSrvHoliday",
  "TPbNvrRecSrvHoliday",
  "TPbNvrRecSrvHoliday",
  "",
  sizeof(TPbNvrRecSrvHoliday),
  6,
  tpb_nvr_rec_srv_holiday__field_descriptors,
  tpb_nvr_rec_srv_holiday__field_indices_by_name,
  1,  tpb_nvr_rec_srv_holiday__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_rec_srv_holiday__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_rec_srv_holiday_tab__field_descriptors[1] =
{
  {
    "nvr_rec_srv_holiday_list",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrRecSrvHolidayTab, n_nvr_rec_srv_holiday_list),
    offsetof(TPbNvrRecSrvHolidayTab, nvr_rec_srv_holiday_list),
    &tpb_nvr_rec_srv_holiday__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_rec_srv_holiday_tab__field_indices_by_name[] = {
  0,   /* field[0] = nvr_rec_srv_holiday_list */
};
static const ProtobufCIntRange tpb_nvr_rec_srv_holiday_tab__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor tpb_nvr_rec_srv_holiday_tab__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrRecSrvHolidayTab",
  "TPbNvrRecSrvHolidayTab",
  "TPbNvrRecSrvHolidayTab",
  "",
  sizeof(TPbNvrRecSrvHolidayTab),
  1,
  tpb_nvr_rec_srv_holiday_tab__field_descriptors,
  tpb_nvr_rec_srv_holiday_tab__field_indices_by_name,
  1,  tpb_nvr_rec_srv_holiday_tab__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_rec_srv_holiday_tab__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_rec_srv_time_param__field_descriptors[8] =
{
  {
    "year",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_SINT32,
    offsetof(TPbNvrRecSrvTimeParam, has_year),
    offsetof(TPbNvrRecSrvTimeParam, year),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "month",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_SINT32,
    offsetof(TPbNvrRecSrvTimeParam, has_month),
    offsetof(TPbNvrRecSrvTimeParam, month),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "day",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_SINT32,
    offsetof(TPbNvrRecSrvTimeParam, has_day),
    offsetof(TPbNvrRecSrvTimeParam, day),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "hour",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_SINT32,
    offsetof(TPbNvrRecSrvTimeParam, has_hour),
    offsetof(TPbNvrRecSrvTimeParam, hour),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Minute",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_SINT32,
    offsetof(TPbNvrRecSrvTimeParam, has_minute),
    offsetof(TPbNvrRecSrvTimeParam, minute),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Second",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_SINT32,
    offsetof(TPbNvrRecSrvTimeParam, has_second),
    offsetof(TPbNvrRecSrvTimeParam, second),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Msec",
    7,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_SINT32,
    offsetof(TPbNvrRecSrvTimeParam, has_msec),
    offsetof(TPbNvrRecSrvTimeParam, msec),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Week",
    8,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_SINT32,
    offsetof(TPbNvrRecSrvTimeParam, has_week),
    offsetof(TPbNvrRecSrvTimeParam, week),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_rec_srv_time_param__field_indices_by_name[] = {
  4,   /* field[4] = Minute */
  6,   /* field[6] = Msec */
  5,   /* field[5] = Second */
  7,   /* field[7] = Week */
  2,   /* field[2] = day */
  3,   /* field[3] = hour */
  1,   /* field[1] = month */
  0,   /* field[0] = year */
};
static const ProtobufCIntRange tpb_nvr_rec_srv_time_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 8 }
};
const ProtobufCMessageDescriptor tpb_nvr_rec_srv_time_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrRecSrvTimeParam",
  "TPbNvrRecSrvTimeParam",
  "TPbNvrRecSrvTimeParam",
  "",
  sizeof(TPbNvrRecSrvTimeParam),
  8,
  tpb_nvr_rec_srv_time_param__field_descriptors,
  tpb_nvr_rec_srv_time_param__field_indices_by_name,
  1,  tpb_nvr_rec_srv_time_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_rec_srv_time_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_rec_anr_task_info__field_descriptors[9] =
{
  {
    "chn_id",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrRecAnrTaskInfo, has_chn_id),
    offsetof(TPbNvrRecAnrTaskInfo, chn_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "offline_param",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrRecAnrTaskInfo, offline_param),
    &tpb_nvr_rec_srv_time_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "online_param",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrRecAnrTaskInfo, online_param),
    &tpb_nvr_rec_srv_time_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "onlined",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrRecAnrTaskInfo, has_onlined),
    offsetof(TPbNvrRecAnrTaskInfo, onlined),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "rec_finish",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrRecAnrTaskInfo, has_rec_finish),
    offsetof(TPbNvrRecAnrTaskInfo, rec_finish),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "snap_finish",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrRecAnrTaskInfo, has_snap_finish),
    offsetof(TPbNvrRecAnrTaskInfo, snap_finish),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "snap_offline_param",
    7,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrRecAnrTaskInfo, snap_offline_param),
    &tpb_nvr_rec_srv_time_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "snap_online_param",
    8,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrRecAnrTaskInfo, snap_online_param),
    &tpb_nvr_rec_srv_time_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "task_type",
    9,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_ENUM,
    offsetof(TPbNvrRecAnrTaskInfo, has_task_type),
    offsetof(TPbNvrRecAnrTaskInfo, task_type),
    &epb_nvr_rec_anr_task_type__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_rec_anr_task_info__field_indices_by_name[] = {
  0,   /* field[0] = chn_id */
  1,   /* field[1] = offline_param */
  2,   /* field[2] = online_param */
  3,   /* field[3] = onlined */
  4,   /* field[4] = rec_finish */
  5,   /* field[5] = snap_finish */
  6,   /* field[6] = snap_offline_param */
  7,   /* field[7] = snap_online_param */
  8,   /* field[8] = task_type */
};
static const ProtobufCIntRange tpb_nvr_rec_anr_task_info__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 9 }
};
const ProtobufCMessageDescriptor tpb_nvr_rec_anr_task_info__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrRecAnrTaskInfo",
  "TPbNvrRecAnrTaskInfo",
  "TPbNvrRecAnrTaskInfo",
  "",
  sizeof(TPbNvrRecAnrTaskInfo),
  9,
  tpb_nvr_rec_anr_task_info__field_descriptors,
  tpb_nvr_rec_anr_task_info__field_indices_by_name,
  1,  tpb_nvr_rec_anr_task_info__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_rec_anr_task_info__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_rec_anr_task_list__field_descriptors[1] =
{
  {
    "tasklist",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrRecAnrTaskList, n_tasklist),
    offsetof(TPbNvrRecAnrTaskList, tasklist),
    &tpb_nvr_rec_anr_task_info__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_rec_anr_task_list__field_indices_by_name[] = {
  0,   /* field[0] = tasklist */
};
static const ProtobufCIntRange tpb_nvr_rec_anr_task_list__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor tpb_nvr_rec_anr_task_list__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrRecAnrTaskList",
  "TPbNvrRecAnrTaskList",
  "TPbNvrRecAnrTaskList",
  "",
  sizeof(TPbNvrRecAnrTaskList),
  1,
  tpb_nvr_rec_anr_task_list__field_descriptors,
  tpb_nvr_rec_anr_task_list__field_indices_by_name,
  1,  tpb_nvr_rec_anr_task_list__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_rec_anr_task_list__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_rec_label_name__field_descriptors[1] =
{
  {
    "lable_name",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrRecLabelName, lable_name),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_rec_label_name__field_indices_by_name[] = {
  0,   /* field[0] = lable_name */
};
static const ProtobufCIntRange tpb_nvr_rec_label_name__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor tpb_nvr_rec_label_name__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrRecLabelName",
  "TPbNvrRecLabelName",
  "TPbNvrRecLabelName",
  "",
  sizeof(TPbNvrRecLabelName),
  1,
  tpb_nvr_rec_label_name__field_descriptors,
  tpb_nvr_rec_label_name__field_indices_by_name,
  1,  tpb_nvr_rec_label_name__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_rec_label_name__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_rec_label_info__field_descriptors[1] =
{
  {
    "lable_info",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrRecLabelInfo, n_lable_info),
    offsetof(TPbNvrRecLabelInfo, lable_info),
    &tpb_nvr_rec_label_name__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_rec_label_info__field_indices_by_name[] = {
  0,   /* field[0] = lable_info */
};
static const ProtobufCIntRange tpb_nvr_rec_label_info__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor tpb_nvr_rec_label_info__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrRecLabelInfo",
  "TPbNvrRecLabelInfo",
  "TPbNvrRecLabelInfo",
  "",
  sizeof(TPbNvrRecLabelInfo),
  1,
  tpb_nvr_rec_label_info__field_descriptors,
  tpb_nvr_rec_label_info__field_indices_by_name,
  1,  tpb_nvr_rec_label_info__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_rec_label_info__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCEnumValue em_pb_nvr_rec_srv_cover_pollcy__enum_values_by_number[2] =
{
  { "FULL_COVER", "EM_PB_NVR_REC_SRV_COVER_POLLCY__FULL_COVER", 0 },
  { "FULL_STOP", "EM_PB_NVR_REC_SRV_COVER_POLLCY__FULL_STOP", 1 },
};
static const ProtobufCIntRange em_pb_nvr_rec_srv_cover_pollcy__value_ranges[] = {
{0, 0},{0, 2}
};
static const ProtobufCEnumValueIndex em_pb_nvr_rec_srv_cover_pollcy__enum_values_by_name[2] =
{
  { "FULL_COVER", 0 },
  { "FULL_STOP", 1 },
};
const ProtobufCEnumDescriptor em_pb_nvr_rec_srv_cover_pollcy__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "EmPbNvrRecSrvCoverPollcy",
  "EmPbNvrRecSrvCoverPollcy",
  "EmPbNvrRecSrvCoverPollcy",
  "",
  2,
  em_pb_nvr_rec_srv_cover_pollcy__enum_values_by_number,
  2,
  em_pb_nvr_rec_srv_cover_pollcy__enum_values_by_name,
  1,
  em_pb_nvr_rec_srv_cover_pollcy__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
static const ProtobufCEnumValue epb_nvr_rec_srv_mode__enum_values_by_number[3] =
{
  { "REC_START", "EPB_NVR_REC_SRV_MODE__REC_START", 0 },
  { "REC_STOP", "EPB_NVR_REC_SRV_MODE__REC_STOP", 1 },
  { "REC_AUTO", "EPB_NVR_REC_SRV_MODE__REC_AUTO", 2 },
};
static const ProtobufCIntRange epb_nvr_rec_srv_mode__value_ranges[] = {
{0, 0},{0, 3}
};
static const ProtobufCEnumValueIndex epb_nvr_rec_srv_mode__enum_values_by_name[3] =
{
  { "REC_AUTO", 2 },
  { "REC_START", 0 },
  { "REC_STOP", 1 },
};
const ProtobufCEnumDescriptor epb_nvr_rec_srv_mode__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "EPbNvrRecSrvMode",
  "EPbNvrRecSrvMode",
  "EPbNvrRecSrvMode",
  "",
  3,
  epb_nvr_rec_srv_mode__enum_values_by_number,
  3,
  epb_nvr_rec_srv_mode__enum_values_by_name,
  1,
  epb_nvr_rec_srv_mode__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
static const ProtobufCEnumValue epb_nvr_rec_srv_snap_mode__enum_values_by_number[2] =
{
  { "SNP_AUTO", "EPB_NVR_REC_SRV_SNAP_MODE__SNP_AUTO", 0 },
  { "SNP_STOP", "EPB_NVR_REC_SRV_SNAP_MODE__SNP_STOP", 1 },
};
static const ProtobufCIntRange epb_nvr_rec_srv_snap_mode__value_ranges[] = {
{0, 0},{0, 2}
};
static const ProtobufCEnumValueIndex epb_nvr_rec_srv_snap_mode__enum_values_by_name[2] =
{
  { "SNP_AUTO", 0 },
  { "SNP_STOP", 1 },
};
const ProtobufCEnumDescriptor epb_nvr_rec_srv_snap_mode__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "EPbNvrRecSrvSnapMode",
  "EPbNvrRecSrvSnapMode",
  "EPbNvrRecSrvSnapMode",
  "",
  2,
  epb_nvr_rec_srv_snap_mode__enum_values_by_number,
  2,
  epb_nvr_rec_srv_snap_mode__enum_values_by_name,
  1,
  epb_nvr_rec_srv_snap_mode__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
static const ProtobufCEnumValue epb_nvr_rec_srv_stream__enum_values_by_number[2] =
{
  { "REC_MAIN_PRIORITY", "EPB_NVR_REC_SRV_STREAM__REC_MAIN_PRIORITY", 0 },
  { "REC_SECONDARY_STREAM_PRIORITY", "EPB_NVR_REC_SRV_STREAM__REC_SECONDARY_STREAM_PRIORITY", 1 },
};
static const ProtobufCIntRange epb_nvr_rec_srv_stream__value_ranges[] = {
{0, 0},{0, 2}
};
static const ProtobufCEnumValueIndex epb_nvr_rec_srv_stream__enum_values_by_name[2] =
{
  { "REC_MAIN_PRIORITY", 0 },
  { "REC_SECONDARY_STREAM_PRIORITY", 1 },
};
const ProtobufCEnumDescriptor epb_nvr_rec_srv_stream__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "EPbNvrRecSrvStream",
  "EPbNvrRecSrvStream",
  "EPbNvrRecSrvStream",
  "",
  2,
  epb_nvr_rec_srv_stream__enum_values_by_number,
  2,
  epb_nvr_rec_srv_stream__enum_values_by_name,
  1,
  epb_nvr_rec_srv_stream__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
static const ProtobufCEnumValue epb_nvr_rec_holiday_type__enum_values_by_number[3] =
{
  { "CNT_BY_DATE", "EPB_NVR_REC_HOLIDAY_TYPE__CNT_BY_DATE", 0 },
  { "CNT_BY_MONTH", "EPB_NVR_REC_HOLIDAY_TYPE__CNT_BY_MONTH", 1 },
  { "CNT_BY_WEEK", "EPB_NVR_REC_HOLIDAY_TYPE__CNT_BY_WEEK", 2 },
};
static const ProtobufCIntRange epb_nvr_rec_holiday_type__value_ranges[] = {
{0, 0},{0, 3}
};
static const ProtobufCEnumValueIndex epb_nvr_rec_holiday_type__enum_values_by_name[3] =
{
  { "CNT_BY_DATE", 0 },
  { "CNT_BY_MONTH", 1 },
  { "CNT_BY_WEEK", 2 },
};
const ProtobufCEnumDescriptor epb_nvr_rec_holiday_type__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "EPbNvrRecHolidayType",
  "EPbNvrRecHolidayType",
  "EPbNvrRecHolidayType",
  "",
  3,
  epb_nvr_rec_holiday_type__enum_values_by_number,
  3,
  epb_nvr_rec_holiday_type__enum_values_by_name,
  1,
  epb_nvr_rec_holiday_type__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
static const ProtobufCEnumValue epb_nvr_rec_anr_task_type__enum_values_by_number[2] =
{
  { "REC_TASK", "EPB_NVR_REC_ANR_TASK_TYPE__REC_TASK", 0 },
  { "SNAP_TASK", "EPB_NVR_REC_ANR_TASK_TYPE__SNAP_TASK", 1 },
};
static const ProtobufCIntRange epb_nvr_rec_anr_task_type__value_ranges[] = {
{0, 0},{0, 2}
};
static const ProtobufCEnumValueIndex epb_nvr_rec_anr_task_type__enum_values_by_name[2] =
{
  { "REC_TASK", 0 },
  { "SNAP_TASK", 1 },
};
const ProtobufCEnumDescriptor epb_nvr_rec_anr_task_type__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "EPbNvrRecAnrTaskType",
  "EPbNvrRecAnrTaskType",
  "EPbNvrRecAnrTaskType",
  "",
  2,
  epb_nvr_rec_anr_task_type__enum_values_by_number,
  2,
  epb_nvr_rec_anr_task_type__enum_values_by_name,
  1,
  epb_nvr_rec_anr_task_type__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
