/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: nvrispcfg.proto */

/* Do not generate deprecated warnings for self */
#ifndef PROTOBUF_C__NO_DEPRECATED
#define PROTOBUF_C__NO_DEPRECATED
#endif

#include "nvrispcfg.pb-c.h"
void   tpb_nvr_isp_image__init
                     (TPbNvrIspImage         *message)
{
  static TPbNvrIspImage init_value = TPB_NVR_ISP_IMAGE__INIT;
  *message = init_value;
}
size_t tpb_nvr_isp_image__get_packed_size
                     (const TPbNvrIspImage *message)
{
  assert(message->base.descriptor == &tpb_nvr_isp_image__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_isp_image__pack
                     (const TPbNvrIspImage *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_isp_image__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_isp_image__pack_to_buffer
                     (const TPbNvrIspImage *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_isp_image__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrIspImage *
       tpb_nvr_isp_image__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrIspImage *)
     protobuf_c_message_unpack (&tpb_nvr_isp_image__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_isp_image__free_unpacked
                     (TPbNvrIspImage *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_isp_image__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_isp_white_balance__init
                     (TPbNvrIspWhiteBalance         *message)
{
  static TPbNvrIspWhiteBalance init_value = TPB_NVR_ISP_WHITE_BALANCE__INIT;
  *message = init_value;
}
size_t tpb_nvr_isp_white_balance__get_packed_size
                     (const TPbNvrIspWhiteBalance *message)
{
  assert(message->base.descriptor == &tpb_nvr_isp_white_balance__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_isp_white_balance__pack
                     (const TPbNvrIspWhiteBalance *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_isp_white_balance__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_isp_white_balance__pack_to_buffer
                     (const TPbNvrIspWhiteBalance *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_isp_white_balance__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrIspWhiteBalance *
       tpb_nvr_isp_white_balance__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrIspWhiteBalance *)
     protobuf_c_message_unpack (&tpb_nvr_isp_white_balance__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_isp_white_balance__free_unpacked
                     (TPbNvrIspWhiteBalance *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_isp_white_balance__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_isp_gain__init
                     (TPbNvrIspGain         *message)
{
  static TPbNvrIspGain init_value = TPB_NVR_ISP_GAIN__INIT;
  *message = init_value;
}
size_t tpb_nvr_isp_gain__get_packed_size
                     (const TPbNvrIspGain *message)
{
  assert(message->base.descriptor == &tpb_nvr_isp_gain__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_isp_gain__pack
                     (const TPbNvrIspGain *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_isp_gain__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_isp_gain__pack_to_buffer
                     (const TPbNvrIspGain *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_isp_gain__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrIspGain *
       tpb_nvr_isp_gain__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrIspGain *)
     protobuf_c_message_unpack (&tpb_nvr_isp_gain__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_isp_gain__free_unpacked
                     (TPbNvrIspGain *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_isp_gain__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_isp_shutter__init
                     (TPbNvrIspShutter         *message)
{
  static TPbNvrIspShutter init_value = TPB_NVR_ISP_SHUTTER__INIT;
  *message = init_value;
}
size_t tpb_nvr_isp_shutter__get_packed_size
                     (const TPbNvrIspShutter *message)
{
  assert(message->base.descriptor == &tpb_nvr_isp_shutter__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_isp_shutter__pack
                     (const TPbNvrIspShutter *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_isp_shutter__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_isp_shutter__pack_to_buffer
                     (const TPbNvrIspShutter *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_isp_shutter__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrIspShutter *
       tpb_nvr_isp_shutter__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrIspShutter *)
     protobuf_c_message_unpack (&tpb_nvr_isp_shutter__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_isp_shutter__free_unpacked
                     (TPbNvrIspShutter *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_isp_shutter__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_isp_pro_exposure__init
                     (TPbNvrIspProExposure         *message)
{
  static TPbNvrIspProExposure init_value = TPB_NVR_ISP_PRO_EXPOSURE__INIT;
  *message = init_value;
}
size_t tpb_nvr_isp_pro_exposure__get_packed_size
                     (const TPbNvrIspProExposure *message)
{
  assert(message->base.descriptor == &tpb_nvr_isp_pro_exposure__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_isp_pro_exposure__pack
                     (const TPbNvrIspProExposure *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_isp_pro_exposure__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_isp_pro_exposure__pack_to_buffer
                     (const TPbNvrIspProExposure *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_isp_pro_exposure__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrIspProExposure *
       tpb_nvr_isp_pro_exposure__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrIspProExposure *)
     protobuf_c_message_unpack (&tpb_nvr_isp_pro_exposure__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_isp_pro_exposure__free_unpacked
                     (TPbNvrIspProExposure *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_isp_pro_exposure__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_isp_window__init
                     (TPbNvrIspWindow         *message)
{
  static TPbNvrIspWindow init_value = TPB_NVR_ISP_WINDOW__INIT;
  *message = init_value;
}
size_t tpb_nvr_isp_window__get_packed_size
                     (const TPbNvrIspWindow *message)
{
  assert(message->base.descriptor == &tpb_nvr_isp_window__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_isp_window__pack
                     (const TPbNvrIspWindow *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_isp_window__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_isp_window__pack_to_buffer
                     (const TPbNvrIspWindow *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_isp_window__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrIspWindow *
       tpb_nvr_isp_window__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrIspWindow *)
     protobuf_c_message_unpack (&tpb_nvr_isp_window__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_isp_window__free_unpacked
                     (TPbNvrIspWindow *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_isp_window__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_isp_day_night_switch__init
                     (TPbNvrIspDayNightSwitch         *message)
{
  static TPbNvrIspDayNightSwitch init_value = TPB_NVR_ISP_DAY_NIGHT_SWITCH__INIT;
  *message = init_value;
}
size_t tpb_nvr_isp_day_night_switch__get_packed_size
                     (const TPbNvrIspDayNightSwitch *message)
{
  assert(message->base.descriptor == &tpb_nvr_isp_day_night_switch__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_isp_day_night_switch__pack
                     (const TPbNvrIspDayNightSwitch *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_isp_day_night_switch__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_isp_day_night_switch__pack_to_buffer
                     (const TPbNvrIspDayNightSwitch *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_isp_day_night_switch__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrIspDayNightSwitch *
       tpb_nvr_isp_day_night_switch__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrIspDayNightSwitch *)
     protobuf_c_message_unpack (&tpb_nvr_isp_day_night_switch__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_isp_day_night_switch__free_unpacked
                     (TPbNvrIspDayNightSwitch *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_isp_day_night_switch__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_isp_timing__init
                     (TPbNvrIspTiming         *message)
{
  static TPbNvrIspTiming init_value = TPB_NVR_ISP_TIMING__INIT;
  *message = init_value;
}
size_t tpb_nvr_isp_timing__get_packed_size
                     (const TPbNvrIspTiming *message)
{
  assert(message->base.descriptor == &tpb_nvr_isp_timing__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_isp_timing__pack
                     (const TPbNvrIspTiming *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_isp_timing__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_isp_timing__pack_to_buffer
                     (const TPbNvrIspTiming *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_isp_timing__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrIspTiming *
       tpb_nvr_isp_timing__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrIspTiming *)
     protobuf_c_message_unpack (&tpb_nvr_isp_timing__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_isp_timing__free_unpacked
                     (TPbNvrIspTiming *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_isp_timing__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_isp_back_light__init
                     (TPbNvrIspBackLight         *message)
{
  static TPbNvrIspBackLight init_value = TPB_NVR_ISP_BACK_LIGHT__INIT;
  *message = init_value;
}
size_t tpb_nvr_isp_back_light__get_packed_size
                     (const TPbNvrIspBackLight *message)
{
  assert(message->base.descriptor == &tpb_nvr_isp_back_light__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_isp_back_light__pack
                     (const TPbNvrIspBackLight *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_isp_back_light__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_isp_back_light__pack_to_buffer
                     (const TPbNvrIspBackLight *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_isp_back_light__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrIspBackLight *
       tpb_nvr_isp_back_light__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrIspBackLight *)
     protobuf_c_message_unpack (&tpb_nvr_isp_back_light__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_isp_back_light__free_unpacked
                     (TPbNvrIspBackLight *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_isp_back_light__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_isp_combin_hdr__init
                     (TPbNvrIspCombinHdr         *message)
{
  static TPbNvrIspCombinHdr init_value = TPB_NVR_ISP_COMBIN_HDR__INIT;
  *message = init_value;
}
size_t tpb_nvr_isp_combin_hdr__get_packed_size
                     (const TPbNvrIspCombinHdr *message)
{
  assert(message->base.descriptor == &tpb_nvr_isp_combin_hdr__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_isp_combin_hdr__pack
                     (const TPbNvrIspCombinHdr *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_isp_combin_hdr__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_isp_combin_hdr__pack_to_buffer
                     (const TPbNvrIspCombinHdr *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_isp_combin_hdr__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrIspCombinHdr *
       tpb_nvr_isp_combin_hdr__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrIspCombinHdr *)
     protobuf_c_message_unpack (&tpb_nvr_isp_combin_hdr__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_isp_combin_hdr__free_unpacked
                     (TPbNvrIspCombinHdr *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_isp_combin_hdr__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_isp_denoise__init
                     (TPbNvrIspDenoise         *message)
{
  static TPbNvrIspDenoise init_value = TPB_NVR_ISP_DENOISE__INIT;
  *message = init_value;
}
size_t tpb_nvr_isp_denoise__get_packed_size
                     (const TPbNvrIspDenoise *message)
{
  assert(message->base.descriptor == &tpb_nvr_isp_denoise__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_isp_denoise__pack
                     (const TPbNvrIspDenoise *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_isp_denoise__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_isp_denoise__pack_to_buffer
                     (const TPbNvrIspDenoise *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_isp_denoise__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrIspDenoise *
       tpb_nvr_isp_denoise__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrIspDenoise *)
     protobuf_c_message_unpack (&tpb_nvr_isp_denoise__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_isp_denoise__free_unpacked
                     (TPbNvrIspDenoise *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_isp_denoise__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_isp_image_enhance__init
                     (TPbNvrIspImageEnhance         *message)
{
  static TPbNvrIspImageEnhance init_value = TPB_NVR_ISP_IMAGE_ENHANCE__INIT;
  *message = init_value;
}
size_t tpb_nvr_isp_image_enhance__get_packed_size
                     (const TPbNvrIspImageEnhance *message)
{
  assert(message->base.descriptor == &tpb_nvr_isp_image_enhance__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_isp_image_enhance__pack
                     (const TPbNvrIspImageEnhance *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_isp_image_enhance__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_isp_image_enhance__pack_to_buffer
                     (const TPbNvrIspImageEnhance *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_isp_image_enhance__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrIspImageEnhance *
       tpb_nvr_isp_image_enhance__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrIspImageEnhance *)
     protobuf_c_message_unpack (&tpb_nvr_isp_image_enhance__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_isp_image_enhance__free_unpacked
                     (TPbNvrIspImageEnhance *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_isp_image_enhance__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_isp_dig_dyn_range__init
                     (TPbNvrIspDigDynRange         *message)
{
  static TPbNvrIspDigDynRange init_value = TPB_NVR_ISP_DIG_DYN_RANGE__INIT;
  *message = init_value;
}
size_t tpb_nvr_isp_dig_dyn_range__get_packed_size
                     (const TPbNvrIspDigDynRange *message)
{
  assert(message->base.descriptor == &tpb_nvr_isp_dig_dyn_range__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_isp_dig_dyn_range__pack
                     (const TPbNvrIspDigDynRange *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_isp_dig_dyn_range__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_isp_dig_dyn_range__pack_to_buffer
                     (const TPbNvrIspDigDynRange *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_isp_dig_dyn_range__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrIspDigDynRange *
       tpb_nvr_isp_dig_dyn_range__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrIspDigDynRange *)
     protobuf_c_message_unpack (&tpb_nvr_isp_dig_dyn_range__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_isp_dig_dyn_range__free_unpacked
                     (TPbNvrIspDigDynRange *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_isp_dig_dyn_range__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_isp_adap_gamma__init
                     (TPbNvrIspAdapGamma         *message)
{
  static TPbNvrIspAdapGamma init_value = TPB_NVR_ISP_ADAP_GAMMA__INIT;
  *message = init_value;
}
size_t tpb_nvr_isp_adap_gamma__get_packed_size
                     (const TPbNvrIspAdapGamma *message)
{
  assert(message->base.descriptor == &tpb_nvr_isp_adap_gamma__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_isp_adap_gamma__pack
                     (const TPbNvrIspAdapGamma *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_isp_adap_gamma__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_isp_adap_gamma__pack_to_buffer
                     (const TPbNvrIspAdapGamma *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_isp_adap_gamma__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrIspAdapGamma *
       tpb_nvr_isp_adap_gamma__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrIspAdapGamma *)
     protobuf_c_message_unpack (&tpb_nvr_isp_adap_gamma__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_isp_adap_gamma__free_unpacked
                     (TPbNvrIspAdapGamma *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_isp_adap_gamma__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_isp_defog__init
                     (TPbNvrIspDefog         *message)
{
  static TPbNvrIspDefog init_value = TPB_NVR_ISP_DEFOG__INIT;
  *message = init_value;
}
size_t tpb_nvr_isp_defog__get_packed_size
                     (const TPbNvrIspDefog *message)
{
  assert(message->base.descriptor == &tpb_nvr_isp_defog__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_isp_defog__pack
                     (const TPbNvrIspDefog *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_isp_defog__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_isp_defog__pack_to_buffer
                     (const TPbNvrIspDefog *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_isp_defog__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrIspDefog *
       tpb_nvr_isp_defog__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrIspDefog *)
     protobuf_c_message_unpack (&tpb_nvr_isp_defog__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_isp_defog__free_unpacked
                     (TPbNvrIspDefog *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_isp_defog__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_isp_stablizer__init
                     (TPbNvrIspStablizer         *message)
{
  static TPbNvrIspStablizer init_value = TPB_NVR_ISP_STABLIZER__INIT;
  *message = init_value;
}
size_t tpb_nvr_isp_stablizer__get_packed_size
                     (const TPbNvrIspStablizer *message)
{
  assert(message->base.descriptor == &tpb_nvr_isp_stablizer__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_isp_stablizer__pack
                     (const TPbNvrIspStablizer *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_isp_stablizer__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_isp_stablizer__pack_to_buffer
                     (const TPbNvrIspStablizer *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_isp_stablizer__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrIspStablizer *
       tpb_nvr_isp_stablizer__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrIspStablizer *)
     protobuf_c_message_unpack (&tpb_nvr_isp_stablizer__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_isp_stablizer__free_unpacked
                     (TPbNvrIspStablizer *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_isp_stablizer__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_isp_day_night_zfparam__init
                     (TPbNvrIspDayNightZFParam         *message)
{
  static TPbNvrIspDayNightZFParam init_value = TPB_NVR_ISP_DAY_NIGHT_ZFPARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_isp_day_night_zfparam__get_packed_size
                     (const TPbNvrIspDayNightZFParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_isp_day_night_zfparam__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_isp_day_night_zfparam__pack
                     (const TPbNvrIspDayNightZFParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_isp_day_night_zfparam__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_isp_day_night_zfparam__pack_to_buffer
                     (const TPbNvrIspDayNightZFParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_isp_day_night_zfparam__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrIspDayNightZFParam *
       tpb_nvr_isp_day_night_zfparam__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrIspDayNightZFParam *)
     protobuf_c_message_unpack (&tpb_nvr_isp_day_night_zfparam__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_isp_day_night_zfparam__free_unpacked
                     (TPbNvrIspDayNightZFParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_isp_day_night_zfparam__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_isp_action__init
                     (TPbNvrIspAction         *message)
{
  static TPbNvrIspAction init_value = TPB_NVR_ISP_ACTION__INIT;
  *message = init_value;
}
size_t tpb_nvr_isp_action__get_packed_size
                     (const TPbNvrIspAction *message)
{
  assert(message->base.descriptor == &tpb_nvr_isp_action__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_isp_action__pack
                     (const TPbNvrIspAction *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_isp_action__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_isp_action__pack_to_buffer
                     (const TPbNvrIspAction *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_isp_action__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrIspAction *
       tpb_nvr_isp_action__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrIspAction *)
     protobuf_c_message_unpack (&tpb_nvr_isp_action__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_isp_action__free_unpacked
                     (TPbNvrIspAction *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_isp_action__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_img_flip_and_play_back__init
                     (TPbNvrImgFlipAndPlayBack         *message)
{
  static TPbNvrImgFlipAndPlayBack init_value = TPB_NVR_IMG_FLIP_AND_PLAY_BACK__INIT;
  *message = init_value;
}
size_t tpb_nvr_img_flip_and_play_back__get_packed_size
                     (const TPbNvrImgFlipAndPlayBack *message)
{
  assert(message->base.descriptor == &tpb_nvr_img_flip_and_play_back__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_img_flip_and_play_back__pack
                     (const TPbNvrImgFlipAndPlayBack *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_img_flip_and_play_back__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_img_flip_and_play_back__pack_to_buffer
                     (const TPbNvrImgFlipAndPlayBack *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_img_flip_and_play_back__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrImgFlipAndPlayBack *
       tpb_nvr_img_flip_and_play_back__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrImgFlipAndPlayBack *)
     protobuf_c_message_unpack (&tpb_nvr_img_flip_and_play_back__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_img_flip_and_play_back__free_unpacked
                     (TPbNvrImgFlipAndPlayBack *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_img_flip_and_play_back__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_infrared_far_near_level__init
                     (TPbNvrInfraredFarNearLevel         *message)
{
  static TPbNvrInfraredFarNearLevel init_value = TPB_NVR_INFRARED_FAR_NEAR_LEVEL__INIT;
  *message = init_value;
}
size_t tpb_nvr_infrared_far_near_level__get_packed_size
                     (const TPbNvrInfraredFarNearLevel *message)
{
  assert(message->base.descriptor == &tpb_nvr_infrared_far_near_level__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_infrared_far_near_level__pack
                     (const TPbNvrInfraredFarNearLevel *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_infrared_far_near_level__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_infrared_far_near_level__pack_to_buffer
                     (const TPbNvrInfraredFarNearLevel *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_infrared_far_near_level__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrInfraredFarNearLevel *
       tpb_nvr_infrared_far_near_level__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrInfraredFarNearLevel *)
     protobuf_c_message_unpack (&tpb_nvr_infrared_far_near_level__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_infrared_far_near_level__free_unpacked
                     (TPbNvrInfraredFarNearLevel *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_infrared_far_near_level__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_infrared_cfg__init
                     (TPbNvrInfraredCfg         *message)
{
  static TPbNvrInfraredCfg init_value = TPB_NVR_INFRARED_CFG__INIT;
  *message = init_value;
}
size_t tpb_nvr_infrared_cfg__get_packed_size
                     (const TPbNvrInfraredCfg *message)
{
  assert(message->base.descriptor == &tpb_nvr_infrared_cfg__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_infrared_cfg__pack
                     (const TPbNvrInfraredCfg *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_infrared_cfg__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_infrared_cfg__pack_to_buffer
                     (const TPbNvrInfraredCfg *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_infrared_cfg__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrInfraredCfg *
       tpb_nvr_infrared_cfg__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrInfraredCfg *)
     protobuf_c_message_unpack (&tpb_nvr_infrared_cfg__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_infrared_cfg__free_unpacked
                     (TPbNvrInfraredCfg *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_infrared_cfg__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_laser_cfg__init
                     (TPbNvrLaserCfg         *message)
{
  static TPbNvrLaserCfg init_value = TPB_NVR_LASER_CFG__INIT;
  *message = init_value;
}
size_t tpb_nvr_laser_cfg__get_packed_size
                     (const TPbNvrLaserCfg *message)
{
  assert(message->base.descriptor == &tpb_nvr_laser_cfg__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_laser_cfg__pack
                     (const TPbNvrLaserCfg *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_laser_cfg__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_laser_cfg__pack_to_buffer
                     (const TPbNvrLaserCfg *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_laser_cfg__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrLaserCfg *
       tpb_nvr_laser_cfg__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrLaserCfg *)
     protobuf_c_message_unpack (&tpb_nvr_laser_cfg__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_laser_cfg__free_unpacked
                     (TPbNvrLaserCfg *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_laser_cfg__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_isp_vid_ldc_param__init
                     (TPbNvrIspVidLdcParam         *message)
{
  static TPbNvrIspVidLdcParam init_value = TPB_NVR_ISP_VID_LDC_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_isp_vid_ldc_param__get_packed_size
                     (const TPbNvrIspVidLdcParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_isp_vid_ldc_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_isp_vid_ldc_param__pack
                     (const TPbNvrIspVidLdcParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_isp_vid_ldc_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_isp_vid_ldc_param__pack_to_buffer
                     (const TPbNvrIspVidLdcParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_isp_vid_ldc_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrIspVidLdcParam *
       tpb_nvr_isp_vid_ldc_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrIspVidLdcParam *)
     protobuf_c_message_unpack (&tpb_nvr_isp_vid_ldc_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_isp_vid_ldc_param__free_unpacked
                     (TPbNvrIspVidLdcParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_isp_vid_ldc_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_isp_ther_adjust_cfg__init
                     (TPbNvrIspTherAdjustCfg         *message)
{
  static TPbNvrIspTherAdjustCfg init_value = TPB_NVR_ISP_THER_ADJUST_CFG__INIT;
  *message = init_value;
}
size_t tpb_nvr_isp_ther_adjust_cfg__get_packed_size
                     (const TPbNvrIspTherAdjustCfg *message)
{
  assert(message->base.descriptor == &tpb_nvr_isp_ther_adjust_cfg__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_isp_ther_adjust_cfg__pack
                     (const TPbNvrIspTherAdjustCfg *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_isp_ther_adjust_cfg__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_isp_ther_adjust_cfg__pack_to_buffer
                     (const TPbNvrIspTherAdjustCfg *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_isp_ther_adjust_cfg__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrIspTherAdjustCfg *
       tpb_nvr_isp_ther_adjust_cfg__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrIspTherAdjustCfg *)
     protobuf_c_message_unpack (&tpb_nvr_isp_ther_adjust_cfg__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_isp_ther_adjust_cfg__free_unpacked
                     (TPbNvrIspTherAdjustCfg *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_isp_ther_adjust_cfg__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_isp_ther_enhance_cfg__init
                     (TPbNvrIspTherEnhanceCfg         *message)
{
  static TPbNvrIspTherEnhanceCfg init_value = TPB_NVR_ISP_THER_ENHANCE_CFG__INIT;
  *message = init_value;
}
size_t tpb_nvr_isp_ther_enhance_cfg__get_packed_size
                     (const TPbNvrIspTherEnhanceCfg *message)
{
  assert(message->base.descriptor == &tpb_nvr_isp_ther_enhance_cfg__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_isp_ther_enhance_cfg__pack
                     (const TPbNvrIspTherEnhanceCfg *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_isp_ther_enhance_cfg__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_isp_ther_enhance_cfg__pack_to_buffer
                     (const TPbNvrIspTherEnhanceCfg *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_isp_ther_enhance_cfg__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrIspTherEnhanceCfg *
       tpb_nvr_isp_ther_enhance_cfg__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrIspTherEnhanceCfg *)
     protobuf_c_message_unpack (&tpb_nvr_isp_ther_enhance_cfg__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_isp_ther_enhance_cfg__free_unpacked
                     (TPbNvrIspTherEnhanceCfg *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_isp_ther_enhance_cfg__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_lcam_isp_param__init
                     (TPbLcamIspParam         *message)
{
  static TPbLcamIspParam init_value = TPB_LCAM_ISP_PARAM__INIT;
  *message = init_value;
}
size_t tpb_lcam_isp_param__get_packed_size
                     (const TPbLcamIspParam *message)
{
  assert(message->base.descriptor == &tpb_lcam_isp_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_lcam_isp_param__pack
                     (const TPbLcamIspParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_lcam_isp_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_lcam_isp_param__pack_to_buffer
                     (const TPbLcamIspParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_lcam_isp_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbLcamIspParam *
       tpb_lcam_isp_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbLcamIspParam *)
     protobuf_c_message_unpack (&tpb_lcam_isp_param__descriptor,
                                allocator, len, data);
}
void   tpb_lcam_isp_param__free_unpacked
                     (TPbLcamIspParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_lcam_isp_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_isp_dynamic_mode_param__init
                     (TPbNvrIspDynamicModeParam         *message)
{
  static TPbNvrIspDynamicModeParam init_value = TPB_NVR_ISP_DYNAMIC_MODE_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_isp_dynamic_mode_param__get_packed_size
                     (const TPbNvrIspDynamicModeParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_isp_dynamic_mode_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_isp_dynamic_mode_param__pack
                     (const TPbNvrIspDynamicModeParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_isp_dynamic_mode_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_isp_dynamic_mode_param__pack_to_buffer
                     (const TPbNvrIspDynamicModeParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_isp_dynamic_mode_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrIspDynamicModeParam *
       tpb_nvr_isp_dynamic_mode_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrIspDynamicModeParam *)
     protobuf_c_message_unpack (&tpb_nvr_isp_dynamic_mode_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_isp_dynamic_mode_param__free_unpacked
                     (TPbNvrIspDynamicModeParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_isp_dynamic_mode_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_isp_dy_scene_start_end_time__init
                     (TPbNvrIspDySceneStartEndTime         *message)
{
  static TPbNvrIspDySceneStartEndTime init_value = TPB_NVR_ISP_DY_SCENE_START_END_TIME__INIT;
  *message = init_value;
}
size_t tpb_nvr_isp_dy_scene_start_end_time__get_packed_size
                     (const TPbNvrIspDySceneStartEndTime *message)
{
  assert(message->base.descriptor == &tpb_nvr_isp_dy_scene_start_end_time__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_isp_dy_scene_start_end_time__pack
                     (const TPbNvrIspDySceneStartEndTime *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_isp_dy_scene_start_end_time__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_isp_dy_scene_start_end_time__pack_to_buffer
                     (const TPbNvrIspDySceneStartEndTime *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_isp_dy_scene_start_end_time__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrIspDySceneStartEndTime *
       tpb_nvr_isp_dy_scene_start_end_time__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrIspDySceneStartEndTime *)
     protobuf_c_message_unpack (&tpb_nvr_isp_dy_scene_start_end_time__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_isp_dy_scene_start_end_time__free_unpacked
                     (TPbNvrIspDySceneStartEndTime *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_isp_dy_scene_start_end_time__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_isp_scene_scale_param__init
                     (TPbNvrIspSceneScaleParam         *message)
{
  static TPbNvrIspSceneScaleParam init_value = TPB_NVR_ISP_SCENE_SCALE_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_isp_scene_scale_param__get_packed_size
                     (const TPbNvrIspSceneScaleParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_isp_scene_scale_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_isp_scene_scale_param__pack
                     (const TPbNvrIspSceneScaleParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_isp_scene_scale_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_isp_scene_scale_param__pack_to_buffer
                     (const TPbNvrIspSceneScaleParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_isp_scene_scale_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrIspSceneScaleParam *
       tpb_nvr_isp_scene_scale_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrIspSceneScaleParam *)
     protobuf_c_message_unpack (&tpb_nvr_isp_scene_scale_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_isp_scene_scale_param__free_unpacked
                     (TPbNvrIspSceneScaleParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_isp_scene_scale_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_isp_scene_timing_param__init
                     (TPbNvrIspSceneTimingParam         *message)
{
  static TPbNvrIspSceneTimingParam init_value = TPB_NVR_ISP_SCENE_TIMING_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_isp_scene_timing_param__get_packed_size
                     (const TPbNvrIspSceneTimingParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_isp_scene_timing_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_isp_scene_timing_param__pack
                     (const TPbNvrIspSceneTimingParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_isp_scene_timing_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_isp_scene_timing_param__pack_to_buffer
                     (const TPbNvrIspSceneTimingParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_isp_scene_timing_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrIspSceneTimingParam *
       tpb_nvr_isp_scene_timing_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrIspSceneTimingParam *)
     protobuf_c_message_unpack (&tpb_nvr_isp_scene_timing_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_isp_scene_timing_param__free_unpacked
                     (TPbNvrIspSceneTimingParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_isp_scene_timing_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_isp_scene_dynamic_param__init
                     (TPbNvrIspSceneDynamicParam         *message)
{
  static TPbNvrIspSceneDynamicParam init_value = TPB_NVR_ISP_SCENE_DYNAMIC_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_isp_scene_dynamic_param__get_packed_size
                     (const TPbNvrIspSceneDynamicParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_isp_scene_dynamic_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_isp_scene_dynamic_param__pack
                     (const TPbNvrIspSceneDynamicParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_isp_scene_dynamic_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_isp_scene_dynamic_param__pack_to_buffer
                     (const TPbNvrIspSceneDynamicParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_isp_scene_dynamic_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrIspSceneDynamicParam *
       tpb_nvr_isp_scene_dynamic_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrIspSceneDynamicParam *)
     protobuf_c_message_unpack (&tpb_nvr_isp_scene_dynamic_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_isp_scene_dynamic_param__free_unpacked
                     (TPbNvrIspSceneDynamicParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_isp_scene_dynamic_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_user_scene_name_num__init
                     (TPbUserSceneNameNum         *message)
{
  static TPbUserSceneNameNum init_value = TPB_USER_SCENE_NAME_NUM__INIT;
  *message = init_value;
}
size_t tpb_user_scene_name_num__get_packed_size
                     (const TPbUserSceneNameNum *message)
{
  assert(message->base.descriptor == &tpb_user_scene_name_num__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_user_scene_name_num__pack
                     (const TPbUserSceneNameNum *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_user_scene_name_num__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_user_scene_name_num__pack_to_buffer
                     (const TPbUserSceneNameNum *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_user_scene_name_num__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbUserSceneNameNum *
       tpb_user_scene_name_num__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbUserSceneNameNum *)
     protobuf_c_message_unpack (&tpb_user_scene_name_num__descriptor,
                                allocator, len, data);
}
void   tpb_user_scene_name_num__free_unpacked
                     (TPbUserSceneNameNum *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_user_scene_name_num__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_lcam_isp_scene_param__init
                     (TPbLcamIspSceneParam         *message)
{
  static TPbLcamIspSceneParam init_value = TPB_LCAM_ISP_SCENE_PARAM__INIT;
  *message = init_value;
}
size_t tpb_lcam_isp_scene_param__get_packed_size
                     (const TPbLcamIspSceneParam *message)
{
  assert(message->base.descriptor == &tpb_lcam_isp_scene_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_lcam_isp_scene_param__pack
                     (const TPbLcamIspSceneParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_lcam_isp_scene_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_lcam_isp_scene_param__pack_to_buffer
                     (const TPbLcamIspSceneParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_lcam_isp_scene_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbLcamIspSceneParam *
       tpb_lcam_isp_scene_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbLcamIspSceneParam *)
     protobuf_c_message_unpack (&tpb_lcam_isp_scene_param__descriptor,
                                allocator, len, data);
}
void   tpb_lcam_isp_scene_param__free_unpacked
                     (TPbLcamIspSceneParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_lcam_isp_scene_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_lcam_isp_param_all__init
                     (TPbLcamIspParamAll         *message)
{
  static TPbLcamIspParamAll init_value = TPB_LCAM_ISP_PARAM_ALL__INIT;
  *message = init_value;
}
size_t tpb_lcam_isp_param_all__get_packed_size
                     (const TPbLcamIspParamAll *message)
{
  assert(message->base.descriptor == &tpb_lcam_isp_param_all__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_lcam_isp_param_all__pack
                     (const TPbLcamIspParamAll *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_lcam_isp_param_all__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_lcam_isp_param_all__pack_to_buffer
                     (const TPbLcamIspParamAll *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_lcam_isp_param_all__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbLcamIspParamAll *
       tpb_lcam_isp_param_all__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbLcamIspParamAll *)
     protobuf_c_message_unpack (&tpb_lcam_isp_param_all__descriptor,
                                allocator, len, data);
}
void   tpb_lcam_isp_param_all__free_unpacked
                     (TPbLcamIspParamAll *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_lcam_isp_param_all__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_lcam_isp_scene_param_all__init
                     (TPbLcamIspSceneParamAll         *message)
{
  static TPbLcamIspSceneParamAll init_value = TPB_LCAM_ISP_SCENE_PARAM_ALL__INIT;
  *message = init_value;
}
size_t tpb_lcam_isp_scene_param_all__get_packed_size
                     (const TPbLcamIspSceneParamAll *message)
{
  assert(message->base.descriptor == &tpb_lcam_isp_scene_param_all__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_lcam_isp_scene_param_all__pack
                     (const TPbLcamIspSceneParamAll *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_lcam_isp_scene_param_all__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_lcam_isp_scene_param_all__pack_to_buffer
                     (const TPbLcamIspSceneParamAll *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_lcam_isp_scene_param_all__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbLcamIspSceneParamAll *
       tpb_lcam_isp_scene_param_all__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbLcamIspSceneParamAll *)
     protobuf_c_message_unpack (&tpb_lcam_isp_scene_param_all__descriptor,
                                allocator, len, data);
}
void   tpb_lcam_isp_scene_param_all__free_unpacked
                     (TPbLcamIspSceneParamAll *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_lcam_isp_scene_param_all__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
static const ProtobufCFieldDescriptor tpb_nvr_isp_image__field_descriptors[5] =
{
  {
    "brightness_level",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspImage, has_brightness_level),
    offsetof(TPbNvrIspImage, brightness_level),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "contrast_level",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspImage, has_contrast_level),
    offsetof(TPbNvrIspImage, contrast_level),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "sharpness_level",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspImage, has_sharpness_level),
    offsetof(TPbNvrIspImage, sharpness_level),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "saturation_level",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspImage, has_saturation_level),
    offsetof(TPbNvrIspImage, saturation_level),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "hue_level",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspImage, has_hue_level),
    offsetof(TPbNvrIspImage, hue_level),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_isp_image__field_indices_by_name[] = {
  0,   /* field[0] = brightness_level */
  1,   /* field[1] = contrast_level */
  4,   /* field[4] = hue_level */
  3,   /* field[3] = saturation_level */
  2,   /* field[2] = sharpness_level */
};
static const ProtobufCIntRange tpb_nvr_isp_image__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 5 }
};
const ProtobufCMessageDescriptor tpb_nvr_isp_image__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrIspImage",
  "TPbNvrIspImage",
  "TPbNvrIspImage",
  "",
  sizeof(TPbNvrIspImage),
  5,
  tpb_nvr_isp_image__field_descriptors,
  tpb_nvr_isp_image__field_indices_by_name,
  1,  tpb_nvr_isp_image__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_isp_image__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_isp_white_balance__field_descriptors[4] =
{
  {
    "white_balance_mode",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspWhiteBalance, has_white_balance_mode),
    offsetof(TPbNvrIspWhiteBalance, white_balance_mode),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "auto_white_balance_speed",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspWhiteBalance, has_auto_white_balance_speed),
    offsetof(TPbNvrIspWhiteBalance, auto_white_balance_speed),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "white_balance_red",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspWhiteBalance, has_white_balance_red),
    offsetof(TPbNvrIspWhiteBalance, white_balance_red),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "white_balance_blue",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspWhiteBalance, has_white_balance_blue),
    offsetof(TPbNvrIspWhiteBalance, white_balance_blue),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_isp_white_balance__field_indices_by_name[] = {
  1,   /* field[1] = auto_white_balance_speed */
  3,   /* field[3] = white_balance_blue */
  0,   /* field[0] = white_balance_mode */
  2,   /* field[2] = white_balance_red */
};
static const ProtobufCIntRange tpb_nvr_isp_white_balance__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 4 }
};
const ProtobufCMessageDescriptor tpb_nvr_isp_white_balance__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrIspWhiteBalance",
  "TPbNvrIspWhiteBalance",
  "TPbNvrIspWhiteBalance",
  "",
  sizeof(TPbNvrIspWhiteBalance),
  4,
  tpb_nvr_isp_white_balance__field_descriptors,
  tpb_nvr_isp_white_balance__field_indices_by_name,
  1,  tpb_nvr_isp_white_balance__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_isp_white_balance__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_isp_gain__field_descriptors[3] =
{
  {
    "gain_mode",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspGain, has_gain_mode),
    offsetof(TPbNvrIspGain, gain_mode),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "gain_level",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspGain, has_gain_level),
    offsetof(TPbNvrIspGain, gain_level),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "gain_max",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspGain, has_gain_max),
    offsetof(TPbNvrIspGain, gain_max),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_isp_gain__field_indices_by_name[] = {
  1,   /* field[1] = gain_level */
  2,   /* field[2] = gain_max */
  0,   /* field[0] = gain_mode */
};
static const ProtobufCIntRange tpb_nvr_isp_gain__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 3 }
};
const ProtobufCMessageDescriptor tpb_nvr_isp_gain__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrIspGain",
  "TPbNvrIspGain",
  "TPbNvrIspGain",
  "",
  sizeof(TPbNvrIspGain),
  3,
  tpb_nvr_isp_gain__field_descriptors,
  tpb_nvr_isp_gain__field_indices_by_name,
  1,  tpb_nvr_isp_gain__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_isp_gain__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_isp_shutter__field_descriptors[4] =
{
  {
    "shutter_mode",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspShutter, has_shutter_mode),
    offsetof(TPbNvrIspShutter, shutter_mode),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "shutter_time",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspShutter, has_shutter_time),
    offsetof(TPbNvrIspShutter, shutter_time),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "shutter_min",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspShutter, has_shutter_min),
    offsetof(TPbNvrIspShutter, shutter_min),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "shutter_max",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspShutter, has_shutter_max),
    offsetof(TPbNvrIspShutter, shutter_max),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_isp_shutter__field_indices_by_name[] = {
  3,   /* field[3] = shutter_max */
  2,   /* field[2] = shutter_min */
  0,   /* field[0] = shutter_mode */
  1,   /* field[1] = shutter_time */
};
static const ProtobufCIntRange tpb_nvr_isp_shutter__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 4 }
};
const ProtobufCMessageDescriptor tpb_nvr_isp_shutter__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrIspShutter",
  "TPbNvrIspShutter",
  "TPbNvrIspShutter",
  "",
  sizeof(TPbNvrIspShutter),
  4,
  tpb_nvr_isp_shutter__field_descriptors,
  tpb_nvr_isp_shutter__field_indices_by_name,
  1,  tpb_nvr_isp_shutter__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_isp_shutter__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_isp_pro_exposure__field_descriptors[4] =
{
  {
    "smart_ae_mode",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspProExposure, has_smart_ae_mode),
    offsetof(TPbNvrIspProExposure, smart_ae_mode),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "exposure_level",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspProExposure, has_exposure_level),
    offsetof(TPbNvrIspProExposure, exposure_level),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "exposure_speed",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspProExposure, has_exposure_speed),
    offsetof(TPbNvrIspProExposure, exposure_speed),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "exposure_window",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrIspProExposure, exposure_window),
    &tpb_nvr_isp_window__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_isp_pro_exposure__field_indices_by_name[] = {
  1,   /* field[1] = exposure_level */
  2,   /* field[2] = exposure_speed */
  3,   /* field[3] = exposure_window */
  0,   /* field[0] = smart_ae_mode */
};
static const ProtobufCIntRange tpb_nvr_isp_pro_exposure__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 4 }
};
const ProtobufCMessageDescriptor tpb_nvr_isp_pro_exposure__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrIspProExposure",
  "TPbNvrIspProExposure",
  "TPbNvrIspProExposure",
  "",
  sizeof(TPbNvrIspProExposure),
  4,
  tpb_nvr_isp_pro_exposure__field_descriptors,
  tpb_nvr_isp_pro_exposure__field_indices_by_name,
  1,  tpb_nvr_isp_pro_exposure__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_isp_pro_exposure__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_isp_window__field_descriptors[4] =
{
  {
    "top_left_x",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspWindow, has_top_left_x),
    offsetof(TPbNvrIspWindow, top_left_x),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "top_left_y",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspWindow, has_top_left_y),
    offsetof(TPbNvrIspWindow, top_left_y),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "width",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspWindow, has_width),
    offsetof(TPbNvrIspWindow, width),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "height",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspWindow, has_height),
    offsetof(TPbNvrIspWindow, height),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_isp_window__field_indices_by_name[] = {
  3,   /* field[3] = height */
  0,   /* field[0] = top_left_x */
  1,   /* field[1] = top_left_y */
  2,   /* field[2] = width */
};
static const ProtobufCIntRange tpb_nvr_isp_window__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 4 }
};
const ProtobufCMessageDescriptor tpb_nvr_isp_window__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrIspWindow",
  "TPbNvrIspWindow",
  "TPbNvrIspWindow",
  "",
  sizeof(TPbNvrIspWindow),
  4,
  tpb_nvr_isp_window__field_descriptors,
  tpb_nvr_isp_window__field_indices_by_name,
  1,  tpb_nvr_isp_window__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_isp_window__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_isp_day_night_switch__field_descriptors[8] =
{
  {
    "day_night_mode",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspDayNightSwitch, has_day_night_mode),
    offsetof(TPbNvrIspDayNightSwitch, day_night_mode),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "day_night_threshold",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspDayNightSwitch, has_day_night_threshold),
    offsetof(TPbNvrIspDayNightSwitch, day_night_threshold),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "day_night_sensity_level",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspDayNightSwitch, has_day_night_sensity_level),
    offsetof(TPbNvrIspDayNightSwitch, day_night_sensity_level),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "day_night_delay_time",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspDayNightSwitch, has_day_night_delay_time),
    offsetof(TPbNvrIspDayNightSwitch, day_night_delay_time),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "trigger_mode",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspDayNightSwitch, has_trigger_mode),
    offsetof(TPbNvrIspDayNightSwitch, trigger_mode),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "tNvr_isp_timing_param",
    6,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrIspDayNightSwitch, n_tnvr_isp_timing_param),
    offsetof(TPbNvrIspDayNightSwitch, tnvr_isp_timing_param),
    &tpb_nvr_isp_timing__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "day_nigtht_light_sens",
    7,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspDayNightSwitch, has_day_nigtht_light_sens),
    offsetof(TPbNvrIspDayNightSwitch, day_nigtht_light_sens),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "day_night_light_thr",
    8,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspDayNightSwitch, has_day_night_light_thr),
    offsetof(TPbNvrIspDayNightSwitch, day_night_light_thr),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_isp_day_night_switch__field_indices_by_name[] = {
  3,   /* field[3] = day_night_delay_time */
  7,   /* field[7] = day_night_light_thr */
  0,   /* field[0] = day_night_mode */
  2,   /* field[2] = day_night_sensity_level */
  1,   /* field[1] = day_night_threshold */
  6,   /* field[6] = day_nigtht_light_sens */
  5,   /* field[5] = tNvr_isp_timing_param */
  4,   /* field[4] = trigger_mode */
};
static const ProtobufCIntRange tpb_nvr_isp_day_night_switch__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 8 }
};
const ProtobufCMessageDescriptor tpb_nvr_isp_day_night_switch__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrIspDayNightSwitch",
  "TPbNvrIspDayNightSwitch",
  "TPbNvrIspDayNightSwitch",
  "",
  sizeof(TPbNvrIspDayNightSwitch),
  8,
  tpb_nvr_isp_day_night_switch__field_descriptors,
  tpb_nvr_isp_day_night_switch__field_indices_by_name,
  1,  tpb_nvr_isp_day_night_switch__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_isp_day_night_switch__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_isp_timing__field_descriptors[3] =
{
  {
    "enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspTiming, has_enable),
    offsetof(TPbNvrIspTiming, enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "start_time",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_INT32,
    offsetof(TPbNvrIspTiming, has_start_time),
    offsetof(TPbNvrIspTiming, start_time),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "end_time",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_INT32,
    offsetof(TPbNvrIspTiming, has_end_time),
    offsetof(TPbNvrIspTiming, end_time),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_isp_timing__field_indices_by_name[] = {
  0,   /* field[0] = enable */
  2,   /* field[2] = end_time */
  1,   /* field[1] = start_time */
};
static const ProtobufCIntRange tpb_nvr_isp_timing__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 3 }
};
const ProtobufCMessageDescriptor tpb_nvr_isp_timing__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrIspTiming",
  "TPbNvrIspTiming",
  "TPbNvrIspTiming",
  "",
  sizeof(TPbNvrIspTiming),
  3,
  tpb_nvr_isp_timing__field_descriptors,
  tpb_nvr_isp_timing__field_indices_by_name,
  1,  tpb_nvr_isp_timing__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_isp_timing__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_isp_back_light__field_descriptors[6] =
{
  {
    "back_light_mode",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspBackLight, has_back_light_mode),
    offsetof(TPbNvrIspBackLight, back_light_mode),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "blc_level",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspBackLight, has_blc_level),
    offsetof(TPbNvrIspBackLight, blc_level),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "slc_level",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspBackLight, has_slc_level),
    offsetof(TPbNvrIspBackLight, slc_level),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "blc_region_type",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspBackLight, has_blc_region_type),
    offsetof(TPbNvrIspBackLight, blc_region_type),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "blc_region",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrIspBackLight, blc_region),
    &tpb_nvr_isp_window__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "smart_ir_level",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspBackLight, has_smart_ir_level),
    offsetof(TPbNvrIspBackLight, smart_ir_level),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_isp_back_light__field_indices_by_name[] = {
  0,   /* field[0] = back_light_mode */
  1,   /* field[1] = blc_level */
  4,   /* field[4] = blc_region */
  3,   /* field[3] = blc_region_type */
  2,   /* field[2] = slc_level */
  5,   /* field[5] = smart_ir_level */
};
static const ProtobufCIntRange tpb_nvr_isp_back_light__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 6 }
};
const ProtobufCMessageDescriptor tpb_nvr_isp_back_light__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrIspBackLight",
  "TPbNvrIspBackLight",
  "TPbNvrIspBackLight",
  "",
  sizeof(TPbNvrIspBackLight),
  6,
  tpb_nvr_isp_back_light__field_descriptors,
  tpb_nvr_isp_back_light__field_indices_by_name,
  1,  tpb_nvr_isp_back_light__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_isp_back_light__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_isp_combin_hdr__field_descriptors[2] =
{
  {
    "combin_hdr_mode",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspCombinHdr, has_combin_hdr_mode),
    offsetof(TPbNvrIspCombinHdr, combin_hdr_mode),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "combin_hdr_level",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspCombinHdr, has_combin_hdr_level),
    offsetof(TPbNvrIspCombinHdr, combin_hdr_level),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_isp_combin_hdr__field_indices_by_name[] = {
  1,   /* field[1] = combin_hdr_level */
  0,   /* field[0] = combin_hdr_mode */
};
static const ProtobufCIntRange tpb_nvr_isp_combin_hdr__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_isp_combin_hdr__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrIspCombinHdr",
  "TPbNvrIspCombinHdr",
  "TPbNvrIspCombinHdr",
  "",
  sizeof(TPbNvrIspCombinHdr),
  2,
  tpb_nvr_isp_combin_hdr__field_descriptors,
  tpb_nvr_isp_combin_hdr__field_indices_by_name,
  1,  tpb_nvr_isp_combin_hdr__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_isp_combin_hdr__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_isp_denoise__field_descriptors[4] =
{
  {
    "denoise_2d_mode",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspDenoise, has_denoise_2d_mode),
    offsetof(TPbNvrIspDenoise, denoise_2d_mode),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "denoise_3d_mode",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspDenoise, has_denoise_3d_mode),
    offsetof(TPbNvrIspDenoise, denoise_3d_mode),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "noise_reduce_2d_level",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspDenoise, has_noise_reduce_2d_level),
    offsetof(TPbNvrIspDenoise, noise_reduce_2d_level),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "noise_reduce_3d_level",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspDenoise, has_noise_reduce_3d_level),
    offsetof(TPbNvrIspDenoise, noise_reduce_3d_level),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_isp_denoise__field_indices_by_name[] = {
  0,   /* field[0] = denoise_2d_mode */
  1,   /* field[1] = denoise_3d_mode */
  2,   /* field[2] = noise_reduce_2d_level */
  3,   /* field[3] = noise_reduce_3d_level */
};
static const ProtobufCIntRange tpb_nvr_isp_denoise__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 4 }
};
const ProtobufCMessageDescriptor tpb_nvr_isp_denoise__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrIspDenoise",
  "TPbNvrIspDenoise",
  "TPbNvrIspDenoise",
  "",
  sizeof(TPbNvrIspDenoise),
  4,
  tpb_nvr_isp_denoise__field_descriptors,
  tpb_nvr_isp_denoise__field_indices_by_name,
  1,  tpb_nvr_isp_denoise__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_isp_denoise__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_isp_image_enhance__field_descriptors[5] =
{
  {
    "image_enhance_mode",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspImageEnhance, has_image_enhance_mode),
    offsetof(TPbNvrIspImageEnhance, image_enhance_mode),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "dig_dyn_range",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrIspImageEnhance, dig_dyn_range),
    &tpb_nvr_isp_dig_dyn_range__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "adap_gamma_param",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrIspImageEnhance, adap_gamma_param),
    &tpb_nvr_isp_adap_gamma__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "isp_defog_param",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrIspImageEnhance, isp_defog_param),
    &tpb_nvr_isp_defog__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "dehaze_optics_mode",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspImageEnhance, has_dehaze_optics_mode),
    offsetof(TPbNvrIspImageEnhance, dehaze_optics_mode),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_isp_image_enhance__field_indices_by_name[] = {
  2,   /* field[2] = adap_gamma_param */
  4,   /* field[4] = dehaze_optics_mode */
  1,   /* field[1] = dig_dyn_range */
  0,   /* field[0] = image_enhance_mode */
  3,   /* field[3] = isp_defog_param */
};
static const ProtobufCIntRange tpb_nvr_isp_image_enhance__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 5 }
};
const ProtobufCMessageDescriptor tpb_nvr_isp_image_enhance__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrIspImageEnhance",
  "TPbNvrIspImageEnhance",
  "TPbNvrIspImageEnhance",
  "",
  sizeof(TPbNvrIspImageEnhance),
  5,
  tpb_nvr_isp_image_enhance__field_descriptors,
  tpb_nvr_isp_image_enhance__field_indices_by_name,
  1,  tpb_nvr_isp_image_enhance__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_isp_image_enhance__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_isp_dig_dyn_range__field_descriptors[2] =
{
  {
    "dig_dyn_range_mode",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspDigDynRange, has_dig_dyn_range_mode),
    offsetof(TPbNvrIspDigDynRange, dig_dyn_range_mode),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "dig_dyn_range_level",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspDigDynRange, has_dig_dyn_range_level),
    offsetof(TPbNvrIspDigDynRange, dig_dyn_range_level),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_isp_dig_dyn_range__field_indices_by_name[] = {
  1,   /* field[1] = dig_dyn_range_level */
  0,   /* field[0] = dig_dyn_range_mode */
};
static const ProtobufCIntRange tpb_nvr_isp_dig_dyn_range__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_isp_dig_dyn_range__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrIspDigDynRange",
  "TPbNvrIspDigDynRange",
  "TPbNvrIspDigDynRange",
  "",
  sizeof(TPbNvrIspDigDynRange),
  2,
  tpb_nvr_isp_dig_dyn_range__field_descriptors,
  tpb_nvr_isp_dig_dyn_range__field_indices_by_name,
  1,  tpb_nvr_isp_dig_dyn_range__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_isp_dig_dyn_range__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_isp_adap_gamma__field_descriptors[2] =
{
  {
    "adap_gamma_mode",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspAdapGamma, has_adap_gamma_mode),
    offsetof(TPbNvrIspAdapGamma, adap_gamma_mode),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "adap_gamma_level",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspAdapGamma, has_adap_gamma_level),
    offsetof(TPbNvrIspAdapGamma, adap_gamma_level),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_isp_adap_gamma__field_indices_by_name[] = {
  1,   /* field[1] = adap_gamma_level */
  0,   /* field[0] = adap_gamma_mode */
};
static const ProtobufCIntRange tpb_nvr_isp_adap_gamma__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_isp_adap_gamma__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrIspAdapGamma",
  "TPbNvrIspAdapGamma",
  "TPbNvrIspAdapGamma",
  "",
  sizeof(TPbNvrIspAdapGamma),
  2,
  tpb_nvr_isp_adap_gamma__field_descriptors,
  tpb_nvr_isp_adap_gamma__field_indices_by_name,
  1,  tpb_nvr_isp_adap_gamma__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_isp_adap_gamma__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_isp_defog__field_descriptors[2] =
{
  {
    "defog_mode",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspDefog, has_defog_mode),
    offsetof(TPbNvrIspDefog, defog_mode),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "defog_level",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspDefog, has_defog_level),
    offsetof(TPbNvrIspDefog, defog_level),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_isp_defog__field_indices_by_name[] = {
  1,   /* field[1] = defog_level */
  0,   /* field[0] = defog_mode */
};
static const ProtobufCIntRange tpb_nvr_isp_defog__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_isp_defog__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrIspDefog",
  "TPbNvrIspDefog",
  "TPbNvrIspDefog",
  "",
  sizeof(TPbNvrIspDefog),
  2,
  tpb_nvr_isp_defog__field_descriptors,
  tpb_nvr_isp_defog__field_indices_by_name,
  1,  tpb_nvr_isp_defog__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_isp_defog__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_isp_stablizer__field_descriptors[2] =
{
  {
    "video_stablizer_mode",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspStablizer, has_video_stablizer_mode),
    offsetof(TPbNvrIspStablizer, video_stablizer_mode),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "video_stablizer_level",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspStablizer, has_video_stablizer_level),
    offsetof(TPbNvrIspStablizer, video_stablizer_level),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_isp_stablizer__field_indices_by_name[] = {
  1,   /* field[1] = video_stablizer_level */
  0,   /* field[0] = video_stablizer_mode */
};
static const ProtobufCIntRange tpb_nvr_isp_stablizer__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_isp_stablizer__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrIspStablizer",
  "TPbNvrIspStablizer",
  "TPbNvrIspStablizer",
  "",
  sizeof(TPbNvrIspStablizer),
  2,
  tpb_nvr_isp_stablizer__field_descriptors,
  tpb_nvr_isp_stablizer__field_indices_by_name,
  1,  tpb_nvr_isp_stablizer__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_isp_stablizer__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_isp_day_night_zfparam__field_descriptors[2] =
{
  {
    "action_type",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspDayNightZFParam, has_action_type),
    offsetof(TPbNvrIspDayNightZFParam, action_type),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "day_night_state",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspDayNightZFParam, has_day_night_state),
    offsetof(TPbNvrIspDayNightZFParam, day_night_state),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_isp_day_night_zfparam__field_indices_by_name[] = {
  0,   /* field[0] = action_type */
  1,   /* field[1] = day_night_state */
};
static const ProtobufCIntRange tpb_nvr_isp_day_night_zfparam__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_isp_day_night_zfparam__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrIspDayNightZFParam",
  "TPbNvrIspDayNightZFParam",
  "TPbNvrIspDayNightZFParam",
  "",
  sizeof(TPbNvrIspDayNightZFParam),
  2,
  tpb_nvr_isp_day_night_zfparam__field_descriptors,
  tpb_nvr_isp_day_night_zfparam__field_indices_by_name,
  1,  tpb_nvr_isp_day_night_zfparam__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_isp_day_night_zfparam__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_isp_action__field_descriptors[12] =
{
  {
    "ipc_isp_focus_mode",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspAction, has_ipc_isp_focus_mode),
    offsetof(TPbNvrIspAction, ipc_isp_focus_mode),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "focus_station",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspAction, has_focus_station),
    offsetof(TPbNvrIspAction, focus_station),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "focus_roi",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrIspAction, focus_roi),
    &tpb_nvr_isp_window__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "focus_limit_max",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspAction, has_focus_limit_max),
    offsetof(TPbNvrIspAction, focus_limit_max),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "focus_limit_min",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspAction, has_focus_limit_min),
    offsetof(TPbNvrIspAction, focus_limit_min),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "iris_mode",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspAction, has_iris_mode),
    offsetof(TPbNvrIspAction, iris_mode),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "iris_position",
    7,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspAction, has_iris_position),
    offsetof(TPbNvrIspAction, iris_position),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "iris_sensitivety",
    8,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspAction, has_iris_sensitivety),
    offsetof(TPbNvrIspAction, iris_sensitivety),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "zoom_speed",
    9,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspAction, has_zoom_speed),
    offsetof(TPbNvrIspAction, zoom_speed),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "zoom_position",
    10,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspAction, has_zoom_position),
    offsetof(TPbNvrIspAction, zoom_position),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "day_night_zfparam",
    11,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrIspAction, day_night_zfparam),
    &tpb_nvr_isp_day_night_zfparam__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "focus_roi_area",
    12,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspAction, has_focus_roi_area),
    offsetof(TPbNvrIspAction, focus_roi_area),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_isp_action__field_indices_by_name[] = {
  10,   /* field[10] = day_night_zfparam */
  3,   /* field[3] = focus_limit_max */
  4,   /* field[4] = focus_limit_min */
  2,   /* field[2] = focus_roi */
  11,   /* field[11] = focus_roi_area */
  1,   /* field[1] = focus_station */
  0,   /* field[0] = ipc_isp_focus_mode */
  5,   /* field[5] = iris_mode */
  6,   /* field[6] = iris_position */
  7,   /* field[7] = iris_sensitivety */
  9,   /* field[9] = zoom_position */
  8,   /* field[8] = zoom_speed */
};
static const ProtobufCIntRange tpb_nvr_isp_action__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 12 }
};
const ProtobufCMessageDescriptor tpb_nvr_isp_action__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrIspAction",
  "TPbNvrIspAction",
  "TPbNvrIspAction",
  "",
  sizeof(TPbNvrIspAction),
  12,
  tpb_nvr_isp_action__field_descriptors,
  tpb_nvr_isp_action__field_indices_by_name,
  1,  tpb_nvr_isp_action__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_isp_action__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_img_flip_and_play_back__field_descriptors[4] =
{
  {
    "flip_mode",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrImgFlipAndPlayBack, has_flip_mode),
    offsetof(TPbNvrImgFlipAndPlayBack, flip_mode),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "mirror_mode",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrImgFlipAndPlayBack, has_mirror_mode),
    offsetof(TPbNvrImgFlipAndPlayBack, mirror_mode),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "rotate_mode",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrImgFlipAndPlayBack, has_rotate_mode),
    offsetof(TPbNvrImgFlipAndPlayBack, rotate_mode),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "play_back_mode",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrImgFlipAndPlayBack, has_play_back_mode),
    offsetof(TPbNvrImgFlipAndPlayBack, play_back_mode),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_img_flip_and_play_back__field_indices_by_name[] = {
  0,   /* field[0] = flip_mode */
  1,   /* field[1] = mirror_mode */
  3,   /* field[3] = play_back_mode */
  2,   /* field[2] = rotate_mode */
};
static const ProtobufCIntRange tpb_nvr_img_flip_and_play_back__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 4 }
};
const ProtobufCMessageDescriptor tpb_nvr_img_flip_and_play_back__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrImgFlipAndPlayBack",
  "TPbNvrImgFlipAndPlayBack",
  "TPbNvrImgFlipAndPlayBack",
  "",
  sizeof(TPbNvrImgFlipAndPlayBack),
  4,
  tpb_nvr_img_flip_and_play_back__field_descriptors,
  tpb_nvr_img_flip_and_play_back__field_indices_by_name,
  1,  tpb_nvr_img_flip_and_play_back__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_img_flip_and_play_back__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_infrared_far_near_level__field_descriptors[4] =
{
  {
    "far_and_near_infred_ctrl",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrInfraredFarNearLevel, has_far_and_near_infred_ctrl),
    offsetof(TPbNvrInfraredFarNearLevel, far_and_near_infred_ctrl),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "infared_level",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrInfraredFarNearLevel, has_infared_level),
    offsetof(TPbNvrInfraredFarNearLevel, infared_level),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "far_",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrInfraredFarNearLevel, has_far_),
    offsetof(TPbNvrInfraredFarNearLevel, far_),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "near_",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrInfraredFarNearLevel, has_near_),
    offsetof(TPbNvrInfraredFarNearLevel, near_),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_infrared_far_near_level__field_indices_by_name[] = {
  2,   /* field[2] = far_ */
  0,   /* field[0] = far_and_near_infred_ctrl */
  1,   /* field[1] = infared_level */
  3,   /* field[3] = near_ */
};
static const ProtobufCIntRange tpb_nvr_infrared_far_near_level__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 4 }
};
const ProtobufCMessageDescriptor tpb_nvr_infrared_far_near_level__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrInfraredFarNearLevel",
  "TPbNvrInfraredFarNearLevel",
  "TPbNvrInfraredFarNearLevel",
  "",
  sizeof(TPbNvrInfraredFarNearLevel),
  4,
  tpb_nvr_infrared_far_near_level__field_descriptors,
  tpb_nvr_infrared_far_near_level__field_indices_by_name,
  1,  tpb_nvr_infrared_far_near_level__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_infrared_far_near_level__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_infrared_cfg__field_descriptors[4] =
{
  {
    "set_infared_mode",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrInfraredCfg, has_set_infared_mode),
    offsetof(TPbNvrInfraredCfg, set_infared_mode),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "infared_mode",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrInfraredCfg, has_infared_mode),
    offsetof(TPbNvrInfraredCfg, infared_mode),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "set_infared_level",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrInfraredCfg, has_set_infared_level),
    offsetof(TPbNvrInfraredCfg, set_infared_level),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "infared_level",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrInfraredCfg, infared_level),
    &tpb_nvr_infrared_far_near_level__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_infrared_cfg__field_indices_by_name[] = {
  3,   /* field[3] = infared_level */
  1,   /* field[1] = infared_mode */
  2,   /* field[2] = set_infared_level */
  0,   /* field[0] = set_infared_mode */
};
static const ProtobufCIntRange tpb_nvr_infrared_cfg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 4 }
};
const ProtobufCMessageDescriptor tpb_nvr_infrared_cfg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrInfraredCfg",
  "TPbNvrInfraredCfg",
  "TPbNvrInfraredCfg",
  "",
  sizeof(TPbNvrInfraredCfg),
  4,
  tpb_nvr_infrared_cfg__field_descriptors,
  tpb_nvr_infrared_cfg__field_indices_by_name,
  1,  tpb_nvr_infrared_cfg__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_infrared_cfg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_laser_cfg__field_descriptors[10] =
{
  {
    "set_switch",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLaserCfg, has_set_switch),
    offsetof(TPbNvrLaserCfg, set_switch),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "switch",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLaserCfg, has_switch_),
    offsetof(TPbNvrLaserCfg, switch_),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "set_intensity",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLaserCfg, has_set_intensity),
    offsetof(TPbNvrLaserCfg, set_intensity),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "intensity",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLaserCfg, has_intensity),
    offsetof(TPbNvrLaserCfg, intensity),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "set_mode",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLaserCfg, has_set_mode),
    offsetof(TPbNvrLaserCfg, set_mode),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "mode",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLaserCfg, has_mode),
    offsetof(TPbNvrLaserCfg, mode),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "set_centrad_mode",
    7,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLaserCfg, has_set_centrad_mode),
    offsetof(TPbNvrLaserCfg, set_centrad_mode),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "centrad_mode",
    8,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLaserCfg, has_centrad_mode),
    offsetof(TPbNvrLaserCfg, centrad_mode),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "set_centrad_mode_speed",
    9,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLaserCfg, has_set_centrad_mode_speed),
    offsetof(TPbNvrLaserCfg, set_centrad_mode_speed),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "centrad_mode_speed",
    10,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLaserCfg, has_centrad_mode_speed),
    offsetof(TPbNvrLaserCfg, centrad_mode_speed),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_laser_cfg__field_indices_by_name[] = {
  7,   /* field[7] = centrad_mode */
  9,   /* field[9] = centrad_mode_speed */
  3,   /* field[3] = intensity */
  5,   /* field[5] = mode */
  6,   /* field[6] = set_centrad_mode */
  8,   /* field[8] = set_centrad_mode_speed */
  2,   /* field[2] = set_intensity */
  4,   /* field[4] = set_mode */
  0,   /* field[0] = set_switch */
  1,   /* field[1] = switch */
};
static const ProtobufCIntRange tpb_nvr_laser_cfg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 10 }
};
const ProtobufCMessageDescriptor tpb_nvr_laser_cfg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrLaserCfg",
  "TPbNvrLaserCfg",
  "TPbNvrLaserCfg",
  "",
  sizeof(TPbNvrLaserCfg),
  10,
  tpb_nvr_laser_cfg__field_descriptors,
  tpb_nvr_laser_cfg__field_indices_by_name,
  1,  tpb_nvr_laser_cfg__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_laser_cfg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_isp_vid_ldc_param__field_descriptors[4] =
{
  {
    "switch",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspVidLdcParam, has_switch_),
    offsetof(TPbNvrIspVidLdcParam, switch_),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ratio1",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspVidLdcParam, has_ratio1),
    offsetof(TPbNvrIspVidLdcParam, ratio1),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ratio2",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspVidLdcParam, has_ratio2),
    offsetof(TPbNvrIspVidLdcParam, ratio2),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "sharp_level",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspVidLdcParam, has_sharp_level),
    offsetof(TPbNvrIspVidLdcParam, sharp_level),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_isp_vid_ldc_param__field_indices_by_name[] = {
  1,   /* field[1] = ratio1 */
  2,   /* field[2] = ratio2 */
  3,   /* field[3] = sharp_level */
  0,   /* field[0] = switch */
};
static const ProtobufCIntRange tpb_nvr_isp_vid_ldc_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 4 }
};
const ProtobufCMessageDescriptor tpb_nvr_isp_vid_ldc_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrIspVidLdcParam",
  "TPbNvrIspVidLdcParam",
  "TPbNvrIspVidLdcParam",
  "",
  sizeof(TPbNvrIspVidLdcParam),
  4,
  tpb_nvr_isp_vid_ldc_param__field_descriptors,
  tpb_nvr_isp_vid_ldc_param__field_indices_by_name,
  1,  tpb_nvr_isp_vid_ldc_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_isp_vid_ldc_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_isp_ther_adjust_cfg__field_descriptors[3] =
{
  {
    "brightness",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspTherAdjustCfg, has_brightness),
    offsetof(TPbNvrIspTherAdjustCfg, brightness),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "contrast",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspTherAdjustCfg, has_contrast),
    offsetof(TPbNvrIspTherAdjustCfg, contrast),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "colorize",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspTherAdjustCfg, has_colorize),
    offsetof(TPbNvrIspTherAdjustCfg, colorize),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_isp_ther_adjust_cfg__field_indices_by_name[] = {
  0,   /* field[0] = brightness */
  2,   /* field[2] = colorize */
  1,   /* field[1] = contrast */
};
static const ProtobufCIntRange tpb_nvr_isp_ther_adjust_cfg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 3 }
};
const ProtobufCMessageDescriptor tpb_nvr_isp_ther_adjust_cfg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrIspTherAdjustCfg",
  "TPbNvrIspTherAdjustCfg",
  "TPbNvrIspTherAdjustCfg",
  "",
  sizeof(TPbNvrIspTherAdjustCfg),
  3,
  tpb_nvr_isp_ther_adjust_cfg__field_descriptors,
  tpb_nvr_isp_ther_adjust_cfg__field_indices_by_name,
  1,  tpb_nvr_isp_ther_adjust_cfg__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_isp_ther_adjust_cfg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_isp_ther_enhance_cfg__field_descriptors[3] =
{
  {
    "ide_enhance",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspTherEnhanceCfg, has_ide_enhance),
    offsetof(TPbNvrIspTherEnhanceCfg, ide_enhance),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "enhance_grade",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspTherEnhanceCfg, has_enhance_grade),
    offsetof(TPbNvrIspTherEnhanceCfg, enhance_grade),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ide_filter",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspTherEnhanceCfg, has_ide_filter),
    offsetof(TPbNvrIspTherEnhanceCfg, ide_filter),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_isp_ther_enhance_cfg__field_indices_by_name[] = {
  1,   /* field[1] = enhance_grade */
  0,   /* field[0] = ide_enhance */
  2,   /* field[2] = ide_filter */
};
static const ProtobufCIntRange tpb_nvr_isp_ther_enhance_cfg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 3 }
};
const ProtobufCMessageDescriptor tpb_nvr_isp_ther_enhance_cfg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrIspTherEnhanceCfg",
  "TPbNvrIspTherEnhanceCfg",
  "TPbNvrIspTherEnhanceCfg",
  "",
  sizeof(TPbNvrIspTherEnhanceCfg),
  3,
  tpb_nvr_isp_ther_enhance_cfg__field_descriptors,
  tpb_nvr_isp_ther_enhance_cfg__field_indices_by_name,
  1,  tpb_nvr_isp_ther_enhance_cfg__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_isp_ther_enhance_cfg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_lcam_isp_param__field_descriptors[21] =
{
  {
    "image",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbLcamIspParam, image),
    &tpb_nvr_isp_image__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "white_balance",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbLcamIspParam, white_balance),
    &tpb_nvr_isp_white_balance__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "gain",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbLcamIspParam, gain),
    &tpb_nvr_isp_gain__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "shutter",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbLcamIspParam, shutter),
    &tpb_nvr_isp_shutter__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "pro_exposure",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbLcamIspParam, pro_exposure),
    &tpb_nvr_isp_pro_exposure__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "frequency_mode",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbLcamIspParam, has_frequency_mode),
    offsetof(TPbLcamIspParam, frequency_mode),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "day_night_switch",
    7,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbLcamIspParam, day_night_switch),
    &tpb_nvr_isp_day_night_switch__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "back_light",
    8,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbLcamIspParam, back_light),
    &tpb_nvr_isp_back_light__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "combin_hdr",
    9,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbLcamIspParam, combin_hdr),
    &tpb_nvr_isp_combin_hdr__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "denoise",
    10,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbLcamIspParam, denoise),
    &tpb_nvr_isp_denoise__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "double_lens_mode",
    11,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbLcamIspParam, has_double_lens_mode),
    offsetof(TPbLcamIspParam, double_lens_mode),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "image_enhance",
    12,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbLcamIspParam, image_enhance),
    &tpb_nvr_isp_image_enhance__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "stablizer",
    13,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbLcamIspParam, stablizer),
    &tpb_nvr_isp_stablizer__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "exp_all_auto",
    14,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbLcamIspParam, has_exp_all_auto),
    offsetof(TPbLcamIspParam, exp_all_auto),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "action",
    15,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbLcamIspParam, action),
    &tpb_nvr_isp_action__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "flip_and_back",
    16,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbLcamIspParam, flip_and_back),
    &tpb_nvr_img_flip_and_play_back__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "infrared_cfg",
    17,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbLcamIspParam, infrared_cfg),
    &tpb_nvr_infrared_cfg__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "laser_cfg",
    18,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbLcamIspParam, laser_cfg),
    &tpb_nvr_laser_cfg__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "vid_ldc",
    19,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbLcamIspParam, vid_ldc),
    &tpb_nvr_isp_vid_ldc_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ther_adjust",
    20,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbLcamIspParam, ther_adjust),
    &tpb_nvr_isp_ther_adjust_cfg__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ther_enhance",
    21,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbLcamIspParam, ther_enhance),
    &tpb_nvr_isp_ther_enhance_cfg__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_lcam_isp_param__field_indices_by_name[] = {
  14,   /* field[14] = action */
  7,   /* field[7] = back_light */
  8,   /* field[8] = combin_hdr */
  6,   /* field[6] = day_night_switch */
  9,   /* field[9] = denoise */
  10,   /* field[10] = double_lens_mode */
  13,   /* field[13] = exp_all_auto */
  15,   /* field[15] = flip_and_back */
  5,   /* field[5] = frequency_mode */
  2,   /* field[2] = gain */
  0,   /* field[0] = image */
  11,   /* field[11] = image_enhance */
  16,   /* field[16] = infrared_cfg */
  17,   /* field[17] = laser_cfg */
  4,   /* field[4] = pro_exposure */
  3,   /* field[3] = shutter */
  12,   /* field[12] = stablizer */
  19,   /* field[19] = ther_adjust */
  20,   /* field[20] = ther_enhance */
  18,   /* field[18] = vid_ldc */
  1,   /* field[1] = white_balance */
};
static const ProtobufCIntRange tpb_lcam_isp_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 21 }
};
const ProtobufCMessageDescriptor tpb_lcam_isp_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbLcamIspParam",
  "TPbLcamIspParam",
  "TPbLcamIspParam",
  "",
  sizeof(TPbLcamIspParam),
  21,
  tpb_lcam_isp_param__field_descriptors,
  tpb_lcam_isp_param__field_indices_by_name,
  1,  tpb_lcam_isp_param__number_ranges,
  (ProtobufCMessageInit) tpb_lcam_isp_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_isp_dynamic_mode_param__field_descriptors[2] =
{
  {
    "scene_mode",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspDynamicModeParam, has_scene_mode),
    offsetof(TPbNvrIspDynamicModeParam, scene_mode),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "user_index",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_INT32,
    offsetof(TPbNvrIspDynamicModeParam, has_user_index),
    offsetof(TPbNvrIspDynamicModeParam, user_index),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_isp_dynamic_mode_param__field_indices_by_name[] = {
  0,   /* field[0] = scene_mode */
  1,   /* field[1] = user_index */
};
static const ProtobufCIntRange tpb_nvr_isp_dynamic_mode_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_isp_dynamic_mode_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrIspDynamicModeParam",
  "TPbNvrIspDynamicModeParam",
  "TPbNvrIspDynamicModeParam",
  "",
  sizeof(TPbNvrIspDynamicModeParam),
  2,
  tpb_nvr_isp_dynamic_mode_param__field_descriptors,
  tpb_nvr_isp_dynamic_mode_param__field_indices_by_name,
  1,  tpb_nvr_isp_dynamic_mode_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_isp_dynamic_mode_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_isp_dy_scene_start_end_time__field_descriptors[4] =
{
  {
    "enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspDySceneStartEndTime, has_enable),
    offsetof(TPbNvrIspDySceneStartEndTime, enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "start_time",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_INT32,
    offsetof(TPbNvrIspDySceneStartEndTime, has_start_time),
    offsetof(TPbNvrIspDySceneStartEndTime, start_time),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "end_time",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_INT32,
    offsetof(TPbNvrIspDySceneStartEndTime, has_end_time),
    offsetof(TPbNvrIspDySceneStartEndTime, end_time),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "dynamic_mode",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrIspDySceneStartEndTime, dynamic_mode),
    &tpb_nvr_isp_dynamic_mode_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_isp_dy_scene_start_end_time__field_indices_by_name[] = {
  3,   /* field[3] = dynamic_mode */
  0,   /* field[0] = enable */
  2,   /* field[2] = end_time */
  1,   /* field[1] = start_time */
};
static const ProtobufCIntRange tpb_nvr_isp_dy_scene_start_end_time__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 4 }
};
const ProtobufCMessageDescriptor tpb_nvr_isp_dy_scene_start_end_time__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrIspDySceneStartEndTime",
  "TPbNvrIspDySceneStartEndTime",
  "TPbNvrIspDySceneStartEndTime",
  "",
  sizeof(TPbNvrIspDySceneStartEndTime),
  4,
  tpb_nvr_isp_dy_scene_start_end_time__field_descriptors,
  tpb_nvr_isp_dy_scene_start_end_time__field_indices_by_name,
  1,  tpb_nvr_isp_dy_scene_start_end_time__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_isp_dy_scene_start_end_time__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_isp_scene_scale_param__field_descriptors[1] =
{
  {
    "time_scale",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrIspSceneScaleParam, n_time_scale),
    offsetof(TPbNvrIspSceneScaleParam, time_scale),
    &tpb_nvr_isp_dy_scene_start_end_time__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_isp_scene_scale_param__field_indices_by_name[] = {
  0,   /* field[0] = time_scale */
};
static const ProtobufCIntRange tpb_nvr_isp_scene_scale_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor tpb_nvr_isp_scene_scale_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrIspSceneScaleParam",
  "TPbNvrIspSceneScaleParam",
  "TPbNvrIspSceneScaleParam",
  "",
  sizeof(TPbNvrIspSceneScaleParam),
  1,
  tpb_nvr_isp_scene_scale_param__field_descriptors,
  tpb_nvr_isp_scene_scale_param__field_indices_by_name,
  1,  tpb_nvr_isp_scene_scale_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_isp_scene_scale_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_isp_scene_timing_param__field_descriptors[1] =
{
  {
    "week_scale",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrIspSceneTimingParam, n_week_scale),
    offsetof(TPbNvrIspSceneTimingParam, week_scale),
    &tpb_nvr_isp_scene_scale_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_isp_scene_timing_param__field_indices_by_name[] = {
  0,   /* field[0] = week_scale */
};
static const ProtobufCIntRange tpb_nvr_isp_scene_timing_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor tpb_nvr_isp_scene_timing_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrIspSceneTimingParam",
  "TPbNvrIspSceneTimingParam",
  "TPbNvrIspSceneTimingParam",
  "",
  sizeof(TPbNvrIspSceneTimingParam),
  1,
  tpb_nvr_isp_scene_timing_param__field_descriptors,
  tpb_nvr_isp_scene_timing_param__field_indices_by_name,
  1,  tpb_nvr_isp_scene_timing_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_isp_scene_timing_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_isp_scene_dynamic_param__field_descriptors[3] =
{
  {
    "scene_timing",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrIspSceneDynamicParam, scene_timing),
    &tpb_nvr_isp_scene_timing_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "dynamic_scene_mode",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIspSceneDynamicParam, has_dynamic_scene_mode),
    offsetof(TPbNvrIspSceneDynamicParam, dynamic_scene_mode),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "daynight_scenemode_param",
    3,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrIspSceneDynamicParam, n_daynight_scenemode_param),
    offsetof(TPbNvrIspSceneDynamicParam, daynight_scenemode_param),
    &tpb_nvr_isp_dynamic_mode_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_isp_scene_dynamic_param__field_indices_by_name[] = {
  2,   /* field[2] = daynight_scenemode_param */
  1,   /* field[1] = dynamic_scene_mode */
  0,   /* field[0] = scene_timing */
};
static const ProtobufCIntRange tpb_nvr_isp_scene_dynamic_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 3 }
};
const ProtobufCMessageDescriptor tpb_nvr_isp_scene_dynamic_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrIspSceneDynamicParam",
  "TPbNvrIspSceneDynamicParam",
  "TPbNvrIspSceneDynamicParam",
  "",
  sizeof(TPbNvrIspSceneDynamicParam),
  3,
  tpb_nvr_isp_scene_dynamic_param__field_descriptors,
  tpb_nvr_isp_scene_dynamic_param__field_indices_by_name,
  1,  tpb_nvr_isp_scene_dynamic_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_isp_scene_dynamic_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_user_scene_name_num__field_descriptors[1] =
{
  {
    "user_scene_name",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BYTES,
    offsetof(TPbUserSceneNameNum, has_user_scene_name),
    offsetof(TPbUserSceneNameNum, user_scene_name),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_user_scene_name_num__field_indices_by_name[] = {
  0,   /* field[0] = user_scene_name */
};
static const ProtobufCIntRange tpb_user_scene_name_num__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor tpb_user_scene_name_num__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbUserSceneNameNum",
  "TPbUserSceneNameNum",
  "TPbUserSceneNameNum",
  "",
  sizeof(TPbUserSceneNameNum),
  1,
  tpb_user_scene_name_num__field_descriptors,
  tpb_user_scene_name_num__field_indices_by_name,
  1,  tpb_user_scene_name_num__number_ranges,
  (ProtobufCMessageInit) tpb_user_scene_name_num__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_lcam_isp_scene_param__field_descriptors[9] =
{
  {
    "dy_scene_param",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbLcamIspSceneParam, dy_scene_param),
    &tpb_nvr_isp_scene_dynamic_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "lcam_isp_param",
    2,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbLcamIspSceneParam, n_lcam_isp_param),
    offsetof(TPbLcamIspSceneParam, lcam_isp_param),
    &tpb_lcam_isp_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "cur_scene_mode",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbLcamIspSceneParam, has_cur_scene_mode),
    offsetof(TPbLcamIspSceneParam, cur_scene_mode),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "isp_param_backup",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbLcamIspSceneParam, isp_param_backup),
    &tpb_lcam_isp_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "user_index",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_INT32,
    offsetof(TPbLcamIspSceneParam, has_user_index),
    offsetof(TPbLcamIspSceneParam, user_index),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "cur_isp_param_num",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbLcamIspSceneParam, has_cur_isp_param_num),
    offsetof(TPbLcamIspSceneParam, cur_isp_param_num),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "user_scene_len",
    7,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_INT32,
    offsetof(TPbLcamIspSceneParam, n_user_scene_len),
    offsetof(TPbLcamIspSceneParam, user_scene_len),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "isp_user_diy_param",
    8,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_INT32,
    offsetof(TPbLcamIspSceneParam, n_isp_user_diy_param),
    offsetof(TPbLcamIspSceneParam, isp_user_diy_param),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "scene_name_num",
    9,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_STRING,
    offsetof(TPbLcamIspSceneParam, n_scene_name_num),
    offsetof(TPbLcamIspSceneParam, scene_name_num),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_lcam_isp_scene_param__field_indices_by_name[] = {
  5,   /* field[5] = cur_isp_param_num */
  2,   /* field[2] = cur_scene_mode */
  0,   /* field[0] = dy_scene_param */
  3,   /* field[3] = isp_param_backup */
  7,   /* field[7] = isp_user_diy_param */
  1,   /* field[1] = lcam_isp_param */
  8,   /* field[8] = scene_name_num */
  4,   /* field[4] = user_index */
  6,   /* field[6] = user_scene_len */
};
static const ProtobufCIntRange tpb_lcam_isp_scene_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 9 }
};
const ProtobufCMessageDescriptor tpb_lcam_isp_scene_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbLcamIspSceneParam",
  "TPbLcamIspSceneParam",
  "TPbLcamIspSceneParam",
  "",
  sizeof(TPbLcamIspSceneParam),
  9,
  tpb_lcam_isp_scene_param__field_descriptors,
  tpb_lcam_isp_scene_param__field_indices_by_name,
  1,  tpb_lcam_isp_scene_param__number_ranges,
  (ProtobufCMessageInit) tpb_lcam_isp_scene_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_lcam_isp_param_all__field_descriptors[1] =
{
  {
    "lcam_isp_param",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbLcamIspParamAll, n_lcam_isp_param),
    offsetof(TPbLcamIspParamAll, lcam_isp_param),
    &tpb_lcam_isp_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_lcam_isp_param_all__field_indices_by_name[] = {
  0,   /* field[0] = lcam_isp_param */
};
static const ProtobufCIntRange tpb_lcam_isp_param_all__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor tpb_lcam_isp_param_all__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbLcamIspParamAll",
  "TPbLcamIspParamAll",
  "TPbLcamIspParamAll",
  "",
  sizeof(TPbLcamIspParamAll),
  1,
  tpb_lcam_isp_param_all__field_descriptors,
  tpb_lcam_isp_param_all__field_indices_by_name,
  1,  tpb_lcam_isp_param_all__number_ranges,
  (ProtobufCMessageInit) tpb_lcam_isp_param_all__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_lcam_isp_scene_param_all__field_descriptors[1] =
{
  {
    "lcam_isp_scene_param",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbLcamIspSceneParamAll, n_lcam_isp_scene_param),
    offsetof(TPbLcamIspSceneParamAll, lcam_isp_scene_param),
    &tpb_lcam_isp_scene_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_lcam_isp_scene_param_all__field_indices_by_name[] = {
  0,   /* field[0] = lcam_isp_scene_param */
};
static const ProtobufCIntRange tpb_lcam_isp_scene_param_all__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor tpb_lcam_isp_scene_param_all__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbLcamIspSceneParamAll",
  "TPbLcamIspSceneParamAll",
  "TPbLcamIspSceneParamAll",
  "",
  sizeof(TPbLcamIspSceneParamAll),
  1,
  tpb_lcam_isp_scene_param_all__field_descriptors,
  tpb_lcam_isp_scene_param_all__field_indices_by_name,
  1,  tpb_lcam_isp_scene_param_all__number_ranges,
  (ProtobufCMessageInit) tpb_lcam_isp_scene_param_all__init,
  NULL,NULL,NULL    /* reserved[123] */
};
