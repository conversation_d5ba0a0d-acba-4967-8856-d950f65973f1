

TOP := ..

COMM_DIR := ../../common/

SRC_DIR := $(TOP)/source
CURDIR := ./

## Name and type of the target for this Makefile

SO_TARGET	      := nvrsnmp


## Define debugging symbols
DEBUG = 0
LINUX_COMPILER = _HIS3519AV100_
PWLIB_SUPPORT = 0

## Object files that compose the target(s)

OBJS := $(SRC_DIR)/commonMIB \
        $(SRC_DIR)/nvrsnmp \
        $(SRC_DIR)/nvrsnmp_in \
        $(SRC_DIR)/nvrMIB
		
## Libraries to include in shared object 
        
#LIBS :=  

## Add driver-specific include directory to the search path

INC_PATH += $(CURDIR)/../include \
		$(CURDIR)/../../common \
		$(CURDIR)/../../../30-cbb/sqlite/sqlite3/include\
		$(CURDIR)/../../../40-service/nvrnetwork/include\
		$(CURDIR)/../../../10-common/include/cbb/sqilte\
		$(CURDIR)/../../../10-common/include/cbb/debuglog\
		$(CURDIR)/../../../10-common/include/cbb/protobuf\
		$(CURDIR)/../../../10-common/include/cbb/osp\
		$(CURDIR)/../../../10-common/include/cbb/charconversion\
		$(CURDIR)/../../../10-common/include/system\
		$(CURDIR)/../../../10-common/include/hal\
		$(CURDIR)/../../../10-common/include/service\
		$(CURDIR)/../../../10-common/include/app\
		$(CURDIR)/../../../10-common/include/cbb/snmp\
		$(CURDIR)/../../../10-common/include/cbb/snmp/net-snmp\
		$(CURDIR)/../../../10-common/include/cbb/snmp/ucd-snmp\
		$(CURDIR)/../../../10-common/include/cbb/snmp/linux_conf
		

CFLAGS += -D_HIS3519AV100_


ifeq ($(PWLIB_SUPPORT),1)
   INC_PATH += $(PWLIBDIR)/include/ptlib/unix $(PWLIBDIR)/include
endif


INSTALL_LIB_PATH = ../../../10-common/lib/release/his3519av100/snmp
LDFLAGS += -L$(INSTALL_LIB_PATH)
LDFLAGS += -lsnmp_agent
include $(COMM_DIR)/common.mk


