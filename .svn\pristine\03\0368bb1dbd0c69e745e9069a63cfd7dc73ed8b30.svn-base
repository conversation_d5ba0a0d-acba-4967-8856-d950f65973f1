

TOP := ..

COMM_DIR := ../../common/

SRC_DIR := $(TOP)/source
CURDIR := ./

## Name and type of the target for this Makefile

APP_TARGET	      := nvrsrv


## Define debugging symbols
DEBUG = 0
LINUX_COMPILER = _HIS3516DV300_
PWLIB_SUPPORT = 0
CFLAGS += -funwind-tables
CFLAGS += -D__MRTC__
CFLAGS += -D_HIS3516DV300_
## Object files that compose the target(s)



OBJS := $(SRC_DIR)/nvrsrv\
## Libraries to include in shared object file
LIB_PATH += $(TOP)/../../10-common/lib/release/his3516dv300
LIB_PATH += $(TOP)/../../10-common/lib/release/his3516dv300/audlib
LIB_PATH += $(TOP)/../../10-common/lib/release/his3516dv300/webrtc
LIB_PATH += $(TOP)/../../10-common/lib/release/his3516dv300/wifim

##todo: aec_3A_mulresample_armhi3516a_linux should be replaced by armhi3519 version

LIBS +=	nvrcfg nvrlog nvrcap nvrusrmgr nvrnetwork nvrsys nvrproto protobufc sqlite3wrapper malloc debuglog netcbb mbnet wifi ddnsc upnpc drv pthread nvrgeo \
	sysdbg goaheadhelper appbase charconv appclt nvrpui nvrvtductrl nvrmpu nvrprobe nvrpcap httpclient mxml ghttp go osp mrtc mediaswitch netpacket kdmtsps kdvencrypt stdc++ nvrqueue mediactrl ispctrl nvralarm nvrdev nvrupgrade nvrcrc ftpc\
	dmsrv nvrrec rpdata rp kdmfileinterface nvrguard nvrsmtp smtp airp gnss dl ais algctrl SDEF pubsecstack curl nvrcoi nvrunifiedlog\
	ive\
	audcodec_armhi3519_linux\
	smartcodec_armhi3516c_linux\
	basicintelligent_his3519\
	md5lib_hisi3519_release\
	aec_mulresample_armhi3519_linux\
	audcodec_kdm_armhi3519_linux\
	audproc_armhi3519_linux\
	g711_armhi3519_linux\
	g719_armhi3519_linux\
	g722_armhi3519_linux\
	g726_armhi3519_linux\
	g728_armhi3519_linux\
	g729_armhi3519_linux\
	mp2_armhi3519_linux\
	mp3dec_armhi3519_linux\
	mp3enc_armhi3519_linux\
	opus_armhi3519_linux\
	stdg722_armhi3519_linux\
	g7221c_armhi3519_linux\
	extexp_armhi3519_linux\
	videomanage_armhi3519_linux\
	resamplev2_armhi3519_linux\
	adpcm_armhi3519_linux\
	aaclcdec_armhi3519_linux\
	aaclcenc_armhi3519_linux\
	aaclddec_armhi3519_linux\
	aacldenc_armhi3519_linux\
	amr_nb_armhi3519_linux\
	spe_armhi3519_linux\
	asd_armhi3519_linux\
	mixer_armhi3519_linux\
	m \
	pcap\
	kdmssl\
	kdmcrypto\
	cjson\
	udm_dm\
	uuid\
	blkid\
	nvrproduct\
	kdmnatagent\
	rpdownload\
	nvrdynamicplugin\
	nvrstitch\
	lwshelper\
	websockets\
	nvrmd5\
#    heapcheck\
#    tcmalloc
ifneq ($(SLIBS),) 
LDFLAGS += -Wl,-static
LDFLAGS += $(foreach lib,$(SLIBS),-l$(lib))
endif




## Add driver-specific include directory to the search path

INC_PATH += $(CURDIR)/../include \
		$(CURDIR)/../../common \
		$(CURDIR)/../../nvrcfg/include \
		$(CURDIR)/../../nvrpui/include \
		$(CURDIR)/../../nvrvtductrl/include \
		$(CURDIR)/../../nvrusrmgr/include \
		$(CURDIR)/../../nvrcap/include \
		$(CURDIR)/../../nvrnetwork/include \
		$(CURDIR)/../../nvrsys/include \
		$(CURDIR)/../../nvrdev/include \
		$(CURDIR)/../../nvralarm/include \
		$(CURDIR)/../../nvrlog/include \
		$(CURDIR)/../../nvrguard/include \
		$(CURDIR)/../../nvrsmtp/include \
		$(CURDIR)/../../nvrmpu/include \
		$(CURDIR)/../../nvrpcap/include \
		$(CURDIR)/../../airp/include \
		$(CURDIR)/../../nvrextdevmgr/include \
		$(CURDIR)/../../../10-common/include/service \
		$(CURDIR)/../../../10-common/include/cbb/debuglog\
		$(CURDIR)/../../../10-common/include/cbb/mbnet\
		$(CURDIR)/../../../10-common/include/system\
		$(CURDIR)/../../../10-common/include/hal/sysdbg\
		$(CURDIR)/../../../10-common/include/cbb/protobuf \
		$(CURDIR)/../../../10-common/include/cbb/osp \
		$(CURDIR)/../../../10-common/include/cbb/sqilte \
		$(CURDIR)/../../../10-common/include/cbb/appclt\
		$(CURDIR)/../../../10-common/include/cbb/mediaswitch\
		$(CURDIR)/../../../10-common/include/cbb/kshield\
		$(CURDIR)/../../../10-common/include/app\
		$(CURDIR)/../../../10-common/include/hal/drvlib\
		$(CURDIR)/../../../10-common/include/hal/drvlib/system

#CFLAGS += -fno-builtin-malloc -fno-builtin-calloc -fno-builtin-realloc -fno-builtin-free

ifeq ($(PWLIB_SUPPORT),1)
   INC_PATH += $(PWLIBDIR)/include/ptlib/unix $(PWLIBDIR)/include
endif


INSTALL_NO_UPX_APP_PATH := ../../../10-common/version/release/his3516dv300/bin_noupx
INSTALL_APP_PATH := ../../../10-common/version/release/his3516dv300/bin
LDFLAGS += -L$(INSTALL_LIB_PATH)
include $(COMM_DIR)/common.mk


