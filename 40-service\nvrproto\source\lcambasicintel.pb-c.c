/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: lcambasicintel.proto */

/* Do not generate deprecated warnings for self */
#ifndef PROTOBUF_C__NO_DEPRECATED
#define PROTOBUF_C__NO_DEPRECATED
#endif

#include "lcambasicintel.pb-c.h"
void   tpb_nvr_event_contact__init
                     (TPbNvrEventContact         *message)
{
  static TPbNvrEventContact init_value = TPB_NVR_EVENT_CONTACT__INIT;
  *message = init_value;
}
size_t tpb_nvr_event_contact__get_packed_size
                     (const TPbNvrEventContact *message)
{
  assert(message->base.descriptor == &tpb_nvr_event_contact__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_event_contact__pack
                     (const TPbNvrEventContact *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_event_contact__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_event_contact__pack_to_buffer
                     (const TPbNvrEventContact *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_event_contact__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrEventContact *
       tpb_nvr_event_contact__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrEventContact *)
     protobuf_c_message_unpack (&tpb_nvr_event_contact__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_event_contact__free_unpacked
                     (TPbNvrEventContact *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_event_contact__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_rect_region__init
                     (TPbNvrRectRegion         *message)
{
  static TPbNvrRectRegion init_value = TPB_NVR_RECT_REGION__INIT;
  *message = init_value;
}
size_t tpb_nvr_rect_region__get_packed_size
                     (const TPbNvrRectRegion *message)
{
  assert(message->base.descriptor == &tpb_nvr_rect_region__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_rect_region__pack
                     (const TPbNvrRectRegion *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_rect_region__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_rect_region__pack_to_buffer
                     (const TPbNvrRectRegion *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_rect_region__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrRectRegion *
       tpb_nvr_rect_region__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrRectRegion *)
     protobuf_c_message_unpack (&tpb_nvr_rect_region__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_rect_region__free_unpacked
                     (TPbNvrRectRegion *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_rect_region__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_intel_point__init
                     (TPbNvrIntelPoint         *message)
{
  static TPbNvrIntelPoint init_value = TPB_NVR_INTEL_POINT__INIT;
  *message = init_value;
}
size_t tpb_nvr_intel_point__get_packed_size
                     (const TPbNvrIntelPoint *message)
{
  assert(message->base.descriptor == &tpb_nvr_intel_point__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_intel_point__pack
                     (const TPbNvrIntelPoint *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_intel_point__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_intel_point__pack_to_buffer
                     (const TPbNvrIntelPoint *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_intel_point__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrIntelPoint *
       tpb_nvr_intel_point__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrIntelPoint *)
     protobuf_c_message_unpack (&tpb_nvr_intel_point__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_intel_point__free_unpacked
                     (TPbNvrIntelPoint *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_intel_point__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_intel_cordon_info__init
                     (TpbNvrIntelCordonInfo         *message)
{
  static TpbNvrIntelCordonInfo init_value = TPB_NVR_INTEL_CORDON_INFO__INIT;
  *message = init_value;
}
size_t tpb_nvr_intel_cordon_info__get_packed_size
                     (const TpbNvrIntelCordonInfo *message)
{
  assert(message->base.descriptor == &tpb_nvr_intel_cordon_info__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_intel_cordon_info__pack
                     (const TpbNvrIntelCordonInfo *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_intel_cordon_info__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_intel_cordon_info__pack_to_buffer
                     (const TpbNvrIntelCordonInfo *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_intel_cordon_info__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TpbNvrIntelCordonInfo *
       tpb_nvr_intel_cordon_info__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TpbNvrIntelCordonInfo *)
     protobuf_c_message_unpack (&tpb_nvr_intel_cordon_info__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_intel_cordon_info__free_unpacked
                     (TpbNvrIntelCordonInfo *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_intel_cordon_info__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_lcam_intel_cordon_param__init
                     (TPbNvrLcamIntelCordonParam         *message)
{
  static TPbNvrLcamIntelCordonParam init_value = TPB_NVR_LCAM_INTEL_CORDON_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_lcam_intel_cordon_param__get_packed_size
                     (const TPbNvrLcamIntelCordonParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_intel_cordon_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_lcam_intel_cordon_param__pack
                     (const TPbNvrLcamIntelCordonParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_intel_cordon_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_lcam_intel_cordon_param__pack_to_buffer
                     (const TPbNvrLcamIntelCordonParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_intel_cordon_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrLcamIntelCordonParam *
       tpb_nvr_lcam_intel_cordon_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrLcamIntelCordonParam *)
     protobuf_c_message_unpack (&tpb_nvr_lcam_intel_cordon_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_lcam_intel_cordon_param__free_unpacked
                     (TPbNvrLcamIntelCordonParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_intel_cordon_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_ploygon__init
                     (TpbNvrPloygon         *message)
{
  static TpbNvrPloygon init_value = TPB_NVR_PLOYGON__INIT;
  *message = init_value;
}
size_t tpb_nvr_ploygon__get_packed_size
                     (const TpbNvrPloygon *message)
{
  assert(message->base.descriptor == &tpb_nvr_ploygon__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_ploygon__pack
                     (const TpbNvrPloygon *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_ploygon__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_ploygon__pack_to_buffer
                     (const TpbNvrPloygon *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_ploygon__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TpbNvrPloygon *
       tpb_nvr_ploygon__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TpbNvrPloygon *)
     protobuf_c_message_unpack (&tpb_nvr_ploygon__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_ploygon__free_unpacked
                     (TpbNvrPloygon *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_ploygon__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_intel_smart_region_info__init
                     (TpbNvrIntelSmartRegionInfo         *message)
{
  static TpbNvrIntelSmartRegionInfo init_value = TPB_NVR_INTEL_SMART_REGION_INFO__INIT;
  *message = init_value;
}
size_t tpb_nvr_intel_smart_region_info__get_packed_size
                     (const TpbNvrIntelSmartRegionInfo *message)
{
  assert(message->base.descriptor == &tpb_nvr_intel_smart_region_info__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_intel_smart_region_info__pack
                     (const TpbNvrIntelSmartRegionInfo *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_intel_smart_region_info__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_intel_smart_region_info__pack_to_buffer
                     (const TpbNvrIntelSmartRegionInfo *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_intel_smart_region_info__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TpbNvrIntelSmartRegionInfo *
       tpb_nvr_intel_smart_region_info__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TpbNvrIntelSmartRegionInfo *)
     protobuf_c_message_unpack (&tpb_nvr_intel_smart_region_info__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_intel_smart_region_info__free_unpacked
                     (TpbNvrIntelSmartRegionInfo *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_intel_smart_region_info__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_intel_smart_region_param__init
                     (TPbNvrIntelSmartRegionParam         *message)
{
  static TPbNvrIntelSmartRegionParam init_value = TPB_NVR_INTEL_SMART_REGION_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_intel_smart_region_param__get_packed_size
                     (const TPbNvrIntelSmartRegionParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_intel_smart_region_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_intel_smart_region_param__pack
                     (const TPbNvrIntelSmartRegionParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_intel_smart_region_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_intel_smart_region_param__pack_to_buffer
                     (const TPbNvrIntelSmartRegionParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_intel_smart_region_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrIntelSmartRegionParam *
       tpb_nvr_intel_smart_region_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrIntelSmartRegionParam *)
     protobuf_c_message_unpack (&tpb_nvr_intel_smart_region_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_intel_smart_region_param__free_unpacked
                     (TPbNvrIntelSmartRegionParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_intel_smart_region_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_intel_smart_object_info__init
                     (TpbNvrIntelSmartObjectInfo         *message)
{
  static TpbNvrIntelSmartObjectInfo init_value = TPB_NVR_INTEL_SMART_OBJECT_INFO__INIT;
  *message = init_value;
}
size_t tpb_nvr_intel_smart_object_info__get_packed_size
                     (const TpbNvrIntelSmartObjectInfo *message)
{
  assert(message->base.descriptor == &tpb_nvr_intel_smart_object_info__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_intel_smart_object_info__pack
                     (const TpbNvrIntelSmartObjectInfo *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_intel_smart_object_info__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_intel_smart_object_info__pack_to_buffer
                     (const TpbNvrIntelSmartObjectInfo *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_intel_smart_object_info__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TpbNvrIntelSmartObjectInfo *
       tpb_nvr_intel_smart_object_info__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TpbNvrIntelSmartObjectInfo *)
     protobuf_c_message_unpack (&tpb_nvr_intel_smart_object_info__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_intel_smart_object_info__free_unpacked
                     (TpbNvrIntelSmartObjectInfo *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_intel_smart_object_info__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_intel_smart_object_param__init
                     (TPbNvrIntelSmartObjectParam         *message)
{
  static TPbNvrIntelSmartObjectParam init_value = TPB_NVR_INTEL_SMART_OBJECT_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_intel_smart_object_param__get_packed_size
                     (const TPbNvrIntelSmartObjectParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_intel_smart_object_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_intel_smart_object_param__pack
                     (const TPbNvrIntelSmartObjectParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_intel_smart_object_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_intel_smart_object_param__pack_to_buffer
                     (const TPbNvrIntelSmartObjectParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_intel_smart_object_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrIntelSmartObjectParam *
       tpb_nvr_intel_smart_object_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrIntelSmartObjectParam *)
     protobuf_c_message_unpack (&tpb_nvr_intel_smart_object_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_intel_smart_object_param__free_unpacked
                     (TPbNvrIntelSmartObjectParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_intel_smart_object_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_intel_de_focus_info__init
                     (TPbNvrIntelDeFocusInfo         *message)
{
  static TPbNvrIntelDeFocusInfo init_value = TPB_NVR_INTEL_DE_FOCUS_INFO__INIT;
  *message = init_value;
}
size_t tpb_nvr_intel_de_focus_info__get_packed_size
                     (const TPbNvrIntelDeFocusInfo *message)
{
  assert(message->base.descriptor == &tpb_nvr_intel_de_focus_info__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_intel_de_focus_info__pack
                     (const TPbNvrIntelDeFocusInfo *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_intel_de_focus_info__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_intel_de_focus_info__pack_to_buffer
                     (const TPbNvrIntelDeFocusInfo *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_intel_de_focus_info__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrIntelDeFocusInfo *
       tpb_nvr_intel_de_focus_info__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrIntelDeFocusInfo *)
     protobuf_c_message_unpack (&tpb_nvr_intel_de_focus_info__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_intel_de_focus_info__free_unpacked
                     (TPbNvrIntelDeFocusInfo *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_intel_de_focus_info__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_intel_de_focus_param__init
                     (TPbNvrIntelDeFocusParam         *message)
{
  static TPbNvrIntelDeFocusParam init_value = TPB_NVR_INTEL_DE_FOCUS_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_intel_de_focus_param__get_packed_size
                     (const TPbNvrIntelDeFocusParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_intel_de_focus_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_intel_de_focus_param__pack
                     (const TPbNvrIntelDeFocusParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_intel_de_focus_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_intel_de_focus_param__pack_to_buffer
                     (const TPbNvrIntelDeFocusParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_intel_de_focus_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrIntelDeFocusParam *
       tpb_nvr_intel_de_focus_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrIntelDeFocusParam *)
     protobuf_c_message_unpack (&tpb_nvr_intel_de_focus_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_intel_de_focus_param__free_unpacked
                     (TPbNvrIntelDeFocusParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_intel_de_focus_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_intel_scene_chg_info__init
                     (TPbNvrIntelSceneChgInfo         *message)
{
  static TPbNvrIntelSceneChgInfo init_value = TPB_NVR_INTEL_SCENE_CHG_INFO__INIT;
  *message = init_value;
}
size_t tpb_nvr_intel_scene_chg_info__get_packed_size
                     (const TPbNvrIntelSceneChgInfo *message)
{
  assert(message->base.descriptor == &tpb_nvr_intel_scene_chg_info__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_intel_scene_chg_info__pack
                     (const TPbNvrIntelSceneChgInfo *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_intel_scene_chg_info__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_intel_scene_chg_info__pack_to_buffer
                     (const TPbNvrIntelSceneChgInfo *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_intel_scene_chg_info__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrIntelSceneChgInfo *
       tpb_nvr_intel_scene_chg_info__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrIntelSceneChgInfo *)
     protobuf_c_message_unpack (&tpb_nvr_intel_scene_chg_info__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_intel_scene_chg_info__free_unpacked
                     (TPbNvrIntelSceneChgInfo *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_intel_scene_chg_info__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_intel_scene_chg_param__init
                     (TPbNvrIntelSceneChgParam         *message)
{
  static TPbNvrIntelSceneChgParam init_value = TPB_NVR_INTEL_SCENE_CHG_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_intel_scene_chg_param__get_packed_size
                     (const TPbNvrIntelSceneChgParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_intel_scene_chg_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_intel_scene_chg_param__pack
                     (const TPbNvrIntelSceneChgParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_intel_scene_chg_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_intel_scene_chg_param__pack_to_buffer
                     (const TPbNvrIntelSceneChgParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_intel_scene_chg_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrIntelSceneChgParam *
       tpb_nvr_intel_scene_chg_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrIntelSceneChgParam *)
     protobuf_c_message_unpack (&tpb_nvr_intel_scene_chg_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_intel_scene_chg_param__free_unpacked
                     (TPbNvrIntelSceneChgParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_intel_scene_chg_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_intel_people_gather_info__init
                     (TPbNvrIntelPeopleGatherInfo         *message)
{
  static TPbNvrIntelPeopleGatherInfo init_value = TPB_NVR_INTEL_PEOPLE_GATHER_INFO__INIT;
  *message = init_value;
}
size_t tpb_nvr_intel_people_gather_info__get_packed_size
                     (const TPbNvrIntelPeopleGatherInfo *message)
{
  assert(message->base.descriptor == &tpb_nvr_intel_people_gather_info__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_intel_people_gather_info__pack
                     (const TPbNvrIntelPeopleGatherInfo *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_intel_people_gather_info__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_intel_people_gather_info__pack_to_buffer
                     (const TPbNvrIntelPeopleGatherInfo *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_intel_people_gather_info__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrIntelPeopleGatherInfo *
       tpb_nvr_intel_people_gather_info__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrIntelPeopleGatherInfo *)
     protobuf_c_message_unpack (&tpb_nvr_intel_people_gather_info__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_intel_people_gather_info__free_unpacked
                     (TPbNvrIntelPeopleGatherInfo *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_intel_people_gather_info__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_intel_people_gather_param__init
                     (TPbNvrIntelPeopleGatherParam         *message)
{
  static TPbNvrIntelPeopleGatherParam init_value = TPB_NVR_INTEL_PEOPLE_GATHER_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_intel_people_gather_param__get_packed_size
                     (const TPbNvrIntelPeopleGatherParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_intel_people_gather_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_intel_people_gather_param__pack
                     (const TPbNvrIntelPeopleGatherParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_intel_people_gather_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_intel_people_gather_param__pack_to_buffer
                     (const TPbNvrIntelPeopleGatherParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_intel_people_gather_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrIntelPeopleGatherParam *
       tpb_nvr_intel_people_gather_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrIntelPeopleGatherParam *)
     protobuf_c_message_unpack (&tpb_nvr_intel_people_gather_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_intel_people_gather_param__free_unpacked
                     (TPbNvrIntelPeopleGatherParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_intel_people_gather_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_intel_aud_abnormal_param__init
                     (TPbNvrIntelAudAbnormalParam         *message)
{
  static TPbNvrIntelAudAbnormalParam init_value = TPB_NVR_INTEL_AUD_ABNORMAL_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_intel_aud_abnormal_param__get_packed_size
                     (const TPbNvrIntelAudAbnormalParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_intel_aud_abnormal_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_intel_aud_abnormal_param__pack
                     (const TPbNvrIntelAudAbnormalParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_intel_aud_abnormal_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_intel_aud_abnormal_param__pack_to_buffer
                     (const TPbNvrIntelAudAbnormalParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_intel_aud_abnormal_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrIntelAudAbnormalParam *
       tpb_nvr_intel_aud_abnormal_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrIntelAudAbnormalParam *)
     protobuf_c_message_unpack (&tpb_nvr_intel_aud_abnormal_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_intel_aud_abnormal_param__free_unpacked
                     (TPbNvrIntelAudAbnormalParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_intel_aud_abnormal_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_intel_aud_real_time_param__init
                     (TPbNvrIntelAudRealTimeParam         *message)
{
  static TPbNvrIntelAudRealTimeParam init_value = TPB_NVR_INTEL_AUD_REAL_TIME_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_intel_aud_real_time_param__get_packed_size
                     (const TPbNvrIntelAudRealTimeParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_intel_aud_real_time_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_intel_aud_real_time_param__pack
                     (const TPbNvrIntelAudRealTimeParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_intel_aud_real_time_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_intel_aud_real_time_param__pack_to_buffer
                     (const TPbNvrIntelAudRealTimeParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_intel_aud_real_time_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrIntelAudRealTimeParam *
       tpb_nvr_intel_aud_real_time_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrIntelAudRealTimeParam *)
     protobuf_c_message_unpack (&tpb_nvr_intel_aud_real_time_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_intel_aud_real_time_param__free_unpacked
                     (TPbNvrIntelAudRealTimeParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_intel_aud_real_time_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_intel_plate_detect_info__init
                     (TPbNvrIntelPlateDetectInfo         *message)
{
  static TPbNvrIntelPlateDetectInfo init_value = TPB_NVR_INTEL_PLATE_DETECT_INFO__INIT;
  *message = init_value;
}
size_t tpb_nvr_intel_plate_detect_info__get_packed_size
                     (const TPbNvrIntelPlateDetectInfo *message)
{
  assert(message->base.descriptor == &tpb_nvr_intel_plate_detect_info__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_intel_plate_detect_info__pack
                     (const TPbNvrIntelPlateDetectInfo *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_intel_plate_detect_info__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_intel_plate_detect_info__pack_to_buffer
                     (const TPbNvrIntelPlateDetectInfo *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_intel_plate_detect_info__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrIntelPlateDetectInfo *
       tpb_nvr_intel_plate_detect_info__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrIntelPlateDetectInfo *)
     protobuf_c_message_unpack (&tpb_nvr_intel_plate_detect_info__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_intel_plate_detect_info__free_unpacked
                     (TPbNvrIntelPlateDetectInfo *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_intel_plate_detect_info__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_intel_plate_detect_param__init
                     (TPbNvrIntelPlateDetectParam         *message)
{
  static TPbNvrIntelPlateDetectParam init_value = TPB_NVR_INTEL_PLATE_DETECT_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_intel_plate_detect_param__get_packed_size
                     (const TPbNvrIntelPlateDetectParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_intel_plate_detect_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_intel_plate_detect_param__pack
                     (const TPbNvrIntelPlateDetectParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_intel_plate_detect_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_intel_plate_detect_param__pack_to_buffer
                     (const TPbNvrIntelPlateDetectParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_intel_plate_detect_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrIntelPlateDetectParam *
       tpb_nvr_intel_plate_detect_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrIntelPlateDetectParam *)
     protobuf_c_message_unpack (&tpb_nvr_intel_plate_detect_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_intel_plate_detect_param__free_unpacked
                     (TPbNvrIntelPlateDetectParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_intel_plate_detect_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_intel_face_detect_param__init
                     (TPbNvrIntelFaceDetectParam         *message)
{
  static TPbNvrIntelFaceDetectParam init_value = TPB_NVR_INTEL_FACE_DETECT_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_intel_face_detect_param__get_packed_size
                     (const TPbNvrIntelFaceDetectParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_intel_face_detect_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_intel_face_detect_param__pack
                     (const TPbNvrIntelFaceDetectParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_intel_face_detect_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_intel_face_detect_param__pack_to_buffer
                     (const TPbNvrIntelFaceDetectParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_intel_face_detect_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrIntelFaceDetectParam *
       tpb_nvr_intel_face_detect_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrIntelFaceDetectParam *)
     protobuf_c_message_unpack (&tpb_nvr_intel_face_detect_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_intel_face_detect_param__free_unpacked
                     (TPbNvrIntelFaceDetectParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_intel_face_detect_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_intel_anti_tamper_param__init
                     (TPbNvrIntelAntiTamperParam         *message)
{
  static TPbNvrIntelAntiTamperParam init_value = TPB_NVR_INTEL_ANTI_TAMPER_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_intel_anti_tamper_param__get_packed_size
                     (const TPbNvrIntelAntiTamperParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_intel_anti_tamper_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_intel_anti_tamper_param__pack
                     (const TPbNvrIntelAntiTamperParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_intel_anti_tamper_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_intel_anti_tamper_param__pack_to_buffer
                     (const TPbNvrIntelAntiTamperParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_intel_anti_tamper_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrIntelAntiTamperParam *
       tpb_nvr_intel_anti_tamper_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrIntelAntiTamperParam *)
     protobuf_c_message_unpack (&tpb_nvr_intel_anti_tamper_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_intel_anti_tamper_param__free_unpacked
                     (TPbNvrIntelAntiTamperParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_intel_anti_tamper_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_intel_mdparam_chn__init
                     (TPbNvrIntelMDParamChn         *message)
{
  static TPbNvrIntelMDParamChn init_value = TPB_NVR_INTEL_MDPARAM_CHN__INIT;
  *message = init_value;
}
size_t tpb_nvr_intel_mdparam_chn__get_packed_size
                     (const TPbNvrIntelMDParamChn *message)
{
  assert(message->base.descriptor == &tpb_nvr_intel_mdparam_chn__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_intel_mdparam_chn__pack
                     (const TPbNvrIntelMDParamChn *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_intel_mdparam_chn__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_intel_mdparam_chn__pack_to_buffer
                     (const TPbNvrIntelMDParamChn *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_intel_mdparam_chn__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrIntelMDParamChn *
       tpb_nvr_intel_mdparam_chn__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrIntelMDParamChn *)
     protobuf_c_message_unpack (&tpb_nvr_intel_mdparam_chn__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_intel_mdparam_chn__free_unpacked
                     (TPbNvrIntelMDParamChn *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_intel_mdparam_chn__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_intel_overlay_param_chn__init
                     (TPbNvrIntelOverlayParamChn         *message)
{
  static TPbNvrIntelOverlayParamChn init_value = TPB_NVR_INTEL_OVERLAY_PARAM_CHN__INIT;
  *message = init_value;
}
size_t tpb_nvr_intel_overlay_param_chn__get_packed_size
                     (const TPbNvrIntelOverlayParamChn *message)
{
  assert(message->base.descriptor == &tpb_nvr_intel_overlay_param_chn__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_intel_overlay_param_chn__pack
                     (const TPbNvrIntelOverlayParamChn *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_intel_overlay_param_chn__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_intel_overlay_param_chn__pack_to_buffer
                     (const TPbNvrIntelOverlayParamChn *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_intel_overlay_param_chn__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrIntelOverlayParamChn *
       tpb_nvr_intel_overlay_param_chn__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrIntelOverlayParamChn *)
     protobuf_c_message_unpack (&tpb_nvr_intel_overlay_param_chn__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_intel_overlay_param_chn__free_unpacked
                     (TPbNvrIntelOverlayParamChn *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_intel_overlay_param_chn__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_lcam_intel_cfg__init
                     (TPbNvrLcamIntelCfg         *message)
{
  static TPbNvrLcamIntelCfg init_value = TPB_NVR_LCAM_INTEL_CFG__INIT;
  *message = init_value;
}
size_t tpb_nvr_lcam_intel_cfg__get_packed_size
                     (const TPbNvrLcamIntelCfg *message)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_intel_cfg__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_lcam_intel_cfg__pack
                     (const TPbNvrLcamIntelCfg *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_intel_cfg__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_lcam_intel_cfg__pack_to_buffer
                     (const TPbNvrLcamIntelCfg *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_intel_cfg__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrLcamIntelCfg *
       tpb_nvr_lcam_intel_cfg__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrLcamIntelCfg *)
     protobuf_c_message_unpack (&tpb_nvr_lcam_intel_cfg__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_lcam_intel_cfg__free_unpacked
                     (TPbNvrLcamIntelCfg *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_intel_cfg__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
static const ProtobufCFieldDescriptor tpb_nvr_event_contact__field_descriptors[6] =
{
  {
    "post_center_set",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrEventContact, has_post_center_set),
    offsetof(TPbNvrEventContact, post_center_set),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "post_center",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrEventContact, has_post_center),
    offsetof(TPbNvrEventContact, post_center),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "osd_show_set",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrEventContact, has_osd_show_set),
    offsetof(TPbNvrEventContact, osd_show_set),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "osd_show",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrEventContact, has_osd_show),
    offsetof(TPbNvrEventContact, osd_show),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "focus_set",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrEventContact, has_focus_set),
    offsetof(TPbNvrEventContact, focus_set),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "focus",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrEventContact, has_focus),
    offsetof(TPbNvrEventContact, focus),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_event_contact__field_indices_by_name[] = {
  5,   /* field[5] = focus */
  4,   /* field[4] = focus_set */
  3,   /* field[3] = osd_show */
  2,   /* field[2] = osd_show_set */
  1,   /* field[1] = post_center */
  0,   /* field[0] = post_center_set */
};
static const ProtobufCIntRange tpb_nvr_event_contact__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 6 }
};
const ProtobufCMessageDescriptor tpb_nvr_event_contact__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrEventContact",
  "TPbNvrEventContact",
  "TPbNvrEventContact",
  "",
  sizeof(TPbNvrEventContact),
  6,
  tpb_nvr_event_contact__field_descriptors,
  tpb_nvr_event_contact__field_indices_by_name,
  1,  tpb_nvr_event_contact__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_event_contact__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_rect_region__field_descriptors[4] =
{
  {
    "start_x",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrRectRegion, has_start_x),
    offsetof(TPbNvrRectRegion, start_x),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "start_y",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrRectRegion, has_start_y),
    offsetof(TPbNvrRectRegion, start_y),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "width",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrRectRegion, has_width),
    offsetof(TPbNvrRectRegion, width),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "height",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrRectRegion, has_height),
    offsetof(TPbNvrRectRegion, height),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_rect_region__field_indices_by_name[] = {
  3,   /* field[3] = height */
  0,   /* field[0] = start_x */
  1,   /* field[1] = start_y */
  2,   /* field[2] = width */
};
static const ProtobufCIntRange tpb_nvr_rect_region__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 4 }
};
const ProtobufCMessageDescriptor tpb_nvr_rect_region__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrRectRegion",
  "TPbNvrRectRegion",
  "TPbNvrRectRegion",
  "",
  sizeof(TPbNvrRectRegion),
  4,
  tpb_nvr_rect_region__field_descriptors,
  tpb_nvr_rect_region__field_indices_by_name,
  1,  tpb_nvr_rect_region__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_rect_region__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_intel_point__field_descriptors[2] =
{
  {
    "pos_x",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIntelPoint, has_pos_x),
    offsetof(TPbNvrIntelPoint, pos_x),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "pos_y",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIntelPoint, has_pos_y),
    offsetof(TPbNvrIntelPoint, pos_y),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_intel_point__field_indices_by_name[] = {
  0,   /* field[0] = pos_x */
  1,   /* field[1] = pos_y */
};
static const ProtobufCIntRange tpb_nvr_intel_point__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_intel_point__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrIntelPoint",
  "TPbNvrIntelPoint",
  "TPbNvrIntelPoint",
  "",
  sizeof(TPbNvrIntelPoint),
  2,
  tpb_nvr_intel_point__field_descriptors,
  tpb_nvr_intel_point__field_indices_by_name,
  1,  tpb_nvr_intel_point__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_intel_point__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_intel_cordon_info__field_descriptors[10] =
{
  {
    "cordon_type",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TpbNvrIntelCordonInfo, has_cordon_type),
    offsetof(TpbNvrIntelCordonInfo, cordon_type),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "sensitity",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TpbNvrIntelCordonInfo, has_sensitity),
    offsetof(TpbNvrIntelCordonInfo, sensitity),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "filter_scale",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TpbNvrIntelCordonInfo, has_filter_scale),
    offsetof(TpbNvrIntelCordonInfo, filter_scale),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "rect_region",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TpbNvrIntelCordonInfo, rect_region),
    &tpb_nvr_rect_region__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "start_point",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TpbNvrIntelCordonInfo, start_point),
    &tpb_nvr_intel_point__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "end_point",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TpbNvrIntelCordonInfo, end_point),
    &tpb_nvr_intel_point__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ref_a_point",
    7,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TpbNvrIntelCordonInfo, ref_a_point),
    &tpb_nvr_intel_point__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ref_b_point",
    8,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TpbNvrIntelCordonInfo, ref_b_point),
    &tpb_nvr_intel_point__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "preset_id",
    9,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TpbNvrIntelCordonInfo, has_preset_id),
    offsetof(TpbNvrIntelCordonInfo, preset_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "link_alarm",
    10,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TpbNvrIntelCordonInfo, link_alarm),
    &tpb_nvr_event_contact__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_intel_cordon_info__field_indices_by_name[] = {
  0,   /* field[0] = cordon_type */
  5,   /* field[5] = end_point */
  2,   /* field[2] = filter_scale */
  9,   /* field[9] = link_alarm */
  8,   /* field[8] = preset_id */
  3,   /* field[3] = rect_region */
  6,   /* field[6] = ref_a_point */
  7,   /* field[7] = ref_b_point */
  1,   /* field[1] = sensitity */
  4,   /* field[4] = start_point */
};
static const ProtobufCIntRange tpb_nvr_intel_cordon_info__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 10 }
};
const ProtobufCMessageDescriptor tpb_nvr_intel_cordon_info__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TpbNvrIntelCordonInfo",
  "TpbNvrIntelCordonInfo",
  "TpbNvrIntelCordonInfo",
  "",
  sizeof(TpbNvrIntelCordonInfo),
  10,
  tpb_nvr_intel_cordon_info__field_descriptors,
  tpb_nvr_intel_cordon_info__field_indices_by_name,
  1,  tpb_nvr_intel_cordon_info__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_intel_cordon_info__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_lcam_intel_cordon_param__field_descriptors[4] =
{
  {
    "enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLcamIntelCordonParam, has_enable),
    offsetof(TPbNvrLcamIntelCordonParam, enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "is_cur_sel",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLcamIntelCordonParam, has_is_cur_sel),
    offsetof(TPbNvrLcamIntelCordonParam, is_cur_sel),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "cur_sel",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLcamIntelCordonParam, has_cur_sel),
    offsetof(TPbNvrLcamIntelCordonParam, cur_sel),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "cordon_info",
    4,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrLcamIntelCordonParam, n_cordon_info),
    offsetof(TPbNvrLcamIntelCordonParam, cordon_info),
    &tpb_nvr_intel_cordon_info__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_lcam_intel_cordon_param__field_indices_by_name[] = {
  3,   /* field[3] = cordon_info */
  2,   /* field[2] = cur_sel */
  0,   /* field[0] = enable */
  1,   /* field[1] = is_cur_sel */
};
static const ProtobufCIntRange tpb_nvr_lcam_intel_cordon_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 4 }
};
const ProtobufCMessageDescriptor tpb_nvr_lcam_intel_cordon_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrLcamIntelCordonParam",
  "TPbNvrLcamIntelCordonParam",
  "TPbNvrLcamIntelCordonParam",
  "",
  sizeof(TPbNvrLcamIntelCordonParam),
  4,
  tpb_nvr_lcam_intel_cordon_param__field_descriptors,
  tpb_nvr_lcam_intel_cordon_param__field_indices_by_name,
  1,  tpb_nvr_lcam_intel_cordon_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_lcam_intel_cordon_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_ploygon__field_descriptors[2] =
{
  {
    "point_num",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TpbNvrPloygon, has_point_num),
    offsetof(TpbNvrPloygon, point_num),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "polygon_point",
    2,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TpbNvrPloygon, n_polygon_point),
    offsetof(TpbNvrPloygon, polygon_point),
    &tpb_nvr_intel_point__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_ploygon__field_indices_by_name[] = {
  0,   /* field[0] = point_num */
  1,   /* field[1] = polygon_point */
};
static const ProtobufCIntRange tpb_nvr_ploygon__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_ploygon__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TpbNvrPloygon",
  "TpbNvrPloygon",
  "TpbNvrPloygon",
  "",
  sizeof(TpbNvrPloygon),
  2,
  tpb_nvr_ploygon__field_descriptors,
  tpb_nvr_ploygon__field_indices_by_name,
  1,  tpb_nvr_ploygon__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_ploygon__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_intel_smart_region_info__field_descriptors[7] =
{
  {
    "time_threshold",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TpbNvrIntelSmartRegionInfo, has_time_threshold),
    offsetof(TpbNvrIntelSmartRegionInfo, time_threshold),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "sensitivity",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TpbNvrIntelSmartRegionInfo, has_sensitivity),
    offsetof(TpbNvrIntelSmartRegionInfo, sensitivity),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "filter_sale",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TpbNvrIntelSmartRegionInfo, has_filter_sale),
    offsetof(TpbNvrIntelSmartRegionInfo, filter_sale),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "max_filter_size",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TpbNvrIntelSmartRegionInfo, max_filter_size),
    &tpb_nvr_rect_region__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "region",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TpbNvrIntelSmartRegionInfo, region),
    &tpb_nvr_ploygon__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "preset_id",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TpbNvrIntelSmartRegionInfo, has_preset_id),
    offsetof(TpbNvrIntelSmartRegionInfo, preset_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "link_alarm",
    7,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TpbNvrIntelSmartRegionInfo, link_alarm),
    &tpb_nvr_event_contact__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_intel_smart_region_info__field_indices_by_name[] = {
  2,   /* field[2] = filter_sale */
  6,   /* field[6] = link_alarm */
  3,   /* field[3] = max_filter_size */
  5,   /* field[5] = preset_id */
  4,   /* field[4] = region */
  1,   /* field[1] = sensitivity */
  0,   /* field[0] = time_threshold */
};
static const ProtobufCIntRange tpb_nvr_intel_smart_region_info__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 7 }
};
const ProtobufCMessageDescriptor tpb_nvr_intel_smart_region_info__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TpbNvrIntelSmartRegionInfo",
  "TpbNvrIntelSmartRegionInfo",
  "TpbNvrIntelSmartRegionInfo",
  "",
  sizeof(TpbNvrIntelSmartRegionInfo),
  7,
  tpb_nvr_intel_smart_region_info__field_descriptors,
  tpb_nvr_intel_smart_region_info__field_indices_by_name,
  1,  tpb_nvr_intel_smart_region_info__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_intel_smart_region_info__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_intel_smart_region_param__field_descriptors[4] =
{
  {
    "enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIntelSmartRegionParam, has_enable),
    offsetof(TPbNvrIntelSmartRegionParam, enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "is_cur_sel",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIntelSmartRegionParam, has_is_cur_sel),
    offsetof(TPbNvrIntelSmartRegionParam, is_cur_sel),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "cur_sel",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIntelSmartRegionParam, has_cur_sel),
    offsetof(TPbNvrIntelSmartRegionParam, cur_sel),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "region_info",
    4,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrIntelSmartRegionParam, n_region_info),
    offsetof(TPbNvrIntelSmartRegionParam, region_info),
    &tpb_nvr_intel_smart_region_info__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_intel_smart_region_param__field_indices_by_name[] = {
  2,   /* field[2] = cur_sel */
  0,   /* field[0] = enable */
  1,   /* field[1] = is_cur_sel */
  3,   /* field[3] = region_info */
};
static const ProtobufCIntRange tpb_nvr_intel_smart_region_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 4 }
};
const ProtobufCMessageDescriptor tpb_nvr_intel_smart_region_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrIntelSmartRegionParam",
  "TPbNvrIntelSmartRegionParam",
  "TPbNvrIntelSmartRegionParam",
  "",
  sizeof(TPbNvrIntelSmartRegionParam),
  4,
  tpb_nvr_intel_smart_region_param__field_descriptors,
  tpb_nvr_intel_smart_region_param__field_indices_by_name,
  1,  tpb_nvr_intel_smart_region_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_intel_smart_region_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_intel_smart_object_info__field_descriptors[7] =
{
  {
    "time_threshold",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TpbNvrIntelSmartObjectInfo, has_time_threshold),
    offsetof(TpbNvrIntelSmartObjectInfo, time_threshold),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "sensitivity",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TpbNvrIntelSmartObjectInfo, has_sensitivity),
    offsetof(TpbNvrIntelSmartObjectInfo, sensitivity),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "filter_sale",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TpbNvrIntelSmartObjectInfo, has_filter_sale),
    offsetof(TpbNvrIntelSmartObjectInfo, filter_sale),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "max_filter_size",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TpbNvrIntelSmartObjectInfo, max_filter_size),
    &tpb_nvr_rect_region__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "region",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TpbNvrIntelSmartObjectInfo, region),
    &tpb_nvr_ploygon__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "preset_id",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TpbNvrIntelSmartObjectInfo, has_preset_id),
    offsetof(TpbNvrIntelSmartObjectInfo, preset_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "link_alarm",
    7,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TpbNvrIntelSmartObjectInfo, link_alarm),
    &tpb_nvr_event_contact__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_intel_smart_object_info__field_indices_by_name[] = {
  2,   /* field[2] = filter_sale */
  6,   /* field[6] = link_alarm */
  3,   /* field[3] = max_filter_size */
  5,   /* field[5] = preset_id */
  4,   /* field[4] = region */
  1,   /* field[1] = sensitivity */
  0,   /* field[0] = time_threshold */
};
static const ProtobufCIntRange tpb_nvr_intel_smart_object_info__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 7 }
};
const ProtobufCMessageDescriptor tpb_nvr_intel_smart_object_info__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TpbNvrIntelSmartObjectInfo",
  "TpbNvrIntelSmartObjectInfo",
  "TpbNvrIntelSmartObjectInfo",
  "",
  sizeof(TpbNvrIntelSmartObjectInfo),
  7,
  tpb_nvr_intel_smart_object_info__field_descriptors,
  tpb_nvr_intel_smart_object_info__field_indices_by_name,
  1,  tpb_nvr_intel_smart_object_info__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_intel_smart_object_info__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_intel_smart_object_param__field_descriptors[4] =
{
  {
    "enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIntelSmartObjectParam, has_enable),
    offsetof(TPbNvrIntelSmartObjectParam, enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "is_cur_sel",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIntelSmartObjectParam, has_is_cur_sel),
    offsetof(TPbNvrIntelSmartObjectParam, is_cur_sel),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "cur_sel",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIntelSmartObjectParam, has_cur_sel),
    offsetof(TPbNvrIntelSmartObjectParam, cur_sel),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "region_info",
    4,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrIntelSmartObjectParam, n_region_info),
    offsetof(TPbNvrIntelSmartObjectParam, region_info),
    &tpb_nvr_intel_smart_object_info__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_intel_smart_object_param__field_indices_by_name[] = {
  2,   /* field[2] = cur_sel */
  0,   /* field[0] = enable */
  1,   /* field[1] = is_cur_sel */
  3,   /* field[3] = region_info */
};
static const ProtobufCIntRange tpb_nvr_intel_smart_object_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 4 }
};
const ProtobufCMessageDescriptor tpb_nvr_intel_smart_object_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrIntelSmartObjectParam",
  "TPbNvrIntelSmartObjectParam",
  "TPbNvrIntelSmartObjectParam",
  "",
  sizeof(TPbNvrIntelSmartObjectParam),
  4,
  tpb_nvr_intel_smart_object_param__field_descriptors,
  tpb_nvr_intel_smart_object_param__field_indices_by_name,
  1,  tpb_nvr_intel_smart_object_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_intel_smart_object_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_intel_de_focus_info__field_descriptors[2] =
{
  {
    "sensitivity",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIntelDeFocusInfo, has_sensitivity),
    offsetof(TPbNvrIntelDeFocusInfo, sensitivity),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "enable",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIntelDeFocusInfo, has_enable),
    offsetof(TPbNvrIntelDeFocusInfo, enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_intel_de_focus_info__field_indices_by_name[] = {
  1,   /* field[1] = enable */
  0,   /* field[0] = sensitivity */
};
static const ProtobufCIntRange tpb_nvr_intel_de_focus_info__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_intel_de_focus_info__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrIntelDeFocusInfo",
  "TPbNvrIntelDeFocusInfo",
  "TPbNvrIntelDeFocusInfo",
  "",
  sizeof(TPbNvrIntelDeFocusInfo),
  2,
  tpb_nvr_intel_de_focus_info__field_descriptors,
  tpb_nvr_intel_de_focus_info__field_indices_by_name,
  1,  tpb_nvr_intel_de_focus_info__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_intel_de_focus_info__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_intel_de_focus_param__field_descriptors[3] =
{
  {
    "de_focus_info",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrIntelDeFocusParam, de_focus_info),
    &tpb_nvr_intel_de_focus_info__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "preset_id",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIntelDeFocusParam, has_preset_id),
    offsetof(TPbNvrIntelDeFocusParam, preset_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "link_alarm",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrIntelDeFocusParam, link_alarm),
    &tpb_nvr_event_contact__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_intel_de_focus_param__field_indices_by_name[] = {
  0,   /* field[0] = de_focus_info */
  2,   /* field[2] = link_alarm */
  1,   /* field[1] = preset_id */
};
static const ProtobufCIntRange tpb_nvr_intel_de_focus_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 3 }
};
const ProtobufCMessageDescriptor tpb_nvr_intel_de_focus_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrIntelDeFocusParam",
  "TPbNvrIntelDeFocusParam",
  "TPbNvrIntelDeFocusParam",
  "",
  sizeof(TPbNvrIntelDeFocusParam),
  3,
  tpb_nvr_intel_de_focus_param__field_descriptors,
  tpb_nvr_intel_de_focus_param__field_indices_by_name,
  1,  tpb_nvr_intel_de_focus_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_intel_de_focus_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_intel_scene_chg_info__field_descriptors[3] =
{
  {
    "chk_type",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIntelSceneChgInfo, has_chk_type),
    offsetof(TPbNvrIntelSceneChgInfo, chk_type),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "sensitivity",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIntelSceneChgInfo, has_sensitivity),
    offsetof(TPbNvrIntelSceneChgInfo, sensitivity),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "enable",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIntelSceneChgInfo, has_enable),
    offsetof(TPbNvrIntelSceneChgInfo, enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_intel_scene_chg_info__field_indices_by_name[] = {
  0,   /* field[0] = chk_type */
  2,   /* field[2] = enable */
  1,   /* field[1] = sensitivity */
};
static const ProtobufCIntRange tpb_nvr_intel_scene_chg_info__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 3 }
};
const ProtobufCMessageDescriptor tpb_nvr_intel_scene_chg_info__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrIntelSceneChgInfo",
  "TPbNvrIntelSceneChgInfo",
  "TPbNvrIntelSceneChgInfo",
  "",
  sizeof(TPbNvrIntelSceneChgInfo),
  3,
  tpb_nvr_intel_scene_chg_info__field_descriptors,
  tpb_nvr_intel_scene_chg_info__field_indices_by_name,
  1,  tpb_nvr_intel_scene_chg_info__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_intel_scene_chg_info__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_intel_scene_chg_param__field_descriptors[3] =
{
  {
    "scene_cha_info",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrIntelSceneChgParam, scene_cha_info),
    &tpb_nvr_intel_scene_chg_info__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "preset_id",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIntelSceneChgParam, has_preset_id),
    offsetof(TPbNvrIntelSceneChgParam, preset_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "link_alarm",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrIntelSceneChgParam, link_alarm),
    &tpb_nvr_event_contact__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_intel_scene_chg_param__field_indices_by_name[] = {
  2,   /* field[2] = link_alarm */
  1,   /* field[1] = preset_id */
  0,   /* field[0] = scene_cha_info */
};
static const ProtobufCIntRange tpb_nvr_intel_scene_chg_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 3 }
};
const ProtobufCMessageDescriptor tpb_nvr_intel_scene_chg_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrIntelSceneChgParam",
  "TPbNvrIntelSceneChgParam",
  "TPbNvrIntelSceneChgParam",
  "",
  sizeof(TPbNvrIntelSceneChgParam),
  3,
  tpb_nvr_intel_scene_chg_param__field_descriptors,
  tpb_nvr_intel_scene_chg_param__field_indices_by_name,
  1,  tpb_nvr_intel_scene_chg_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_intel_scene_chg_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_intel_people_gather_info__field_descriptors[4] =
{
  {
    "accounting",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIntelPeopleGatherInfo, has_accounting),
    offsetof(TPbNvrIntelPeopleGatherInfo, accounting),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "region",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrIntelPeopleGatherInfo, region),
    &tpb_nvr_ploygon__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "preset_id",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIntelPeopleGatherInfo, has_preset_id),
    offsetof(TPbNvrIntelPeopleGatherInfo, preset_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "link_alarm",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrIntelPeopleGatherInfo, link_alarm),
    &tpb_nvr_event_contact__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_intel_people_gather_info__field_indices_by_name[] = {
  0,   /* field[0] = accounting */
  3,   /* field[3] = link_alarm */
  2,   /* field[2] = preset_id */
  1,   /* field[1] = region */
};
static const ProtobufCIntRange tpb_nvr_intel_people_gather_info__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 4 }
};
const ProtobufCMessageDescriptor tpb_nvr_intel_people_gather_info__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrIntelPeopleGatherInfo",
  "TPbNvrIntelPeopleGatherInfo",
  "TPbNvrIntelPeopleGatherInfo",
  "",
  sizeof(TPbNvrIntelPeopleGatherInfo),
  4,
  tpb_nvr_intel_people_gather_info__field_descriptors,
  tpb_nvr_intel_people_gather_info__field_indices_by_name,
  1,  tpb_nvr_intel_people_gather_info__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_intel_people_gather_info__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_intel_people_gather_param__field_descriptors[4] =
{
  {
    "enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIntelPeopleGatherParam, has_enable),
    offsetof(TPbNvrIntelPeopleGatherParam, enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "is_cur_sel",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIntelPeopleGatherParam, has_is_cur_sel),
    offsetof(TPbNvrIntelPeopleGatherParam, is_cur_sel),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "cursel",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIntelPeopleGatherParam, has_cursel),
    offsetof(TPbNvrIntelPeopleGatherParam, cursel),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "people_gather",
    4,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrIntelPeopleGatherParam, n_people_gather),
    offsetof(TPbNvrIntelPeopleGatherParam, people_gather),
    &tpb_nvr_intel_people_gather_info__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_intel_people_gather_param__field_indices_by_name[] = {
  2,   /* field[2] = cursel */
  0,   /* field[0] = enable */
  1,   /* field[1] = is_cur_sel */
  3,   /* field[3] = people_gather */
};
static const ProtobufCIntRange tpb_nvr_intel_people_gather_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 4 }
};
const ProtobufCMessageDescriptor tpb_nvr_intel_people_gather_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrIntelPeopleGatherParam",
  "TPbNvrIntelPeopleGatherParam",
  "TPbNvrIntelPeopleGatherParam",
  "",
  sizeof(TPbNvrIntelPeopleGatherParam),
  4,
  tpb_nvr_intel_people_gather_param__field_descriptors,
  tpb_nvr_intel_people_gather_param__field_indices_by_name,
  1,  tpb_nvr_intel_people_gather_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_intel_people_gather_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_intel_aud_abnormal_param__field_descriptors[5] =
{
  {
    "enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIntelAudAbnormalParam, has_enable),
    offsetof(TPbNvrIntelAudAbnormalParam, enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "threshold",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIntelAudAbnormalParam, has_threshold),
    offsetof(TPbNvrIntelAudAbnormalParam, threshold),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "sensitivity",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIntelAudAbnormalParam, has_sensitivity),
    offsetof(TPbNvrIntelAudAbnormalParam, sensitivity),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "preset_id",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIntelAudAbnormalParam, has_preset_id),
    offsetof(TPbNvrIntelAudAbnormalParam, preset_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "link_alarm",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrIntelAudAbnormalParam, link_alarm),
    &tpb_nvr_event_contact__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_intel_aud_abnormal_param__field_indices_by_name[] = {
  0,   /* field[0] = enable */
  4,   /* field[4] = link_alarm */
  3,   /* field[3] = preset_id */
  2,   /* field[2] = sensitivity */
  1,   /* field[1] = threshold */
};
static const ProtobufCIntRange tpb_nvr_intel_aud_abnormal_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 5 }
};
const ProtobufCMessageDescriptor tpb_nvr_intel_aud_abnormal_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrIntelAudAbnormalParam",
  "TPbNvrIntelAudAbnormalParam",
  "TPbNvrIntelAudAbnormalParam",
  "",
  sizeof(TPbNvrIntelAudAbnormalParam),
  5,
  tpb_nvr_intel_aud_abnormal_param__field_descriptors,
  tpb_nvr_intel_aud_abnormal_param__field_indices_by_name,
  1,  tpb_nvr_intel_aud_abnormal_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_intel_aud_abnormal_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_intel_aud_real_time_param__field_descriptors[1] =
{
  {
    "aud_meter",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIntelAudRealTimeParam, has_aud_meter),
    offsetof(TPbNvrIntelAudRealTimeParam, aud_meter),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_intel_aud_real_time_param__field_indices_by_name[] = {
  0,   /* field[0] = aud_meter */
};
static const ProtobufCIntRange tpb_nvr_intel_aud_real_time_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor tpb_nvr_intel_aud_real_time_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrIntelAudRealTimeParam",
  "TPbNvrIntelAudRealTimeParam",
  "TPbNvrIntelAudRealTimeParam",
  "",
  sizeof(TPbNvrIntelAudRealTimeParam),
  1,
  tpb_nvr_intel_aud_real_time_param__field_descriptors,
  tpb_nvr_intel_aud_real_time_param__field_indices_by_name,
  1,  tpb_nvr_intel_aud_real_time_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_intel_aud_real_time_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_intel_plate_detect_info__field_descriptors[3] =
{
  {
    "region",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrIntelPlateDetectInfo, n_region),
    offsetof(TPbNvrIntelPlateDetectInfo, region),
    &tpb_nvr_ploygon__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "preset_id",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIntelPlateDetectInfo, has_preset_id),
    offsetof(TPbNvrIntelPlateDetectInfo, preset_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "link_alarm",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrIntelPlateDetectInfo, link_alarm),
    &tpb_nvr_event_contact__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_intel_plate_detect_info__field_indices_by_name[] = {
  2,   /* field[2] = link_alarm */
  1,   /* field[1] = preset_id */
  0,   /* field[0] = region */
};
static const ProtobufCIntRange tpb_nvr_intel_plate_detect_info__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 3 }
};
const ProtobufCMessageDescriptor tpb_nvr_intel_plate_detect_info__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrIntelPlateDetectInfo",
  "TPbNvrIntelPlateDetectInfo",
  "TPbNvrIntelPlateDetectInfo",
  "",
  sizeof(TPbNvrIntelPlateDetectInfo),
  3,
  tpb_nvr_intel_plate_detect_info__field_descriptors,
  tpb_nvr_intel_plate_detect_info__field_indices_by_name,
  1,  tpb_nvr_intel_plate_detect_info__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_intel_plate_detect_info__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_intel_plate_detect_param__field_descriptors[9] =
{
  {
    "enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIntelPlateDetectParam, has_enable),
    offsetof(TPbNvrIntelPlateDetectParam, enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "cursel",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIntelPlateDetectParam, has_cursel),
    offsetof(TPbNvrIntelPlateDetectParam, cursel),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "plate_speed",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIntelPlateDetectParam, has_plate_speed),
    offsetof(TPbNvrIntelPlateDetectParam, plate_speed),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "snap_position_type",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIntelPlateDetectParam, has_snap_position_type),
    offsetof(TPbNvrIntelPlateDetectParam, snap_position_type),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "plate_pix",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIntelPlateDetectParam, has_plate_pix),
    offsetof(TPbNvrIntelPlateDetectParam, plate_pix),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "camera_angle",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIntelPlateDetectParam, has_camera_angle),
    offsetof(TPbNvrIntelPlateDetectParam, camera_angle),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "default_province",
    7,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BYTES,
    offsetof(TPbNvrIntelPlateDetectParam, has_default_province),
    offsetof(TPbNvrIntelPlateDetectParam, default_province),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "code",
    8,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIntelPlateDetectParam, has_code),
    offsetof(TPbNvrIntelPlateDetectParam, code),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "plate_detect_info",
    9,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrIntelPlateDetectParam, n_plate_detect_info),
    offsetof(TPbNvrIntelPlateDetectParam, plate_detect_info),
    &tpb_nvr_intel_plate_detect_info__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_intel_plate_detect_param__field_indices_by_name[] = {
  5,   /* field[5] = camera_angle */
  7,   /* field[7] = code */
  1,   /* field[1] = cursel */
  6,   /* field[6] = default_province */
  0,   /* field[0] = enable */
  8,   /* field[8] = plate_detect_info */
  4,   /* field[4] = plate_pix */
  2,   /* field[2] = plate_speed */
  3,   /* field[3] = snap_position_type */
};
static const ProtobufCIntRange tpb_nvr_intel_plate_detect_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 9 }
};
const ProtobufCMessageDescriptor tpb_nvr_intel_plate_detect_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrIntelPlateDetectParam",
  "TPbNvrIntelPlateDetectParam",
  "TPbNvrIntelPlateDetectParam",
  "",
  sizeof(TPbNvrIntelPlateDetectParam),
  9,
  tpb_nvr_intel_plate_detect_param__field_descriptors,
  tpb_nvr_intel_plate_detect_param__field_indices_by_name,
  1,  tpb_nvr_intel_plate_detect_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_intel_plate_detect_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_intel_face_detect_param__field_descriptors[4] =
{
  {
    "enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIntelFaceDetectParam, has_enable),
    offsetof(TPbNvrIntelFaceDetectParam, enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "sensitivity",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIntelFaceDetectParam, has_sensitivity),
    offsetof(TPbNvrIntelFaceDetectParam, sensitivity),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "preset_id",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIntelFaceDetectParam, has_preset_id),
    offsetof(TPbNvrIntelFaceDetectParam, preset_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "link_alarm",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrIntelFaceDetectParam, link_alarm),
    &tpb_nvr_event_contact__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_intel_face_detect_param__field_indices_by_name[] = {
  0,   /* field[0] = enable */
  3,   /* field[3] = link_alarm */
  2,   /* field[2] = preset_id */
  1,   /* field[1] = sensitivity */
};
static const ProtobufCIntRange tpb_nvr_intel_face_detect_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 4 }
};
const ProtobufCMessageDescriptor tpb_nvr_intel_face_detect_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrIntelFaceDetectParam",
  "TPbNvrIntelFaceDetectParam",
  "TPbNvrIntelFaceDetectParam",
  "",
  sizeof(TPbNvrIntelFaceDetectParam),
  4,
  tpb_nvr_intel_face_detect_param__field_descriptors,
  tpb_nvr_intel_face_detect_param__field_indices_by_name,
  1,  tpb_nvr_intel_face_detect_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_intel_face_detect_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_intel_anti_tamper_param__field_descriptors[2] =
{
  {
    "enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIntelAntiTamperParam, has_enable),
    offsetof(TPbNvrIntelAntiTamperParam, enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "link_alarm",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrIntelAntiTamperParam, link_alarm),
    &tpb_nvr_event_contact__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_intel_anti_tamper_param__field_indices_by_name[] = {
  0,   /* field[0] = enable */
  1,   /* field[1] = link_alarm */
};
static const ProtobufCIntRange tpb_nvr_intel_anti_tamper_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_intel_anti_tamper_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrIntelAntiTamperParam",
  "TPbNvrIntelAntiTamperParam",
  "TPbNvrIntelAntiTamperParam",
  "",
  sizeof(TPbNvrIntelAntiTamperParam),
  2,
  tpb_nvr_intel_anti_tamper_param__field_descriptors,
  tpb_nvr_intel_anti_tamper_param__field_indices_by_name,
  1,  tpb_nvr_intel_anti_tamper_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_intel_anti_tamper_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_intel_mdparam_chn__field_descriptors[5] =
{
  {
    "enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIntelMDParamChn, has_enable),
    offsetof(TPbNvrIntelMDParamChn, enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "sensitivity",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIntelMDParamChn, has_sensitivity),
    offsetof(TPbNvrIntelMDParamChn, sensitivity),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "region_num",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIntelMDParamChn, has_region_num),
    offsetof(TPbNvrIntelMDParamChn, region_num),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "zone_info",
    4,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrIntelMDParamChn, n_zone_info),
    offsetof(TPbNvrIntelMDParamChn, zone_info),
    &tpb_nvr_rect_region__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "link_alarm",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrIntelMDParamChn, link_alarm),
    &tpb_nvr_event_contact__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_intel_mdparam_chn__field_indices_by_name[] = {
  0,   /* field[0] = enable */
  4,   /* field[4] = link_alarm */
  2,   /* field[2] = region_num */
  1,   /* field[1] = sensitivity */
  3,   /* field[3] = zone_info */
};
static const ProtobufCIntRange tpb_nvr_intel_mdparam_chn__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 5 }
};
const ProtobufCMessageDescriptor tpb_nvr_intel_mdparam_chn__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrIntelMDParamChn",
  "TPbNvrIntelMDParamChn",
  "TPbNvrIntelMDParamChn",
  "",
  sizeof(TPbNvrIntelMDParamChn),
  5,
  tpb_nvr_intel_mdparam_chn__field_descriptors,
  tpb_nvr_intel_mdparam_chn__field_indices_by_name,
  1,  tpb_nvr_intel_mdparam_chn__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_intel_mdparam_chn__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_intel_overlay_param_chn__field_descriptors[5] =
{
  {
    "enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIntelOverlayParamChn, has_enable),
    offsetof(TPbNvrIntelOverlayParamChn, enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "sensitivity",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIntelOverlayParamChn, has_sensitivity),
    offsetof(TPbNvrIntelOverlayParamChn, sensitivity),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "region_num",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrIntelOverlayParamChn, has_region_num),
    offsetof(TPbNvrIntelOverlayParamChn, region_num),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "zone_info",
    4,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrIntelOverlayParamChn, n_zone_info),
    offsetof(TPbNvrIntelOverlayParamChn, zone_info),
    &tpb_nvr_rect_region__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "link_alarm",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrIntelOverlayParamChn, link_alarm),
    &tpb_nvr_event_contact__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_intel_overlay_param_chn__field_indices_by_name[] = {
  0,   /* field[0] = enable */
  4,   /* field[4] = link_alarm */
  2,   /* field[2] = region_num */
  1,   /* field[1] = sensitivity */
  3,   /* field[3] = zone_info */
};
static const ProtobufCIntRange tpb_nvr_intel_overlay_param_chn__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 5 }
};
const ProtobufCMessageDescriptor tpb_nvr_intel_overlay_param_chn__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrIntelOverlayParamChn",
  "TPbNvrIntelOverlayParamChn",
  "TPbNvrIntelOverlayParamChn",
  "",
  sizeof(TPbNvrIntelOverlayParamChn),
  5,
  tpb_nvr_intel_overlay_param_chn__field_descriptors,
  tpb_nvr_intel_overlay_param_chn__field_indices_by_name,
  1,  tpb_nvr_intel_overlay_param_chn__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_intel_overlay_param_chn__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_lcam_intel_cfg__field_descriptors[16] =
{
  {
    "cordon_param",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrLcamIntelCfg, n_cordon_param),
    offsetof(TPbNvrLcamIntelCfg, cordon_param),
    &tpb_nvr_lcam_intel_cordon_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "area_invasion",
    2,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrLcamIntelCfg, n_area_invasion),
    offsetof(TPbNvrLcamIntelCfg, area_invasion),
    &tpb_nvr_intel_smart_region_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "area_enter",
    3,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrLcamIntelCfg, n_area_enter),
    offsetof(TPbNvrLcamIntelCfg, area_enter),
    &tpb_nvr_intel_smart_region_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "area_leave",
    4,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrLcamIntelCfg, n_area_leave),
    offsetof(TPbNvrLcamIntelCfg, area_leave),
    &tpb_nvr_intel_smart_region_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "object_pick",
    5,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrLcamIntelCfg, n_object_pick),
    offsetof(TPbNvrLcamIntelCfg, object_pick),
    &tpb_nvr_intel_smart_object_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "object_leave",
    6,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrLcamIntelCfg, n_object_leave),
    offsetof(TPbNvrLcamIntelCfg, object_leave),
    &tpb_nvr_intel_smart_object_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "de_focus",
    7,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrLcamIntelCfg, n_de_focus),
    offsetof(TPbNvrLcamIntelCfg, de_focus),
    &tpb_nvr_intel_de_focus_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "chg_scene",
    8,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrLcamIntelCfg, n_chg_scene),
    offsetof(TPbNvrLcamIntelCfg, chg_scene),
    &tpb_nvr_intel_scene_chg_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "people_gather",
    9,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrLcamIntelCfg, n_people_gather),
    offsetof(TPbNvrLcamIntelCfg, people_gather),
    &tpb_nvr_intel_people_gather_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "aud_abnormal",
    10,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrLcamIntelCfg, n_aud_abnormal),
    offsetof(TPbNvrLcamIntelCfg, aud_abnormal),
    &tpb_nvr_intel_aud_abnormal_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "aud_real_time",
    11,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrLcamIntelCfg, n_aud_real_time),
    offsetof(TPbNvrLcamIntelCfg, aud_real_time),
    &tpb_nvr_intel_aud_real_time_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "plate_detect",
    12,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrLcamIntelCfg, n_plate_detect),
    offsetof(TPbNvrLcamIntelCfg, plate_detect),
    &tpb_nvr_intel_plate_detect_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "face_detect",
    13,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrLcamIntelCfg, n_face_detect),
    offsetof(TPbNvrLcamIntelCfg, face_detect),
    &tpb_nvr_intel_face_detect_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "anti_tamper",
    14,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrLcamIntelCfg, n_anti_tamper),
    offsetof(TPbNvrLcamIntelCfg, anti_tamper),
    &tpb_nvr_intel_anti_tamper_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "md_param",
    15,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrLcamIntelCfg, n_md_param),
    offsetof(TPbNvrLcamIntelCfg, md_param),
    &tpb_nvr_intel_mdparam_chn__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "overlay_param",
    16,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrLcamIntelCfg, n_overlay_param),
    offsetof(TPbNvrLcamIntelCfg, overlay_param),
    &tpb_nvr_intel_overlay_param_chn__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_lcam_intel_cfg__field_indices_by_name[] = {
  13,   /* field[13] = anti_tamper */
  2,   /* field[2] = area_enter */
  1,   /* field[1] = area_invasion */
  3,   /* field[3] = area_leave */
  9,   /* field[9] = aud_abnormal */
  10,   /* field[10] = aud_real_time */
  7,   /* field[7] = chg_scene */
  0,   /* field[0] = cordon_param */
  6,   /* field[6] = de_focus */
  12,   /* field[12] = face_detect */
  14,   /* field[14] = md_param */
  5,   /* field[5] = object_leave */
  4,   /* field[4] = object_pick */
  15,   /* field[15] = overlay_param */
  8,   /* field[8] = people_gather */
  11,   /* field[11] = plate_detect */
};
static const ProtobufCIntRange tpb_nvr_lcam_intel_cfg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 16 }
};
const ProtobufCMessageDescriptor tpb_nvr_lcam_intel_cfg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrLcamIntelCfg",
  "TPbNvrLcamIntelCfg",
  "TPbNvrLcamIntelCfg",
  "",
  sizeof(TPbNvrLcamIntelCfg),
  16,
  tpb_nvr_lcam_intel_cfg__field_descriptors,
  tpb_nvr_lcam_intel_cfg__field_indices_by_name,
  1,  tpb_nvr_lcam_intel_cfg__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_lcam_intel_cfg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
