path="../../10-common/version/compileinfo/nvrunifiedlog_his3531dv200.txt"
date>>$path

cd ./prj_linux

echo ==============================================
echo =      nvr_unifiedlog_linux for his3531dv200           =
echo ==============================================

echo "============compile libnvrunifiedlog his3531dv200============">>../$path

make -j4 -e DEBUG=0 -f makefile_his3531dv200 clean
make -j4 -e DEBUG=0 -f makefile_his3531dv200 2>>../$path

cp -L -r -f libnvrunifiedlog.so ../../../10-common/lib/release/his3531dv200/

cd ..
