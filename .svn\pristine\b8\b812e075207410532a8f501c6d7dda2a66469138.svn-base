/*
 * Note: this file originally auto-generated by mib2c using
 *        $
 */
#ifndef COMMONMIB_H
#define COMMONMIB_H

/* function declarations */
void init_commonMIB(void);
Netsnmp_Node_Handler handle_csId;
Netsnmp_Node_Handler handle_csModel;
Netsnmp_Node_Handler handle_csSoftwareVersion;
Netsnmp_Node_Handler handle_csHardwareVersion;
Netsnmp_Node_Handler handle_csManufacturer;
Netsnmp_Node_Handler handle_cnnmNmsIp;
Netsnmp_Node_Handler handle_cnnmNmsTrapPort;
Netsnmp_Node_Handler handle_cnNtpIp;
Netsnmp_Node_Handler handle_cnNtpPort;
Netsnmp_Node_Handler handle_cnNtpStatus;
Netsnmp_Node_Handler handle_cnNtpSync;
Netsnmp_Node_Handler handle_cpCpuUsage;
Netsnmp_Node_Handler handle_cpCpuRisingThreshold;
Netsnmp_Node_Handler handle_cpCpuFallingThreshold;
Netsnmp_Node_Handler handle_cpCpuTrapEnable;
Netsnmp_Node_Handler handle_cpMemoryUsage;
Netsnmp_Node_Handler handle_cpMemoryRisingThreshold;
Netsnmp_Node_Handler handle_cpMemoryFallingThreshold;
Netsnmp_Node_Handler handle_cpMemoryTrapEnable;
Netsnmp_Node_Handler handle_csoReboot;
Netsnmp_Node_Handler handle_csouImageUrl;
Netsnmp_Node_Handler handle_csouAdminStatus;
Netsnmp_Node_Handler handle_csouOperStatus;
Netsnmp_Node_Handler handle_csouUsername;
Netsnmp_Node_Handler handle_csouPassword;
Netsnmp_Node_Handler handle_csouUpgradeTime;
Netsnmp_Node_Handler handle_csouCheckString;
Netsnmp_Node_Handler handle_csoFactoryDefault;
Netsnmp_Node_Handler handle_csoImportConfig;
Netsnmp_Node_Handler handle_csoExportConfig;

#endif /* COMMONMIB_H */
