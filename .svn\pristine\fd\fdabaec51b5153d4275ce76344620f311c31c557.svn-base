path="../../../10-common/version/compileinfo/nvrfixsgwenc_his3531dv200.txt"
date>>$path

pwd
cd ./prj_linux

echo ==============================================
echo =      nvr_sgw_linux for his3531dv200        =
echo ==============================================

echo "============compile nvrfixsgw his3531dv200============">>../$path

make -e DEBUG=0 -f makefile_his3531dv200 clean
make -e DEBUG=0 -f makefile_his3531dv200

### tmp use start ###
cp -L -r -f libnvrsgwencpro.so ../../../../10-common/lib/release/his3531dv200/speprolib/
### tmp use end ###
cd ..
