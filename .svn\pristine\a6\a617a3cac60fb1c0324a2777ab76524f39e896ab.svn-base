

TOP := ..

COMM_DIR := ../../common/

SRC_DIR := $(TOP)/source
CURDIR := ./

## Name and type of the target for this Makefile

APP_TARGET	      := nvrsrv


## Define debugging symbols
DO_UPX = 0
DEBUG = 1 
PWLIB_SUPPORT = 0
FSANITIZE = 1
CFLAGS += -funwind-tables
## Object files that compose the target(s)



OBJS := $(SRC_DIR)/nvrsrv\
## Libraries to include in shared object file
LIB_PATH += $(TOP)/../../10-common/lib/release/ubuntu_64
LIB_PATH += $(TOP)/../../10-common/lib/release/ubuntu_64/mediactrllib
LIB_PATH += $(TOP)/../../10-common/lib/release/ubuntu_64/capnvr
#LIB_PATH += $(TOP)/../../10-common/lib/release/ubuntu_64/tcmalloc
#update:2020-10-20 mediactrl lib link path
#LIB_PATH += $(TOP)/../../10-common/exe/ubuntu_64/vaapi-sdk-out/lib


LIBS +=	nvrlog nvrcap nvrcustcap nvrusrmgr nvrnetwork nvrsys nvrproduct nvrpcap nvrproto protobufc sqlite3wrapper debuglog netcbb ddnsc ddnscext upnpc drv nvrgeo \
	sysdbg goaheadhelper charconv nvrpui nvrvtductrl nvrmpu ais algctrl nvrcfg appclt httpclient mxml ghttp go rtspclient kdmposa mediaswitch mrtc nvrqueue netpacket kdmtsps malloc kdvencrypt nvralarm nvrdev nvrupgrade nvrcrc ftpc airp\
	dmsrv nvrrec rpdata rp kdmfileinterface kdmmp4lib asf nvrguard nvrsmtp osp smtp appbase pcap kdmssl kdmcrypto pubsecstack curl nvrcoi nvrunifiedlog\
	cjson rt dl pthread stdc++ udm uuid blkid kdmnatagent rpdownload nvrdynamicplugin lwshelper websockets nvrextdev nvrftp nvrmd5 asan
ifneq ($(SLIBS),) 
LDFLAGS += -Wl,-static
LDFLAGS += $(foreach lib,$(SLIBS),-l$(lib))
endif
## Add driver-specific include directory to the search path

INC_PATH += $(CURDIR)/../include \
		$(CURDIR)/../../common \
		$(CURDIR)/../../nvrcfg/include \
		$(CURDIR)/../../nvrpui/include \
		$(CURDIR)/../../nvrvtductrl/include \
		$(CURDIR)/../../nvrusrmgr/include \
		$(CURDIR)/../../nvrcap/include \
		$(CURDIR)/../../nvrnetwork/include \
		$(CURDIR)/../../nvrsys/include \
		$(CURDIR)/../../nvrdev/include \
		$(CURDIR)/../../nvralarm/include \
		$(CURDIR)/../../nvrlog/include \
		$(CURDIR)/../../nvrguard/include \
		$(CURDIR)/../../nvrsmtp/include \
		$(CURDIR)/../../nvrmpu/include \
		$(CURDIR)/../../nvrpcap/include \
		$(CURDIR)/../../airp/include \
		$(CURDIR)/../../nvrextdevmgr/include \
		$(CURDIR)/../../../10-common/include/service \
		$(CURDIR)/../../../10-common/include/cbb/debuglog\
		$(CURDIR)/../../../10-common/include/cbb/mbnet\
		$(CURDIR)/../../../10-common/include/system\
		$(CURDIR)/../../../10-common/include/hal/sysdbg\
		$(CURDIR)/../../../10-common/include/cbb/protobuf \
		$(CURDIR)/../../../10-common/include/cbb/osp \
		$(CURDIR)/../../../10-common/include/cbb/sqilte \
		$(CURDIR)/../../../10-common/include/cbb/kshield\
		$(CURDIR)/../../../10-common/include/cbb/appclt\
		$(CURDIR)/../../../10-common/include/cbb/mediaswitch\
		$(CURDIR)/../../../10-common/include/app\
		$(CURDIR)/../../../10-common/include/cbb/libwebsockets\
		$(CURDIR)/../../../10-common/include/cbb/libwebsockets/config_file/linux\
		$(CURDIR)/../../../10-common/include/app/lwshelper
CFLAGS += -D_SKYLATE_
CFLAGS += -D_NOTINITMPU_
#CFLAGS += -D_TCMALLOC_
#CFLAGS += -fno-builtin-malloc -fno-builtin-calloc -fno-builtin-realloc -fno-builtin-free

ifeq ($(PWLIB_SUPPORT),1)
   INC_PATH += $(PWLIBDIR)/include/ptlib/unix $(PWLIBDIR)/include
endif


INSTALL_APP_PATH := ../../../10-common/version/debug/ubuntu_64_sanitizer/bin/
LDFLAGS += -Wl,-rpath=$(TOP)/../../10-common/lib/release/ubuntu_64/mediactrllib -Wl,-rpath=$(TOP)/../../10-common/lib/release/ubuntu_64/ -Wl,-rpath=$(TOP)/../../10-common/exe/ubuntu_64/vaapi-sdk-out/lib
include $(COMM_DIR)/common.mk


