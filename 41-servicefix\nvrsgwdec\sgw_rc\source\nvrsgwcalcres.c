/*
 * nvrsgwcalcres.c
 *
 *  Created on: 2021年1月28日
 *      Author: kdm_work
 */

#include "debuglog.h"
#include "nvrsgwcore.h"
#include "nvrsgwcommon.h"
#include "nvrsys_in.h"
#include <pthread.h>


#define RES_BITMAP_MASK             (0xFFFFFFFF)

#define RES_BITMAP_LEN              32          ///(NVR_SGW_MAX_RES_WIDTH / RES_WIDTH_UNIT)    ///< 32
#define RES_BITMAP_MAX_NUM          12          ///(NVR_SGW_MAX_RES_HEIGHT / RES_HEIGHT_UNIT)  ///< 12

#define UNIT_LEN_MASK(n)            ((((unsigned long long)0x1 << n) - 1) & 0xffffffff)
                                                                                ///< bitmap:1111 ---> 2^4 -1
                                                                                ///< bitmap:111 ---> 2^3 -1

#define GET_RES_WIDTH_BASE(n)       ((n) / RES_WIDTH_UNIT)
#define GET_RES_WIDTH_LEFT(n)       ((n) % RES_WIDTH_UNIT)
#define GET_RES_WIDTH_UNIT(n)       ((n + RES_WIDTH_UNIT - 1) / RES_WIDTH_UNIT)
#define GET_RES_HEIGHT_UNIT(n)      ((n + RES_HEIGHT_UNIT - 1) / RES_HEIGHT_UNIT)
#define CHECK_MASK(n, diff)         (UNIT_LEN_MASK(n) << (RES_BITMAP_LEN - n - diff))                         ///<


#define MASK_GET_BIT(x, bit)        ((x & (1 << (bit))) >> (bit))   /* 获取第xbit位 */

typedef enum
{
    RES_BITMAP_INFO_LEN = 0,            ///< bitmap中连续的可用单位长度
    RES_BITMAP_INFO_POS,                ///< bitmap中连续的可用单位开始索引（从左往右）
    RES_BITMAP_INFO_BEGIN,              ///< bitmap中连续的可用单位开始索引（从左往右）
    RES_BITMAP_INFO_END,                ///< bitmap中连续的可用单位开始索引（从左往右）
    RES_BITMAP_INFO_NUM,

}ENvrSgwabyBitMapInfo;

typedef struct
{
    u8 byPos;
    u8 byUnit;
    u8 byBlen;
    u8 byElen;
}TNvrSgwResFreeInfo;

typedef struct
{
    s32 nArrayCount;
    u8 abyBitMapInfo[RES_BITMAP_LEN][RES_BITMAP_INFO_NUM];
    TNvrSgwResFreeInfo tFreeInfo[RES_BITMAP_LEN];
}TNvrSgwResAvailableInfo;

typedef struct
{
    s32 nArrayCount;
    s32 nArrayAudCount;
    TNvrSgwResPosInfo atResPos[NVR_SGW_MAX_CHN_VID_NUM + NVR_SGW_MAX_CHN_AUD_NUM];
}TNvrSgwResUseInfo;

typedef struct
{
    s32 nArrayCount;
    TNvrSgwResPosInfo atResPos[NVR_SGW_MAX_VID_NUM + NVR_SGW_MAX_AUD_NUM];
}TNvrSgwResAllUseInfo;


TNvrSgwResUseInfo g_atResUseMap[NVR_SGW_MAX_CHN_NUM];
TNvrSgwResAllUseInfo g_atStreamUseInfo[NVR_SGW_MAX_CHIP_CHN_NUM];
u32 g_adwResMap[NVR_SGW_MAX_CHN_NUM][RES_BITMAP_MAX_NUM] = {0};
u32 g_adwResMapSpe[NVR_SGW_MAX_CHN_NUM][RES_BITMAP_MAX_NUM][RES_BITMAP_LEN] = {0};
u32 g_dwAudDecIdMap = 0xffffffff;
u32 g_adwVidDecIdMap[NVR_SGW_MAX_CHIP_CHN_NUM] = {0xffffffff, 0xffffffff};
u16 g_adwVidDecWinMap[NVR_SGW_MAX_CHN_NUM] = {0xffff, 0xffff, 0xffff, 0xffff};

TNvrSgwResUseInfo g_atResUseMap_bk[NVR_SGW_MAX_CHN_NUM];
TNvrSgwResAllUseInfo g_atStreamUseInfo_bk[NVR_SGW_MAX_CHIP_CHN_NUM];
u32 g_adwResMap_bk[NVR_SGW_MAX_CHN_NUM][RES_BITMAP_MAX_NUM] = {0};
u32 g_adwResMapSpe_bk[NVR_SGW_MAX_CHN_NUM][RES_BITMAP_MAX_NUM][RES_BITMAP_LEN] = {0};
u32 g_dwAudDecIdMap_bk = 0xffffffff;
u32 g_adwVidDecIdMap_bk[NVR_SGW_MAX_CHIP_CHN_NUM] = {0xffffffff, 0xffffffff};
u16 g_adwVidDecWinMap_bk[NVR_SGW_MAX_CHN_NUM] = {0xffff, 0xffff, 0xffff, 0xffff};

static pthread_mutex_t  res_apply_lock;

static s32 g_nBitmapPrint = 1;

s32 NvrSgwFreeResToBitMap(s32 nChn, u16 x, u16 y, u16 nUnitLen, u16 nDepth);

void NvrSgwResBitMapUpdate(s32 nChn, BOOL bUse, u32 dwMask, s32 nIndex, s32 nDepth)
{
    s32 i = 0;

    if ((nIndex + nDepth) > RES_BITMAP_MAX_NUM)
    {
        SGWPRINTERR("depth large  err ! %2d %2d %2d \n", nIndex, nDepth, RES_BITMAP_MAX_NUM);
        return;
    }

    if (g_nBitmapPrint > 1)
    {
        SGWPRINTDBG("nChn:%d bUse:%d nIndex:%d nDepth:%d mask:%x\n", nChn, bUse, nIndex, nDepth, dwMask);
    }

    for (i = nIndex; i < (nIndex + nDepth); i++)
    {
        if (bUse)
        {
            g_adwResMap[nChn][i] = g_adwResMap[nChn][i] & (~dwMask);
        }
        else
        {
            g_adwResMap[nChn][i] = g_adwResMap[nChn][i] | dwMask;
        }
    }
}

void NvrSgwAudDecIdBitMapUpdate(BOOL bUse, s32 nIndex)
{
    if (nIndex > 0)
    {
        nIndex -= 1;

        if (g_nBitmapPrint > 1)
        {
            SGWPRINTDBG("bUse:%d nIndex:%d \n", bUse, nIndex);
        }
        if (bUse)
        {
            g_dwAudDecIdMap = g_dwAudDecIdMap & (~(0x1 << nIndex));
        }
        else
        {
            g_dwAudDecIdMap = g_dwAudDecIdMap | (0x1 << nIndex);
        }
    }

}

void NvrSgwVidDecIdBitMapUpdate(s32 nChn, BOOL bUse, s32 nIndex)
{
    if (nIndex > 0)
    {
        nIndex -= 1;
        if (g_nBitmapPrint > 1)
        {
            SGWPRINTDBG("nChn:%d  bUse:%d nIndex:%d \n", nChn, bUse, nIndex);
        }
        if (bUse)
        {
            g_adwVidDecIdMap[nChn / NVR_SGW_MAX_CHIP_CHN_NUM] = g_adwVidDecIdMap[nChn / NVR_SGW_MAX_CHIP_CHN_NUM] & (~(0x1 << nIndex));
        }
        else
        {
            g_adwVidDecIdMap[nChn / NVR_SGW_MAX_CHIP_CHN_NUM] = g_adwVidDecIdMap[nChn / NVR_SGW_MAX_CHIP_CHN_NUM] | (0x1 << nIndex);
        }
    }
}

void NvrSgwVidWinIdBitMapUpdate(s32 nChn, BOOL bUse, s32 nIndex)
{
    if (nIndex > 0)
    {
        nIndex -= 1;
        if (g_nBitmapPrint > 1)
        {
            SGWPRINTDBG("nChn:%d bUse:%d nIndex:%d \n", nChn, bUse, nIndex);
        }
        if (bUse)
        {
            g_adwVidDecWinMap[nChn] = g_adwVidDecWinMap[nChn] & (~(0x1 << nIndex));
        }
        else
        {
            g_adwVidDecWinMap[nChn] = g_adwVidDecWinMap[nChn] | (0x1 << nIndex);
        }
    }
}

s32 NvrSgwGetVidDecWinFromMap(s32 nChn)
{
    s32 i = 0;
    s32 nDecWinId = 0;

    u32 dwBitMap = g_adwVidDecWinMap[nChn];
    for (i = 0; i < NVR_SGW_MAX_CHN_VID_NUM; i++)
    {
        if (MASK_GET_BIT(dwBitMap, i))
        {
            nDecWinId = i + 1;
            break;
        }
    }
    if (g_nBitmapPrint > 1)
    {
        SGWPRINTDBG("nIndex:%d \n", nDecWinId);
    }
    return nDecWinId;
}

s32 NvrSgwGetVidDecFromMap(s32 nChn)
{
    s32 i = 0;
    s32 nVidDecId = 0;
    u32 dwBitMap = g_adwVidDecIdMap[nChn/NVR_SGW_MAX_CHIP_CHN_NUM];

    for (i = 0; i < NVR_SGW_MAX_VID_NUM / NVR_SGW_MAX_CHIP_CHN_NUM; i++)
    {
        if (MASK_GET_BIT(dwBitMap, i))
        {
            nVidDecId = i + 1;
            break;
        }
    }
    if (g_nBitmapPrint > 1)
    {
        SGWPRINTDBG("nIndex:%d \n", nVidDecId);
    }
    return nVidDecId;
}

s32 NvrSgwGetAudDecFromMap(s32 nChn)
{
    s32 i = 0;
    s32 nAudDecId = 0;
#ifdef _use_aud_chn_8_
    s32 nRange = nChn;
    s32 nDiff = NVR_SGW_MAX_CHN_AUD_NUM;
#else
    s32 nRange = nChn / NVR_SGW_MAX_CHIP_CHN_NUM;
    s32 nDiff = NVR_SGW_MAX_CHN_AUD_NUM  * NVR_SGW_MAX_CHIP_CHN_NUM;
#endif

    if (nChn >= 0 && nChn < 4)
    {
        for (i = nRange * nDiff; i < (nRange + 1) * nDiff; i++)
        {
            if (MASK_GET_BIT(g_dwAudDecIdMap, i))
            {
                nAudDecId = i + 1;
                break;
            }
        }
    }
    else
    {
        for (i = 0; i < NVR_SGW_MAX_AUD_NUM; i++)
        {
            if (MASK_GET_BIT(g_dwAudDecIdMap, i))
            {
                nAudDecId = i + 1;
                break;
            }
        }
    }
    if (g_nBitmapPrint > 1)
    {
        SGWPRINTDBG("nIndex:%d \n", nAudDecId);
    }
    return nAudDecId;
}

s32 NvrSgwAudDecMapCheck(s32 nDecId)
{
    s32 nAudDecId = 0;

    if (MASK_GET_BIT(g_dwAudDecIdMap, nDecId))
    {
        nAudDecId = nDecId + 1;
    }

    if (g_nBitmapPrint > 1)
    {
        SGWPRINTDBG("check nIndex:%d return %d\n", nDecId, nAudDecId);
    }
    return nAudDecId;
}

s32 NvrSgwGetResMapHeightCheck(TNvrSgwResPosInfo *ptPosInfo, s32 nHIndex, TNvrSgwResAvailableInfo *ptFreeInfo, s32 nArrIndex)
{
    u32 dwBitMapTmp = 0;
    s32 i = 0;
    s32 nRet = 0;
    s32 nBlen = 0, nElen = 0, nBpos = 0, nEpos = 0, nFreelen = 0;
    s32 nDepth = ptPosInfo->h;
    u32 dwMask = 0;
    s32 nMaskW = 0;

    if ((nHIndex + nDepth) > RES_BITMAP_MAX_NUM)
    {
        SGWPRINTDBG("depth large  err ! %2d %2d %2d \n", nHIndex, nDepth, RES_BITMAP_MAX_NUM);
        nRet = -1;
        return nRet;
    }
    nBpos = ptFreeInfo->abyBitMapInfo[nArrIndex][RES_BITMAP_INFO_POS] - 1;
    nEpos = ptFreeInfo->abyBitMapInfo[nArrIndex][RES_BITMAP_INFO_POS] +  ptFreeInfo->abyBitMapInfo[nArrIndex][RES_BITMAP_INFO_LEN];
    nBlen = ptFreeInfo->abyBitMapInfo[nArrIndex][RES_BITMAP_INFO_BEGIN];
    nElen = ptFreeInfo->abyBitMapInfo[nArrIndex][RES_BITMAP_INFO_END];

    nMaskW = ptPosInfo->w;

    SGWPRINTDBG("b:%d e:%d bp:%d ep:%d wa:%d (w:%d %d %d)\n", nBlen, nElen, nBpos, nEpos, ptPosInfo->byWadd,
            nMaskW, nMaskW * RES_WIDTH_UNIT, ptPosInfo->ow);
    if(ptPosInfo->byWadd)
    {
        if ((nMaskW * RES_WIDTH_UNIT + nBlen + nElen) < ptPosInfo->ow)
        {
            nMaskW += 1;
        }
    }

    dwMask = CHECK_MASK(nMaskW, ptFreeInfo->abyBitMapInfo[nArrIndex][RES_BITMAP_INFO_POS]);

    if (g_nBitmapPrint > 1)
    {
        SGWPRINTDBG("check mask 0x%x -----> 0x%x (offset:%d)\n", UNIT_LEN_MASK(nMaskW), dwMask,
                ptFreeInfo->abyBitMapInfo[nArrIndex][RES_BITMAP_INFO_POS]);
    }

    for (i = nHIndex; i < nHIndex + nDepth; i++)
    {
        dwBitMapTmp = g_adwResMap[ptPosInfo->nChn][i];

        if ((dwBitMapTmp & dwMask) < dwMask)
        {
            SGWPRINTDBG("index %2d check err ! %2d %2d 0x%x 0x%x \n", i, nHIndex, nDepth, dwMask, dwBitMapTmp);
            nRet = -1;
            break;
        }

        if (nBlen)
        {
            nFreelen = RES_WIDTH_UNIT - ((g_adwResMapSpe[ptPosInfo->nChn][nHIndex][nEpos] >> 8) & 0xff);
            if (nFreelen < nBlen)
            {
                SGWPRINTDBG("index %2d check nBlen err ! %2d %2d 0x%x 0x%x \n", i, nHIndex, nDepth,
                        nBlen, g_adwResMapSpe[ptPosInfo->nChn][nHIndex][nBpos]);
                nRet = -1;
                break;
            }
        }

        if (nElen)
        {
            nFreelen = RES_WIDTH_UNIT - (g_adwResMapSpe[ptPosInfo->nChn][nHIndex][nBpos] & 0xff);
            if ( nFreelen < nElen)
            {
                SGWPRINTDBG("index %2d check nElen err ! %2d %2d 0x%x 0x%x \n", i, nHIndex, nDepth,
                        nElen, g_adwResMapSpe[ptPosInfo->nChn][nHIndex][nEpos]);
                nRet = -1;
                break;
            }
        }
    }

    return nRet;
}


void NvrSgwBitMapInfoUpdate(s32 nPos, s32 nCount, s32 nBLen, s32 nELen, TNvrSgwResAvailableInfo *ptInfo)
{
    s32 i = 0;
    u8 abyBitMapInfo[RES_BITMAP_LEN][RES_BITMAP_INFO_NUM];
    s32 nFree = 0, nArrayFree = 0;;

    mzero(abyBitMapInfo);

    if (ptInfo->nArrayCount)
    {
        memcpy(abyBitMapInfo, ptInfo->abyBitMapInfo, sizeof(abyBitMapInfo[0]) * ptInfo->nArrayCount);

        nFree = nCount * RES_WIDTH_UNIT + nBLen + nELen;
        for (i = 0; i < ptInfo->nArrayCount; i++ )
        {
            nArrayFree = ptInfo->abyBitMapInfo[i][RES_BITMAP_INFO_LEN] * RES_WIDTH_UNIT
                    + ptInfo->abyBitMapInfo[i][RES_BITMAP_INFO_BEGIN] + ptInfo->abyBitMapInfo[i][RES_BITMAP_INFO_END];
            if (nFree <= nArrayFree)
            {
                ///< find pos
                if (g_nBitmapPrint > 1)
                {
                    SGWPRINTDBG("find pos %d , count:%d\n", i, ptInfo->nArrayCount);
                }
                break;
            }
        }
    }

    ptInfo->abyBitMapInfo[i][RES_BITMAP_INFO_LEN] = nCount;
    ptInfo->abyBitMapInfo[i][RES_BITMAP_INFO_POS] = nPos;
    ptInfo->abyBitMapInfo[i][RES_BITMAP_INFO_BEGIN] = nBLen;
    ptInfo->abyBitMapInfo[i][RES_BITMAP_INFO_END] = nELen;

    if (g_nBitmapPrint > 1)
    {
        SGWPRINTDBG("add index:%d, pos %d, len:%d b:%d e:%d\n", i, nPos, nCount, nBLen, nELen);
    }

    if (i != ptInfo->nArrayCount)
    {
        memcpy(ptInfo->abyBitMapInfo[i + 1], abyBitMapInfo[i], sizeof(abyBitMapInfo[0]) * (ptInfo->nArrayCount - i));
    }

    ptInfo->nArrayCount += 1;
}

s32 NvrSgwGetResMapWidthCheck(TNvrSgwResPosInfo *ptPosInfo, s32 nHIndex, TNvrSgwResAvailableInfo *ptFreeInfo)
{
    u32 dwBitMap = 0, dwMask = 0;
    s32 i = 0;
    s32 nFreeLen = 0, nCount = 0, nBLen = 0, nELen = 0;
    BOOL bZero = FALSE, bFFree = TRUE;
    s32 nPos = 0;
    u32 dwSpecMap = 0;

    dwBitMap = g_adwResMap[ptPosInfo->nChn][nHIndex];
    dwMask = UNIT_LEN_MASK(ptPosInfo->w);

    ///< 判断bitmap值大于需要的连续mask的值，才认为存在
    if (dwBitMap >= dwMask)
    {
        for (i = 1; i <= RES_BITMAP_LEN; i++)
        {
            nPos = i - 1;
            if (g_nBitmapPrint > 4)
            {
                SGWPRINTDBG("bit:%d pos:%d zero:%d bFFree:%d\n", MASK_GET_BIT(dwBitMap, RES_BITMAP_LEN - i), nPos, bZero, bFFree);
            }
            if (MASK_GET_BIT(dwBitMap, RES_BITMAP_LEN - i))
            {
                if (bZero)
                {
                    dwSpecMap = g_adwResMapSpe[ptPosInfo->nChn][nHIndex][nPos - 1];
                    ///< 判断前一个不可用是否有剩余
                    if (g_nBitmapPrint > 4)
                    {
                        SGWPRINTDBG("get pos %d, 0x%x\n", nPos, dwSpecMap);
                    }
                    if (dwSpecMap && ptPosInfo->byWadd)
                    {
                        if (0 == (dwSpecMap & 0xff))
                        {
                            nBLen = RES_WIDTH_UNIT - ((dwSpecMap >> 8) & 0xff);
                        }
                    }
                    else
                    {
                        nBLen = 0;
                    }
                }

                nCount++;
                bFFree = TRUE;
                bZero = FALSE;
            }
            else
            {
                bZero = TRUE;
                ///< 判断当前不可用是否有剩余
                ///< 判断前一个不可用是否有剩余
                if (bFFree)
                {
                    dwSpecMap = g_adwResMapSpe[ptPosInfo->nChn][nHIndex][nPos];
                    if (g_nBitmapPrint > 4)
                    {
                        SGWPRINTDBG("zero pos %d, 0x%x\n", nPos, dwSpecMap);
                    }
                    if (dwSpecMap && ptPosInfo->byWadd)
                    {
                        if (0 == ((dwSpecMap >> 8) & 0xff))
                        {
                            nELen = RES_WIDTH_UNIT - (dwSpecMap & 0xff);
                        }
                    }
                    else
                    {
                        nELen = 0;
                    }

                    bFFree = FALSE;
                }
            }
            if (g_nBitmapPrint > 4)
            {
                SGWPRINTDBG("i:%2d, count:%2d, bZero:%d nBLen:%2d nELen:%d\n", i, nCount, bZero, nBLen, nELen);
            }
            if (bZero || i == (RES_BITMAP_LEN))
            {
                nPos = bZero ? i - 1 - nCount : RES_BITMAP_LEN - nCount;

                nFreeLen = nCount * RES_WIDTH_UNIT + nBLen + nELen;
                if (nFreeLen >= ptPosInfo->ow)
                {
                    NvrSgwBitMapInfoUpdate(nPos, nCount, nBLen, nELen, ptFreeInfo);
                    if (g_nBitmapPrint > 1)
                    {
                        SGWPRINTDBG("index:%2d, i:%2d, count:%2d, pos:%2d--%2d, b:%d, e:%d is empty (%d)\n",
                            nHIndex, i, nCount, nPos, nPos + nCount - 1, nBLen, nELen, nFreeLen);
                    }
                    ///< 符合长度，记录
                }

                nCount = 0;
            }
        }
    }
    else
    {
//        SGWPRINTINFO("bitmap %d not empty \n", nHIndex);
    }

    return 0;
}


// 检查两个矩形是否重叠
int do_rectangles_overlap(TNvrSgwResPosInfo rect1, TNvrSgwResPosInfo rect2)
{
    // 检查是否不重叠
    int x1 = rect1.x * RES_WIDTH_UNIT - rect1.byBadd;
    int x2 = rect2.x * RES_WIDTH_UNIT - rect2.byBadd;
    int y1 = rect1.y * RES_HEIGHT_UNIT;
    int y2 = rect2.y * RES_HEIGHT_UNIT;
    int h1 = rect1.h * RES_HEIGHT_UNIT;
    int h2 = rect2.h * RES_HEIGHT_UNIT;

    if ((x1 + rect1.ow) <= x2 ||  // rect1 在 rect2 左侧
           x1 >= (x2 + rect2.ow )||  // rect1 在 rect2 右侧
            (y1 + h1) <= y2 ||  // rect1 在 rect2 上侧
            y1 >= (y2 + h2))
    {  // rect1 在 rect2 下侧
        return 0;
    }

    OspPrintf(TRUE, FALSE, "x:%-4d, y:%-4d, w:%-4d, h:%-4d \t", x1, y1, rect1.ow, h1);
    OspPrintf(TRUE, FALSE, "x:%-4d, y:%-4d, w:%-4d, h:%-4d \n", x2, y2, rect2.ow, h2);

    if ( (x1 + rect1.ow)  > 3840|| (x2 + rect2.ow)  > 3840
            || (y1 + h1) > 2160 || (y2 + h2) > 2160)
    {
        return 1;
    }

    return 1;
}

// 检查所有矩形是否有重叠
int check_all_rectangles(TNvrSgwResPosInfo rects[], int n)
{
    for (int i = 0; i < n; i++)
    {
        for (int j = i + 1; j < n; j++)
        {
            if (do_rectangles_overlap(rects[i], rects[j]))
            {
                SGWPRINTERR("check_all_rectangles true i = %d  j = %d\n", i , j);
                return 1;  // 存在重叠
            }
        }
    }
    return 0;  // 无重叠
}

s32 NvrSgwCheckAllRects(s32 nChn)
{
    if(check_all_rectangles(g_atResUseMap[nChn].atResPos, g_atResUseMap[nChn].nArrayCount))
    {
        SGWPRINTERR("check_all_rectangles true chn %d \n", nChn);

        return 1;
    }

    return 0;
}

void NvrSgwShowResBitMap(s32 nChip)
{
    s32 i = 0, j = 0, k = 0;
    s32 nChn = nChip * 2;

    nChn = nChn > 2 ? 2 : nChn;

    OspPrintf(TRUE, FALSE, "%d   0                  10                  20                  30\n", nChn);
    for (i = 0; i < RES_BITMAP_MAX_NUM; i++)
    {
        OspPrintf(TRUE, FALSE, "%2d: ", i);
        for (j = 0; j < RES_BITMAP_LEN; j++)
        {
            OspPrintf(TRUE, FALSE, "%d ",  MASK_GET_BIT(g_adwResMap[nChn][i], RES_BITMAP_LEN - 1 - j));
        }

        OspPrintf(TRUE, FALSE, "\n");
    }
    OspPrintf(TRUE, FALSE, "\n");
    for (i = 0; i < RES_BITMAP_MAX_NUM; i++)
    {
        OspPrintf(TRUE, FALSE, "%2d: ", i);
        for (j = 0; j < RES_BITMAP_LEN; j++)
        {
            OspPrintf(TRUE, FALSE, "%d ",  MASK_GET_BIT(g_adwResMap[nChn + 1][i], RES_BITMAP_LEN - 1 - j));
        }

        OspPrintf(TRUE, FALSE, "\n");
    }

    OspPrintf(TRUE, FALSE, "use list   x   y   w   h  \n");

    k = nChn + 2;
    for (; nChn < k; nChn++ )
    {
        for (i = 0; i < g_atResUseMap[nChn].nArrayCount; i++)
        {
            OspPrintf(TRUE, FALSE, "index:%2d %3d %3d %3d %3d  %-3u [%d] vid:%2d aud:%2d win:%2d wa:%2d b:%2d e:%2d\n",  i,
                    g_atResUseMap[nChn].atResPos[i].x,
                    g_atResUseMap[nChn].atResPos[i].y,
                    g_atResUseMap[nChn].atResPos[i].w,
                    g_atResUseMap[nChn].atResPos[i].h,
                    g_atResUseMap[nChn].atResPos[i].nUniq,
                    g_atResUseMap[nChn].atResPos[i].nChn,
                    g_atResUseMap[nChn].atResPos[i].nVidDecId,
                    g_atResUseMap[nChn].atResPos[i].nAudDecId,
                    g_atResUseMap[nChn].atResPos[i].nChnWinIndex,
                    g_atResUseMap[nChn].atResPos[i].byWadd,
                    g_atResUseMap[nChn].atResPos[i].byBadd,
                    g_atResUseMap[nChn].atResPos[i].byEadd);
        }
        OspPrintf(TRUE, FALSE, "\n");
        OspPrintf(TRUE, FALSE, "total %d \n", g_atResUseMap[nChn].nArrayCount);
        if(check_all_rectangles(g_atResUseMap[nChn].atResPos, g_atResUseMap[nChn].nArrayCount))
        {
            SGWPRINTERR("check_all_rectangles true chn %d\n", nChn);
        }
    }
}

void NvrSgwShowChnResBitMap(s32 nChn)
{
    s32 i = 0, j = 0;

    OspPrintf(TRUE, FALSE, "%d   0                  10                  20                  30\n", nChn);
    for (i = 0; i < RES_BITMAP_MAX_NUM; i++)
    {
        OspPrintf(TRUE, FALSE, "%2d: ", i);
        for (j = 0; j < RES_BITMAP_LEN; j++)
        {
            OspPrintf(TRUE, FALSE, "%d ",  MASK_GET_BIT(g_adwResMap[nChn][i], RES_BITMAP_LEN - 1 - j));
        }

        OspPrintf(TRUE, FALSE, "\n");
    }
}

void NvrSgwShowAllStreamBitMap(s32 nChip)
{
    s32 i = 0;

    for (i = 0; i < g_atStreamUseInfo[nChip].nArrayCount; i++)
    {
        OspPrintf(TRUE, FALSE, "all list   x   y   w   h  \n");
        OspPrintf(TRUE, FALSE, "index:%2d %3d %3d %3d %3d  %-3u [%d] vid:%d aud:%d win:%d\n",  i,
                g_atStreamUseInfo[nChip].atResPos[i].x,
                g_atStreamUseInfo[nChip].atResPos[i].y,
                g_atStreamUseInfo[nChip].atResPos[i].w,
                g_atStreamUseInfo[nChip].atResPos[i].h,
                g_atStreamUseInfo[nChip].atResPos[i].nUniq,
                g_atStreamUseInfo[nChip].atResPos[i].nChn,
                g_atStreamUseInfo[nChip].atResPos[i].nVidDecId,
                g_atStreamUseInfo[nChip].atResPos[i].nAudDecId,
                g_atStreamUseInfo[nChip].atResPos[i].nChnWinIndex);
    }

}

s32 NvrSgwStreamChipUseMapUpdate(TNvrSgwResPosInfo *ptPosInfo)
{
    TNvrSgwResAllUseInfo *ptStreamInfo = NULL;
    TNvrSgwResPosInfo atResPos[NVR_SGW_MAX_VID_NUM + NVR_SGW_MAX_AUD_NUM];
    s32 i = 0;
    s32  w = 0,  h = 0;

    ptStreamInfo = &g_atStreamUseInfo[ptPosInfo->nChn / 2];

    w = ptPosInfo->w;
    h = ptPosInfo->h;

    if (ptStreamInfo->nArrayCount)
    {
        memcpy(atResPos, ptStreamInfo->atResPos, sizeof(atResPos[0]) * ptStreamInfo->nArrayCount);

        for (i = 0; i < ptStreamInfo->nArrayCount; i++ )
        {
            if (w <= ptStreamInfo->atResPos[i].w)
            {
                if (h <= ptStreamInfo->atResPos[i].h)
                {
                    ///< wh<= find pos
                    SGWPRINTFREQ("wh<= find res pos %d , count:%d\n", i, ptStreamInfo->nArrayCount);
                    break;
                }
                else
                {
                    ///< w<=  h> find next pos
                    if (w < ptStreamInfo->atResPos[i].w)
                    {
                        SGWPRINTFREQ("w< find res pos %d , count:%d\n", i, ptStreamInfo->nArrayCount);
                        break;
                    }

                    if (i < (ptStreamInfo->nArrayCount - 1))
                    {
                        if (ptStreamInfo->atResPos[i].w < ptStreamInfo->atResPos[i + 1].w)
                        {
                            if (w == ptStreamInfo->atResPos[i].w)
                            {
                                i++;
                            }
                            SGWPRINTFREQ("find res pos %d , count:%d\n", i, ptStreamInfo->nArrayCount);
                            break;
                        }
                    }
                    else if (i == (ptStreamInfo->nArrayCount - 1))
                    {
                        i++;
                        SGWPRINTFREQ("last find res pos %d , count:%d\n", i, ptStreamInfo->nArrayCount);
                        break;
                    }
                    else
                    {
                        SGWPRINTFREQ("find res pos %d , count:%d\n", i, ptStreamInfo->nArrayCount);
                        break;
                    }
                }

            }
        }
    }

    memcpy(&ptStreamInfo->atResPos[i], ptPosInfo, sizeof(ptStreamInfo->atResPos[0]));

    if (i != ptStreamInfo->nArrayCount)
    {
        memcpy(&ptStreamInfo->atResPos[i + 1], &atResPos[i], sizeof(atResPos[0]) * (ptStreamInfo->nArrayCount - i));
    }

    ptStreamInfo->nArrayCount += 1;

    return ptStreamInfo->nArrayCount;
}


void NvrSgwSpecMapArrUpdate(TNvrSgwResPosInfo *ptPosInfo, u8 byOpt)
{
    s32 i = 0;
    s32 x = 0, y = 0, w = 0,  h = 0, nChn = 0;
    nChn = ptPosInfo->nChn;
    x = ptPosInfo->x;
    y = ptPosInfo->y;
    w = ptPosInfo->w;
    h = ptPosInfo->h;

    for (i = y; i < (y + h); i++)
    {
        if (ptPosInfo->byBadd && x > 0)
        {
            if (g_nBitmapPrint > 4)
            {
                SGWPRINTDBG("orig 0x%x, %d %d 0x%x(%d)\n", g_adwResMapSpe[nChn][i][x - 1], i, x - 1, ptPosInfo->byBadd, ptPosInfo->byBadd);
            }
            if (1 == byOpt)
            {
                g_adwResMapSpe[nChn][i][x - 1] |=  ptPosInfo->byBadd;
            }
            else
            {
                g_adwResMapSpe[nChn][i][x - 1] &=  (~0xff);
                if (0 == g_adwResMapSpe[nChn][i][x - 1])
                {
                    NvrSgwFreeResToBitMap(nChn, x - 1, i, 1, 1);
                }
            }
            if (g_nBitmapPrint > 4)
            {
                SGWPRINTDBG("orig byBadd 0x%x\n", g_adwResMapSpe[nChn][i][x - 1]);
            }
        }
        if (ptPosInfo->byEadd && (x + w) < 32)
        {
            u32 dwTmp = ptPosInfo->byEadd;
            if (g_nBitmapPrint > 4)
            {
                SGWPRINTDBG("orig 0x%x, %d %d 0x%x(%d)\n", g_adwResMapSpe[nChn][i][x + w], i, x + w, ptPosInfo->byEadd, ptPosInfo->byEadd);
            }
            if (1 == byOpt)
            {
                g_adwResMapSpe[nChn][i][x + w] |=  ((dwTmp << 8) & (~0xff));
            }
            else
            {
                g_adwResMapSpe[nChn][i][x + w] &=  (~0xff00);
                if (0 == g_adwResMapSpe[nChn][i][x + w])
                {
                    NvrSgwFreeResToBitMap(nChn, x + w, i, 1, 1);
                }
            }
            if (g_nBitmapPrint > 4)
            {
                SGWPRINTDBG("orig byEadd %x\n", g_adwResMapSpe[nChn][i][x + w]);
            }
        }
    }
}

u32 NvrSgwStreamUseMapUpdate(TNvrSgwResUseInfo *ptResUseInfo, TNvrSgwResPosInfo *ptPosInfo)
{
    static s32 s_nUniq = 1;
    s32 i = 0;
    TNvrSgwResPosInfo atResPos[NVR_SGW_MAX_CHN_VID_NUM];
    s32 x = 0, y = 0, w = 0,  h = 0, nChn = 0;
    u32 dwMask = 0;
    s32 nMaskW = 0;

    if (!ptPosInfo || !ptResUseInfo)
    {
        return 0;
    }
    nChn = ptPosInfo->nChn;
    x = ptPosInfo->x;
    y = ptPosInfo->y;
    w = ptPosInfo->w;
    h = ptPosInfo->h;

    dwMask = UNIT_LEN_MASK(w);

    if (ptResUseInfo->nArrayCount)
    {
        memcpy(atResPos, ptResUseInfo->atResPos, sizeof(atResPos[0]) * ptResUseInfo->nArrayCount);

        for (i = 0; i < ptResUseInfo->nArrayCount; i++ )
        {
            if (w <= ptResUseInfo->atResPos[i].w)
            {
                if (h <= ptResUseInfo->atResPos[i].h)
                {
                    ///< wh<= find pos
                    SGWPRINTFREQ("wh<= find res pos %d , count:%d\n", i, ptResUseInfo->nArrayCount);
                    break;
                }
                else
                {
                    ///< w<=  h> find next pos
                    if (w < ptResUseInfo->atResPos[i].w)
                    {
                        SGWPRINTFREQ("w< find res pos %d , count:%d\n", i, ptResUseInfo->nArrayCount);
                        break;
                    }

                    if (i < (ptResUseInfo->nArrayCount - 1))
                    {
                        if (ptResUseInfo->atResPos[i].w < ptResUseInfo->atResPos[i + 1].w)
                        {
                            if (w == ptResUseInfo->atResPos[i].w)
                            {
                                i++;
                            }
                            SGWPRINTFREQ("find res pos %d , count:%d\n", i, ptResUseInfo->nArrayCount);
                            break;
                        }
                    }
                    else if (i == (ptResUseInfo->nArrayCount - 1))
                    {
                        i++;
                        SGWPRINTFREQ("last find res pos %d , count:%d\n", i, ptResUseInfo->nArrayCount);
                        break;
                    }
                    else
                    {
                        SGWPRINTFREQ("find res pos %d , count:%d\n", i, ptResUseInfo->nArrayCount);
                        break;
                    }
                }

            }
        }
    }

    ptResUseInfo->atResPos[i].x = x;
    ptResUseInfo->atResPos[i].y = y;
    ptResUseInfo->atResPos[i].w = w;
    ptResUseInfo->atResPos[i].h = h;
    ptResUseInfo->atResPos[i].ow = ptPosInfo->ow;
    ptResUseInfo->atResPos[i].byWadd = ptPosInfo->byWadd;
    ptResUseInfo->atResPos[i].byBadd = ptPosInfo->byBadd;
    ptResUseInfo->atResPos[i].byEadd = ptPosInfo->byEadd;
    ptResUseInfo->atResPos[i].nUniq = ptPosInfo->nUniq ? ptPosInfo->nUniq : s_nUniq;
    ptResUseInfo->atResPos[i].nChn = nChn;

    ///< 需要分配音频
    if (0 == ptPosInfo->nUniq && ptPosInfo->nAudDecId)
    {
        ptResUseInfo->nArrayAudCount += 1;
        NvrSgwAudDecIdBitMapUpdate(TRUE, ptPosInfo->nAudDecId);
    }
    ptResUseInfo->atResPos[i].nAudDecId = ptPosInfo->nAudDecId;

    if (w > 0 && h > 0)
    {
        ptResUseInfo->atResPos[i].nVidDecId = ptPosInfo->nVidDecId ? ptPosInfo->nVidDecId : NvrSgwGetVidDecFromMap(nChn);
        ptResUseInfo->atResPos[i].nChnWinIndex = ptPosInfo->nChnWinIndex ? ptPosInfo->nChnWinIndex : NvrSgwGetVidDecWinFromMap(nChn);

        SGWPRINTDBG("b:%d e:%d wa:%d\n", ptPosInfo->byBadd, ptPosInfo->byEadd,  ptPosInfo->byWadd);
        nMaskW = w;
        if (ptPosInfo->byEadd)
        {
            nMaskW = w + 1;
        }
        dwMask = CHECK_MASK(nMaskW, x);
        NvrSgwResBitMapUpdate(nChn, TRUE, dwMask, y, h);
        NvrSgwSpecMapArrUpdate(ptPosInfo, 1);

        if (ptResUseInfo->atResPos[i].nVidDecId > 0)
        {
            NvrSgwVidDecIdBitMapUpdate(nChn, TRUE, ptResUseInfo->atResPos[i].nVidDecId);
        }
        if (ptResUseInfo->atResPos[i].nChnWinIndex > 0)
        {
            NvrSgwVidWinIdBitMapUpdate(nChn, TRUE, ptResUseInfo->atResPos[i].nChnWinIndex);
        }


    }
    else
    {
        ptResUseInfo->atResPos[i].nVidDecId = 0;
        ptResUseInfo->atResPos[i].nChnWinIndex = 0;
    }

    NvrSgwStreamChipUseMapUpdate(&ptResUseInfo->atResPos[i]);

    s_nUniq = (s_nUniq + 1) % 0x7fffffff;

    if (i != ptResUseInfo->nArrayCount)
    {
        memcpy(&ptResUseInfo->atResPos[i + 1], &atResPos[i], sizeof(atResPos[0]) * (ptResUseInfo->nArrayCount - i));
    }

    ptResUseInfo->nArrayCount += 1;

    return ptResUseInfo->atResPos[i].nUniq;
}

u32 NvrSgwGetResFromBitMap(TNvrSgwResPosInfo *ptPosInfo)
{
    s32 i = 0, j = 0;
    u32 dwMask = 0;
    s32 nRet = 0;
    BOOL bFind = FALSE;
    TNvrSgwResAvailableInfo tBitMapInfo;
    u32 dwUniq = 0;
    s32 nChn = 0, nUnitLen = 0, nDepth = 0;
    static s32 s_nCallCount = 0;

    mzero(tBitMapInfo);
    s_nCallCount++;

    if (!ptPosInfo)
    {
        SGWPRINTERR("param in err \n");
        return 0;
    }
    
    nChn = ptPosInfo->nChn;
    nUnitLen = ptPosInfo->w;
    nDepth = ptPosInfo->h;

    dwMask = UNIT_LEN_MASK(nUnitLen);

    if (g_nBitmapPrint > 0)
    {
        SGWPRINTIMP(">>>>> [CALL-%d] NvrSgwGetResFromBitMap ENTER: ow=%d w=%d h=%d chn=%d vid=%d aud=%d uniq=%u wa=%d ba=%d ea=%d\n",
            s_nCallCount,
            ptPosInfo ? ptPosInfo->ow : -1,
            ptPosInfo ? ptPosInfo->w : -1, 
            ptPosInfo ? ptPosInfo->h : -1,
            ptPosInfo ? ptPosInfo->nChn : -1,
            ptPosInfo ? ptPosInfo->nVidDecId : -1,
            ptPosInfo ? ptPosInfo->nAudDecId : -1,
            ptPosInfo ? ptPosInfo->nUniq : 0,
            ptPosInfo ? ptPosInfo->byWadd : -1,
            ptPosInfo ? ptPosInfo->byBadd : -1,
            ptPosInfo ? ptPosInfo->byEadd : -1);
    }

    if ((g_atResUseMap[nChn].nArrayCount - g_atResUseMap[nChn].nArrayAudCount ) >= NVR_SGW_MAX_CHN_VID_NUM)
    {
        return 0;
    }

    if (0 == nUnitLen || 0 == nDepth)
    {
        dwUniq = NvrSgwStreamUseMapUpdate(&g_atResUseMap[nChn], ptPosInfo);
        return dwUniq;
    }

    ///< 单通道内检测宽符合,预留需要申请的高度
    for (i = 0; i < (RES_BITMAP_MAX_NUM - nDepth + 1); i++)
    {
        nRet = NvrSgwGetResMapWidthCheck(ptPosInfo, i, &tBitMapInfo);
        if (tBitMapInfo.nArrayCount > 0)
        {
            for (j = 0; j < tBitMapInfo.nArrayCount; j++)
            {
                nRet = NvrSgwGetResMapHeightCheck(ptPosInfo, i, &tBitMapInfo, j);
                if (0 == nRet)
                {
                    bFind = TRUE;
                    if (g_nBitmapPrint > 0)
                    {
                        SGWPRINTDBG("chn:%d find res succ! index:%d pos: (x:%2d, y:%2d, len:%2d, depth:%2d) is empty \n",
                            nChn, j, tBitMapInfo.abyBitMapInfo[j][RES_BITMAP_INFO_POS], i, nUnitLen, nDepth);
                    }

                    if (ptPosInfo->byPrepare)
                    {
                        dwUniq = 1;
                        SGWPRINTDBG("arrange only test not true apply\n");
                    }
                    else
                    {
                        ptPosInfo->x = tBitMapInfo.abyBitMapInfo[j][RES_BITMAP_INFO_POS];
                        ptPosInfo->y = i;
                        ptPosInfo->w = ptPosInfo->w < tBitMapInfo.abyBitMapInfo[j][RES_BITMAP_INFO_LEN]
                                     ? ptPosInfo->w : tBitMapInfo.abyBitMapInfo[j][RES_BITMAP_INFO_LEN];
                        ptPosInfo->byBadd = tBitMapInfo.abyBitMapInfo[j][RES_BITMAP_INFO_BEGIN];
                        ptPosInfo->byEadd = tBitMapInfo.abyBitMapInfo[j][RES_BITMAP_INFO_END];

                        if (ptPosInfo->byWadd)
                        {
                            if (0 == ptPosInfo->byBadd && 0 == ptPosInfo->byEadd )
                            {
                                ptPosInfo->byEadd = ptPosInfo->byWadd;
                            }
                            else if (ptPosInfo->byBadd && 0 == ptPosInfo->byEadd )
                            {
                                ptPosInfo->byEadd = ptPosInfo->byWadd - ptPosInfo->byBadd;
                            }
                        }

                        dwUniq = NvrSgwStreamUseMapUpdate(&g_atResUseMap[nChn], ptPosInfo);
                    }
                    break;
                }
            }

            mzero(tBitMapInfo);
        }

        if (bFind)
        {
            break;
        }
    }
    if (g_nBitmapPrint > 0)
    {
        SGWPRINTIMP("<<<<< [CALL-%d] NvrSgwGetResFromBitMap EXIT: ow=%d result=%u bFind=%d x=%d y=%d final_w=%d final_h=%d\n",
          s_nCallCount,
          ptPosInfo ? ptPosInfo->ow : -1,
          dwUniq,
          bFind,
          ptPosInfo ? ptPosInfo->x : -1,
          ptPosInfo ? ptPosInfo->y : -1,
          ptPosInfo ? ptPosInfo->w : -1,
          ptPosInfo ? ptPosInfo->h : -1); 
    }
    return dwUniq;
}

s32 NvrSgwFreeResToBitMap(s32 nChn, u16 x, u16 y, u16 nUnitLen, u16 nDepth)
{
    u32 dwMask = 0;

    dwMask = CHECK_MASK(nUnitLen, x);

    SGWPRINTIMP("free x:%2d, y:%2d, len:%d d:%d mask:%x\n", x, y, nUnitLen, nDepth, dwMask);

    NvrSgwResBitMapUpdate(nChn, FALSE, dwMask, y, nDepth);
    return 0;
}

s32 NvrSgwResMapGetAvailableNum(s32 nChn)
{
    s32 i = 0, j = 0;
    s32 nCount = 0;
    u64 timeIn = NvrSysGetCurTimeMSec();

    for (j = 0; j < RES_BITMAP_MAX_NUM; j++)
    {
        for (i = 1; i <= RES_BITMAP_LEN; i++)
        {
            if (MASK_GET_BIT(g_adwResMap[nChn][j], RES_BITMAP_LEN - i))
            {
                nCount++;
            }
        }
    }
    SGWPRINTIMP("get available chn:%d take time %u ms\n", nChn, NvrSysGetCurTimeMSec() - timeIn);

    return nCount;
}


void NvrSgwResMapShow(s32 nChn)
{
    if (nChn > 100)
    {
        s32 i = 0, j  = 0;
        SGWPRINTIMP(" g_adwResMap\n");
        for (j = 0; j < NVR_SGW_MAX_CHN_NUM; j++)
        {
            for (i = 0; i < RES_BITMAP_MAX_NUM; i++)
            {
                SGWPRINTIMP(" g_adwResMap[%d][%-2d] 0x%8x\n", j, i, g_adwResMap[j][i]);
            }
        }
        SGWPRINTIMP(" g_dwAudDecIdMap 0x%x\n", g_dwAudDecIdMap);
        SGWPRINTIMP(" g_dwVidDecIdMap 0x%x  0x%x\n", g_adwVidDecIdMap[0], g_adwVidDecIdMap[1]);
        SGWPRINTIMP(" g_dwVidDecWinMap 0x%4x  0x%4x  0x%4x  0x%4x\n", g_adwVidDecWinMap[0], g_adwVidDecWinMap[1],
                g_adwVidDecWinMap[2], g_adwVidDecWinMap[3]);
    }
    else
    {
        NvrSgwShowResBitMap(nChn);
    }
}

void NvrSgwResMapShowAll(s32 nChn)
{
    s32 i = 0, j = 0, k = 0;

    OspPrintf(TRUE, FALSE, "%d   0                  10                  20                  30\n", nChn);

    for (k = 0; k < 4; k++)
    {
        for (i = 0; i < RES_BITMAP_MAX_NUM; i++)
        {
            OspPrintf(TRUE, FALSE, "%2d: ", i);
            for (j = 0; j < RES_BITMAP_LEN; j++)
            {
                OspPrintf(TRUE, FALSE, "%d ",  MASK_GET_BIT(g_adwResMap[k][i], RES_BITMAP_LEN - 1 - j));
            }

            OspPrintf(TRUE, FALSE, "\n");
        }
        OspPrintf(TRUE, FALSE, "\n");
    }

    NvrSgwShowAllStreamBitMap(0);
    NvrSgwShowAllStreamBitMap(1);
}

s32 NvrSgwGetAvInfoByUniq(u32 dwUniq, TNvrSgwResPosInfo *ptPosInfo)
{
    s32 i = 0, nChn = 0;
    BOOL bFind = 0;

    for (nChn = 0; nChn < NVR_SGW_MAX_CHN_NUM; nChn++)
    {
        for (i = 0; i < g_atResUseMap[nChn].nArrayCount; i++)
        {
            if (dwUniq == g_atResUseMap[nChn].atResPos[i].nUniq)
            {
                memcpy(ptPosInfo, &g_atResUseMap[nChn].atResPos[i], sizeof(g_atResUseMap[nChn].atResPos[i]));
                ptPosInfo->nVidDecId -= 1;
                ptPosInfo->nChnWinIndex -= 1;
                ptPosInfo->nAudDecId -= 1;
                bFind = TRUE;
                break;
            }
        }
    }

    return bFind  ? 1 : 0;
}

BOOL32 g_byManualPullRtmpTest = 0;

u32 NvrSgwStreamAdd(TNvrSgwResPosInfo *ptPosInfo)
{
    s32 i = 0;
    u64 timeIn = NvrSysGetCurTimeMSec();
    u32 dwUniq = 0;
    s32 nChnB = 0, nChnE = 0;


    if (g_nBitmapPrint > 0)
    {
        SGWPRINTDBG("stream add uniq:%d w:%d h:%d vid:%d aud:%d prepare:%d wa:%d ow:%d\n",
                ptPosInfo->nUniq, ptPosInfo->w, ptPosInfo->h,
                ptPosInfo->nVidDecId, ptPosInfo->nAudDecId, ptPosInfo->byPrepare,
                ptPosInfo->byWadd, ptPosInfo->ow);
    }

    if ((0 == ptPosInfo->w || 0 == ptPosInfo->h) && 0 == ptPosInfo->nAudDecId)
    {
        return 0;
    }

    if (ptPosInfo->nAudDecId > 0)
    {
        nChnB = ((ptPosInfo->nAudDecId - 1) / (NVR_SGW_MAX_CHN_AUD_NUM * NVR_SGW_MAX_CHIP_CHN_NUM)) * NVR_SGW_MAX_CHIP_CHN_NUM;
        nChnE = nChnB + NVR_SGW_MAX_CHIP_CHN_NUM;
    }
    else
    {
        nChnB = 0;
        nChnE = nChnB + 4;
    }

	//这里优先根据音频资源进行分配,调试时可以缩小nChnB到nChnE的范围达到强制使用某物理光口的目的
	if(g_byManualPullRtmpTest && ptPosInfo->nAudDecId > 0)
	{
		nChnB = ptPosInfo->nAudDecId / NVR_SGW_MAX_CHN_AUD_NUM;  //0-3
		nChnE = nChnB + 1;
		g_byManualPullRtmpTest = 0;
	}

    for (i = nChnB; i < nChnE; i++)
    {
        ptPosInfo->nChn = i;
        if (g_nBitmapPrint > 3)
        {
            NvrSgwShowChnResBitMap(i);
        }
        dwUniq = NvrSgwGetResFromBitMap(ptPosInfo);
        if (dwUniq)
        {
            break;
        }
    }

    if (dwUniq)
    {
        if (g_nBitmapPrint > 1)
        {
            SGWPRINTDBG("calc take time %u ms\n",NvrSysGetCurTimeMSec() - timeIn);
        }
    }
    else
    {
        SGWPRINTERR("calc w:%d h:%d vid:%d aud:%d fail take time %u ms\n",
                ptPosInfo->w, ptPosInfo->h, ptPosInfo->nVidDecId, ptPosInfo->nAudDecId, NvrSysGetCurTimeMSec() - timeIn);
    }

    return dwUniq;
}

void NvrSgwStreamDel(u32 dwUniq)
{
    SGWPRINTIMP("free dwUniq 0x%x\n", dwUniq);
    TNvrSgwResUseInfo tChnResUseMap;
    TNvrSgwResAllUseInfo tAllResUseMap;
    s32 i = 0, nChn = 0;
    BOOL bDel = FALSE;

    mzero(tChnResUseMap);

    for (nChn = 0; nChn < NVR_SGW_MAX_CHN_NUM; nChn++)
    {
        for (i = 0; i < g_atResUseMap[nChn].nArrayCount; i++)
        {
            if (dwUniq == g_atResUseMap[nChn].atResPos[i].nUniq)
            {
                memcpy(&tChnResUseMap, &g_atResUseMap[nChn], sizeof(tChnResUseMap));

                if (g_atResUseMap[nChn].atResPos[i].nAudDecId > 0)
                {
                    g_atResUseMap[nChn].nArrayAudCount--;
                    NvrSgwAudDecIdBitMapUpdate(FALSE, g_atResUseMap[nChn].atResPos[i].nAudDecId);
                }

                if (g_atResUseMap[nChn].atResPos[i].nVidDecId > 0)
                {
                    NvrSgwVidDecIdBitMapUpdate(nChn, FALSE, g_atResUseMap[nChn].atResPos[i].nVidDecId);
                }

                if (g_atResUseMap[nChn].atResPos[i].nChnWinIndex > 0)
                {
                    NvrSgwVidWinIdBitMapUpdate(nChn, FALSE, g_atResUseMap[nChn].atResPos[i].nChnWinIndex);
                }

                NvrSgwSpecMapArrUpdate(&g_atResUseMap[nChn].atResPos[i], 0);

                SGWPRINTDBG("free dwUniq nChn %d vid %d, aud %d, i:%d(%d) diff:%d\n", nChn, g_atResUseMap[nChn].atResPos[i].nVidDecId,
                        g_atResUseMap[nChn].atResPos[i].nAudDecId, i, g_atResUseMap[nChn].nArrayCount,
                        g_atResUseMap[nChn].nArrayCount - 1 - i);

                if ((i + 1) != g_atResUseMap[nChn].nArrayCount)
                {
                    memcpy(&g_atResUseMap[nChn].atResPos[i], &tChnResUseMap.atResPos[i + 1],
                            sizeof(TNvrSgwResPosInfo) * (g_atResUseMap[nChn].nArrayCount - 1 - i));
                }
                g_atResUseMap[nChn].nArrayCount--;

                NvrSgwFreeResToBitMap(nChn, tChnResUseMap.atResPos[i].x, tChnResUseMap.atResPos[i].y,
                        tChnResUseMap.atResPos[i].w, tChnResUseMap.atResPos[i].h);
                bDel = TRUE;
                break;
            }
        }

        if (bDel)
        {
            TNvrSgwResAllUseInfo *ptStream = &g_atStreamUseInfo[nChn / 2];
            for (i = 0; i < ptStream->nArrayCount; i++)
            {
                memcpy(&tAllResUseMap, ptStream, sizeof(tAllResUseMap));

                if (dwUniq == ptStream->atResPos[i].nUniq)
                {

                    memcpy(&ptStream->atResPos[i], &tAllResUseMap.atResPos[i + 1], sizeof(tAllResUseMap.atResPos[0]) * (ptStream->nArrayCount - 1 - i));
                    ptStream->nArrayCount--;
                }
            }

            SGWPRINTIMP("del dwUniq 0x%x succ\n", dwUniq);
            break;
        }
    }

    if (FALSE == bDel)
    {
        SGWPRINTERR("del dwUniq 0x%x fail\n", dwUniq);
    }
}

s32 NvrSgwStreamDecApply(TNvrSgwResPosInfo *ptPosInfo)
{
    s32 nRet = 0;
    s32 dwUniq = 0;
    s32 nAudDecId = 0;
    pthread_mutex_lock(&res_apply_lock);

    if (0 == ptPosInfo->nUniq)
    {
        if (ptPosInfo->nAudDecId >= 0)
        {
            nAudDecId = NvrSgwAudDecMapCheck(ptPosInfo->nAudDecId);
            if (nAudDecId)
            {
                ptPosInfo->nAudDecId = nAudDecId;
            }
            else
            {
                ptPosInfo->nAudDecId = -1;
                pthread_mutex_unlock(&res_apply_lock);
                return dwUniq;
            }
        }
        else
        {
            ptPosInfo->nAudDecId = 0;
        }
        ///< 传入uniq为0，则表示使用新分配uniq，直接申请，对于音视频id需要特殊处理
        ptPosInfo->ow = ptPosInfo->w;
        if (ptPosInfo->w > 1200 && ptPosInfo->w <= 1280)
        {
            ptPosInfo->w = GET_RES_WIDTH_BASE(ptPosInfo->ow);
            ptPosInfo->byWadd =  GET_RES_WIDTH_LEFT(ptPosInfo->ow);
        }
        else
        {
            ptPosInfo->w = GET_RES_WIDTH_UNIT(ptPosInfo->ow);
        }
        ptPosInfo->h = GET_RES_HEIGHT_UNIT(ptPosInfo->h);

        dwUniq =  NvrSgwStreamAdd(ptPosInfo);
    }
    else
    {
        ///< 传入uniq非0，则表示使用已于的uniq，直接获取
        dwUniq = ptPosInfo->nUniq;
    }


    if (dwUniq)
    {
        nRet = NvrSgwGetAvInfoByUniq(dwUniq, ptPosInfo);
        if (nRet)
        {
            SGWPRINTDBG("\nstream add uniq:%d x:%d y:%d w:%d h:%d \n"
                    "ow:%d wa:%d b:%d e:%d\n"
                    "chn:%d vid:%d aud:%d win:%d\n",
                    ptPosInfo->nUniq, ptPosInfo->x, ptPosInfo->y, ptPosInfo->w, ptPosInfo->h,
                    ptPosInfo->ow, ptPosInfo->byWadd, ptPosInfo->byBadd, ptPosInfo->byEadd,
                    ptPosInfo->nChn, ptPosInfo->nVidDecId, ptPosInfo->nAudDecId, ptPosInfo->nChnWinIndex);

            NvrSgwCheckAllRects(ptPosInfo->nChn);
        }
    }
    else
    {
        ptPosInfo->nAudDecId = nAudDecId - 1;
    }

    pthread_mutex_unlock(&res_apply_lock);

    return dwUniq;
}

s32 NvrSgwStreamDecAudPrepare(s32 nDecID)
{

    if (g_nBitmapPrint > 2)
    {
        SGWPRINTDBG("%x %x %x %x \n", g_dwAudDecIdMap & 0xff, (g_dwAudDecIdMap >> 8) & 0xff,
            (g_dwAudDecIdMap >> 16) & 0xff, (g_dwAudDecIdMap >> 24) & 0xff);
    }
    if (MASK_GET_BIT(g_dwAudDecIdMap, nDecID))
    {
        return nDecID + 1;
    }
    else
    {
        return 0;
    }
}

s32 NvrSgwStreamDecApplyTest(s32 w, s32 h, s32 nAudDecId)
{
    s32 nRet = 0;
    s32 dwUniq = 0;
    TNvrSgwResPosInfo tPos;

    mzero(tPos);

    pthread_mutex_lock(&res_apply_lock);

    tPos.ow = w;
    if (w > 1200 && w <= 1280)
    {
        tPos.w = GET_RES_WIDTH_BASE(w);
        tPos.byWadd =  GET_RES_WIDTH_LEFT(w);
    }
    else
    {
        tPos.w = GET_RES_WIDTH_UNIT(w);
    }
    tPos.h = GET_RES_HEIGHT_UNIT(h);
    tPos.nAudDecId = nAudDecId;
    dwUniq = NvrSgwStreamAdd(&tPos);

    if (dwUniq)
    {
        nRet = NvrSgwGetAvInfoByUniq(dwUniq, &tPos);
        if (nRet)
        {
            SGWPRINTDBG("\nstream add uniq:%d x:%d y:%d w:%d h:%d \n"
                    "ow:%d wa:%d b:%d e:%d\n"
                    "chn:%d vid:%d aud:%d win:%d\n",
                    tPos.nUniq, tPos.x, tPos.y, tPos.w, tPos.h,
                    tPos.ow, tPos.byWadd, tPos.byBadd, tPos.byEadd,
                    tPos.nChn, tPos.nVidDecId, tPos.nAudDecId, tPos.nChnWinIndex);
        }
    }

    pthread_mutex_unlock(&res_apply_lock);

    return dwUniq;
}

s32 NvrSgwStreamDecFree(s32 nUniq)
{

    pthread_mutex_lock(&res_apply_lock);

    if (nUniq)
    {
        NvrSgwStreamDel(nUniq);
    }

    pthread_mutex_unlock(&res_apply_lock);

    return 0;
}

void NvrSgwResChipCopy(s32 nChip)
{
    memcpy(g_atResUseMap_bk, g_atResUseMap, sizeof(g_atResUseMap));
    memcpy(g_atStreamUseInfo_bk, g_atStreamUseInfo, sizeof(g_atStreamUseInfo));
    memcpy(g_adwResMap_bk, g_adwResMap, sizeof(g_adwResMap));
    g_dwAudDecIdMap_bk = g_dwAudDecIdMap;
    memcpy(g_adwVidDecIdMap_bk, g_adwVidDecIdMap, sizeof(g_adwVidDecIdMap));
    memcpy(g_adwVidDecWinMap_bk, g_adwVidDecWinMap, sizeof(g_adwVidDecWinMap));
    memcpy(g_adwResMapSpe_bk, g_adwResMapSpe, sizeof(g_adwResMapSpe));
}

void NvrSgwResChipResume(s32 nChip)
{
    memcpy(g_atResUseMap, g_atResUseMap_bk, sizeof(g_atResUseMap));
    memcpy(g_atStreamUseInfo, g_atStreamUseInfo_bk, sizeof(g_atStreamUseInfo));
    memcpy(g_adwResMap, g_adwResMap_bk, sizeof(g_adwResMap));
    g_dwAudDecIdMap = g_dwAudDecIdMap_bk;
    memcpy(g_adwVidDecIdMap, g_adwVidDecIdMap_bk, sizeof(g_adwVidDecIdMap));
    memcpy(g_adwVidDecWinMap, g_adwVidDecWinMap_bk, sizeof(g_adwVidDecWinMap));
    memcpy(g_adwResMapSpe, g_adwResMapSpe_bk, sizeof(g_adwResMapSpe));
}

void NvrSgwResDelAll()
{
    mzero(g_atResUseMap);
    mzero(g_atStreamUseInfo);
    mzero(g_adwResMapSpe);

    memset(g_adwResMap, 0xff, sizeof(g_adwResMap));
    memset(&g_dwAudDecIdMap, 0xff, sizeof(g_dwAudDecIdMap));
    memset(g_adwVidDecIdMap, 0xff, sizeof(g_adwVidDecIdMap));
    memset(g_adwVidDecWinMap, 0xff, sizeof(g_adwVidDecWinMap));
}

void NvrSgwResChipDel(s32 nChip)
{
    s32 i = 0;
    s32 nChn = nChip * NVR_SGW_MAX_CHIP_CHN_NUM;

    mzero(g_atStreamUseInfo[nChip]);

    for (i = nChn; i < nChn + NVR_SGW_MAX_CHIP_CHN_NUM; i++)
    {
        mzero(g_atResUseMap[i]);
        memset(&g_adwResMap[i], 0xFF, sizeof(g_adwResMap[0]));
        memset(&g_adwVidDecWinMap[i], 0xFF, sizeof(g_adwVidDecWinMap[0]));
        mzero(g_adwResMapSpe[i]);
    }
}

s32 NvrSgwStreamChipArrange(s32 nChip, TNvrSgwResPosInfo *ptReqPos)
{
    s32 i = 0, j = 0;
    u64 timeIn = NvrSysGetCurTimeMSec();
    s32 nChn = nChip * NVR_SGW_MAX_CHIP_CHN_NUM;
    s32 dwUniq = 0;
    BOOL bSUcc = FALSE;
    TNvrSgwResAllUseInfo tUseMap;

          // ===== 新增调试打印 =====
    SGWPRINTIMP("######### NvrSgwStreamChipArrange START: chip=%d reqPos(ow=%d w=%d h=%d) existing_streams=%d #########\n",
          nChip,
          ptReqPos ? ptReqPos->ow : -1,
          ptReqPos ? ptReqPos->w : -1,
          ptReqPos ? ptReqPos->h : -1,
          g_atStreamUseInfo[nChip].nArrayCount);

    pthread_mutex_lock(&res_apply_lock);
    if (g_atStreamUseInfo[nChip].nArrayCount > 0)
    {
        memcpy(&tUseMap, &g_atStreamUseInfo[nChip], sizeof(g_atStreamUseInfo[0]));

        NvrSgwResChipCopy(nChip);
        NvrSgwResChipDel(nChip);

        for (i = tUseMap.nArrayCount - 1; i >= 0; i--)
        {
              // ===== 新增调试打印 =====
            SGWPRINTIMP("### Rearranging stream [%d/%d]: uniq=%u ow=%d original(w=%d h=%d x=%d y=%d)\n",
                  (tUseMap.nArrayCount - 1 - i), tUseMap.nArrayCount - 1,
                  tUseMap.atResPos[i].nUniq,
                  tUseMap.atResPos[i].ow,
                  tUseMap.atResPos[i].w,
                  tUseMap.atResPos[i].h,
                  tUseMap.atResPos[i].x,
                  tUseMap.atResPos[i].y);

            bSUcc = FALSE;
            for (j = nChn; j < nChn + NVR_SGW_MAX_CHIP_CHN_NUM; j++)
            {
                tUseMap.atResPos[i].nChn = j;
                tUseMap.atResPos[i].nChnWinIndex = 0;
                if (tUseMap.atResPos[i].byWadd)
                {
                    tUseMap.atResPos[i].w = GET_RES_WIDTH_BASE(tUseMap.atResPos[i].ow);
                    tUseMap.atResPos[i].byBadd = 0;
                    tUseMap.atResPos[i].byEadd = 0;
                }

                if (g_nBitmapPrint > 2)
                {
                    SGWPRINTIMP("before arrange show bitmap \n");
                    NvrSgwShowChnResBitMap(j);
                }
                dwUniq = NvrSgwGetResFromBitMap(&tUseMap.atResPos[i]);
                // ===== 新增调试打印 =====
                SGWPRINTIMP("    Channel %d result: %s (dwUniq=%u)\n", j, dwUniq ? "SUCCESS" : "FAILED", dwUniq);
                if (dwUniq)
                {
                    bSUcc = TRUE;
                    break;
                }
            }

            if (!bSUcc)
            {
                SGWPRINTERR("### REARRANGE FAILED: stream [%d] uniq=%u ow=%d - RESTORE ALL\n",
                      i, tUseMap.atResPos[i].nUniq, tUseMap.atResPos[i].ow);
                // 原有区域重新分配失败，恢复原有状态
                NvrSgwResChipResume(nChip);
                break;
            }
        }
    }

    if (bSUcc)
    {
        SGWPRINTIMP("### All existing streams rearranged successfully, now allocating NEW REQUEST:\n");
        SGWPRINTIMP("    Original request: ow=%d w=%d h=%d\n", ptReqPos->ow, ptReqPos->w, ptReqPos->h);

        //记录的区域已分配完，尝试申请分配刚才失败的，ptReqPos->byPrepare = 1只是做预分配，不是实际占用，调用者重新申请
        ptReqPos->byPrepare = 1;
        ptReqPos->w = GET_RES_WIDTH_UNIT(ptReqPos->w);
        ptReqPos->h = GET_RES_HEIGHT_UNIT(ptReqPos->h);

        dwUniq = NvrSgwStreamAdd(ptReqPos);
        if (dwUniq)
        {
            SGWPRINTIMP("### NEW REQUEST ALLOCATED SUCCESS, uniq:%d\n", dwUniq);
        }
        else
        {
            SGWPRINTIMP("### NEW REQUEST ALLOCATED FAILED, RESTORE ALL\n");
            // 新申请的区域分配失败，重排没有价值并且会增加额外的操作，恢复原有状态
            NvrSgwResChipResume(nChip);
        }

    }
    SGWPRINTIMP("arrage chip:%d over, take time %u ms\n", nChip, NvrSysGetCurTimeMSec() - timeIn);
    pthread_mutex_unlock(&res_apply_lock);
    return dwUniq;
}

s32 NvrSgwStreamArrange(TNvrSgwResPosInfo *ptPosInfo, s32 *pnChip)
{
    s32 nRet = 0;
    s32 nChipB = 0, nChipE = 0, i = 0;
    u64 timeIn = NvrSysGetCurTimeMSec();
    TNvrSgwResPosInfo tTmpPosInfo;

    if (-1 != ptPosInfo->nAudDecId)
    {
        nChipB = ptPosInfo->nAudDecId / 16;
        nChipE = nChipB + 1;
    }
    else
    {
        nChipB = 0;
        nChipE = nChipB + 2;
    }

    for (i = nChipB; i < nChipE; i++)
    {
        memcpy(&tTmpPosInfo, ptPosInfo, sizeof(tTmpPosInfo));
        nRet = NvrSgwStreamChipArrange(i, &tTmpPosInfo);
        if (nRet)
        {
            *pnChip = i;
            break;
        }
    }

    if (nRet)
    {
        NvrSgwStreamDecApply(ptPosInfo);
    }

    SGWPRINTIMP("arrage  take time %u ms\n", NvrSysGetCurTimeMSec() - timeIn);

    return nRet;
}

void NvrSgwStreamArrangeTest(s32 nAud)
{
    TNvrSgwResPosInfo tPosInfo;
    s32 nChip = 0;
    tPosInfo.w = 1000;
    tPosInfo.h = 600;
    tPosInfo.nAudDecId = nAud > 0 ? (nAud - 1) : -1;

    NvrSgwStreamArrange(&tPosInfo, &nChip);
}

s32 NvrSgwStreamResume(s32 nChip)
{
    NvrSgwResChipResume(nChip);
    return 0;
}

void NvrSgwResUse(u16 x, u16 y, u16 w, u16 h, s32 nChn)
{
    u32 dwMask = 0;

    dwMask = CHECK_MASK(w, x);
    NvrSgwResBitMapUpdate(nChn, TRUE, dwMask, y, h);

}

void NvrSgwResUseChn(s32 nChn)
{
    NvrSgwResUse(0, 0, 32, 12, nChn);
}

void NvrSgwResDbg(s32 nLevel)
{
    SGWPRINTIMP("print level %d --> %d\n", g_nBitmapPrint, nLevel);
    g_nBitmapPrint = nLevel;
}

void NvrSgwResShowSpeArr(s32 n)
{
    s32 i = 0, j = 0, k = 0;

    i = n;
    for (j = 0; j < RES_BITMAP_MAX_NUM; j++)
    {
        OspPrintf(TRUE, FALSE, "%2d-%2d  ", i, j);
        for (k = 0; k < 32; k++)
        {
            if (g_adwResMapSpe[i][j][k])
            {
                OspPrintf(TRUE, FALSE, "[%2d=0x%2x] ", k, g_adwResMapSpe[i][j][k]);
            }
        }
        OspPrintf(TRUE, FALSE, "\n");
    }
}

static BOOL bAutoTestRun = TRUE;
#define my_random(x) (rand()%(x))
#define min_res_w   (720)
#define min_res_h   (576)
#define GET_RES_ALIGN(n, m)       (n + n % m)

#define arr_max     (8)

u32 adwRes[arr_max][2] = {{3840, 2160}, {2560, 1920}, {1920, 1080}, {1280, 720},
                    {960, 540}, {720, 576}, {704, 576}, {640, 480}};

void NvrSgwCalcTask()
{
    s32 nResW = 0, nResH = 0, nAudId = -1;
    TNvrSgwResPosInfo tReqPosInfo;
    s32 nRet = 0, nChip = 0, nRand = 0, nUniq = 0;

    while (bAutoTestRun)
    {
        mzero(tReqPosInfo);

        srand((int)time(0));
        nRand = my_random(arr_max);
        nResW = adwRes[nRand][0];
        nResH = adwRes[nRand][1];

        SGWPRINTIMP("===auto test res  %-4d x %-4d aud:%d \n", nResW, nResH, nAudId);

        tReqPosInfo.w = nResW;
        tReqPosInfo.h = nResH;
        tReqPosInfo.nAudDecId = -1;

        NvrSgwStreamDecApply(&tReqPosInfo);
        if (0 == tReqPosInfo.nUniq)
        {
            SGWPRINTIMP("nomal calc err and arrange\n");
            tReqPosInfo.w = nResW;
            tReqPosInfo.h = nResH;
            tReqPosInfo.nAudDecId = -1;

            nRet = NvrSgwStreamArrange(&tReqPosInfo, &nChip);
            SGWPRINTIMP("arrange %s \n", nRet ? "succ" : "fail");
            if (0 == nRet)
            {
                s32 nChn = 0, nIndex = 0;
                while (1)
                {
                    srand((int)time(0) + nResW);
                    nChn = my_random(4);
                    OspPrintf(TRUE, FALSE, "del chn:%d ", nChn);
                    if (g_atResUseMap[nChn].nArrayCount)
                    {
                        if (1 == g_atResUseMap[nChn].nArrayCount)
                        {
                            nUniq = g_atResUseMap[nChn].atResPos[0].nUniq;
                            OspPrintf(TRUE, FALSE, "del index:%d\n", 1);
                        }
                        else
                        {
                            srand((int)time(0) + nChn);
                            nIndex = my_random(g_atResUseMap[nChn].nArrayCount);
                            nUniq = g_atResUseMap[nChn].atResPos[nIndex].nUniq;
                            OspPrintf(TRUE, FALSE, "del index:%d\n", nIndex);
                        }
                        break;
                    }
                }
                ///< free
                NvrSgwStreamDecFree(nUniq);
            }
        }
        if (tReqPosInfo.nUniq)
        {
            NvrSgwShowChnResBitMap(tReqPosInfo.nChn);
        }
        SGWPRINTIMP("===auto test res  %-4d x %-4d aud:%d over\n", nResW, nResH, nAudId);
        sleep(10);
    }

    OsApi_TaskExit();
    return ;
}

void NvrSgwResAutoCalc(s32 n)
{
    if (n > 1)
    {
        bAutoTestRun = FALSE;
        SGWPRINTIMP("NvrSgwCalcTask exit\n");
        NvrSgwResDbg(1);
    }
    else
    {
        NvrSgwResDbg(4);
        bAutoTestRun = TRUE;
        if ((TASKHANDLE)NULL == OsApi_TaskCreate((void*)NvrSgwCalcTask, "SgwAuroCalcDeal", NVR_TASK_COMMON_PRIORITY, 1024<<10, 0, 0, NULL))
        {
            SGWFLASHERR("NvrSgwCalcTask create failed\n");
        }
    }
}

s32 NvrSgwResBitMapInit()
{
    memset(g_adwResMap, 0xffffffff, sizeof(g_adwResMap));

    pthread_mutex_init(&res_apply_lock, NULL);

    OsApi_RegCommandEx( "cas", (void *)NvrSgwResMapShow, "dec res show  ", "i");
    OsApi_RegCommandEx( "casa", (void *)NvrSgwResMapShowAll, "dec res show  ", "i");
    OsApi_RegCommandEx( "caa", (void *)NvrSgwStreamDecApplyTest, "dec res calc   ", "iii");
    OsApi_RegCommandEx( "cad", (void *)NvrSgwStreamDel, "dec res del   ", "i");
    OsApi_RegCommandEx( "cada", (void *)NvrSgwResDelAll, "dec res del all  ", "i");
    OsApi_RegCommandEx( "carr", (void *)NvrSgwStreamArrangeTest, "res arrange   ", "i");
    OsApi_RegCommandEx( "cause", (void *)NvrSgwResUse, "dec res calc   ", "iiiii");
    OsApi_RegCommandEx( "cadbg", (void *)NvrSgwResDbg, "res calc print level   ", "i");
    OsApi_RegCommandEx( "caspe", (void *)NvrSgwResShowSpeArr, "res print spe arr   ", "i");
    OsApi_RegCommandEx( "casauto", (void *)NvrSgwResAutoCalc, "auto test calc   ", "i");
    OsApi_RegCommandEx( "causechn", (void *)NvrSgwResUseChn, "dec res calc   ", "i");
    return 0;
}
