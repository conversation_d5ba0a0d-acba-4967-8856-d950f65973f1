path="../../10-common/version/compileinfo/nvrunifiedlog_ax603a.txt"
date>>$path

cd ./prj_linux

echo ==============================================
echo =      nvr_unifiedlog_linux for ax603a           =
echo ==============================================

echo "============compile libnvrunifiedlog ax603a============">>../$path

make -j4 -e DEBUG=0 -f makefile_ax603a clean
make -j4 -e DEBUG=0 -f makefile_ax603a 2>>../$path

cp -L -r -f libnvrunifiedlog.so ../../../10-common/lib/release/ax603a/

cd ..

