

TOP := ..

COMM_DIR := ../../common/

SRC_DIR := $(TOP)/source
CURDIR := ./

## Name and type of the target for this Makefile

SO_TARGET	      := lcamclt


## Define debugging symbols
DEBUG = 0
LINUX_COMPILER = _SSC339G_
PWLIB_SUPPORT = 0
CFLAGS += -funwind-tables
## Object files that compose the target(s)

OBJS := $(SRC_DIR)/lcamclt\
		$(SRC_DIR)/lcammc\
		$(SRC_DIR)/lcamisp\
		$(SRC_DIR)/nvrosd\
		$(SRC_DIR)/fontedit\
		$(SRC_DIR)/lcammcgpsmix\
		$(SRC_DIR)/lcambasicintel\
		$(SRC_DIR)/lcamalarmframe\
		
## Libraries to include in shared object file

SLIBS += freetype
ifneq ($(SLIBS),) 
LDFLAGS += -Wl,-static
LDFLAGS += $(foreach lib,$(SLIBS),-l$(lib))
endif
## Add driver-specific include directory to the search path

INC_PATH += $(CURDIR)/../include \
		$(CURDIR)/../../nvrcap/include \
		$(CURDIR)/../../nvrsys/include \
		$(CURDIR)/../../nvrpui/include \
		$(CURDIR)/../../nvrdev/include \
		$(CURDIR)/../../nvrmpu/include \
		$(CURDIR)/../../nvrrec/include \
		$(CURDIR)/../../aiu/include \
		$(CURDIR)/../../nvrnetwork/include \
		$(CURDIR)/../../common \
		$(CURDIR)/../../../10-common/include/service\
		$(CURDIR)/../../../10-common/include/cbb/osp\
		$(CURDIR)/../../../10-common/include/cbb/protobuf \
		$(CURDIR)/../../../10-common/include/cbb/ipcmediactrl \
		$(CURDIR)/../../../10-common/include/cbb/ispctrl \
		$(CURDIR)/../../../10-common/include/cbb/debuglog \
		$(CURDIR)/../../../10-common/include/cbb/appclt \
		$(CURDIR)/../../../10-common/include/cbb/charconversion \
		$(CURDIR)/../../../10-common/include/cbb/mediaswitch \
		$(CURDIR)/../../../10-common/include/system\
		$(CURDIR)/../../../10-common/include/hal/drvlib_64 \
		$(CURDIR)/../../../10-common/include/app \
		$(CURDIR)/../../../10-common/include/cbb/freetype \
		$(CURDIR)/../../../10-common/include/cbb/freetype/freetype \
		$(CURDIR)/../../../10-common/include/cbb/wmf \
		

CFLAGS += -D_SSC339G_



ifeq ($(PWLIB_SUPPORT),1)
   INC_PATH += $(PWLIBDIR)/include/ptlib/unix $(PWLIBDIR)/include
endif

LIB_PATH += ../../../10-common/lib/release/ssc339g
LIB_PATH += ../../../10-common/lib/release/ssc339g/webrtc

INSTALL_LIB_PATH = ../../../10-common/lib/release/ssc339g/appcltlib

LDFLAGS += $(foreach lib,$(LIB_PATH),-L$(lib))

#LDFLAGS += -ldebuglog
#LDFLAGS += -lnvrcore
#LDFLAGS += -lmediactrl
#LDFLAGS += -lmediaswitch
#LDFLAGS += -lcharconv

include $(COMM_DIR)/common.mk


