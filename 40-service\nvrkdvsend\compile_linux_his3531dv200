path="../../10-common/version/compileinfo/nvrkdvsend_his3531dv200.txt"
date>>$path

cd ./prj_linux

echo ==============================================
echo =      nvr_kdvsend_linux for his3531dv200             =
echo ==============================================

echo "============compile libnvrkdvsend his3531dv200============">>../$path

make -j4 -e DEBUG=1 -f makefile_his3531dv200 clean
make -j4 -e DEBUG=1 -f makefile_his3531dv200 2>>../$path

#makefile install已经安装到指定目录了
#cp -L -r -f libnvrkdvsend.so ../../../10-common/lib/release/his3531dv200/nvrkdvsend

cd ..
