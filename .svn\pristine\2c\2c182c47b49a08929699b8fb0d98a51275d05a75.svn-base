#include "nvrrec_in.h"


NVRSTATUS NvrRecGetPlayTaskChnOutHandle(u32 dwTaskId, u16 wChnId, TNvrRecPlayMediaOutputHandle* ptOutputHandle){return NVR_ERR__OK;}
NVRSTATUS NvrRecSetAlarmRecState(u16 wChnId, BOOL32 bStart, const TNvrAlarmSrc* ptAlarmSrc){return NVR_ERR__OK;}
NVRSTATUS NvrRecSetChnAlarmEvent(const TNvrAlarmSrc* ptAlarmSrc, BOOL32 bAlarm){return NVR_ERR__OK;}
NVRSTATUS NvrRecSetAlarmSnapState(u16 wChnId, BOOL32 bStart, const TNvrAlarmSrc* ptAlarmSrc){return NVR_ERR__OK;}
NVRSTATUS NvrRecSetPlayTaskChnOutCB(u32 dwTaskId, u16 wChnId, const TNvrRecMsFrameCBParam* ptCBParam){return NVR_ERR__OK;}
NVRSTATUS NvrRecRegAlarmCB(PFNvrRecRegAlarmCB pfCB){return NVR_ERR__OK;}
NVRSTATUS NvrRecSaveSnapPic(const TNvrRecSnapPicInfo* ptPicInfo){return NVR_ERR__OK;}
NVRSTATUS NvrRecSetHdPreRecState(BOOL32 bStart){return NVR_ERR__OK;}
NVRSTATUS NvrRecGetBandwidth(u32 *pdwSndBandwidth){return NVR_ERR__OK;}
NVRSTATUS NvrRecSetPlayTaskChnAudOutCB(u32 dwTaskId, u16 wChnId, u16 wEncId, const TNvrRecMsFrameCBParam* ptCBParam){return NVR_ERR__OK;}
NVRSTATUS NvrRecClearPlayTaskChnAudOutCB(u32 dwTaskId, u16 wChnId, u16 wEncId){return NVR_ERR__OK;}



