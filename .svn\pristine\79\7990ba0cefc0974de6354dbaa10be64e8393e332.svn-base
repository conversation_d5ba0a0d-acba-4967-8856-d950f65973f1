

TOP := ..

COMM_DIR := ../../common/

SRC_DIR := $(TOP)/source
CURDIR := ./

## Name and type of the target for this Makefile

SO_TARGET	      := nvrstartap


## Define debugging symbols
DEBUG = 0
LINUX_COMPILER = _HIS3519AV100_
PWLIB_SUPPORT = 0
CFLAGS += -funwind-tables
## Object files that compose the target(s)

OBJS := $(SRC_DIR)/nvrstartap\


		
## Libraries to include in shared object file
        
ifneq ($(SLIBS),) 
LDFLAGS += -Wl,-static
LDFLAGS += $(foreach lib,$(SLIBS),-l$(lib))
endif
## Add driver-specific include directory to the search path

INC_PATH += $(CURDIR)/../include \
		$(CURDIR)/../../common \
		$(CURDIR)/../../../10-common/include/cbb/osp\
		$(CURDIR)/../../../10-common/include/cbb/wifim \
		$(CURDIR)/../../../10-common/include/system\
		$(CURDIR)/../../../10-common/include/service \
		$(CURDIR)/../../../10-common/include/cbb/protobuf \
		$(CURDIR)/../../../10-common/include/app\
		
CFLAGS += -D_HIS3519AV100_



ifeq ($(PWLIB_SUPPORT),1)
   INC_PATH += $(PWLIBDIR)/include/ptlib/unix $(PWLIBDIR)/include
endif
LIB_PATH += ../../../10-common/lib/release/his3519av100/wifim

INSTALL_LIB_PATH = ../../../10-common/lib/release/his3519av100/nvrstartap

LDFLAGS += -L$(LIB_PATH)
LDFLAGS += -lwifi

include $(COMM_DIR)/common.mk

