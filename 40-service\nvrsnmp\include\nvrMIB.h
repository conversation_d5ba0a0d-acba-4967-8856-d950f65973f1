/*
 * Note: this file originally auto-generated by mib2c using
 *  $
 */
#ifndef IPCMIB_H
#define IPCMIB_H

#define NNN 256

struct ipcAlarmTable_entry 
{
    /* Index values */
    long ipcAlarmType;
    u_long ipcAlarmResouceId;

    /* Illustrate using a simple linked list */
    int   valid;
    struct ipcAlarmTable_entry *next;
};

struct ipcAlarmStateTable_entry
{
    /* Index values */
    long ipcAlarmType;
    u_long ipcAlarmResouceId;

    /* Column values */
    long ipcAlarmState;

    /* Illustrate using a simple linked list */
    int   valid;
    struct ipcAlarmStateTable_entry *next;
};

struct ipcAlarmLinkTable_entry
{
    /* Index values */
    long ipcAlarmType;
    u_long ipcAlarmResouceId;

    /* Column values */
    long ipcAlarmLinkTrap;
    long old_ipcAlarmLinkTrap;

    /* Illustrate using a simple linked list */
    int   valid;
    struct ipcAlarmLinkTable_entry *next;
};

struct ipcVideoEncoderTable_entry
{
    /* Index values */
    u_long ipcVideoEncoderID;

    /* Column values */
    long ipcVideoEncoderFormat;
    long old_ipcVideoEncoderFormat;
    long ipcVideoEncoderProfile;
    long old_ipcVideoEncoderProfile;
    char ipcVideoEncoderResolution[NNN];
    size_t ipcVideoEncoderResolution_len;
    char old_ipcVideoEncoderResolution[NNN];
    size_t old_ipcVideoEncoderResolution_len;
    u_long ipcVideoEncoderFramerate;
    u_long old_ipcVideoEncoderFramerate;
    u_long ipcVideoEncoderBitrate;
    u_long old_ipcVideoEncoderBitrate;
    u_long ipcVideoEncoderKeyframeInterval;
    u_long old_ipcVideoEncoderKeyframeInterval;

    /* Illustrate using a simple linked list */
    int   valid;
    struct ipcVideoEncoderTable_entry *next;
};

struct ipcAudioEncoderTable_entry 
{
    /* Index values */
    u_long ipcAudioEncoderId;

    /* Column values */
    long ipcAudioEncoderFormat;
    long old_ipcAudioEncoderFormat;
    u_long ipcAudioEncoderSampling;
    u_long old_ipcAudioEncoderSampling;
    u_long ipcAudioEncoderVolume;
    u_long old_ipcAudioEncoderVolume;
    long ipcAudioEncoderAec;
    long old_ipcAudioEncoderAec;

    /* Illustrate using a simple linked list */
    int   valid;
    struct ipcAudioEncoderTable_entry *next;
};

    /* Typical data structure for a row entry */
struct ipcPlatformTable_entry {
    /* Index values */
    char ipcUuid[NNN];
    size_t ipcUuid_len;

    /* Column values */
    in_addr_t ipcPlatformIp;
    in_addr_t old_ipcPlatformIp;
    long ipcPlatformPort;
    long old_ipcPlatformPort;
    char ipcPlatformType[NNN];
    size_t ipcPlatformType_len;
    char old_ipcPlatformType[NNN];
    size_t old_ipcPlatformType_len;
    long ipcPlatformState;

    /* Illustrate using a simple linked list */
    int   valid;
    struct ipcPlatformTable_entry *next;
};

/* function declarations */
void init_ipcMIB(void);
Netsnmp_Node_Handler handle_ipcVideoEncoderNumber;
Netsnmp_Node_Handler handle_ipcAudioEncoderNumber;
Netsnmp_Node_Handler handle_ipcPlatformNumber;
void initialize_table_ipcAlarmTable(void);
Netsnmp_Node_Handler ipcAlarmTable_handler;
Netsnmp_First_Data_Point  ipcAlarmTable_get_first_data_point;
Netsnmp_Next_Data_Point   ipcAlarmTable_get_next_data_point;
NetsnmpCacheLoad ipcAlarmTable_load;
NetsnmpCacheFree ipcAlarmTable_free;
#define IPCALARMTABLE_TIMEOUT  5
/* column number definitions for table ipcAlarmTable */
#define COLUMN_IPCALARMTYPE		        1
#define COLUMN_IPCALARMRESOUCEID		2
void initialize_table_ipcAlarmStateTable(void);
Netsnmp_Node_Handler ipcAlarmStateTable_handler;
Netsnmp_First_Data_Point  ipcAlarmStateTable_get_first_data_point;
Netsnmp_Next_Data_Point   ipcAlarmStateTable_get_next_data_point;
NetsnmpCacheLoad ipcAlarmStateTable_load;
NetsnmpCacheFree ipcAlarmStateTable_free;
#define IPCALARMSTATETABLE_TIMEOUT  5
/* column number definitions for table ipcAlarmStateTable */
#define COLUMN_IPCALARMSTATE		1
void initialize_table_ipcAlarmLinkTable(void);
Netsnmp_Node_Handler ipcAlarmLinkTable_handler;
Netsnmp_First_Data_Point  ipcAlarmLinkTable_get_first_data_point;
Netsnmp_Next_Data_Point   ipcAlarmLinkTable_get_next_data_point;
NetsnmpCacheLoad ipcAlarmLinkTable_load;
NetsnmpCacheFree ipcAlarmLinkTable_free;
#define IPCALARMLINKTABLE_TIMEOUT  5
/* column number definitions for table ipcAlarmLinkTable */
#define COLUMN_IPCALARMLINKTRAP		2
void initialize_table_ipcVideoEncoderTable(void);
Netsnmp_Node_Handler ipcVideoEncoderTable_handler;
Netsnmp_First_Data_Point  ipcVideoEncoderTable_get_first_data_point;
Netsnmp_Next_Data_Point   ipcVideoEncoderTable_get_next_data_point;
NetsnmpCacheLoad ipcVideoEncoderTable_load;
NetsnmpCacheFree ipcVideoEncoderTable_free;
#define IPCVIDEOENCODERTABLE_TIMEOUT  5
/* column number definitions for table ipcVideoEncoderTable */
#define COLUMN_IPCVIDEOENCODERID		            1
#define COLUMN_IPCVIDEOENCODERFORMAT		        2
#define COLUMN_IPCVIDEOENCODERPROFILE		        3
#define COLUMN_IPCVIDEOENCODERRESOLUTION		    4
#define COLUMN_IPCVIDEOENCODERFRAMERATE	        	6
#define COLUMN_IPCVIDEOENCODERBITRATE	        	7
#define COLUMN_IPCVIDEOENCODERKEYFRAMEINTERVAL		8
void initialize_table_ipcAudioEncoderTable(void);
Netsnmp_Node_Handler ipcAudioEncoderTable_handler;
Netsnmp_First_Data_Point  ipcAudioEncoderTable_get_first_data_point;
Netsnmp_Next_Data_Point   ipcAudioEncoderTable_get_next_data_point;
NetsnmpCacheLoad ipcAudioEncoderTable_load;
NetsnmpCacheFree ipcAudioEncoderTable_free;
#define IPCAUDIOENCODERTABLE_TIMEOUT  5
/* column number definitions for table ipcAudioEncoderTable */
#define COLUMN_IPCAUDIOENCODERID		    1
#define COLUMN_IPCAUDIOENCODERFORMAT		2
#define COLUMN_IPCAUDIOENCODERSAMPLING		3
#define COLUMN_IPCAUDIOENCODERVOLUME		4
#define COLUMN_IPCAUDIOENCODERAEC		    5
void initialize_table_ipcPlatformTable(void);
Netsnmp_Node_Handler ipcPlatformTable_handler;
Netsnmp_First_Data_Point  ipcPlatformTable_get_first_data_point;
Netsnmp_Next_Data_Point   ipcPlatformTable_get_next_data_point;
NetsnmpCacheLoad ipcPlatformTable_load;
NetsnmpCacheFree ipcPlatformTable_free;
#define IPCPLATFORMTABLE_TIMEOUT  5
/* column number definitions for table ipcPlatformTable */
#define COLUMN_IPCUUID		        1
#define COLUMN_IPCPLATFORMIP		2
#define COLUMN_IPCPLATFORMPORT		3
#define COLUMN_IPCPLATFORMTYPE		4
#define COLUMN_IPCPLATFORMSTATE		5
#endif /* IPCMIB_H */
