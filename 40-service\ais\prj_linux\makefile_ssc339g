

TOP := ..

COMM_DIR := ../../common/

SRC_DIR := $(TOP)/../nvrsimcode
CURDIR := ./

## Name and type of the target for this Makefile

SO_TARGET	      := ais


## Define debugging symbols
DEBUG = 0
LINUX_COMPILER = _SSC339G_
PWLIB_SUPPORT = 0
CFLAGS += -funwind-tables
## Object files that compose the target(s)

OBJS := $(SRC_DIR)/simais

## Libraries to include in shared object file

LIB_PATH += $(TOP)/../../10-common/lib/release/ssc339g
LIBS += algctrl      

ifneq ($(SLIBS),) 
LDFLAGS += -Wl,-static
LDFLAGS += $(foreach lib,$(SLIBS),-l$(lib))
endif
## Add driver-specific include directory to the search path

INC_PATH += $(CURDIR)/../include \
		$(CURDIR)/../../common \
		$(CURDIR)/../../../30-cbb/sqlite/sqlite3/include \
		$(CURDIR)/../../../40-service/nvralarm/include \
		$(CURDIR)/../../../10-common/include/cbb/sqilte \
		$(CURDIR)/../../../10-common/include/service \
		$(CURDIR)/../../../10-common/include/cbb/debuglog\
		$(CURDIR)/../../../10-common/include/system\
		$(CURDIR)/../../../10-common/include/cbb/protobuf \
		$(CURDIR)/../../../10-common/include/cbb/osp \
		$(CURDIR)/../../../10-common/include/app \
		$(CURDIR)/../../../10-common/include/cbb/charconversion\
		$(CURDIR)/../../../40-service/airp/include\
		$(CURDIR)/../../../40-service/nvrcap/include\
		$(CURDIR)/../../../10-common/include/cbb/crc_check\
		$(CURDIR)/../../../10-common/include/algapp\
		$(CURDIR)/../../../10-common/include/cbb/appclt\
		$(CURDIR)/../../../10-common/include/cbb/mxml
		
CFLAGS += -D_SSC339G_



ifeq ($(PWLIB_SUPPORT),1)
   INC_PATH += $(PWLIBDIR)/include/ptlib/unix $(PWLIBDIR)/include
endif


INSTALL_LIB_PATH = ../../../10-common/lib/release/ssc339g
LDFLAGS += -L$(INSTALL_LIB_PATH)
include $(COMM_DIR)/common.mk


