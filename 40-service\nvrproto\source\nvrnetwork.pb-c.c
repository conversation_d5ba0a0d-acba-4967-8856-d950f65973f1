/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: nvrnetwork.proto */

/* Do not generate deprecated warnings for self */
#ifndef PROTOBUF_C__NO_DEPRECATED
#define PROTOBUF_C__NO_DEPRECATED
#endif

#include "nvrnetwork.pb-c.h"
void   tpb_nvr_net_ipv4_ip_param__init
                     (TPbNvrNetIpv4IpParam         *message)
{
  static TPbNvrNetIpv4IpParam init_value = TPB_NVR_NET_IPV4_IP_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_net_ipv4_ip_param__get_packed_size
                     (const TPbNvrNetIpv4IpParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_net_ipv4_ip_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_net_ipv4_ip_param__pack
                     (const TPbNvrNetIpv4IpParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_net_ipv4_ip_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_net_ipv4_ip_param__pack_to_buffer
                     (const TPbNvrNetIpv4IpParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_net_ipv4_ip_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrNetIpv4IpParam *
       tpb_nvr_net_ipv4_ip_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrNetIpv4IpParam *)
     protobuf_c_message_unpack (&tpb_nvr_net_ipv4_ip_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_net_ipv4_ip_param__free_unpacked
                     (TPbNvrNetIpv4IpParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_net_ipv4_ip_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_net_ipv6_ip_param__init
                     (TPbNvrNetIpv6IpParam         *message)
{
  static TPbNvrNetIpv6IpParam init_value = TPB_NVR_NET_IPV6_IP_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_net_ipv6_ip_param__get_packed_size
                     (const TPbNvrNetIpv6IpParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_net_ipv6_ip_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_net_ipv6_ip_param__pack
                     (const TPbNvrNetIpv6IpParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_net_ipv6_ip_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_net_ipv6_ip_param__pack_to_buffer
                     (const TPbNvrNetIpv6IpParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_net_ipv6_ip_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrNetIpv6IpParam *
       tpb_nvr_net_ipv6_ip_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrNetIpv6IpParam *)
     protobuf_c_message_unpack (&tpb_nvr_net_ipv6_ip_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_net_ipv6_ip_param__free_unpacked
                     (TPbNvrNetIpv6IpParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_net_ipv6_ip_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_net_ipv6_ip_addr__init
                     (TPbNvrNetIpv6IpAddr         *message)
{
  static TPbNvrNetIpv6IpAddr init_value = TPB_NVR_NET_IPV6_IP_ADDR__INIT;
  *message = init_value;
}
size_t tpb_nvr_net_ipv6_ip_addr__get_packed_size
                     (const TPbNvrNetIpv6IpAddr *message)
{
  assert(message->base.descriptor == &tpb_nvr_net_ipv6_ip_addr__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_net_ipv6_ip_addr__pack
                     (const TPbNvrNetIpv6IpAddr *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_net_ipv6_ip_addr__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_net_ipv6_ip_addr__pack_to_buffer
                     (const TPbNvrNetIpv6IpAddr *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_net_ipv6_ip_addr__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrNetIpv6IpAddr *
       tpb_nvr_net_ipv6_ip_addr__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrNetIpv6IpAddr *)
     protobuf_c_message_unpack (&tpb_nvr_net_ipv6_ip_addr__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_net_ipv6_ip_addr__free_unpacked
                     (TPbNvrNetIpv6IpAddr *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_net_ipv6_ip_addr__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_net_ipv4_eth_param__init
                     (TPbNvrNetIpv4EthParam         *message)
{
  static TPbNvrNetIpv4EthParam init_value = TPB_NVR_NET_IPV4_ETH_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_net_ipv4_eth_param__get_packed_size
                     (const TPbNvrNetIpv4EthParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_net_ipv4_eth_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_net_ipv4_eth_param__pack
                     (const TPbNvrNetIpv4EthParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_net_ipv4_eth_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_net_ipv4_eth_param__pack_to_buffer
                     (const TPbNvrNetIpv4EthParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_net_ipv4_eth_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrNetIpv4EthParam *
       tpb_nvr_net_ipv4_eth_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrNetIpv4EthParam *)
     protobuf_c_message_unpack (&tpb_nvr_net_ipv4_eth_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_net_ipv4_eth_param__free_unpacked
                     (TPbNvrNetIpv4EthParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_net_ipv4_eth_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_net_ipv6_eth_param__init
                     (TPbNvrNetIpv6EthParam         *message)
{
  static TPbNvrNetIpv6EthParam init_value = TPB_NVR_NET_IPV6_ETH_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_net_ipv6_eth_param__get_packed_size
                     (const TPbNvrNetIpv6EthParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_net_ipv6_eth_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_net_ipv6_eth_param__pack
                     (const TPbNvrNetIpv6EthParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_net_ipv6_eth_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_net_ipv6_eth_param__pack_to_buffer
                     (const TPbNvrNetIpv6EthParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_net_ipv6_eth_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrNetIpv6EthParam *
       tpb_nvr_net_ipv6_eth_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrNetIpv6EthParam *)
     protobuf_c_message_unpack (&tpb_nvr_net_ipv6_eth_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_net_ipv6_eth_param__free_unpacked
                     (TPbNvrNetIpv6EthParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_net_ipv6_eth_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_net_eth_cfg__init
                     (TPbNvrNetEthCfg         *message)
{
  static TPbNvrNetEthCfg init_value = TPB_NVR_NET_ETH_CFG__INIT;
  *message = init_value;
}
size_t tpb_nvr_net_eth_cfg__get_packed_size
                     (const TPbNvrNetEthCfg *message)
{
  assert(message->base.descriptor == &tpb_nvr_net_eth_cfg__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_net_eth_cfg__pack
                     (const TPbNvrNetEthCfg *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_net_eth_cfg__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_net_eth_cfg__pack_to_buffer
                     (const TPbNvrNetEthCfg *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_net_eth_cfg__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrNetEthCfg *
       tpb_nvr_net_eth_cfg__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrNetEthCfg *)
     protobuf_c_message_unpack (&tpb_nvr_net_eth_cfg__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_net_eth_cfg__free_unpacked
                     (TPbNvrNetEthCfg *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_net_eth_cfg__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_net_eth_param__init
                     (TPbNvrNetEthParam         *message)
{
  static TPbNvrNetEthParam init_value = TPB_NVR_NET_ETH_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_net_eth_param__get_packed_size
                     (const TPbNvrNetEthParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_net_eth_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_net_eth_param__pack
                     (const TPbNvrNetEthParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_net_eth_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_net_eth_param__pack_to_buffer
                     (const TPbNvrNetEthParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_net_eth_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrNetEthParam *
       tpb_nvr_net_eth_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrNetEthParam *)
     protobuf_c_message_unpack (&tpb_nvr_net_eth_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_net_eth_param__free_unpacked
                     (TPbNvrNetEthParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_net_eth_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_net_service_port__init
                     (TPbNvrNetServicePort         *message)
{
  static TPbNvrNetServicePort init_value = TPB_NVR_NET_SERVICE_PORT__INIT;
  *message = init_value;
}
size_t tpb_nvr_net_service_port__get_packed_size
                     (const TPbNvrNetServicePort *message)
{
  assert(message->base.descriptor == &tpb_nvr_net_service_port__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_net_service_port__pack
                     (const TPbNvrNetServicePort *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_net_service_port__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_net_service_port__pack_to_buffer
                     (const TPbNvrNetServicePort *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_net_service_port__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrNetServicePort *
       tpb_nvr_net_service_port__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrNetServicePort *)
     protobuf_c_message_unpack (&tpb_nvr_net_service_port__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_net_service_port__free_unpacked
                     (TPbNvrNetServicePort *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_net_service_port__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_net_work_mode__init
                     (TPbNvrNetWorkMode         *message)
{
  static TPbNvrNetWorkMode init_value = TPB_NVR_NET_WORK_MODE__INIT;
  *message = init_value;
}
size_t tpb_nvr_net_work_mode__get_packed_size
                     (const TPbNvrNetWorkMode *message)
{
  assert(message->base.descriptor == &tpb_nvr_net_work_mode__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_net_work_mode__pack
                     (const TPbNvrNetWorkMode *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_net_work_mode__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_net_work_mode__pack_to_buffer
                     (const TPbNvrNetWorkMode *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_net_work_mode__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrNetWorkMode *
       tpb_nvr_net_work_mode__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrNetWorkMode *)
     protobuf_c_message_unpack (&tpb_nvr_net_work_mode__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_net_work_mode__free_unpacked
                     (TPbNvrNetWorkMode *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_net_work_mode__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_network_pppoe_param__init
                     (TPbNvrNetworkPppoeParam         *message)
{
  static TPbNvrNetworkPppoeParam init_value = TPB_NVR_NETWORK_PPPOE_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_network_pppoe_param__get_packed_size
                     (const TPbNvrNetworkPppoeParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_network_pppoe_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_network_pppoe_param__pack
                     (const TPbNvrNetworkPppoeParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_network_pppoe_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_network_pppoe_param__pack_to_buffer
                     (const TPbNvrNetworkPppoeParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_network_pppoe_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrNetworkPppoeParam *
       tpb_nvr_network_pppoe_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrNetworkPppoeParam *)
     protobuf_c_message_unpack (&tpb_nvr_network_pppoe_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_network_pppoe_param__free_unpacked
                     (TPbNvrNetworkPppoeParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_network_pppoe_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_network_ddns_param__init
                     (TPbNvrNetworkDdnsParam         *message)
{
  static TPbNvrNetworkDdnsParam init_value = TPB_NVR_NETWORK_DDNS_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_network_ddns_param__get_packed_size
                     (const TPbNvrNetworkDdnsParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_network_ddns_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_network_ddns_param__pack
                     (const TPbNvrNetworkDdnsParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_network_ddns_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_network_ddns_param__pack_to_buffer
                     (const TPbNvrNetworkDdnsParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_network_ddns_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrNetworkDdnsParam *
       tpb_nvr_network_ddns_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrNetworkDdnsParam *)
     protobuf_c_message_unpack (&tpb_nvr_network_ddns_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_network_ddns_param__free_unpacked
                     (TPbNvrNetworkDdnsParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_network_ddns_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_network_upnp_map_param__init
                     (TPbNvrNetworkUpnpMapParam         *message)
{
  static TPbNvrNetworkUpnpMapParam init_value = TPB_NVR_NETWORK_UPNP_MAP_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_network_upnp_map_param__get_packed_size
                     (const TPbNvrNetworkUpnpMapParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_network_upnp_map_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_network_upnp_map_param__pack
                     (const TPbNvrNetworkUpnpMapParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_network_upnp_map_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_network_upnp_map_param__pack_to_buffer
                     (const TPbNvrNetworkUpnpMapParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_network_upnp_map_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrNetworkUpnpMapParam *
       tpb_nvr_network_upnp_map_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrNetworkUpnpMapParam *)
     protobuf_c_message_unpack (&tpb_nvr_network_upnp_map_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_network_upnp_map_param__free_unpacked
                     (TPbNvrNetworkUpnpMapParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_network_upnp_map_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_network_upnp_param__init
                     (TPbNvrNetworkUpnpParam         *message)
{
  static TPbNvrNetworkUpnpParam init_value = TPB_NVR_NETWORK_UPNP_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_network_upnp_param__get_packed_size
                     (const TPbNvrNetworkUpnpParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_network_upnp_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_network_upnp_param__pack
                     (const TPbNvrNetworkUpnpParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_network_upnp_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_network_upnp_param__pack_to_buffer
                     (const TPbNvrNetworkUpnpParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_network_upnp_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrNetworkUpnpParam *
       tpb_nvr_network_upnp_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrNetworkUpnpParam *)
     protobuf_c_message_unpack (&tpb_nvr_network_upnp_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_network_upnp_param__free_unpacked
                     (TPbNvrNetworkUpnpParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_network_upnp_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_network_port_map_manual_param__init
                     (TPbNvrNetworkPortMapManualParam         *message)
{
  static TPbNvrNetworkPortMapManualParam init_value = TPB_NVR_NETWORK_PORT_MAP_MANUAL_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_network_port_map_manual_param__get_packed_size
                     (const TPbNvrNetworkPortMapManualParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_network_port_map_manual_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_network_port_map_manual_param__pack
                     (const TPbNvrNetworkPortMapManualParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_network_port_map_manual_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_network_port_map_manual_param__pack_to_buffer
                     (const TPbNvrNetworkPortMapManualParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_network_port_map_manual_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrNetworkPortMapManualParam *
       tpb_nvr_network_port_map_manual_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrNetworkPortMapManualParam *)
     protobuf_c_message_unpack (&tpb_nvr_network_port_map_manual_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_network_port_map_manual_param__free_unpacked
                     (TPbNvrNetworkPortMapManualParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_network_port_map_manual_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_network_port_map_param__init
                     (TPbNvrNetworkPortMapParam         *message)
{
  static TPbNvrNetworkPortMapParam init_value = TPB_NVR_NETWORK_PORT_MAP_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_network_port_map_param__get_packed_size
                     (const TPbNvrNetworkPortMapParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_network_port_map_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_network_port_map_param__pack
                     (const TPbNvrNetworkPortMapParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_network_port_map_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_network_port_map_param__pack_to_buffer
                     (const TPbNvrNetworkPortMapParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_network_port_map_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrNetworkPortMapParam *
       tpb_nvr_network_port_map_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrNetworkPortMapParam *)
     protobuf_c_message_unpack (&tpb_nvr_network_port_map_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_network_port_map_param__free_unpacked
                     (TPbNvrNetworkPortMapParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_network_port_map_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_network8021x_param__init
                     (TPbNvrNetwork8021xParam         *message)
{
  static TPbNvrNetwork8021xParam init_value = TPB_NVR_NETWORK8021X_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_network8021x_param__get_packed_size
                     (const TPbNvrNetwork8021xParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_network8021x_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_network8021x_param__pack
                     (const TPbNvrNetwork8021xParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_network8021x_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_network8021x_param__pack_to_buffer
                     (const TPbNvrNetwork8021xParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_network8021x_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrNetwork8021xParam *
       tpb_nvr_network8021x_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrNetwork8021xParam *)
     protobuf_c_message_unpack (&tpb_nvr_network8021x_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_network8021x_param__free_unpacked
                     (TPbNvrNetwork8021xParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_network8021x_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_net_route_v4_param__init
                     (TPbNvrNetRouteV4Param         *message)
{
  static TPbNvrNetRouteV4Param init_value = TPB_NVR_NET_ROUTE_V4_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_net_route_v4_param__get_packed_size
                     (const TPbNvrNetRouteV4Param *message)
{
  assert(message->base.descriptor == &tpb_nvr_net_route_v4_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_net_route_v4_param__pack
                     (const TPbNvrNetRouteV4Param *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_net_route_v4_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_net_route_v4_param__pack_to_buffer
                     (const TPbNvrNetRouteV4Param *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_net_route_v4_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrNetRouteV4Param *
       tpb_nvr_net_route_v4_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrNetRouteV4Param *)
     protobuf_c_message_unpack (&tpb_nvr_net_route_v4_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_net_route_v4_param__free_unpacked
                     (TPbNvrNetRouteV4Param *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_net_route_v4_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_net_route_v6_param__init
                     (TPbNvrNetRouteV6Param         *message)
{
  static TPbNvrNetRouteV6Param init_value = TPB_NVR_NET_ROUTE_V6_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_net_route_v6_param__get_packed_size
                     (const TPbNvrNetRouteV6Param *message)
{
  assert(message->base.descriptor == &tpb_nvr_net_route_v6_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_net_route_v6_param__pack
                     (const TPbNvrNetRouteV6Param *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_net_route_v6_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_net_route_v6_param__pack_to_buffer
                     (const TPbNvrNetRouteV6Param *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_net_route_v6_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrNetRouteV6Param *
       tpb_nvr_net_route_v6_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrNetRouteV6Param *)
     protobuf_c_message_unpack (&tpb_nvr_net_route_v6_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_net_route_v6_param__free_unpacked
                     (TPbNvrNetRouteV6Param *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_net_route_v6_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_net_route_list_v4_param__init
                     (TPbNvrNetRouteListV4Param         *message)
{
  static TPbNvrNetRouteListV4Param init_value = TPB_NVR_NET_ROUTE_LIST_V4_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_net_route_list_v4_param__get_packed_size
                     (const TPbNvrNetRouteListV4Param *message)
{
  assert(message->base.descriptor == &tpb_nvr_net_route_list_v4_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_net_route_list_v4_param__pack
                     (const TPbNvrNetRouteListV4Param *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_net_route_list_v4_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_net_route_list_v4_param__pack_to_buffer
                     (const TPbNvrNetRouteListV4Param *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_net_route_list_v4_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrNetRouteListV4Param *
       tpb_nvr_net_route_list_v4_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrNetRouteListV4Param *)
     protobuf_c_message_unpack (&tpb_nvr_net_route_list_v4_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_net_route_list_v4_param__free_unpacked
                     (TPbNvrNetRouteListV4Param *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_net_route_list_v4_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_net_route_list_v6_param__init
                     (TPbNvrNetRouteListV6Param         *message)
{
  static TPbNvrNetRouteListV6Param init_value = TPB_NVR_NET_ROUTE_LIST_V6_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_net_route_list_v6_param__get_packed_size
                     (const TPbNvrNetRouteListV6Param *message)
{
  assert(message->base.descriptor == &tpb_nvr_net_route_list_v6_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_net_route_list_v6_param__pack
                     (const TPbNvrNetRouteListV6Param *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_net_route_list_v6_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_net_route_list_v6_param__pack_to_buffer
                     (const TPbNvrNetRouteListV6Param *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_net_route_list_v6_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrNetRouteListV6Param *
       tpb_nvr_net_route_list_v6_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrNetRouteListV6Param *)
     protobuf_c_message_unpack (&tpb_nvr_net_route_list_v6_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_net_route_list_v6_param__free_unpacked
                     (TPbNvrNetRouteListV6Param *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_net_route_list_v6_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   t_pb_nvr_net_route_list_param__init
                     (TPbNvrNetRouteListParam         *message)
{
  static TPbNvrNetRouteListParam init_value = T_PB_NVR_NET_ROUTE_LIST_PARAM__INIT;
  *message = init_value;
}
size_t t_pb_nvr_net_route_list_param__get_packed_size
                     (const TPbNvrNetRouteListParam *message)
{
  assert(message->base.descriptor == &t_pb_nvr_net_route_list_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t t_pb_nvr_net_route_list_param__pack
                     (const TPbNvrNetRouteListParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &t_pb_nvr_net_route_list_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t t_pb_nvr_net_route_list_param__pack_to_buffer
                     (const TPbNvrNetRouteListParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &t_pb_nvr_net_route_list_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrNetRouteListParam *
       t_pb_nvr_net_route_list_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrNetRouteListParam *)
     protobuf_c_message_unpack (&t_pb_nvr_net_route_list_param__descriptor,
                                allocator, len, data);
}
void   t_pb_nvr_net_route_list_param__free_unpacked
                     (TPbNvrNetRouteListParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &t_pb_nvr_net_route_list_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_snmp_v3_param__init
                     (TPbNvrSnmpV3Param         *message)
{
  static TPbNvrSnmpV3Param init_value = TPB_NVR_SNMP_V3_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_snmp_v3_param__get_packed_size
                     (const TPbNvrSnmpV3Param *message)
{
  assert(message->base.descriptor == &tpb_nvr_snmp_v3_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_snmp_v3_param__pack
                     (const TPbNvrSnmpV3Param *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_snmp_v3_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_snmp_v3_param__pack_to_buffer
                     (const TPbNvrSnmpV3Param *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_snmp_v3_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrSnmpV3Param *
       tpb_nvr_snmp_v3_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrSnmpV3Param *)
     protobuf_c_message_unpack (&tpb_nvr_snmp_v3_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_snmp_v3_param__free_unpacked
                     (TPbNvrSnmpV3Param *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_snmp_v3_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_net_snmp_param__init
                     (TPbNvrNetSnmpParam         *message)
{
  static TPbNvrNetSnmpParam init_value = TPB_NVR_NET_SNMP_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_net_snmp_param__get_packed_size
                     (const TPbNvrNetSnmpParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_net_snmp_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_net_snmp_param__pack
                     (const TPbNvrNetSnmpParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_net_snmp_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_net_snmp_param__pack_to_buffer
                     (const TPbNvrNetSnmpParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_net_snmp_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrNetSnmpParam *
       tpb_nvr_net_snmp_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrNetSnmpParam *)
     protobuf_c_message_unpack (&tpb_nvr_net_snmp_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_net_snmp_param__free_unpacked
                     (TPbNvrNetSnmpParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_net_snmp_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_net_addr_mult__init
                     (TPbNvrNetAddrMult         *message)
{
  static TPbNvrNetAddrMult init_value = TPB_NVR_NET_ADDR_MULT__INIT;
  *message = init_value;
}
size_t tpb_nvr_net_addr_mult__get_packed_size
                     (const TPbNvrNetAddrMult *message)
{
  assert(message->base.descriptor == &tpb_nvr_net_addr_mult__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_net_addr_mult__pack
                     (const TPbNvrNetAddrMult *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_net_addr_mult__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_net_addr_mult__pack_to_buffer
                     (const TPbNvrNetAddrMult *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_net_addr_mult__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrNetAddrMult *
       tpb_nvr_net_addr_mult__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrNetAddrMult *)
     protobuf_c_message_unpack (&tpb_nvr_net_addr_mult__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_net_addr_mult__free_unpacked
                     (TPbNvrNetAddrMult *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_net_addr_mult__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_net_multicast_param__init
                     (TPbNvrNetMulticastParam         *message)
{
  static TPbNvrNetMulticastParam init_value = TPB_NVR_NET_MULTICAST_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_net_multicast_param__get_packed_size
                     (const TPbNvrNetMulticastParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_net_multicast_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_net_multicast_param__pack
                     (const TPbNvrNetMulticastParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_net_multicast_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_net_multicast_param__pack_to_buffer
                     (const TPbNvrNetMulticastParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_net_multicast_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrNetMulticastParam *
       tpb_nvr_net_multicast_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrNetMulticastParam *)
     protobuf_c_message_unpack (&tpb_nvr_net_multicast_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_net_multicast_param__free_unpacked
                     (TPbNvrNetMulticastParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_net_multicast_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_net_ext_ipv4_eth_param__init
                     (TPbNvrNetExtIpv4EthParam         *message)
{
  static TPbNvrNetExtIpv4EthParam init_value = TPB_NVR_NET_EXT_IPV4_ETH_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_net_ext_ipv4_eth_param__get_packed_size
                     (const TPbNvrNetExtIpv4EthParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_net_ext_ipv4_eth_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_net_ext_ipv4_eth_param__pack
                     (const TPbNvrNetExtIpv4EthParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_net_ext_ipv4_eth_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_net_ext_ipv4_eth_param__pack_to_buffer
                     (const TPbNvrNetExtIpv4EthParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_net_ext_ipv4_eth_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrNetExtIpv4EthParam *
       tpb_nvr_net_ext_ipv4_eth_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrNetExtIpv4EthParam *)
     protobuf_c_message_unpack (&tpb_nvr_net_ext_ipv4_eth_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_net_ext_ipv4_eth_param__free_unpacked
                     (TPbNvrNetExtIpv4EthParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_net_ext_ipv4_eth_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_network_priority_param__init
                     (TPbNvrNetworkPriorityParam         *message)
{
  static TPbNvrNetworkPriorityParam init_value = TPB_NVR_NETWORK_PRIORITY_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_network_priority_param__get_packed_size
                     (const TPbNvrNetworkPriorityParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_network_priority_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_network_priority_param__pack
                     (const TPbNvrNetworkPriorityParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_network_priority_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_network_priority_param__pack_to_buffer
                     (const TPbNvrNetworkPriorityParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_network_priority_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrNetworkPriorityParam *
       tpb_nvr_network_priority_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrNetworkPriorityParam *)
     protobuf_c_message_unpack (&tpb_nvr_network_priority_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_network_priority_param__free_unpacked
                     (TPbNvrNetworkPriorityParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_network_priority_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_media_push_param__init
                     (TPbNvrMediaPushParam         *message)
{
  static TPbNvrMediaPushParam init_value = TPB_NVR_MEDIA_PUSH_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_media_push_param__get_packed_size
                     (const TPbNvrMediaPushParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_media_push_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_media_push_param__pack
                     (const TPbNvrMediaPushParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_media_push_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_media_push_param__pack_to_buffer
                     (const TPbNvrMediaPushParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_media_push_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrMediaPushParam *
       tpb_nvr_media_push_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrMediaPushParam *)
     protobuf_c_message_unpack (&tpb_nvr_media_push_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_media_push_param__free_unpacked
                     (TPbNvrMediaPushParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_media_push_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_media_push_param_array__init
                     (TPbNvrMediaPushParamArray         *message)
{
  static TPbNvrMediaPushParamArray init_value = TPB_NVR_MEDIA_PUSH_PARAM_ARRAY__INIT;
  *message = init_value;
}
size_t tpb_nvr_media_push_param_array__get_packed_size
                     (const TPbNvrMediaPushParamArray *message)
{
  assert(message->base.descriptor == &tpb_nvr_media_push_param_array__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_media_push_param_array__pack
                     (const TPbNvrMediaPushParamArray *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_media_push_param_array__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_media_push_param_array__pack_to_buffer
                     (const TPbNvrMediaPushParamArray *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_media_push_param_array__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrMediaPushParamArray *
       tpb_nvr_media_push_param_array__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrMediaPushParamArray *)
     protobuf_c_message_unpack (&tpb_nvr_media_push_param_array__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_media_push_param_array__free_unpacked
                     (TPbNvrMediaPushParamArray *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_media_push_param_array__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
static const ProtobufCFieldDescriptor tpb_nvr_net_ipv4_ip_param__field_descriptors[2] =
{
  {
    "ipv4_addr",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetIpv4IpParam, has_ipv4_addr),
    offsetof(TPbNvrNetIpv4IpParam, ipv4_addr),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "sub_mask",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetIpv4IpParam, has_sub_mask),
    offsetof(TPbNvrNetIpv4IpParam, sub_mask),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_net_ipv4_ip_param__field_indices_by_name[] = {
  0,   /* field[0] = ipv4_addr */
  1,   /* field[1] = sub_mask */
};
static const ProtobufCIntRange tpb_nvr_net_ipv4_ip_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_net_ipv4_ip_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrNetIpv4IpParam",
  "TPbNvrNetIpv4IpParam",
  "TPbNvrNetIpv4IpParam",
  "",
  sizeof(TPbNvrNetIpv4IpParam),
  2,
  tpb_nvr_net_ipv4_ip_param__field_descriptors,
  tpb_nvr_net_ipv4_ip_param__field_indices_by_name,
  1,  tpb_nvr_net_ipv4_ip_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_net_ipv4_ip_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_net_ipv6_ip_param__field_descriptors[2] =
{
  {
    "ipv6_addr",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetIpv6IpParam, n_ipv6_addr),
    offsetof(TPbNvrNetIpv6IpParam, ipv6_addr),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ipv6_prefix",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetIpv6IpParam, has_ipv6_prefix),
    offsetof(TPbNvrNetIpv6IpParam, ipv6_prefix),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_net_ipv6_ip_param__field_indices_by_name[] = {
  0,   /* field[0] = ipv6_addr */
  1,   /* field[1] = ipv6_prefix */
};
static const ProtobufCIntRange tpb_nvr_net_ipv6_ip_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_net_ipv6_ip_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrNetIpv6IpParam",
  "TPbNvrNetIpv6IpParam",
  "TPbNvrNetIpv6IpParam",
  "",
  sizeof(TPbNvrNetIpv6IpParam),
  2,
  tpb_nvr_net_ipv6_ip_param__field_descriptors,
  tpb_nvr_net_ipv6_ip_param__field_indices_by_name,
  1,  tpb_nvr_net_ipv6_ip_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_net_ipv6_ip_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_net_ipv6_ip_addr__field_descriptors[1] =
{
  {
    "ipv6_addr",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetIpv6IpAddr, n_ipv6_addr),
    offsetof(TPbNvrNetIpv6IpAddr, ipv6_addr),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_net_ipv6_ip_addr__field_indices_by_name[] = {
  0,   /* field[0] = ipv6_addr */
};
static const ProtobufCIntRange tpb_nvr_net_ipv6_ip_addr__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor tpb_nvr_net_ipv6_ip_addr__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrNetIpv6IpAddr",
  "TPbNvrNetIpv6IpAddr",
  "TPbNvrNetIpv6IpAddr",
  "",
  sizeof(TPbNvrNetIpv6IpAddr),
  1,
  tpb_nvr_net_ipv6_ip_addr__field_descriptors,
  tpb_nvr_net_ipv6_ip_addr__field_indices_by_name,
  1,  tpb_nvr_net_ipv6_ip_addr__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_net_ipv6_ip_addr__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_net_ipv4_eth_param__field_descriptors[6] =
{
  {
    "dhcp_enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrNetIpv4EthParam, has_dhcp_enable),
    offsetof(TPbNvrNetIpv4EthParam, dhcp_enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ip_num",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetIpv4EthParam, has_ip_num),
    offsetof(TPbNvrNetIpv4EthParam, ip_num),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ip_addr",
    3,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrNetIpv4EthParam, n_ip_addr),
    offsetof(TPbNvrNetIpv4EthParam, ip_addr),
    &tpb_nvr_net_ipv4_ip_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "gate_way",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetIpv4EthParam, has_gate_way),
    offsetof(TPbNvrNetIpv4EthParam, gate_way),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "dns_auto",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrNetIpv4EthParam, has_dns_auto),
    offsetof(TPbNvrNetIpv4EthParam, dns_auto),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "dns_addr",
    6,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetIpv4EthParam, n_dns_addr),
    offsetof(TPbNvrNetIpv4EthParam, dns_addr),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_net_ipv4_eth_param__field_indices_by_name[] = {
  0,   /* field[0] = dhcp_enable */
  5,   /* field[5] = dns_addr */
  4,   /* field[4] = dns_auto */
  3,   /* field[3] = gate_way */
  2,   /* field[2] = ip_addr */
  1,   /* field[1] = ip_num */
};
static const ProtobufCIntRange tpb_nvr_net_ipv4_eth_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 6 }
};
const ProtobufCMessageDescriptor tpb_nvr_net_ipv4_eth_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrNetIpv4EthParam",
  "TPbNvrNetIpv4EthParam",
  "TPbNvrNetIpv4EthParam",
  "",
  sizeof(TPbNvrNetIpv4EthParam),
  6,
  tpb_nvr_net_ipv4_eth_param__field_descriptors,
  tpb_nvr_net_ipv4_eth_param__field_indices_by_name,
  1,  tpb_nvr_net_ipv4_eth_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_net_ipv4_eth_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_net_ipv6_eth_param__field_descriptors[6] =
{
  {
    "get_ip_mode",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_ENUM,
    offsetof(TPbNvrNetIpv6EthParam, has_get_ip_mode),
    offsetof(TPbNvrNetIpv6EthParam, get_ip_mode),
    &em_pb_nvr_net_get_v6_ip_mode__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ip_num",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetIpv6EthParam, has_ip_num),
    offsetof(TPbNvrNetIpv6EthParam, ip_num),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ip_addr",
    3,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrNetIpv6EthParam, n_ip_addr),
    offsetof(TPbNvrNetIpv6EthParam, ip_addr),
    &tpb_nvr_net_ipv6_ip_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "gate_way",
    4,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetIpv6EthParam, n_gate_way),
    offsetof(TPbNvrNetIpv6EthParam, gate_way),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "dns_auto",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrNetIpv6EthParam, has_dns_auto),
    offsetof(TPbNvrNetIpv6EthParam, dns_auto),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "dns_addr",
    6,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrNetIpv6EthParam, n_dns_addr),
    offsetof(TPbNvrNetIpv6EthParam, dns_addr),
    &tpb_nvr_net_ipv6_ip_addr__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_net_ipv6_eth_param__field_indices_by_name[] = {
  5,   /* field[5] = dns_addr */
  4,   /* field[4] = dns_auto */
  3,   /* field[3] = gate_way */
  0,   /* field[0] = get_ip_mode */
  2,   /* field[2] = ip_addr */
  1,   /* field[1] = ip_num */
};
static const ProtobufCIntRange tpb_nvr_net_ipv6_eth_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 6 }
};
const ProtobufCMessageDescriptor tpb_nvr_net_ipv6_eth_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrNetIpv6EthParam",
  "TPbNvrNetIpv6EthParam",
  "TPbNvrNetIpv6EthParam",
  "",
  sizeof(TPbNvrNetIpv6EthParam),
  6,
  tpb_nvr_net_ipv6_eth_param__field_descriptors,
  tpb_nvr_net_ipv6_eth_param__field_indices_by_name,
  1,  tpb_nvr_net_ipv6_eth_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_net_ipv6_eth_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_net_eth_cfg__field_descriptors[4] =
{
  {
    "mtu",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetEthCfg, has_mtu),
    offsetof(TPbNvrNetEthCfg, mtu),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ipv4_eth_param",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrNetEthCfg, ipv4_eth_param),
    &tpb_nvr_net_ipv4_eth_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ipv6_eth_param",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrNetEthCfg, ipv6_eth_param),
    &tpb_nvr_net_ipv6_eth_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ipv6_eth_param_new",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrNetEthCfg, ipv6_eth_param_new),
    &tpb_nvr_net_ipv6_eth_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_net_eth_cfg__field_indices_by_name[] = {
  1,   /* field[1] = ipv4_eth_param */
  2,   /* field[2] = ipv6_eth_param */
  3,   /* field[3] = ipv6_eth_param_new */
  0,   /* field[0] = mtu */
};
static const ProtobufCIntRange tpb_nvr_net_eth_cfg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 4 }
};
const ProtobufCMessageDescriptor tpb_nvr_net_eth_cfg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrNetEthCfg",
  "TPbNvrNetEthCfg",
  "TPbNvrNetEthCfg",
  "",
  sizeof(TPbNvrNetEthCfg),
  4,
  tpb_nvr_net_eth_cfg__field_descriptors,
  tpb_nvr_net_eth_cfg__field_indices_by_name,
  1,  tpb_nvr_net_eth_cfg__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_net_eth_cfg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_net_eth_param__field_descriptors[2] =
{
  {
    "eth_cfg_param",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrNetEthParam, eth_cfg_param),
    &tpb_nvr_net_eth_cfg__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "lan_speed",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_ENUM,
    offsetof(TPbNvrNetEthParam, has_lan_speed),
    offsetof(TPbNvrNetEthParam, lan_speed),
    &em_pb_nvr_net_lan_speed_type__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_net_eth_param__field_indices_by_name[] = {
  0,   /* field[0] = eth_cfg_param */
  1,   /* field[1] = lan_speed */
};
static const ProtobufCIntRange tpb_nvr_net_eth_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_net_eth_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrNetEthParam",
  "TPbNvrNetEthParam",
  "TPbNvrNetEthParam",
  "",
  sizeof(TPbNvrNetEthParam),
  2,
  tpb_nvr_net_eth_param__field_descriptors,
  tpb_nvr_net_eth_param__field_indices_by_name,
  1,  tpb_nvr_net_eth_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_net_eth_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_net_service_port__field_descriptors[5] =
{
  {
    "http_port",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetServicePort, has_http_port),
    offsetof(TPbNvrNetServicePort, http_port),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "rtsp_port",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetServicePort, has_rtsp_port),
    offsetof(TPbNvrNetServicePort, rtsp_port),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "service_port",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetServicePort, has_service_port),
    offsetof(TPbNvrNetServicePort, service_port),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "https_port",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetServicePort, has_https_port),
    offsetof(TPbNvrNetServicePort, https_port),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "websocket_port",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetServicePort, has_websocket_port),
    offsetof(TPbNvrNetServicePort, websocket_port),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_net_service_port__field_indices_by_name[] = {
  0,   /* field[0] = http_port */
  3,   /* field[3] = https_port */
  1,   /* field[1] = rtsp_port */
  2,   /* field[2] = service_port */
  4,   /* field[4] = websocket_port */
};
static const ProtobufCIntRange tpb_nvr_net_service_port__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 5 }
};
const ProtobufCMessageDescriptor tpb_nvr_net_service_port__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrNetServicePort",
  "TPbNvrNetServicePort",
  "TPbNvrNetServicePort",
  "",
  sizeof(TPbNvrNetServicePort),
  5,
  tpb_nvr_net_service_port__field_descriptors,
  tpb_nvr_net_service_port__field_indices_by_name,
  1,  tpb_nvr_net_service_port__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_net_service_port__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_net_work_mode__field_descriptors[2] =
{
  {
    "net_work_mode",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_ENUM,
    offsetof(TPbNvrNetWorkMode, has_net_work_mode),
    offsetof(TPbNvrNetWorkMode, net_work_mode),
    &em_pb_nvr_net_work_mode__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "def_route_eth_id",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetWorkMode, has_def_route_eth_id),
    offsetof(TPbNvrNetWorkMode, def_route_eth_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_net_work_mode__field_indices_by_name[] = {
  1,   /* field[1] = def_route_eth_id */
  0,   /* field[0] = net_work_mode */
};
static const ProtobufCIntRange tpb_nvr_net_work_mode__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_net_work_mode__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrNetWorkMode",
  "TPbNvrNetWorkMode",
  "TPbNvrNetWorkMode",
  "",
  sizeof(TPbNvrNetWorkMode),
  2,
  tpb_nvr_net_work_mode__field_descriptors,
  tpb_nvr_net_work_mode__field_indices_by_name,
  1,  tpb_nvr_net_work_mode__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_net_work_mode__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_network_pppoe_param__field_descriptors[7] =
{
  {
    "pppoe_enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrNetworkPppoeParam, has_pppoe_enable),
    offsetof(TPbNvrNetworkPppoeParam, pppoe_enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "eth_id",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetworkPppoeParam, has_eth_id),
    offsetof(TPbNvrNetworkPppoeParam, eth_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "user_name",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrNetworkPppoeParam, user_name),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "user_pass",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrNetworkPppoeParam, user_pass),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "pppoe_status",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_ENUM,
    offsetof(TPbNvrNetworkPppoeParam, has_pppoe_status),
    offsetof(TPbNvrNetworkPppoeParam, pppoe_status),
    &em_pb_nvr_net_pppoe_status__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "pppoe_ip",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetworkPppoeParam, has_pppoe_ip),
    offsetof(TPbNvrNetworkPppoeParam, pppoe_ip),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "pppoe_dns",
    7,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetworkPppoeParam, n_pppoe_dns),
    offsetof(TPbNvrNetworkPppoeParam, pppoe_dns),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_network_pppoe_param__field_indices_by_name[] = {
  1,   /* field[1] = eth_id */
  6,   /* field[6] = pppoe_dns */
  0,   /* field[0] = pppoe_enable */
  5,   /* field[5] = pppoe_ip */
  4,   /* field[4] = pppoe_status */
  2,   /* field[2] = user_name */
  3,   /* field[3] = user_pass */
};
static const ProtobufCIntRange tpb_nvr_network_pppoe_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 7 }
};
const ProtobufCMessageDescriptor tpb_nvr_network_pppoe_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrNetworkPppoeParam",
  "TPbNvrNetworkPppoeParam",
  "TPbNvrNetworkPppoeParam",
  "",
  sizeof(TPbNvrNetworkPppoeParam),
  7,
  tpb_nvr_network_pppoe_param__field_descriptors,
  tpb_nvr_network_pppoe_param__field_indices_by_name,
  1,  tpb_nvr_network_pppoe_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_network_pppoe_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_network_ddns_param__field_descriptors[7] =
{
  {
    "ddns_enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrNetworkDdnsParam, has_ddns_enable),
    offsetof(TPbNvrNetworkDdnsParam, ddns_enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ddns_srv_type",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_ENUM,
    offsetof(TPbNvrNetworkDdnsParam, has_ddns_srv_type),
    offsetof(TPbNvrNetworkDdnsParam, ddns_srv_type),
    &em_pb_nvr_net_ddns_service_type__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ach_srv_addr",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrNetworkDdnsParam, ach_srv_addr),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "user_name",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrNetworkDdnsParam, user_name),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "user_pass",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrNetworkDdnsParam, user_pass),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "domain",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrNetworkDdnsParam, domain),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ddns_port",
    7,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetworkDdnsParam, has_ddns_port),
    offsetof(TPbNvrNetworkDdnsParam, ddns_port),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_network_ddns_param__field_indices_by_name[] = {
  2,   /* field[2] = ach_srv_addr */
  0,   /* field[0] = ddns_enable */
  6,   /* field[6] = ddns_port */
  1,   /* field[1] = ddns_srv_type */
  5,   /* field[5] = domain */
  3,   /* field[3] = user_name */
  4,   /* field[4] = user_pass */
};
static const ProtobufCIntRange tpb_nvr_network_ddns_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 7 }
};
const ProtobufCMessageDescriptor tpb_nvr_network_ddns_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrNetworkDdnsParam",
  "TPbNvrNetworkDdnsParam",
  "TPbNvrNetworkDdnsParam",
  "",
  sizeof(TPbNvrNetworkDdnsParam),
  7,
  tpb_nvr_network_ddns_param__field_descriptors,
  tpb_nvr_network_ddns_param__field_indices_by_name,
  1,  tpb_nvr_network_ddns_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_network_ddns_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_network_upnp_map_param__field_descriptors[1] =
{
  {
    "external_port",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetworkUpnpMapParam, has_external_port),
    offsetof(TPbNvrNetworkUpnpMapParam, external_port),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_network_upnp_map_param__field_indices_by_name[] = {
  0,   /* field[0] = external_port */
};
static const ProtobufCIntRange tpb_nvr_network_upnp_map_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor tpb_nvr_network_upnp_map_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrNetworkUpnpMapParam",
  "TPbNvrNetworkUpnpMapParam",
  "TPbNvrNetworkUpnpMapParam",
  "",
  sizeof(TPbNvrNetworkUpnpMapParam),
  1,
  tpb_nvr_network_upnp_map_param__field_descriptors,
  tpb_nvr_network_upnp_map_param__field_indices_by_name,
  1,  tpb_nvr_network_upnp_map_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_network_upnp_map_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_network_upnp_param__field_descriptors[6] =
{
  {
    "upnp_enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrNetworkUpnpParam, has_upnp_enable),
    offsetof(TPbNvrNetworkUpnpParam, upnp_enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "upnp_mode",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_ENUM,
    offsetof(TPbNvrNetworkUpnpParam, has_upnp_mode),
    offsetof(TPbNvrNetworkUpnpParam, upnp_mode),
    &em_pb_nvr_upnp_mode_type__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "upnp_map",
    3,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrNetworkUpnpParam, n_upnp_map),
    offsetof(TPbNvrNetworkUpnpParam, upnp_map),
    &tpb_nvr_network_upnp_map_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "upnp_alias",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BYTES,
    offsetof(TPbNvrNetworkUpnpParam, has_upnp_alias),
    offsetof(TPbNvrNetworkUpnpParam, upnp_alias),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ip_type",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetworkUpnpParam, has_ip_type),
    offsetof(TPbNvrNetworkUpnpParam, ip_type),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "external_ip",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrNetworkUpnpParam, external_ip),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_network_upnp_param__field_indices_by_name[] = {
  5,   /* field[5] = external_ip */
  4,   /* field[4] = ip_type */
  3,   /* field[3] = upnp_alias */
  0,   /* field[0] = upnp_enable */
  2,   /* field[2] = upnp_map */
  1,   /* field[1] = upnp_mode */
};
static const ProtobufCIntRange tpb_nvr_network_upnp_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 6 }
};
const ProtobufCMessageDescriptor tpb_nvr_network_upnp_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrNetworkUpnpParam",
  "TPbNvrNetworkUpnpParam",
  "TPbNvrNetworkUpnpParam",
  "",
  sizeof(TPbNvrNetworkUpnpParam),
  6,
  tpb_nvr_network_upnp_param__field_descriptors,
  tpb_nvr_network_upnp_param__field_indices_by_name,
  1,  tpb_nvr_network_upnp_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_network_upnp_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_network_port_map_manual_param__field_descriptors[3] =
{
  {
    "ip_type",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetworkPortMapManualParam, has_ip_type),
    offsetof(TPbNvrNetworkPortMapManualParam, ip_type),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "external_ip",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrNetworkPortMapManualParam, external_ip),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "manual_map",
    3,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrNetworkPortMapManualParam, n_manual_map),
    offsetof(TPbNvrNetworkPortMapManualParam, manual_map),
    &tpb_nvr_network_upnp_map_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_network_port_map_manual_param__field_indices_by_name[] = {
  1,   /* field[1] = external_ip */
  0,   /* field[0] = ip_type */
  2,   /* field[2] = manual_map */
};
static const ProtobufCIntRange tpb_nvr_network_port_map_manual_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 3 }
};
const ProtobufCMessageDescriptor tpb_nvr_network_port_map_manual_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrNetworkPortMapManualParam",
  "TPbNvrNetworkPortMapManualParam",
  "TPbNvrNetworkPortMapManualParam",
  "",
  sizeof(TPbNvrNetworkPortMapManualParam),
  3,
  tpb_nvr_network_port_map_manual_param__field_descriptors,
  tpb_nvr_network_port_map_manual_param__field_indices_by_name,
  1,  tpb_nvr_network_port_map_manual_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_network_port_map_manual_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_network_port_map_param__field_descriptors[3] =
{
  {
    "port_map_mode",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetworkPortMapParam, has_port_map_mode),
    offsetof(TPbNvrNetworkPortMapParam, port_map_mode),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "upnp_param",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrNetworkPortMapParam, upnp_param),
    &tpb_nvr_network_upnp_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "manual_param",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrNetworkPortMapParam, manual_param),
    &tpb_nvr_network_port_map_manual_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_network_port_map_param__field_indices_by_name[] = {
  2,   /* field[2] = manual_param */
  0,   /* field[0] = port_map_mode */
  1,   /* field[1] = upnp_param */
};
static const ProtobufCIntRange tpb_nvr_network_port_map_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 3 }
};
const ProtobufCMessageDescriptor tpb_nvr_network_port_map_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrNetworkPortMapParam",
  "TPbNvrNetworkPortMapParam",
  "TPbNvrNetworkPortMapParam",
  "",
  sizeof(TPbNvrNetworkPortMapParam),
  3,
  tpb_nvr_network_port_map_param__field_descriptors,
  tpb_nvr_network_port_map_param__field_indices_by_name,
  1,  tpb_nvr_network_port_map_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_network_port_map_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_network8021x_param__field_descriptors[7] =
{
  {
    "enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrNetwork8021xParam, has_enable),
    offsetof(TPbNvrNetwork8021xParam, enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "eth_id",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetwork8021xParam, has_eth_id),
    offsetof(TPbNvrNetwork8021xParam, eth_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "type",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetwork8021xParam, has_type),
    offsetof(TPbNvrNetwork8021xParam, type),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "version",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetwork8021xParam, has_version),
    offsetof(TPbNvrNetwork8021xParam, version),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "user_name",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrNetwork8021xParam, user_name),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "user_pass",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrNetwork8021xParam, user_pass),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "crt_path",
    7,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrNetwork8021xParam, crt_path),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_network8021x_param__field_indices_by_name[] = {
  6,   /* field[6] = crt_path */
  0,   /* field[0] = enable */
  1,   /* field[1] = eth_id */
  2,   /* field[2] = type */
  4,   /* field[4] = user_name */
  5,   /* field[5] = user_pass */
  3,   /* field[3] = version */
};
static const ProtobufCIntRange tpb_nvr_network8021x_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 7 }
};
const ProtobufCMessageDescriptor tpb_nvr_network8021x_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrNetwork8021xParam",
  "TPbNvrNetwork8021xParam",
  "TPbNvrNetwork8021xParam",
  "",
  sizeof(TPbNvrNetwork8021xParam),
  7,
  tpb_nvr_network8021x_param__field_descriptors,
  tpb_nvr_network8021x_param__field_indices_by_name,
  1,  tpb_nvr_network8021x_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_network8021x_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_net_route_v4_param__field_descriptors[3] =
{
  {
    "dest_ip_addr",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetRouteV4Param, has_dest_ip_addr),
    offsetof(TPbNvrNetRouteV4Param, dest_ip_addr),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "sub_mask",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetRouteV4Param, has_sub_mask),
    offsetof(TPbNvrNetRouteV4Param, sub_mask),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "gate_way",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetRouteV4Param, has_gate_way),
    offsetof(TPbNvrNetRouteV4Param, gate_way),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_net_route_v4_param__field_indices_by_name[] = {
  0,   /* field[0] = dest_ip_addr */
  2,   /* field[2] = gate_way */
  1,   /* field[1] = sub_mask */
};
static const ProtobufCIntRange tpb_nvr_net_route_v4_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 3 }
};
const ProtobufCMessageDescriptor tpb_nvr_net_route_v4_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrNetRouteV4Param",
  "TPbNvrNetRouteV4Param",
  "TPbNvrNetRouteV4Param",
  "",
  sizeof(TPbNvrNetRouteV4Param),
  3,
  tpb_nvr_net_route_v4_param__field_descriptors,
  tpb_nvr_net_route_v4_param__field_indices_by_name,
  1,  tpb_nvr_net_route_v4_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_net_route_v4_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_net_route_v6_param__field_descriptors[3] =
{
  {
    "dest_ip_addr",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetRouteV6Param, n_dest_ip_addr),
    offsetof(TPbNvrNetRouteV6Param, dest_ip_addr),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ipv6_prefix",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetRouteV6Param, has_ipv6_prefix),
    offsetof(TPbNvrNetRouteV6Param, ipv6_prefix),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "gate_way",
    3,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetRouteV6Param, n_gate_way),
    offsetof(TPbNvrNetRouteV6Param, gate_way),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_net_route_v6_param__field_indices_by_name[] = {
  0,   /* field[0] = dest_ip_addr */
  2,   /* field[2] = gate_way */
  1,   /* field[1] = ipv6_prefix */
};
static const ProtobufCIntRange tpb_nvr_net_route_v6_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 3 }
};
const ProtobufCMessageDescriptor tpb_nvr_net_route_v6_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrNetRouteV6Param",
  "TPbNvrNetRouteV6Param",
  "TPbNvrNetRouteV6Param",
  "",
  sizeof(TPbNvrNetRouteV6Param),
  3,
  tpb_nvr_net_route_v6_param__field_descriptors,
  tpb_nvr_net_route_v6_param__field_indices_by_name,
  1,  tpb_nvr_net_route_v6_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_net_route_v6_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_net_route_list_v4_param__field_descriptors[2] =
{
  {
    "cur_route_num",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetRouteListV4Param, has_cur_route_num),
    offsetof(TPbNvrNetRouteListV4Param, cur_route_num),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ipv4_route_param",
    2,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrNetRouteListV4Param, n_ipv4_route_param),
    offsetof(TPbNvrNetRouteListV4Param, ipv4_route_param),
    &tpb_nvr_net_route_v4_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_net_route_list_v4_param__field_indices_by_name[] = {
  0,   /* field[0] = cur_route_num */
  1,   /* field[1] = ipv4_route_param */
};
static const ProtobufCIntRange tpb_nvr_net_route_list_v4_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_net_route_list_v4_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrNetRouteListV4Param",
  "TPbNvrNetRouteListV4Param",
  "TPbNvrNetRouteListV4Param",
  "",
  sizeof(TPbNvrNetRouteListV4Param),
  2,
  tpb_nvr_net_route_list_v4_param__field_descriptors,
  tpb_nvr_net_route_list_v4_param__field_indices_by_name,
  1,  tpb_nvr_net_route_list_v4_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_net_route_list_v4_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_net_route_list_v6_param__field_descriptors[2] =
{
  {
    "cur_route_num",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetRouteListV6Param, has_cur_route_num),
    offsetof(TPbNvrNetRouteListV6Param, cur_route_num),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ipv6_route_param",
    2,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrNetRouteListV6Param, n_ipv6_route_param),
    offsetof(TPbNvrNetRouteListV6Param, ipv6_route_param),
    &tpb_nvr_net_route_v6_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_net_route_list_v6_param__field_indices_by_name[] = {
  0,   /* field[0] = cur_route_num */
  1,   /* field[1] = ipv6_route_param */
};
static const ProtobufCIntRange tpb_nvr_net_route_list_v6_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_net_route_list_v6_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrNetRouteListV6Param",
  "TPbNvrNetRouteListV6Param",
  "TPbNvrNetRouteListV6Param",
  "",
  sizeof(TPbNvrNetRouteListV6Param),
  2,
  tpb_nvr_net_route_list_v6_param__field_descriptors,
  tpb_nvr_net_route_list_v6_param__field_indices_by_name,
  1,  tpb_nvr_net_route_list_v6_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_net_route_list_v6_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor t_pb_nvr_net_route_list_param__field_descriptors[2] =
{
  {
    "ipv4_route_list_param",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrNetRouteListParam, ipv4_route_list_param),
    &tpb_nvr_net_route_list_v4_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ipv6_route_list_param",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrNetRouteListParam, ipv6_route_list_param),
    &tpb_nvr_net_route_list_v6_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned t_pb_nvr_net_route_list_param__field_indices_by_name[] = {
  0,   /* field[0] = ipv4_route_list_param */
  1,   /* field[1] = ipv6_route_list_param */
};
static const ProtobufCIntRange t_pb_nvr_net_route_list_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor t_pb_nvr_net_route_list_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "tPbNvrNetRouteListParam",
  "TPbNvrNetRouteListParam",
  "TPbNvrNetRouteListParam",
  "",
  sizeof(TPbNvrNetRouteListParam),
  2,
  t_pb_nvr_net_route_list_param__field_descriptors,
  t_pb_nvr_net_route_list_param__field_indices_by_name,
  1,  t_pb_nvr_net_route_list_param__number_ranges,
  (ProtobufCMessageInit) t_pb_nvr_net_route_list_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_snmp_v3_param__field_descriptors[5] =
{
  {
    "user_name",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrSnmpV3Param, user_name),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "authen_type",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_ENUM,
    offsetof(TPbNvrSnmpV3Param, has_authen_type),
    offsetof(TPbNvrSnmpV3Param, authen_type),
    &em_pb_nvr_net_authen_type__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "authen_pass",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrSnmpV3Param, authen_pass),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "key_algtype",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_ENUM,
    offsetof(TPbNvrSnmpV3Param, has_key_algtype),
    offsetof(TPbNvrSnmpV3Param, key_algtype),
    &em_pb_private_key_alg_type__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "private_pass",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrSnmpV3Param, private_pass),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_snmp_v3_param__field_indices_by_name[] = {
  2,   /* field[2] = authen_pass */
  1,   /* field[1] = authen_type */
  3,   /* field[3] = key_algtype */
  4,   /* field[4] = private_pass */
  0,   /* field[0] = user_name */
};
static const ProtobufCIntRange tpb_nvr_snmp_v3_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 5 }
};
const ProtobufCMessageDescriptor tpb_nvr_snmp_v3_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrSnmpV3Param",
  "TPbNvrSnmpV3Param",
  "TPbNvrSnmpV3Param",
  "",
  sizeof(TPbNvrSnmpV3Param),
  5,
  tpb_nvr_snmp_v3_param__field_descriptors,
  tpb_nvr_snmp_v3_param__field_indices_by_name,
  1,  tpb_nvr_snmp_v3_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_snmp_v3_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_net_snmp_param__field_descriptors[13] =
{
  {
    "benable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrNetSnmpParam, has_benable),
    offsetof(TPbNvrNetSnmpParam, benable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "snmp_ver",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_ENUM,
    offsetof(TPbNvrNetSnmpParam, has_snmp_ver),
    offsetof(TPbNvrNetSnmpParam, snmp_ver),
    &em_pb_nvr_net_snmp_ver__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "snmp_port",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetSnmpParam, has_snmp_port),
    offsetof(TPbNvrNetSnmpParam, snmp_port),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "read_union",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrNetSnmpParam, read_union),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "write_union",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrNetSnmpParam, write_union),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "trap_name",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrNetSnmpParam, trap_name),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "trap_addr",
    7,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetSnmpParam, has_trap_addr),
    offsetof(TPbNvrNetSnmpParam, trap_addr),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "trap_port",
    8,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetSnmpParam, has_trap_port),
    offsetof(TPbNvrNetSnmpParam, trap_port),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "snmp_v3_read_union_param",
    9,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrNetSnmpParam, snmp_v3_read_union_param),
    &tpb_nvr_snmp_v3_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "snmp_v3_write_union_param",
    10,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrNetSnmpParam, snmp_v3_write_union_param),
    &tpb_nvr_snmp_v3_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "snmp_puid",
    11,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrNetSnmpParam, snmp_puid),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "snmp_enterprise_id",
    12,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrNetSnmpParam, snmp_enterprise_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "trap_addr_v4v6",
    13,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrNetSnmpParam, trap_addr_v4v6),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_net_snmp_param__field_indices_by_name[] = {
  0,   /* field[0] = benable */
  3,   /* field[3] = read_union */
  11,   /* field[11] = snmp_enterprise_id */
  2,   /* field[2] = snmp_port */
  10,   /* field[10] = snmp_puid */
  8,   /* field[8] = snmp_v3_read_union_param */
  9,   /* field[9] = snmp_v3_write_union_param */
  1,   /* field[1] = snmp_ver */
  6,   /* field[6] = trap_addr */
  12,   /* field[12] = trap_addr_v4v6 */
  5,   /* field[5] = trap_name */
  7,   /* field[7] = trap_port */
  4,   /* field[4] = write_union */
};
static const ProtobufCIntRange tpb_nvr_net_snmp_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 13 }
};
const ProtobufCMessageDescriptor tpb_nvr_net_snmp_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrNetSnmpParam",
  "TPbNvrNetSnmpParam",
  "TPbNvrNetSnmpParam",
  "",
  sizeof(TPbNvrNetSnmpParam),
  13,
  tpb_nvr_net_snmp_param__field_descriptors,
  tpb_nvr_net_snmp_param__field_indices_by_name,
  1,  tpb_nvr_net_snmp_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_net_snmp_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_net_addr_mult__field_descriptors[2] =
{
  {
    "IP_Type",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetAddrMult, has_ip_type),
    offsetof(TPbNvrNetAddrMult, ip_type),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "IP_V4_Or_V6",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BYTES,
    offsetof(TPbNvrNetAddrMult, has_ip_v4_or_v6),
    offsetof(TPbNvrNetAddrMult, ip_v4_or_v6),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_net_addr_mult__field_indices_by_name[] = {
  0,   /* field[0] = IP_Type */
  1,   /* field[1] = IP_V4_Or_V6 */
};
static const ProtobufCIntRange tpb_nvr_net_addr_mult__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_net_addr_mult__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrNetAddrMult",
  "TPbNvrNetAddrMult",
  "TPbNvrNetAddrMult",
  "",
  sizeof(TPbNvrNetAddrMult),
  2,
  tpb_nvr_net_addr_mult__field_descriptors,
  tpb_nvr_net_addr_mult__field_indices_by_name,
  1,  tpb_nvr_net_addr_mult__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_net_addr_mult__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_net_multicast_param__field_descriptors[4] =
{
  {
    "start_port",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetMulticastParam, has_start_port),
    offsetof(TPbNvrNetMulticastParam, start_port),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "mult_addr",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrNetMulticastParam, mult_addr),
    &tpb_nvr_net_addr_mult__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "snd_mode",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetMulticastParam, has_snd_mode),
    offsetof(TPbNvrNetMulticastParam, snd_mode),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ttl",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetMulticastParam, has_ttl),
    offsetof(TPbNvrNetMulticastParam, ttl),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_net_multicast_param__field_indices_by_name[] = {
  1,   /* field[1] = mult_addr */
  2,   /* field[2] = snd_mode */
  0,   /* field[0] = start_port */
  3,   /* field[3] = ttl */
};
static const ProtobufCIntRange tpb_nvr_net_multicast_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 4 }
};
const ProtobufCMessageDescriptor tpb_nvr_net_multicast_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrNetMulticastParam",
  "TPbNvrNetMulticastParam",
  "TPbNvrNetMulticastParam",
  "",
  sizeof(TPbNvrNetMulticastParam),
  4,
  tpb_nvr_net_multicast_param__field_descriptors,
  tpb_nvr_net_multicast_param__field_indices_by_name,
  1,  tpb_nvr_net_multicast_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_net_multicast_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_net_ext_ipv4_eth_param__field_descriptors[1] =
{
  {
    "ip_addr",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrNetExtIpv4EthParam, n_ip_addr),
    offsetof(TPbNvrNetExtIpv4EthParam, ip_addr),
    &tpb_nvr_net_ipv4_ip_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_net_ext_ipv4_eth_param__field_indices_by_name[] = {
  0,   /* field[0] = ip_addr */
};
static const ProtobufCIntRange tpb_nvr_net_ext_ipv4_eth_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor tpb_nvr_net_ext_ipv4_eth_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrNetExtIpv4EthParam",
  "TPbNvrNetExtIpv4EthParam",
  "TPbNvrNetExtIpv4EthParam",
  "",
  sizeof(TPbNvrNetExtIpv4EthParam),
  1,
  tpb_nvr_net_ext_ipv4_eth_param__field_descriptors,
  tpb_nvr_net_ext_ipv4_eth_param__field_indices_by_name,
  1,  tpb_nvr_net_ext_ipv4_eth_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_net_ext_ipv4_eth_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_network_priority_param__field_descriptors[1] =
{
  {
    "level",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrNetworkPriorityParam, n_level),
    offsetof(TPbNvrNetworkPriorityParam, level),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_network_priority_param__field_indices_by_name[] = {
  0,   /* field[0] = level */
};
static const ProtobufCIntRange tpb_nvr_network_priority_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor tpb_nvr_network_priority_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrNetworkPriorityParam",
  "TPbNvrNetworkPriorityParam",
  "TPbNvrNetworkPriorityParam",
  "",
  sizeof(TPbNvrNetworkPriorityParam),
  1,
  tpb_nvr_network_priority_param__field_descriptors,
  tpb_nvr_network_priority_param__field_indices_by_name,
  1,  tpb_nvr_network_priority_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_network_priority_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_media_push_param__field_descriptors[6] =
{
  {
    "dest_url",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrMediaPushParam, dest_url),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "w_src_chn_id",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMediaPushParam, has_w_src_chn_id),
    offsetof(TPbNvrMediaPushParam, w_src_chn_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "by_enc_id",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMediaPushParam, has_by_enc_id),
    offsetof(TPbNvrMediaPushParam, by_enc_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "b_push_aud",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_SINT32,
    offsetof(TPbNvrMediaPushParam, has_b_push_aud),
    offsetof(TPbNvrMediaPushParam, b_push_aud),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "e_stat",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_SINT32,
    offsetof(TPbNvrMediaPushParam, has_e_stat),
    offsetof(TPbNvrMediaPushParam, e_stat),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "e_push_src_type",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_SINT32,
    offsetof(TPbNvrMediaPushParam, has_e_push_src_type),
    offsetof(TPbNvrMediaPushParam, e_push_src_type),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_media_push_param__field_indices_by_name[] = {
  3,   /* field[3] = b_push_aud */
  2,   /* field[2] = by_enc_id */
  0,   /* field[0] = dest_url */
  5,   /* field[5] = e_push_src_type */
  4,   /* field[4] = e_stat */
  1,   /* field[1] = w_src_chn_id */
};
static const ProtobufCIntRange tpb_nvr_media_push_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 6 }
};
const ProtobufCMessageDescriptor tpb_nvr_media_push_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrMediaPushParam",
  "TPbNvrMediaPushParam",
  "TPbNvrMediaPushParam",
  "",
  sizeof(TPbNvrMediaPushParam),
  6,
  tpb_nvr_media_push_param__field_descriptors,
  tpb_nvr_media_push_param__field_indices_by_name,
  1,  tpb_nvr_media_push_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_media_push_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_media_push_param_array__field_descriptors[1] =
{
  {
    "atPbNvrMediaPushParam",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrMediaPushParamArray, n_atpbnvrmediapushparam),
    offsetof(TPbNvrMediaPushParamArray, atpbnvrmediapushparam),
    &tpb_nvr_media_push_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_media_push_param_array__field_indices_by_name[] = {
  0,   /* field[0] = atPbNvrMediaPushParam */
};
static const ProtobufCIntRange tpb_nvr_media_push_param_array__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor tpb_nvr_media_push_param_array__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrMediaPushParamArray",
  "TPbNvrMediaPushParamArray",
  "TPbNvrMediaPushParamArray",
  "",
  sizeof(TPbNvrMediaPushParamArray),
  1,
  tpb_nvr_media_push_param_array__field_descriptors,
  tpb_nvr_media_push_param_array__field_indices_by_name,
  1,  tpb_nvr_media_push_param_array__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_media_push_param_array__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCEnumValue em_pb_nvr_net_get_v6_ip_mode__enum_values_by_number[3] =
{
  { "IM_MANUAL", "EM_PB_NVR_NET_GET_V6_IP_MODE__IM_MANUAL", 0 },
  { "IM_AUTO", "EM_PB_NVR_NET_GET_V6_IP_MODE__IM_AUTO", 1 },
  { "IM_ROUTE", "EM_PB_NVR_NET_GET_V6_IP_MODE__IM_ROUTE", 2 },
};
static const ProtobufCIntRange em_pb_nvr_net_get_v6_ip_mode__value_ranges[] = {
{0, 0},{0, 3}
};
static const ProtobufCEnumValueIndex em_pb_nvr_net_get_v6_ip_mode__enum_values_by_name[3] =
{
  { "IM_AUTO", 1 },
  { "IM_MANUAL", 0 },
  { "IM_ROUTE", 2 },
};
const ProtobufCEnumDescriptor em_pb_nvr_net_get_v6_ip_mode__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "EmPbNvrNetGetV6IpMode",
  "EmPbNvrNetGetV6IpMode",
  "EmPbNvrNetGetV6IpMode",
  "",
  3,
  em_pb_nvr_net_get_v6_ip_mode__enum_values_by_number,
  3,
  em_pb_nvr_net_get_v6_ip_mode__enum_values_by_name,
  1,
  em_pb_nvr_net_get_v6_ip_mode__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
static const ProtobufCEnumValue em_pb_nvr_net_lan_type__enum_values_by_number[3] =
{
  { "INVALID", "EM_PB_NVR_NET_LAN_TYPE__INVALID", 0 },
  { "RJ45", "EM_PB_NVR_NET_LAN_TYPE__RJ45", 1 },
  { "x80211", "EM_PB_NVR_NET_LAN_TYPE__x80211", 2 },
};
static const ProtobufCIntRange em_pb_nvr_net_lan_type__value_ranges[] = {
{0, 0},{0, 3}
};
static const ProtobufCEnumValueIndex em_pb_nvr_net_lan_type__enum_values_by_name[3] =
{
  { "INVALID", 0 },
  { "RJ45", 1 },
  { "x80211", 2 },
};
const ProtobufCEnumDescriptor em_pb_nvr_net_lan_type__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "EmPbNvrNetLanType",
  "EmPbNvrNetLanType",
  "EmPbNvrNetLanType",
  "",
  3,
  em_pb_nvr_net_lan_type__enum_values_by_number,
  3,
  em_pb_nvr_net_lan_type__enum_values_by_name,
  1,
  em_pb_nvr_net_lan_type__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
static const ProtobufCEnumValue em_pb_nvr_net_lan_speed_type__enum_values_by_number[4] =
{
  { "LS_AUTO", "EM_PB_NVR_NET_LAN_SPEED_TYPE__LS_AUTO", 0 },
  { "LS_10M", "EM_PB_NVR_NET_LAN_SPEED_TYPE__LS_10M", 10 },
  { "LS_100M", "EM_PB_NVR_NET_LAN_SPEED_TYPE__LS_100M", 100 },
  { "LS_1000M", "EM_PB_NVR_NET_LAN_SPEED_TYPE__LS_1000M", 1000 },
};
static const ProtobufCIntRange em_pb_nvr_net_lan_speed_type__value_ranges[] = {
{0, 0},{10, 1},{100, 2},{1000, 3},{0, 4}
};
static const ProtobufCEnumValueIndex em_pb_nvr_net_lan_speed_type__enum_values_by_name[4] =
{
  { "LS_1000M", 3 },
  { "LS_100M", 2 },
  { "LS_10M", 1 },
  { "LS_AUTO", 0 },
};
const ProtobufCEnumDescriptor em_pb_nvr_net_lan_speed_type__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "EmPbNvrNetLanSpeedType",
  "EmPbNvrNetLanSpeedType",
  "EmPbNvrNetLanSpeedType",
  "",
  4,
  em_pb_nvr_net_lan_speed_type__enum_values_by_number,
  4,
  em_pb_nvr_net_lan_speed_type__enum_values_by_name,
  4,
  em_pb_nvr_net_lan_speed_type__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
static const ProtobufCEnumValue em_pb_nvr_net_work_mode__enum_values_by_number[3] =
{
  { "MUL_ADDR", "EM_PB_NVR_NET_WORK_MODE__MUL_ADDR", 0 },
  { "FAULT_TOL", "EM_PB_NVR_NET_WORK_MODE__FAULT_TOL", 1 },
  { "LOAD_BAL", "EM_PB_NVR_NET_WORK_MODE__LOAD_BAL", 2 },
};
static const ProtobufCIntRange em_pb_nvr_net_work_mode__value_ranges[] = {
{0, 0},{0, 3}
};
static const ProtobufCEnumValueIndex em_pb_nvr_net_work_mode__enum_values_by_name[3] =
{
  { "FAULT_TOL", 1 },
  { "LOAD_BAL", 2 },
  { "MUL_ADDR", 0 },
};
const ProtobufCEnumDescriptor em_pb_nvr_net_work_mode__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "EmPbNvrNetWorkMode",
  "EmPbNvrNetWorkMode",
  "EmPbNvrNetWorkMode",
  "",
  3,
  em_pb_nvr_net_work_mode__enum_values_by_number,
  3,
  em_pb_nvr_net_work_mode__enum_values_by_name,
  1,
  em_pb_nvr_net_work_mode__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
static const ProtobufCEnumValue em_pb_nvr_net_pppoe_status__enum_values_by_number[8] =
{
  { "STOP", "EM_PB_NVR_NET_PPPOE_STATUS__STOP", 0 },
  { "LINK_UP", "EM_PB_NVR_NET_PPPOE_STATUS__LINK_UP", 1 },
  { "LINK_DOWN", "EM_PB_NVR_NET_PPPOE_STATUS__LINK_DOWN", 2 },
  { "DIALING", "EM_PB_NVR_NET_PPPOE_STATUS__DIALING", 3 },
  { "AUTH_FAILED", "EM_PB_NVR_NET_PPPOE_STATUS__AUTH_FAILED", 4 },
  { "TIMEOUT", "EM_PB_NVR_NET_PPPOE_STATUS__TIMEOUT", 5 },
  { "NO_ISP", "EM_PB_NVR_NET_PPPOE_STATUS__NO_ISP", 6 },
  { "SERVER_ERR", "EM_PB_NVR_NET_PPPOE_STATUS__SERVER_ERR", 7 },
};
static const ProtobufCIntRange em_pb_nvr_net_pppoe_status__value_ranges[] = {
{0, 0},{0, 8}
};
static const ProtobufCEnumValueIndex em_pb_nvr_net_pppoe_status__enum_values_by_name[8] =
{
  { "AUTH_FAILED", 4 },
  { "DIALING", 3 },
  { "LINK_DOWN", 2 },
  { "LINK_UP", 1 },
  { "NO_ISP", 6 },
  { "SERVER_ERR", 7 },
  { "STOP", 0 },
  { "TIMEOUT", 5 },
};
const ProtobufCEnumDescriptor em_pb_nvr_net_pppoe_status__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "EmPbNvrNetPppoeStatus",
  "EmPbNvrNetPppoeStatus",
  "EmPbNvrNetPppoeStatus",
  "",
  8,
  em_pb_nvr_net_pppoe_status__enum_values_by_number,
  8,
  em_pb_nvr_net_pppoe_status__enum_values_by_name,
  1,
  em_pb_nvr_net_pppoe_status__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
static const ProtobufCEnumValue em_pb_nvr_net_ddns_service_type__enum_values_by_number[6] =
{
  { "ORAY", "EM_PB_NVR_NET_DDNS_SERVICE_TYPE__ORAY", 0 },
  { "DYNDNS", "EM_PB_NVR_NET_DDNS_SERVICE_TYPE__DYNDNS", 1 },
  { "IP", "EM_PB_NVR_NET_DDNS_SERVICE_TYPE__IP", 2 },
  { "NO_IP", "EM_PB_NVR_NET_DDNS_SERVICE_TYPE__NO_IP", 3 },
  { "CHANGE_IP", "EM_PB_NVR_NET_DDNS_SERVICE_TYPE__CHANGE_IP", 4 },
  { "EASYDDNS", "EM_PB_NVR_NET_DDNS_SERVICE_TYPE__EASYDDNS", 5 },
};
static const ProtobufCIntRange em_pb_nvr_net_ddns_service_type__value_ranges[] = {
{0, 0},{0, 6}
};
static const ProtobufCEnumValueIndex em_pb_nvr_net_ddns_service_type__enum_values_by_name[6] =
{
  { "CHANGE_IP", 4 },
  { "DYNDNS", 1 },
  { "EASYDDNS", 5 },
  { "IP", 2 },
  { "NO_IP", 3 },
  { "ORAY", 0 },
};
const ProtobufCEnumDescriptor em_pb_nvr_net_ddns_service_type__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "EmPbNvrNetDdnsServiceType",
  "EmPbNvrNetDdnsServiceType",
  "EmPbNvrNetDdnsServiceType",
  "",
  6,
  em_pb_nvr_net_ddns_service_type__enum_values_by_number,
  6,
  em_pb_nvr_net_ddns_service_type__enum_values_by_name,
  1,
  em_pb_nvr_net_ddns_service_type__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
static const ProtobufCEnumValue em_pb_nvr_upnp_mode_type__enum_values_by_number[2] =
{
  { "UPNP_AUTO", "EM_PB_NVR_UPNP_MODE_TYPE__UPNP_AUTO", 0 },
  { "UPNP_MANUAL", "EM_PB_NVR_UPNP_MODE_TYPE__UPNP_MANUAL", 1 },
};
static const ProtobufCIntRange em_pb_nvr_upnp_mode_type__value_ranges[] = {
{0, 0},{0, 2}
};
static const ProtobufCEnumValueIndex em_pb_nvr_upnp_mode_type__enum_values_by_name[2] =
{
  { "UPNP_AUTO", 0 },
  { "UPNP_MANUAL", 1 },
};
const ProtobufCEnumDescriptor em_pb_nvr_upnp_mode_type__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "EmPbNvrUpnpModeType",
  "EmPbNvrUpnpModeType",
  "EmPbNvrUpnpModeType",
  "",
  2,
  em_pb_nvr_upnp_mode_type__enum_values_by_number,
  2,
  em_pb_nvr_upnp_mode_type__enum_values_by_name,
  1,
  em_pb_nvr_upnp_mode_type__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
static const ProtobufCEnumValue em_pb_nvr_net_authen_type__enum_values_by_number[2] =
{
  { "AUTHEN_MD5", "EM_PB_NVR_NET_AUTHEN_TYPE__AUTHEN_MD5", 0 },
  { "AUTHEN_SHA", "EM_PB_NVR_NET_AUTHEN_TYPE__AUTHEN_SHA", 1 },
};
static const ProtobufCIntRange em_pb_nvr_net_authen_type__value_ranges[] = {
{0, 0},{0, 2}
};
static const ProtobufCEnumValueIndex em_pb_nvr_net_authen_type__enum_values_by_name[2] =
{
  { "AUTHEN_MD5", 0 },
  { "AUTHEN_SHA", 1 },
};
const ProtobufCEnumDescriptor em_pb_nvr_net_authen_type__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "EmPbNvrNetAuthenType",
  "EmPbNvrNetAuthenType",
  "EmPbNvrNetAuthenType",
  "",
  2,
  em_pb_nvr_net_authen_type__enum_values_by_number,
  2,
  em_pb_nvr_net_authen_type__enum_values_by_name,
  1,
  em_pb_nvr_net_authen_type__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
static const ProtobufCEnumValue em_pb_private_key_alg_type__enum_values_by_number[1] =
{
  { "KEY_CBC_DES", "EM_PB_PRIVATE_KEY_ALG_TYPE__KEY_CBC_DES", 0 },
};
static const ProtobufCIntRange em_pb_private_key_alg_type__value_ranges[] = {
{0, 0},{0, 1}
};
static const ProtobufCEnumValueIndex em_pb_private_key_alg_type__enum_values_by_name[1] =
{
  { "KEY_CBC_DES", 0 },
};
const ProtobufCEnumDescriptor em_pb_private_key_alg_type__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "EmPbPrivateKeyAlgType",
  "EmPbPrivateKeyAlgType",
  "EmPbPrivateKeyAlgType",
  "",
  1,
  em_pb_private_key_alg_type__enum_values_by_number,
  1,
  em_pb_private_key_alg_type__enum_values_by_name,
  1,
  em_pb_private_key_alg_type__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
static const ProtobufCEnumValue em_pb_nvr_net_snmp_ver__enum_values_by_number[2] =
{
  { "SNMP_V1V2", "EM_PB_NVR_NET_SNMP_VER__SNMP_V1V2", 0 },
  { "SNMP_V3", "EM_PB_NVR_NET_SNMP_VER__SNMP_V3", 1 },
};
static const ProtobufCIntRange em_pb_nvr_net_snmp_ver__value_ranges[] = {
{0, 0},{0, 2}
};
static const ProtobufCEnumValueIndex em_pb_nvr_net_snmp_ver__enum_values_by_name[2] =
{
  { "SNMP_V1V2", 0 },
  { "SNMP_V3", 1 },
};
const ProtobufCEnumDescriptor em_pb_nvr_net_snmp_ver__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "EmPbNvrNetSnmpVer",
  "EmPbNvrNetSnmpVer",
  "EmPbNvrNetSnmpVer",
  "",
  2,
  em_pb_nvr_net_snmp_ver__enum_values_by_number,
  2,
  em_pb_nvr_net_snmp_ver__enum_values_by_name,
  1,
  em_pb_nvr_net_snmp_ver__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
