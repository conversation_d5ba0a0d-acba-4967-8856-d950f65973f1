

TOP := ..

COMM_DIR := ../../common/

SRC_DIR := $(TOP)/source
CURDIR := ./

## Name and type of the target for this Makefile

SO_TARGET	      := nvrupgrade
#ARC_TARGET	      := nvrupgrade


## Define debugging symbols
DEBUG = 0
LINUX_COMPILER = _NETRA81XX_
PWLIB_SUPPORT = 0
CFLAGS += -funwind-tables
## Object files that compose the target(s)

OBJS := $(SRC_DIR)/nvrupgrade\
## Libraries to include in shared object file
        
ifneq ($(SLIBS),) 
LDFLAGS += -Wl,-static
LDFLAGS += $(foreach lib,$(SLIBS),-l$(lib))
endif
## Add driver-specific include directory to the search path 

INC_PATH += $(CURDIR)/../include \
		$(CURDIR)/../../common \
		$(CURDIR)/../../../10-common/include/service \
		$(CURDIR)/../../../10-common/include/system\
		$(CURDIR)/../../../10-common/include/cbb/osp\
		$(CURDIR)/../../../10-common/include/cbb/ftpc\
		$(CURDIR)/../../../10-common/include/cbb/crc_check\
		$(CURDIR)/../../../10-common/include/cbb/protobuf\
		$(CURDIR)/../../../10-common/include/cbb/goahead/linux\
		$(CURDIR)/../../../10-common/include/hal/netcbb\
		$(CURDIR)/../../../10-common/include/hal/drvlib_64\
		$(CURDIR)/../../../10-common/include/hal/drvlib_64/system\
		$(CURDIR)/../../../40-service/nvrsys/include\
		$(CURDIR)/../../../40-service/nvrstartap/include
CFLAGS += -D_NETRA81XX_
CFLAGS += -D_NVRUPDATE_CGI_


ifeq ($(PWLIB_SUPPORT),1)
   INC_PATH += $(PWLIBDIR)/include/ptlib/unix $(PWLIBDIR)/include
endif


INSTALL_LIB_PATH = ../../../10-common/lib/release/netra8107
LDFLAGS += -L$(INSTALL_LIB_PATH)
include $(COMM_DIR)/common.mk


