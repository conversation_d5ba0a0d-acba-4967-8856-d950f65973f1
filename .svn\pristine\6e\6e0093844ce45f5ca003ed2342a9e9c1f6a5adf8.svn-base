#include "nvrrec.h"

NVRSTATUS NvrRecInit(){return NVR_ERR__OK;}
NVRSTATUS NvrRecGetChnRecStateDetailList(u16 wChnIdx, u16* pwListNum, TNvrRecChnRecStateDetailInfo *ptRecDetailState){return NVR_ERR__OK;}
NVRSTATUS  NvrRecGetRecStatList(u16 wChnIdx, TNvrRecState* ptRecStatList, u16* pwListNum){return NVR_ERR__OK;}
NVRSTATUS  NvrRecSetStateCallback(BOOL32 bSet,  NvrRecStateCallBack pCallBack){return NVR_ERR__OK;}
NVRSTATUS  NvrRecSetAdvCfg(const TNvrRecAdvCfg* ptRecAdvCfg){return NVR_ERR__OK;}
NVRSTATUS  NvrRecGetAdvCfg(TNvrRecAdvCfg* ptRecAdvCfg){return NVR_ERR__OK;}
NVRSTATUS NvrRecSetChnCfg(u16 wNum, const TNvrRecChnCfg* ptNvrRecChnCfgList){return NVR_ERR__OK;}
NVRSTATUS NvrRecRegSetChnCfgCallBack(PFNvrRecSetCfgNotify pfMp4SetChnCfgCallBack){return NVR_ERR__OK;}
NVRSTATUS NvrRecSetChnMemCfg(u16 wNum, const TNvrRecChnCfg* ptNvrRecChnCfgList){return NVR_ERR__OK;}
NVRSTATUS NvrRecGetChnCfg(u16 wChnId, u16 wNum, TNvrRecChnCfg* ptNvrRecChnCfgList){return NVR_ERR__OK;}
NVRSTATUS NvrRecGetChnMemCfg(u16 wChnId, u16 wNum, TNvrRecChnCfg* ptNvrRecChnCfgList){return NVR_ERR__OK;}
NVRSTATUS NvrRecCopyChnCfg(u16 wSrcChnId, u16 wNum, const u16* pwDstChnIdList){return NVR_ERR__OK;}
NVRSTATUS NvrRecPause(u16 wChnId, ENvrRecType eRecoderType){return NVR_ERR__OK;}
NVRSTATUS NvrRecResume(u16 wChnId, ENvrRecType eRecoderType){return NVR_ERR__OK;}
NVRSTATUS NvrRecSetChnSnapCfg(u16 wNum, const TNvrRecChnSnapCfg* ptChnSnapCfgList){return NVR_ERR__OK;}
NVRSTATUS NvrRecGetChnSnapCfg(u16 wChnId, u16 wNum, TNvrRecChnSnapCfg* ptChnSnapCfgList){return NVR_ERR__OK;}
NVRSTATUS NvrRecCopyChnSnapCfg(u16 wSrcChnId, u16 wNum, const u16* pwDstChnIdList){return NVR_ERR__OK;}
NVRSTATUS NvrRecSetHolidayCfg(u16 wNum, const TNvrRecHolidayCfg* ptRecHolidayCfgList){return NVR_ERR__OK;}
NVRSTATUS NvrRecGetHolidayCfg(u16 wStartIdIdx, u16 wNum,TNvrRecHolidayCfg* ptRecHolidayCfg){return NVR_ERR__OK;}
NVRSTATUS NvrRecGetMonthMap(TNvrRecMonthMapTab* ptMonthMapTab){return NVR_ERR__OK;}
NVRSTATUS NvrRecRegGetMonthMapCallBack(PFNvrRecGetMonthMapCallBack pfMp4MonthMapCallBack){return NVR_ERR__OK;}
NVRSTATUS NvrRecCreateQueryEventTask(const TNvrRecQueryCond* ptRecQueryCond, u32* pdwSearchTaskId){return NVR_ERR__OK;}
NVRSTATUS NvrRecDestroyQueryRecTask(u32 dwTaskId){return NVR_ERR__OK;}
NVRSTATUS NvrRecEventQuery(u32 dwTaskId, u32 dwBeginIndex, u32 dwNeedNum,TNvrRecEventQueryResult* ptQueryResult){return NVR_ERR__OK;}
NVRSTATUS NvrRecQueryImg(TNvrRecImgQueryCond* ptCond, TNvrRecImgQueryResult* ptResult,TNvrRecImgQueryResultItem* pItemList){return NVR_ERR__OK;}
NVRSTATUS NvrRecGetSectionSize(TNvrRecSectionSize* ptSize){return NVR_ERR__OK;}
NVRSTATUS NvrRecReadImg(s8* szImgUri, u8* pbyBuf, u32 dwBufLen, u32 *pdwImageRealSize){return NVR_ERR__OK;}
NVRSTATUS NvrRecGetImgSize(s8* szImgUri, u32 *pdwImageRealSize){return NVR_ERR__OK;}
NVRSTATUS NvrRecDelImg(s8* szImgUri){return NVR_ERR__OK;}
NVRSTATUS NvrRecCreatePlayTask(u32* pdwTaskId, BOOL32 bLocalPlay, NvrRecPlyProgCallBack pfPlayProgCB, ENvrRecSetCHnPlayCondType eParamSetType){return NVR_ERR__OK;}
NVRSTATUS NvrRecQueryPlayProg(u32 dwTaskId, u16 wChnId,u64* pqwSystime, u64* pqwRtpTime, ENvrRecPlayCBState* peStat, TNvrRecPlayTaskStatusList *ptChnPlayErrStatusList){return NVR_ERR__OK;}
NVRSTATUS NvrRecPlayVCRCtrl(u32 dwTaskId, u16 wChnId, const TNvrRecPlayVCRCtrlCmd* ptPlayVCRCmd){return NVR_ERR__OK;}
NVRSTATUS NvrRecSetPlaySyncProperty(u32 dwTaskId, const TNvrRecSyncProperty* ptSyncProperty){return NVR_ERR__OK;}
NVRSTATUS NvrRecSetPlayTaskChn(BOOL32 bAddChn, u32 dwTaskId, u16 wChnId, TNvrRecPlayCond* ptPlayCond){return NVR_ERR__OK;}
NVRSTATUS NvrRecModifyPlayTaskChn(u32 dwTaskId, u16 wChnId, TNvrRecPlayCondMod* ptPlayCondMod){return NVR_ERR__OK;}
NVRSTATUS NvrRecStartPlay(u32 dwTaskId, u16 wChnId){return NVR_ERR__OK;}
NVRSTATUS NvrRecStopPlay(u32 dwTaskId, u16 wChnId){return NVR_ERR__OK;}
NVRSTATUS NvrRecDestroyPlayTask(u32 dwTaskId){return NVR_ERR__OK;}
NVRSTATUS NvrRecStartABPointPlay(u32 dwTaskId, u16 wChnId, u64 qwStartTime, u64 qwEndTime, BOOL32 bMp4){return NVR_ERR__OK;}
NVRSTATUS NvrRecStopABPointPlay(u32 dwTaskId, u16 wChnId){return NVR_ERR__OK;}
NVRSTATUS NvrRecCreateBackupTask(u32* pdwTaskId){return NVR_ERR__OK;}
NVRSTATUS NvrRecDestroyBackupTask(u32 dwTaskId){return NVR_ERR__OK;}
NVRSTATUS NvrRecAddSectionToBackupTask(u32 dwTaskId, const TNvrRecBackupSectionParam* ptSectionList, u16 wSectionNum, u64* pqwTotCap){return NVR_ERR__OK;}
NVRSTATUS NvrRecStartBackupTask(u32 dwTaskId,const TNvrRecUnicodePathString* ptDstPath){return NVR_ERR__OK;}
NVRSTATUS NvrRecQueryBackupTaskProg(u32 dwTaskId, const TNvrRecBackupSectionParam* ptSection, u64* pqwSystime, ENvrRecBakupItemState* peState){return NVR_ERR__OK;}
NVRSTATUS NvrRecCreateImgBackupTask(u32* pdwTaskId){return NVR_ERR__OK;}
NVRSTATUS NvrRecDestroyImgBackupTask(u32 dwTaskId){return NVR_ERR__OK;}
NVRSTATUS NvrRecAddImgToBackupTask(u32 dwTaskId, const TNvrRecImgBackupParam* ptImgList, u16 wImgNum){return NVR_ERR__OK;}
NVRSTATUS NvrRecStartImgBackupTask(u32 dwTaskId,const TNvrRecUnicodePathString* ptDstPath){return NVR_ERR__OK;}
NVRSTATUS NvrRecQueryImgBackupTaskProg(u32 dwTaskId, TNvrRecImgBackupParam* ptImgBakParam, ENvrRecBakupItemState* peState){return NVR_ERR__OK;}
NVRSTATUS NvrRecGetRtMp4RecordInfo(u16 wChnId, ENvrRecType eRecType, TNvrRecMp4RecordInfo* ptMp4RecInfo){return NVR_ERR__OK;}
NVRSTATUS NvrRecGetFileInfo(TNvrRecFileInfo* ptInfo){return NVR_ERR__OK;}
NVRSTATUS NvrRecGetKFSRecordInfo(char * pchUrl, TNvrRecFileInfo * ptInfo){return NVR_ERR__OK;}
NVRSTATUS  NvrRecSetMp4StateCallback(BOOL32 bSet,  NvrRecMp4StateCallBack pCallBack){return NVR_ERR__OK;}
NVRSTATUS NvrRecSndDropBufAndCacheCmd(){return NVR_ERR__OK;}
NVRSTATUS NvrRecGetFrameData(u32 dwTaskId,u16 wChnId, BOOL32 byGetVideo,BOOL32 byCleanBuf,void * ptMsFrame,void  *pnMsRet){return NVR_ERR__OK;}
NVRSTATUS NvrRecLockRecord(TNvrRecLockParam *ptLockParam){return NVR_ERR__OK;}
NVRSTATUS NvrRecUnLockRecord(TNvrRecLockParam *ptLockParam){return NVR_ERR__OK;}
NVRSTATUS NvrRecQuerySearchFileSize(u16 wChnId, u64* pqwStartTime, u64* pqwEndTime, u64* pqwFileSize){return NVR_ERR__OK;}
NVRSTATUS NvrRecGetRecentlyLabel(TNvrRecLabelParam *pLabelParam, u32* pdwNum){return NVR_ERR__OK;}
NVRSTATUS NvrRecRegLabelDiyOperateCallBack(PFNvrRecRecLabelOperateCB pfMp4GetUrlCallBack){return NVR_ERR__OK;}
NVRSTATUS NvrRecLabelOperate(TNvrRecLabelParam *pLabelParam, ENvrLabeEditType eEditType, TNvrRecLabelParam *ptOldLabelParam){return NVR_ERR__OK;}
NVRSTATUS NvrRecCreateLabelSearchTask(u32* pdwTaskId, u16 wChnId, u64 qwStartTime, u64 qwEndTime, u8*abyLableName, u32 dwLabelLen){return NVR_ERR__OK;}
NVRSTATUS NvrRecGetLabelSearchResult(u32 dwTaskId, u32 dwBeginIndex, u32* pdwNeedNum, TNvrRecLabelSearchItem* ptQueryResult, BOOL32 *pbFinish){return NVR_ERR__OK;}
NVRSTATUS NvrRecRegLabelDiySearchCallBack(PFNvrRecRecLabelSearchCB pfMp4GetUrlCallBack){return NVR_ERR__OK;}
NVRSTATUS NvrRecDestoryLabelSearchTask(u32 dwTaskId){return NVR_ERR__OK;}
NVRSTATUS NvrRecStartManualEventRec(const TNvrRecManualEventInfo *ptRecInfo, u64 *pqwRecId){return NVR_ERR__OK;}
NVRSTATUS NvrRecModifyManualEventRec(const u64 qwRecId, const TNvrRecEventDescInfo* pDescInfo){return NVR_ERR__OK;}
NVRSTATUS NvrRecUpdatePlyWebNetParam(u32 dwTaskId, u16 wChnId, const TNvrRecWebPlayTransParam* ptPlayNetParam){return NVR_ERR__OK;}
NVRSTATUS NvrRecSetRpRecThreadNum(u32 dwThreadNum){return NVR_ERR__OK;}
NVRSTATUS NvrRecSwitchCardDeal(){return NVR_ERR__OK;}
NVRSTATUS NvrRecQuerySwitchStatus(ENvrRecSwitchSdCardStatus *peSwtichStatus){return NVR_ERR__OK;}
NVRSTATUS NvrRecMp4Cut(const char ppSrcFilePath[][NVR_MAX_STR256_LEN],u8 byFileNum, const  char *pchDstFilePath, u32 dwStartTime, u32 dwTimeDuration){return NVR_ERR__OK;}
NVRSTATUS NvrRecSetMp4RecThread(u32 dwRecThreadNum){return NVR_ERR__OK;}
NVRSTATUS NvrRecSetRecTimerInternal(u32 dwTimerInternal){return NVR_ERR__OK;}
NVRSTATUS NvrRecSetSnapTimerInternal(u32 dwTimerInternal){return NVR_ERR__OK;}
NVRSTATUS NvrRecRegSnapDataCallBack(NvrRecSnapDataCallBack pfCallBack){return NVR_ERR__OK;}
NVRSTATUS NvrRecRPDataSetNotify(NvrRecRPBlkCoverNotifyCB pfCallBack){return NVR_ERR__OK;}
NVRSTATUS NvrRecRegChangeImgQueryCondCB(NvrRecChangeImgQueryCondCB pfCallBack){return NVR_ERR__OK;}
NVRSTATUS NvrRecRecDownloadStart(u16 wPort, ENvrRecDownloadType eType){return NVR_ERR__OK;}
NVRSTATUS NvrRecRecDownloadStop(){return NVR_ERR__OK;}
NVRSTATUS NvrRecRpdataSync(u16 wChnId, u16 wEncId, ENvrRecType eRecType, u8 byType){return NVR_ERR__OK;}
NVRSTATUS NvrRecSrvSaveSnapPic(const TNvrRecSnapPicInfo* ptPicInfo){return NVR_ERR__OK;}
NVRSTATUS NvrRecNoRecRecordCheckTask(u16 wChnId, TNvrBrokenDownTime* ptTime){return NVR_ERR__OK;}
NVRSTATUS NvrRecRegRecQueryCallBack(NvrRecEventQueryCallBack pfRecQueryCallBack){return NVR_ERR__OK;}
NVRSTATUS NvrRecGetChnRecordInfo(u16 wChnId, TNvrRecChnRecrodInfo *ptRecRecordInfo){return NVR_ERR__OK;}
NVRSTATUS NvrRecRegMp4FileDiyQueryGetUrlCallBack(PFNvrRecMp4FileDiyQueryGetUrl pfMp4GetUrlCallBack){return NVR_ERR__OK;}
NVRSTATUS NvrRecSetBackupSpeed(u32 dwSpeed){return NVR_ERR__OK;}
NVRSTATUS NvrRecGetCurDownLoadPort(TNvrRecCurDownLoadStatu *ptCurDownLoadStatus){return NVR_ERR__OK;}
NVRSTATUS NvrRecSrvSetNextMp4NewDir(u16 wChnId, ENvrRecType eRecType, char *pchFIlePath){return NVR_ERR__OK;}
NVRSTATUS NvrRecGetRecordInfoByTime( u32 dwTaskId, u16 wChnId, u64 qwStartTime, u64 qwEndTime, TNvrRecFileInfo *ptInfo){return NVR_ERR__OK;}
u16 NvrRecGetRpInstanceId(){return 0;}
NVRSTATUS NvrRecSrvPlaySndRtspMsgByTcp(u32 dwTaskId, u16 wChnId, u16 dwSendBufSize, u8* pbySendBuf){return NVR_ERR__OK;}
BOOL32 NvrRecSrvBHavePlyTask(){return FALSE;}



