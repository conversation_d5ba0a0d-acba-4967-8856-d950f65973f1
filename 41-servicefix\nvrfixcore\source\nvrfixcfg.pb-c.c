/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: nvrfixcfg.proto */

/* Do not generate deprecated warnings for self */
#ifndef PROTOBUF_C__NO_DEPRECATED
#define PROTOBUF_C__NO_DEPRECATED
#endif

#include "nvrfixcfg.pb-c.h"
void   tpb_fix_isp_cfg__init
                     (TPbFixIspCfg         *message)
{
  static TPbFixIspCfg init_value = TPB_FIX_ISP_CFG__INIT;
  *message = init_value;
}
size_t tpb_fix_isp_cfg__get_packed_size
                     (const TPbFixIspCfg *message)
{
  assert(message->base.descriptor == &tpb_fix_isp_cfg__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_fix_isp_cfg__pack
                     (const TPbFixIspCfg *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_fix_isp_cfg__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_fix_isp_cfg__pack_to_buffer
                     (const TPbFixIspCfg *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_fix_isp_cfg__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbFixIspCfg *
       tpb_fix_isp_cfg__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbFixIspCfg *)
     protobuf_c_message_unpack (&tpb_fix_isp_cfg__descriptor,
                                allocator, len, data);
}
void   tpb_fix_isp_cfg__free_unpacked
                     (TPbFixIspCfg *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_fix_isp_cfg__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_ptz_ctrl_prm__init
                     (TPbNvrPtzCtrlPrm         *message)
{
  static TPbNvrPtzCtrlPrm init_value = TPB_NVR_PTZ_CTRL_PRM__INIT;
  *message = init_value;
}
size_t tpb_nvr_ptz_ctrl_prm__get_packed_size
                     (const TPbNvrPtzCtrlPrm *message)
{
  assert(message->base.descriptor == &tpb_nvr_ptz_ctrl_prm__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_ptz_ctrl_prm__pack
                     (const TPbNvrPtzCtrlPrm *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_ptz_ctrl_prm__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_ptz_ctrl_prm__pack_to_buffer
                     (const TPbNvrPtzCtrlPrm *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_ptz_ctrl_prm__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrPtzCtrlPrm *
       tpb_nvr_ptz_ctrl_prm__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrPtzCtrlPrm *)
     protobuf_c_message_unpack (&tpb_nvr_ptz_ctrl_prm__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_ptz_ctrl_prm__free_unpacked
                     (TPbNvrPtzCtrlPrm *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_ptz_ctrl_prm__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_ptz_basic_state__init
                     (TPbNvrPtzBasicState         *message)
{
  static TPbNvrPtzBasicState init_value = TPB_NVR_PTZ_BASIC_STATE__INIT;
  *message = init_value;
}
size_t tpb_nvr_ptz_basic_state__get_packed_size
                     (const TPbNvrPtzBasicState *message)
{
  assert(message->base.descriptor == &tpb_nvr_ptz_basic_state__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_ptz_basic_state__pack
                     (const TPbNvrPtzBasicState *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_ptz_basic_state__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_ptz_basic_state__pack_to_buffer
                     (const TPbNvrPtzBasicState *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_ptz_basic_state__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrPtzBasicState *
       tpb_nvr_ptz_basic_state__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrPtzBasicState *)
     protobuf_c_message_unpack (&tpb_nvr_ptz_basic_state__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_ptz_basic_state__free_unpacked
                     (TPbNvrPtzBasicState *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_ptz_basic_state__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_power_off_rsm_cfg__init
                     (TPbNvrPowerOffRsmCfg         *message)
{
  static TPbNvrPowerOffRsmCfg init_value = TPB_NVR_POWER_OFF_RSM_CFG__INIT;
  *message = init_value;
}
size_t tpb_nvr_power_off_rsm_cfg__get_packed_size
                     (const TPbNvrPowerOffRsmCfg *message)
{
  assert(message->base.descriptor == &tpb_nvr_power_off_rsm_cfg__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_power_off_rsm_cfg__pack
                     (const TPbNvrPowerOffRsmCfg *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_power_off_rsm_cfg__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_power_off_rsm_cfg__pack_to_buffer
                     (const TPbNvrPowerOffRsmCfg *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_power_off_rsm_cfg__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrPowerOffRsmCfg *
       tpb_nvr_power_off_rsm_cfg__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrPowerOffRsmCfg *)
     protobuf_c_message_unpack (&tpb_nvr_power_off_rsm_cfg__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_power_off_rsm_cfg__free_unpacked
                     (TPbNvrPowerOffRsmCfg *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_power_off_rsm_cfg__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_preset_info__init
                     (TPbNvrPresetInfo         *message)
{
  static TPbNvrPresetInfo init_value = TPB_NVR_PRESET_INFO__INIT;
  *message = init_value;
}
size_t tpb_nvr_preset_info__get_packed_size
                     (const TPbNvrPresetInfo *message)
{
  assert(message->base.descriptor == &tpb_nvr_preset_info__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_preset_info__pack
                     (const TPbNvrPresetInfo *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_preset_info__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_preset_info__pack_to_buffer
                     (const TPbNvrPresetInfo *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_preset_info__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrPresetInfo *
       tpb_nvr_preset_info__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrPresetInfo *)
     protobuf_c_message_unpack (&tpb_nvr_preset_info__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_preset_info__free_unpacked
                     (TPbNvrPresetInfo *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_preset_info__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_path_crs_preset_info__init
                     (TPbNvrPathCrsPresetInfo         *message)
{
  static TPbNvrPathCrsPresetInfo init_value = TPB_NVR_PATH_CRS_PRESET_INFO__INIT;
  *message = init_value;
}
size_t tpb_nvr_path_crs_preset_info__get_packed_size
                     (const TPbNvrPathCrsPresetInfo *message)
{
  assert(message->base.descriptor == &tpb_nvr_path_crs_preset_info__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_path_crs_preset_info__pack
                     (const TPbNvrPathCrsPresetInfo *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_path_crs_preset_info__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_path_crs_preset_info__pack_to_buffer
                     (const TPbNvrPathCrsPresetInfo *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_path_crs_preset_info__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrPathCrsPresetInfo *
       tpb_nvr_path_crs_preset_info__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrPathCrsPresetInfo *)
     protobuf_c_message_unpack (&tpb_nvr_path_crs_preset_info__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_path_crs_preset_info__free_unpacked
                     (TPbNvrPathCrsPresetInfo *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_path_crs_preset_info__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_path_crs_info__init
                     (TPbNvrPathCrsInfo         *message)
{
  static TPbNvrPathCrsInfo init_value = TPB_NVR_PATH_CRS_INFO__INIT;
  *message = init_value;
}
size_t tpb_nvr_path_crs_info__get_packed_size
                     (const TPbNvrPathCrsInfo *message)
{
  assert(message->base.descriptor == &tpb_nvr_path_crs_info__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_path_crs_info__pack
                     (const TPbNvrPathCrsInfo *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_path_crs_info__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_path_crs_info__pack_to_buffer
                     (const TPbNvrPathCrsInfo *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_path_crs_info__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrPathCrsInfo *
       tpb_nvr_path_crs_info__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrPathCrsInfo *)
     protobuf_c_message_unpack (&tpb_nvr_path_crs_info__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_path_crs_info__free_unpacked
                     (TPbNvrPathCrsInfo *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_path_crs_info__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_watch_on_param__init
                     (TPbNvrWatchOnParam         *message)
{
  static TPbNvrWatchOnParam init_value = TPB_NVR_WATCH_ON_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_watch_on_param__get_packed_size
                     (const TPbNvrWatchOnParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_watch_on_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_watch_on_param__pack
                     (const TPbNvrWatchOnParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_watch_on_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_watch_on_param__pack_to_buffer
                     (const TPbNvrWatchOnParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_watch_on_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrWatchOnParam *
       tpb_nvr_watch_on_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrWatchOnParam *)
     protobuf_c_message_unpack (&tpb_nvr_watch_on_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_watch_on_param__free_unpacked
                     (TPbNvrWatchOnParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_watch_on_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_tming_task_param__init
                     (TPbNvrTmingTaskParam         *message)
{
  static TPbNvrTmingTaskParam init_value = TPB_NVR_TMING_TASK_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_tming_task_param__get_packed_size
                     (const TPbNvrTmingTaskParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_tming_task_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_tming_task_param__pack
                     (const TPbNvrTmingTaskParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_tming_task_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_tming_task_param__pack_to_buffer
                     (const TPbNvrTmingTaskParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_tming_task_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrTmingTaskParam *
       tpb_nvr_tming_task_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrTmingTaskParam *)
     protobuf_c_message_unpack (&tpb_nvr_tming_task_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_tming_task_param__free_unpacked
                     (TPbNvrTmingTaskParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_tming_task_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_tming_task_head__init
                     (TPbNvrTmingTaskHead         *message)
{
  static TPbNvrTmingTaskHead init_value = TPB_NVR_TMING_TASK_HEAD__INIT;
  *message = init_value;
}
size_t tpb_nvr_tming_task_head__get_packed_size
                     (const TPbNvrTmingTaskHead *message)
{
  assert(message->base.descriptor == &tpb_nvr_tming_task_head__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_tming_task_head__pack
                     (const TPbNvrTmingTaskHead *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_tming_task_head__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_tming_task_head__pack_to_buffer
                     (const TPbNvrTmingTaskHead *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_tming_task_head__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrTmingTaskHead *
       tpb_nvr_tming_task_head__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrTmingTaskHead *)
     protobuf_c_message_unpack (&tpb_nvr_tming_task_head__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_tming_task_head__free_unpacked
                     (TPbNvrTmingTaskHead *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_tming_task_head__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_tming_info__init
                     (TPbNvrTmingInfo         *message)
{
  static TPbNvrTmingInfo init_value = TPB_NVR_TMING_INFO__INIT;
  *message = init_value;
}
size_t tpb_nvr_tming_info__get_packed_size
                     (const TPbNvrTmingInfo *message)
{
  assert(message->base.descriptor == &tpb_nvr_tming_info__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_tming_info__pack
                     (const TPbNvrTmingInfo *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_tming_info__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_tming_info__pack_to_buffer
                     (const TPbNvrTmingInfo *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_tming_info__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrTmingInfo *
       tpb_nvr_tming_info__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrTmingInfo *)
     protobuf_c_message_unpack (&tpb_nvr_tming_info__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_tming_info__free_unpacked
                     (TPbNvrTmingInfo *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_tming_info__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_evday_param__init
                     (TPbNvrEvdayParam         *message)
{
  static TPbNvrEvdayParam init_value = TPB_NVR_EVDAY_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_evday_param__get_packed_size
                     (const TPbNvrEvdayParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_evday_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_evday_param__pack
                     (const TPbNvrEvdayParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_evday_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_evday_param__pack_to_buffer
                     (const TPbNvrEvdayParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_evday_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrEvdayParam *
       tpb_nvr_evday_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrEvdayParam *)
     protobuf_c_message_unpack (&tpb_nvr_evday_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_evday_param__free_unpacked
                     (TPbNvrEvdayParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_evday_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_ptz_limit_state__init
                     (TPbNvrPtzLimitState         *message)
{
  static TPbNvrPtzLimitState init_value = TPB_NVR_PTZ_LIMIT_STATE__INIT;
  *message = init_value;
}
size_t tpb_nvr_ptz_limit_state__get_packed_size
                     (const TPbNvrPtzLimitState *message)
{
  assert(message->base.descriptor == &tpb_nvr_ptz_limit_state__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_ptz_limit_state__pack
                     (const TPbNvrPtzLimitState *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_ptz_limit_state__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_ptz_limit_state__pack_to_buffer
                     (const TPbNvrPtzLimitState *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_ptz_limit_state__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrPtzLimitState *
       tpb_nvr_ptz_limit_state__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrPtzLimitState *)
     protobuf_c_message_unpack (&tpb_nvr_ptz_limit_state__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_ptz_limit_state__free_unpacked
                     (TPbNvrPtzLimitState *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_ptz_limit_state__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_sync_scan_info__init
                     (TPbNvrSyncScanInfo         *message)
{
  static TPbNvrSyncScanInfo init_value = TPB_NVR_SYNC_SCAN_INFO__INIT;
  *message = init_value;
}
size_t tpb_nvr_sync_scan_info__get_packed_size
                     (const TPbNvrSyncScanInfo *message)
{
  assert(message->base.descriptor == &tpb_nvr_sync_scan_info__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_sync_scan_info__pack
                     (const TPbNvrSyncScanInfo *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_sync_scan_info__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_sync_scan_info__pack_to_buffer
                     (const TPbNvrSyncScanInfo *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_sync_scan_info__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrSyncScanInfo *
       tpb_nvr_sync_scan_info__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrSyncScanInfo *)
     protobuf_c_message_unpack (&tpb_nvr_sync_scan_info__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_sync_scan_info__free_unpacked
                     (TPbNvrSyncScanInfo *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_sync_scan_info__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_coo_param__init
                     (TPbNvrCooParam         *message)
{
  static TPbNvrCooParam init_value = TPB_NVR_COO_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_coo_param__get_packed_size
                     (const TPbNvrCooParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_coo_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_coo_param__pack
                     (const TPbNvrCooParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_coo_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_coo_param__pack_to_buffer
                     (const TPbNvrCooParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_coo_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrCooParam *
       tpb_nvr_coo_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrCooParam *)
     protobuf_c_message_unpack (&tpb_nvr_coo_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_coo_param__free_unpacked
                     (TPbNvrCooParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_coo_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_de_mist_param__init
                     (TPbNvrDeMistParam         *message)
{
  static TPbNvrDeMistParam init_value = TPB_NVR_DE_MIST_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_de_mist_param__get_packed_size
                     (const TPbNvrDeMistParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_de_mist_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_de_mist_param__pack
                     (const TPbNvrDeMistParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_de_mist_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_de_mist_param__pack_to_buffer
                     (const TPbNvrDeMistParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_de_mist_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrDeMistParam *
       tpb_nvr_de_mist_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrDeMistParam *)
     protobuf_c_message_unpack (&tpb_nvr_de_mist_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_de_mist_param__free_unpacked
                     (TPbNvrDeMistParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_de_mist_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_fix_ptz_cfg__init
                     (TPbFixPtzCfg         *message)
{
  static TPbFixPtzCfg init_value = TPB_FIX_PTZ_CFG__INIT;
  *message = init_value;
}
size_t tpb_fix_ptz_cfg__get_packed_size
                     (const TPbFixPtzCfg *message)
{
  assert(message->base.descriptor == &tpb_fix_ptz_cfg__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_fix_ptz_cfg__pack
                     (const TPbFixPtzCfg *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_fix_ptz_cfg__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_fix_ptz_cfg__pack_to_buffer
                     (const TPbFixPtzCfg *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_fix_ptz_cfg__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbFixPtzCfg *
       tpb_fix_ptz_cfg__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbFixPtzCfg *)
     protobuf_c_message_unpack (&tpb_fix_ptz_cfg__descriptor,
                                allocator, len, data);
}
void   tpb_fix_ptz_cfg__free_unpacked
                     (TPbFixPtzCfg *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_fix_ptz_cfg__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_fix_cfg__init
                     (TPbFixCfg         *message)
{
  static TPbFixCfg init_value = TPB_FIX_CFG__INIT;
  *message = init_value;
}
size_t tpb_fix_cfg__get_packed_size
                     (const TPbFixCfg *message)
{
  assert(message->base.descriptor == &tpb_fix_cfg__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_fix_cfg__pack
                     (const TPbFixCfg *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_fix_cfg__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_fix_cfg__pack_to_buffer
                     (const TPbFixCfg *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_fix_cfg__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbFixCfg *
       tpb_fix_cfg__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbFixCfg *)
     protobuf_c_message_unpack (&tpb_fix_cfg__descriptor,
                                allocator, len, data);
}
void   tpb_fix_cfg__free_unpacked
                     (TPbFixCfg *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_fix_cfg__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
static const ProtobufCFieldDescriptor tpb_fix_isp_cfg__field_descriptors[5] =
{
  {
    "manual_focus",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbFixIspCfg, has_manual_focus),
    offsetof(TPbFixIspCfg, manual_focus),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "zf_day_zoom",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbFixIspCfg, has_zf_day_zoom),
    offsetof(TPbFixIspCfg, zf_day_zoom),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "zf_day_focus",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbFixIspCfg, has_zf_day_focus),
    offsetof(TPbFixIspCfg, zf_day_focus),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "zf_night_zoom",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbFixIspCfg, has_zf_night_zoom),
    offsetof(TPbFixIspCfg, zf_night_zoom),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "zf_night_focus",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbFixIspCfg, has_zf_night_focus),
    offsetof(TPbFixIspCfg, zf_night_focus),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_fix_isp_cfg__field_indices_by_name[] = {
  0,   /* field[0] = manual_focus */
  2,   /* field[2] = zf_day_focus */
  1,   /* field[1] = zf_day_zoom */
  4,   /* field[4] = zf_night_focus */
  3,   /* field[3] = zf_night_zoom */
};
static const ProtobufCIntRange tpb_fix_isp_cfg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 5 }
};
const ProtobufCMessageDescriptor tpb_fix_isp_cfg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbFixIspCfg",
  "TPbFixIspCfg",
  "TPbFixIspCfg",
  "",
  sizeof(TPbFixIspCfg),
  5,
  tpb_fix_isp_cfg__field_descriptors,
  tpb_fix_isp_cfg__field_indices_by_name,
  1,  tpb_fix_isp_cfg__number_ranges,
  (ProtobufCMessageInit) tpb_fix_isp_cfg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_ptz_ctrl_prm__field_descriptors[3] =
{
  {
    "protocal_type",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPtzCtrlPrm, has_protocal_type),
    offsetof(TPbNvrPtzCtrlPrm, protocal_type),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "address",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPtzCtrlPrm, has_address),
    offsetof(TPbNvrPtzCtrlPrm, address),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "extra_address",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPtzCtrlPrm, has_extra_address),
    offsetof(TPbNvrPtzCtrlPrm, extra_address),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_ptz_ctrl_prm__field_indices_by_name[] = {
  1,   /* field[1] = address */
  2,   /* field[2] = extra_address */
  0,   /* field[0] = protocal_type */
};
static const ProtobufCIntRange tpb_nvr_ptz_ctrl_prm__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 3 }
};
const ProtobufCMessageDescriptor tpb_nvr_ptz_ctrl_prm__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrPtzCtrlPrm",
  "TPbNvrPtzCtrlPrm",
  "TPbNvrPtzCtrlPrm",
  "",
  sizeof(TPbNvrPtzCtrlPrm),
  3,
  tpb_nvr_ptz_ctrl_prm__field_descriptors,
  tpb_nvr_ptz_ctrl_prm__field_indices_by_name,
  1,  tpb_nvr_ptz_ctrl_prm__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_ptz_ctrl_prm__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_ptz_basic_state__field_descriptors[38] =
{
  {
    "depth_rate_spd",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPtzBasicState, has_depth_rate_spd),
    offsetof(TPbNvrPtzBasicState, depth_rate_spd),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "scan_speed_value",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPtzBasicState, has_scan_speed_value),
    offsetof(TPbNvrPtzBasicState, scan_speed_value),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "preset_spd_value",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPtzBasicState, has_preset_spd_value),
    offsetof(TPbNvrPtzBasicState, preset_spd_value),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ezoom_speed_value",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPtzBasicState, has_ezoom_speed_value),
    offsetof(TPbNvrPtzBasicState, ezoom_speed_value),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ept_osd_mode",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPtzBasicState, has_ept_osd_mode),
    offsetof(TPbNvrPtzBasicState, ept_osd_mode),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ptz_soft_ver",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrPtzBasicState, ptz_soft_ver),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "vid_enc_freeze",
    7,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPtzBasicState, has_vid_enc_freeze),
    offsetof(TPbNvrPtzBasicState, vid_enc_freeze),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "priority_type",
    8,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPtzBasicState, has_priority_type),
    offsetof(TPbNvrPtzBasicState, priority_type),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "pri_delay_time",
    9,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPtzBasicState, has_pri_delay_time),
    offsetof(TPbNvrPtzBasicState, pri_delay_time),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "infared_state",
    10,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPtzBasicState, has_infared_state),
    offsetof(TPbNvrPtzBasicState, infared_state),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "eInfared_mode",
    11,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPtzBasicState, has_einfared_mode),
    offsetof(TPbNvrPtzBasicState, einfared_mode),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "infared_sens",
    12,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPtzBasicState, has_infared_sens),
    offsetof(TPbNvrPtzBasicState, infared_sens),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "infared_value",
    13,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPtzBasicState, has_infared_value),
    offsetof(TPbNvrPtzBasicState, infared_value),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "near_Infared_value",
    14,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPtzBasicState, has_near_infared_value),
    offsetof(TPbNvrPtzBasicState, near_infared_value),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "e_laser_switch",
    15,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPtzBasicState, has_e_laser_switch),
    offsetof(TPbNvrPtzBasicState, e_laser_switch),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "e_laser_mode",
    16,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPtzBasicState, has_e_laser_mode),
    offsetof(TPbNvrPtzBasicState, e_laser_mode),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "laser_dist",
    17,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPtzBasicState, has_laser_dist),
    offsetof(TPbNvrPtzBasicState, laser_dist),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "laser_intensity",
    18,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPtzBasicState, has_laser_intensity),
    offsetof(TPbNvrPtzBasicState, laser_intensity),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "laser_centrad_mode",
    19,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPtzBasicState, has_laser_centrad_mode),
    offsetof(TPbNvrPtzBasicState, laser_centrad_mode),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "wiper_state",
    20,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPtzBasicState, has_wiper_state),
    offsetof(TPbNvrPtzBasicState, wiper_state),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "defrost_state",
    21,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPtzBasicState, has_defrost_state),
    offsetof(TPbNvrPtzBasicState, defrost_state),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "pos_limit_display",
    22,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPtzBasicState, has_pos_limit_display),
    offsetof(TPbNvrPtzBasicState, pos_limit_display),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "evertica_range",
    23,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPtzBasicState, has_evertica_range),
    offsetof(TPbNvrPtzBasicState, evertica_range),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "laser_centrad_speed",
    24,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPtzBasicState, has_laser_centrad_speed),
    offsetof(TPbNvrPtzBasicState, laser_centrad_speed),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "demist_state",
    25,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPtzBasicState, has_demist_state),
    offsetof(TPbNvrPtzBasicState, demist_state),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "demist_time",
    26,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPtzBasicState, has_demist_time),
    offsetof(TPbNvrPtzBasicState, demist_time),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "sl_state",
    27,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPtzBasicState, has_sl_state),
    offsetof(TPbNvrPtzBasicState, sl_state),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "esl_mode",
    28,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPtzBasicState, has_esl_mode),
    offsetof(TPbNvrPtzBasicState, esl_mode),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "sl_value",
    29,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPtzBasicState, has_sl_value),
    offsetof(TPbNvrPtzBasicState, sl_value),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "power_on_mode",
    30,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPtzBasicState, has_power_on_mode),
    offsetof(TPbNvrPtzBasicState, power_on_mode),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "led_main_switch",
    31,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPtzBasicState, has_led_main_switch),
    offsetof(TPbNvrPtzBasicState, led_main_switch),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "efan_demister_mode",
    32,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPtzBasicState, has_efan_demister_mode),
    offsetof(TPbNvrPtzBasicState, efan_demister_mode),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "power_off_mode",
    33,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPtzBasicState, has_power_off_mode),
    offsetof(TPbNvrPtzBasicState, power_off_mode),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "car_mode",
    34,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPtzBasicState, has_car_mode),
    offsetof(TPbNvrPtzBasicState, car_mode),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ptz_id",
    35,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPtzBasicState, has_ptz_id),
    offsetof(TPbNvrPtzBasicState, ptz_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "manu_limit_pos",
    36,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPtzBasicState, has_manu_limit_pos),
    offsetof(TPbNvrPtzBasicState, manu_limit_pos),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "scan_limit_pos",
    37,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPtzBasicState, has_scan_limit_pos),
    offsetof(TPbNvrPtzBasicState, scan_limit_pos),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ext_wifi_switch",
    38,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPtzBasicState, has_ext_wifi_switch),
    offsetof(TPbNvrPtzBasicState, ext_wifi_switch),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_ptz_basic_state__field_indices_by_name[] = {
  33,   /* field[33] = car_mode */
  20,   /* field[20] = defrost_state */
  24,   /* field[24] = demist_state */
  25,   /* field[25] = demist_time */
  0,   /* field[0] = depth_rate_spd */
  10,   /* field[10] = eInfared_mode */
  15,   /* field[15] = e_laser_mode */
  14,   /* field[14] = e_laser_switch */
  31,   /* field[31] = efan_demister_mode */
  4,   /* field[4] = ept_osd_mode */
  27,   /* field[27] = esl_mode */
  22,   /* field[22] = evertica_range */
  37,   /* field[37] = ext_wifi_switch */
  3,   /* field[3] = ezoom_speed_value */
  11,   /* field[11] = infared_sens */
  9,   /* field[9] = infared_state */
  12,   /* field[12] = infared_value */
  18,   /* field[18] = laser_centrad_mode */
  23,   /* field[23] = laser_centrad_speed */
  16,   /* field[16] = laser_dist */
  17,   /* field[17] = laser_intensity */
  30,   /* field[30] = led_main_switch */
  35,   /* field[35] = manu_limit_pos */
  13,   /* field[13] = near_Infared_value */
  21,   /* field[21] = pos_limit_display */
  32,   /* field[32] = power_off_mode */
  29,   /* field[29] = power_on_mode */
  2,   /* field[2] = preset_spd_value */
  8,   /* field[8] = pri_delay_time */
  7,   /* field[7] = priority_type */
  34,   /* field[34] = ptz_id */
  5,   /* field[5] = ptz_soft_ver */
  36,   /* field[36] = scan_limit_pos */
  1,   /* field[1] = scan_speed_value */
  26,   /* field[26] = sl_state */
  28,   /* field[28] = sl_value */
  6,   /* field[6] = vid_enc_freeze */
  19,   /* field[19] = wiper_state */
};
static const ProtobufCIntRange tpb_nvr_ptz_basic_state__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 38 }
};
const ProtobufCMessageDescriptor tpb_nvr_ptz_basic_state__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrPtzBasicState",
  "TPbNvrPtzBasicState",
  "TPbNvrPtzBasicState",
  "",
  sizeof(TPbNvrPtzBasicState),
  38,
  tpb_nvr_ptz_basic_state__field_descriptors,
  tpb_nvr_ptz_basic_state__field_indices_by_name,
  1,  tpb_nvr_ptz_basic_state__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_ptz_basic_state__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_power_off_rsm_cfg__field_descriptors[3] =
{
  {
    "enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPowerOffRsmCfg, has_enable),
    offsetof(TPbNvrPowerOffRsmCfg, enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "resume_mode",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPowerOffRsmCfg, has_resume_mode),
    offsetof(TPbNvrPowerOffRsmCfg, resume_mode),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "param",
    3,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPowerOffRsmCfg, n_param),
    offsetof(TPbNvrPowerOffRsmCfg, param),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_power_off_rsm_cfg__field_indices_by_name[] = {
  0,   /* field[0] = enable */
  2,   /* field[2] = param */
  1,   /* field[1] = resume_mode */
};
static const ProtobufCIntRange tpb_nvr_power_off_rsm_cfg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 3 }
};
const ProtobufCMessageDescriptor tpb_nvr_power_off_rsm_cfg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrPowerOffRsmCfg",
  "TPbNvrPowerOffRsmCfg",
  "TPbNvrPowerOffRsmCfg",
  "",
  sizeof(TPbNvrPowerOffRsmCfg),
  3,
  tpb_nvr_power_off_rsm_cfg__field_descriptors,
  tpb_nvr_power_off_rsm_cfg__field_indices_by_name,
  1,  tpb_nvr_power_off_rsm_cfg__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_power_off_rsm_cfg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_preset_info__field_descriptors[9] =
{
  {
    "is_set",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPresetInfo, has_is_set),
    offsetof(TPbNvrPresetInfo, is_set),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "foucs_pos",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPresetInfo, has_foucs_pos),
    offsetof(TPbNvrPresetInfo, foucs_pos),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "zoom_pos",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPresetInfo, has_zoom_pos),
    offsetof(TPbNvrPresetInfo, zoom_pos),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "speed",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPresetInfo, has_speed),
    offsetof(TPbNvrPresetInfo, speed),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "alias",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrPresetInfo, alias),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "alias_len",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPresetInfo, has_alias_len),
    offsetof(TPbNvrPresetInfo, alias_len),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "h_pos",
    7,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPresetInfo, has_h_pos),
    offsetof(TPbNvrPresetInfo, h_pos),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "w_vPos",
    8,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPresetInfo, has_w_vpos),
    offsetof(TPbNvrPresetInfo, w_vpos),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "special",
    9,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPresetInfo, has_special),
    offsetof(TPbNvrPresetInfo, special),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_preset_info__field_indices_by_name[] = {
  4,   /* field[4] = alias */
  5,   /* field[5] = alias_len */
  1,   /* field[1] = foucs_pos */
  6,   /* field[6] = h_pos */
  0,   /* field[0] = is_set */
  8,   /* field[8] = special */
  3,   /* field[3] = speed */
  7,   /* field[7] = w_vPos */
  2,   /* field[2] = zoom_pos */
};
static const ProtobufCIntRange tpb_nvr_preset_info__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 9 }
};
const ProtobufCMessageDescriptor tpb_nvr_preset_info__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrPresetInfo",
  "TPbNvrPresetInfo",
  "TPbNvrPresetInfo",
  "",
  sizeof(TPbNvrPresetInfo),
  9,
  tpb_nvr_preset_info__field_descriptors,
  tpb_nvr_preset_info__field_indices_by_name,
  1,  tpb_nvr_preset_info__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_preset_info__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_path_crs_preset_info__field_descriptors[5] =
{
  {
    "preset_id",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPathCrsPresetInfo, has_preset_id),
    offsetof(TPbNvrPathCrsPresetInfo, preset_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "stay_time",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPathCrsPresetInfo, has_stay_time),
    offsetof(TPbNvrPathCrsPresetInfo, stay_time),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "is_set",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPathCrsPresetInfo, has_is_set),
    offsetof(TPbNvrPathCrsPresetInfo, is_set),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "alias",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrPathCrsPresetInfo, alias),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "alias_len",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPathCrsPresetInfo, has_alias_len),
    offsetof(TPbNvrPathCrsPresetInfo, alias_len),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_path_crs_preset_info__field_indices_by_name[] = {
  3,   /* field[3] = alias */
  4,   /* field[4] = alias_len */
  2,   /* field[2] = is_set */
  0,   /* field[0] = preset_id */
  1,   /* field[1] = stay_time */
};
static const ProtobufCIntRange tpb_nvr_path_crs_preset_info__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 5 }
};
const ProtobufCMessageDescriptor tpb_nvr_path_crs_preset_info__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrPathCrsPresetInfo",
  "TPbNvrPathCrsPresetInfo",
  "TPbNvrPathCrsPresetInfo",
  "",
  sizeof(TPbNvrPathCrsPresetInfo),
  5,
  tpb_nvr_path_crs_preset_info__field_descriptors,
  tpb_nvr_path_crs_preset_info__field_indices_by_name,
  1,  tpb_nvr_path_crs_preset_info__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_path_crs_preset_info__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_path_crs_info__field_descriptors[2] =
{
  {
    "preset_cnt",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPathCrsInfo, has_preset_cnt),
    offsetof(TPbNvrPathCrsInfo, preset_cnt),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "pathcrs_preset_info",
    2,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrPathCrsInfo, n_pathcrs_preset_info),
    offsetof(TPbNvrPathCrsInfo, pathcrs_preset_info),
    &tpb_nvr_path_crs_preset_info__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_path_crs_info__field_indices_by_name[] = {
  1,   /* field[1] = pathcrs_preset_info */
  0,   /* field[0] = preset_cnt */
};
static const ProtobufCIntRange tpb_nvr_path_crs_info__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_path_crs_info__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrPathCrsInfo",
  "TPbNvrPathCrsInfo",
  "TPbNvrPathCrsInfo",
  "",
  sizeof(TPbNvrPathCrsInfo),
  2,
  tpb_nvr_path_crs_info__field_descriptors,
  tpb_nvr_path_crs_info__field_indices_by_name,
  1,  tpb_nvr_path_crs_info__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_path_crs_info__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_watch_on_param__field_descriptors[6] =
{
  {
    "enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrWatchOnParam, has_enable),
    offsetof(TPbNvrWatchOnParam, enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "wait_time_ec",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrWatchOnParam, has_wait_time_ec),
    offsetof(TPbNvrWatchOnParam, wait_time_ec),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "task_type",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrWatchOnParam, has_task_type),
    offsetof(TPbNvrWatchOnParam, task_type),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "preset_id",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrWatchOnParam, has_preset_id),
    offsetof(TPbNvrWatchOnParam, preset_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "sync_scan_id",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrWatchOnParam, has_sync_scan_id),
    offsetof(TPbNvrWatchOnParam, sync_scan_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "path_cruise_id",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrWatchOnParam, has_path_cruise_id),
    offsetof(TPbNvrWatchOnParam, path_cruise_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_watch_on_param__field_indices_by_name[] = {
  0,   /* field[0] = enable */
  5,   /* field[5] = path_cruise_id */
  3,   /* field[3] = preset_id */
  4,   /* field[4] = sync_scan_id */
  2,   /* field[2] = task_type */
  1,   /* field[1] = wait_time_ec */
};
static const ProtobufCIntRange tpb_nvr_watch_on_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 6 }
};
const ProtobufCMessageDescriptor tpb_nvr_watch_on_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrWatchOnParam",
  "TPbNvrWatchOnParam",
  "TPbNvrWatchOnParam",
  "",
  sizeof(TPbNvrWatchOnParam),
  6,
  tpb_nvr_watch_on_param__field_descriptors,
  tpb_nvr_watch_on_param__field_indices_by_name,
  1,  tpb_nvr_watch_on_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_watch_on_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_tming_task_param__field_descriptors[2] =
{
  {
    "timing_task_head",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrTmingTaskParam, timing_task_head),
    &tpb_nvr_tming_task_head__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "evday_param",
    2,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrTmingTaskParam, n_evday_param),
    offsetof(TPbNvrTmingTaskParam, evday_param),
    &tpb_nvr_evday_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_tming_task_param__field_indices_by_name[] = {
  1,   /* field[1] = evday_param */
  0,   /* field[0] = timing_task_head */
};
static const ProtobufCIntRange tpb_nvr_tming_task_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_tming_task_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrTmingTaskParam",
  "TPbNvrTmingTaskParam",
  "TPbNvrTmingTaskParam",
  "",
  sizeof(TPbNvrTmingTaskParam),
  2,
  tpb_nvr_tming_task_param__field_descriptors,
  tpb_nvr_tming_task_param__field_indices_by_name,
  1,  tpb_nvr_tming_task_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_tming_task_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_tming_task_head__field_descriptors[5] =
{
  {
    "enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrTmingTaskHead, has_enable),
    offsetof(TPbNvrTmingTaskHead, enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "resume_time",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrTmingTaskHead, has_resume_time),
    offsetof(TPbNvrTmingTaskHead, resume_time),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "data_addr",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrTmingTaskHead, has_data_addr),
    offsetof(TPbNvrTmingTaskHead, data_addr),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "time_period_num",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrTmingTaskHead, has_time_period_num),
    offsetof(TPbNvrTmingTaskHead, time_period_num),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "tming_info_size",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrTmingTaskHead, has_tming_info_size),
    offsetof(TPbNvrTmingTaskHead, tming_info_size),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_tming_task_head__field_indices_by_name[] = {
  2,   /* field[2] = data_addr */
  0,   /* field[0] = enable */
  1,   /* field[1] = resume_time */
  3,   /* field[3] = time_period_num */
  4,   /* field[4] = tming_info_size */
};
static const ProtobufCIntRange tpb_nvr_tming_task_head__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 5 }
};
const ProtobufCMessageDescriptor tpb_nvr_tming_task_head__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrTmingTaskHead",
  "TPbNvrTmingTaskHead",
  "TPbNvrTmingTaskHead",
  "",
  sizeof(TPbNvrTmingTaskHead),
  5,
  tpb_nvr_tming_task_head__field_descriptors,
  tpb_nvr_tming_task_head__field_indices_by_name,
  1,  tpb_nvr_tming_task_head__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_tming_task_head__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_tming_info__field_descriptors[5] =
{
  {
    "is_enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrTmingInfo, has_is_enable),
    offsetof(TPbNvrTmingInfo, is_enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "start_time",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrTmingInfo, has_start_time),
    offsetof(TPbNvrTmingInfo, start_time),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "end_time",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrTmingInfo, has_end_time),
    offsetof(TPbNvrTmingInfo, end_time),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "task_type",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrTmingInfo, has_task_type),
    offsetof(TPbNvrTmingInfo, task_type),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "task_param",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrTmingInfo, has_task_param),
    offsetof(TPbNvrTmingInfo, task_param),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_tming_info__field_indices_by_name[] = {
  2,   /* field[2] = end_time */
  0,   /* field[0] = is_enable */
  1,   /* field[1] = start_time */
  4,   /* field[4] = task_param */
  3,   /* field[3] = task_type */
};
static const ProtobufCIntRange tpb_nvr_tming_info__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 5 }
};
const ProtobufCMessageDescriptor tpb_nvr_tming_info__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrTmingInfo",
  "TPbNvrTmingInfo",
  "TPbNvrTmingInfo",
  "",
  sizeof(TPbNvrTmingInfo),
  5,
  tpb_nvr_tming_info__field_descriptors,
  tpb_nvr_tming_info__field_indices_by_name,
  1,  tpb_nvr_tming_info__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_tming_info__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_evday_param__field_descriptors[1] =
{
  {
    "tming_info",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrEvdayParam, n_tming_info),
    offsetof(TPbNvrEvdayParam, tming_info),
    &tpb_nvr_tming_info__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_evday_param__field_indices_by_name[] = {
  0,   /* field[0] = tming_info */
};
static const ProtobufCIntRange tpb_nvr_evday_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor tpb_nvr_evday_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrEvdayParam",
  "TPbNvrEvdayParam",
  "TPbNvrEvdayParam",
  "",
  sizeof(TPbNvrEvdayParam),
  1,
  tpb_nvr_evday_param__field_descriptors,
  tpb_nvr_evday_param__field_indices_by_name,
  1,  tpb_nvr_evday_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_evday_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_ptz_limit_state__field_descriptors[2] =
{
  {
    "manu_limit_pos",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPtzLimitState, has_manu_limit_pos),
    offsetof(TPbNvrPtzLimitState, manu_limit_pos),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "scan_limit_pos",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPtzLimitState, has_scan_limit_pos),
    offsetof(TPbNvrPtzLimitState, scan_limit_pos),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_ptz_limit_state__field_indices_by_name[] = {
  0,   /* field[0] = manu_limit_pos */
  1,   /* field[1] = scan_limit_pos */
};
static const ProtobufCIntRange tpb_nvr_ptz_limit_state__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_ptz_limit_state__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrPtzLimitState",
  "TPbNvrPtzLimitState",
  "TPbNvrPtzLimitState",
  "",
  sizeof(TPbNvrPtzLimitState),
  2,
  tpb_nvr_ptz_limit_state__field_descriptors,
  tpb_nvr_ptz_limit_state__field_indices_by_name,
  1,  tpb_nvr_ptz_limit_state__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_ptz_limit_state__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_sync_scan_info__field_descriptors[3] =
{
  {
    "e_state",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSyncScanInfo, has_e_state),
    offsetof(TPbNvrSyncScanInfo, e_state),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "path_num",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSyncScanInfo, has_path_num),
    offsetof(TPbNvrSyncScanInfo, path_num),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "exist_flag",
    3,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSyncScanInfo, n_exist_flag),
    offsetof(TPbNvrSyncScanInfo, exist_flag),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_sync_scan_info__field_indices_by_name[] = {
  0,   /* field[0] = e_state */
  2,   /* field[2] = exist_flag */
  1,   /* field[1] = path_num */
};
static const ProtobufCIntRange tpb_nvr_sync_scan_info__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 3 }
};
const ProtobufCMessageDescriptor tpb_nvr_sync_scan_info__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrSyncScanInfo",
  "TPbNvrSyncScanInfo",
  "TPbNvrSyncScanInfo",
  "",
  sizeof(TPbNvrSyncScanInfo),
  3,
  tpb_nvr_sync_scan_info__field_descriptors,
  tpb_nvr_sync_scan_info__field_indices_by_name,
  1,  tpb_nvr_sync_scan_info__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_sync_scan_info__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_coo_param__field_descriptors[2] =
{
  {
    "x_pos",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrCooParam, has_x_pos),
    offsetof(TPbNvrCooParam, x_pos),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "y_pos",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrCooParam, has_y_pos),
    offsetof(TPbNvrCooParam, y_pos),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_coo_param__field_indices_by_name[] = {
  0,   /* field[0] = x_pos */
  1,   /* field[1] = y_pos */
};
static const ProtobufCIntRange tpb_nvr_coo_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_coo_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrCooParam",
  "TPbNvrCooParam",
  "TPbNvrCooParam",
  "",
  sizeof(TPbNvrCooParam),
  2,
  tpb_nvr_coo_param__field_descriptors,
  tpb_nvr_coo_param__field_indices_by_name,
  1,  tpb_nvr_coo_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_coo_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_de_mist_param__field_descriptors[2] =
{
  {
    "demist_mode",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrDeMistParam, has_demist_mode),
    offsetof(TPbNvrDeMistParam, demist_mode),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "demist_time",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrDeMistParam, has_demist_time),
    offsetof(TPbNvrDeMistParam, demist_time),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_de_mist_param__field_indices_by_name[] = {
  0,   /* field[0] = demist_mode */
  1,   /* field[1] = demist_time */
};
static const ProtobufCIntRange tpb_nvr_de_mist_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_de_mist_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrDeMistParam",
  "TPbNvrDeMistParam",
  "TPbNvrDeMistParam",
  "",
  sizeof(TPbNvrDeMistParam),
  2,
  tpb_nvr_de_mist_param__field_descriptors,
  tpb_nvr_de_mist_param__field_indices_by_name,
  1,  tpb_nvr_de_mist_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_de_mist_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_fix_ptz_cfg__field_descriptors[11] =
{
  {
    "ptz_ctrl_param",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbFixPtzCfg, ptz_ctrl_param),
    &tpb_nvr_ptz_ctrl_prm__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ptz_state",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbFixPtzCfg, ptz_state),
    &tpb_nvr_ptz_basic_state__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "poweroff_rsm_cfg",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbFixPtzCfg, poweroff_rsm_cfg),
    &tpb_nvr_power_off_rsm_cfg__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "preset_info",
    4,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbFixPtzCfg, n_preset_info),
    offsetof(TPbFixPtzCfg, preset_info),
    &tpb_nvr_preset_info__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "path_crs_info",
    5,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbFixPtzCfg, n_path_crs_info),
    offsetof(TPbFixPtzCfg, path_crs_info),
    &tpb_nvr_path_crs_info__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "watch_on_param",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbFixPtzCfg, watch_on_param),
    &tpb_nvr_watch_on_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "timing_task_param",
    7,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbFixPtzCfg, timing_task_param),
    &tpb_nvr_tming_task_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "limit_state_param",
    8,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbFixPtzCfg, limit_state_param),
    &tpb_nvr_ptz_limit_state__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "sync_scan_info_param",
    9,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbFixPtzCfg, sync_scan_info_param),
    &tpb_nvr_sync_scan_info__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "cool_param",
    10,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbFixPtzCfg, cool_param),
    &tpb_nvr_coo_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "demist_parm",
    11,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbFixPtzCfg, demist_parm),
    &tpb_nvr_de_mist_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_fix_ptz_cfg__field_indices_by_name[] = {
  9,   /* field[9] = cool_param */
  10,   /* field[10] = demist_parm */
  7,   /* field[7] = limit_state_param */
  4,   /* field[4] = path_crs_info */
  2,   /* field[2] = poweroff_rsm_cfg */
  3,   /* field[3] = preset_info */
  0,   /* field[0] = ptz_ctrl_param */
  1,   /* field[1] = ptz_state */
  8,   /* field[8] = sync_scan_info_param */
  6,   /* field[6] = timing_task_param */
  5,   /* field[5] = watch_on_param */
};
static const ProtobufCIntRange tpb_fix_ptz_cfg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 11 }
};
const ProtobufCMessageDescriptor tpb_fix_ptz_cfg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbFixPtzCfg",
  "TPbFixPtzCfg",
  "TPbFixPtzCfg",
  "",
  sizeof(TPbFixPtzCfg),
  11,
  tpb_fix_ptz_cfg__field_descriptors,
  tpb_fix_ptz_cfg__field_indices_by_name,
  1,  tpb_fix_ptz_cfg__number_ranges,
  (ProtobufCMessageInit) tpb_fix_ptz_cfg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_fix_cfg__field_descriptors[2] =
{
  {
    "fix_isp",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbFixCfg, n_fix_isp),
    offsetof(TPbFixCfg, fix_isp),
    &tpb_fix_isp_cfg__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "fix_ptz",
    2,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbFixCfg, n_fix_ptz),
    offsetof(TPbFixCfg, fix_ptz),
    &tpb_fix_ptz_cfg__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_fix_cfg__field_indices_by_name[] = {
  0,   /* field[0] = fix_isp */
  1,   /* field[1] = fix_ptz */
};
static const ProtobufCIntRange tpb_fix_cfg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_fix_cfg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbFixCfg",
  "TPbFixCfg",
  "TPbFixCfg",
  "",
  sizeof(TPbFixCfg),
  2,
  tpb_fix_cfg__field_descriptors,
  tpb_fix_cfg__field_indices_by_name,
  1,  tpb_fix_cfg__number_ranges,
  (ProtobufCMessageInit) tpb_fix_cfg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
