

TOP := ..

COMM_DIR := ../../common/

SRC_DIR := $(TOP)/../
CURDIR := ./

## Name and type of the target for this Makefile

SO_TARGET	      := nvrcore


## Define debugging symbols
DEBUG = 0
LINUX_COMPILER = _SIMPU_

CFLAGS += -funwind-tables -D_SIMPU_

## Object files that compose the target(s)
## $(SRC_DIR)/nvrsrv/source/nvrsrv \

OBJS := $(SRC_DIR)/nvrsimcode/simais\
		$(SRC_DIR)/nvrsimcode/simdrvlib_simpu\
		$(SRC_DIR)/nvralarm/source/nvralarm \
		$(SRC_DIR)/nvrcap/source/nvrcap \
		$(SRC_DIR)/nvrcoi/source/nvrcoi \
		$(SRC_DIR)/nvrcap/source/nvrcapedgeos \
		$(SRC_DIR)/nvrgeo/source/nvrgeo \
		$(SRC_DIR)/nvrcfg/source/nvrcfg \
		$(SRC_DIR)/nvrdev/source/nvrdev \
		$(SRC_DIR)/nvrguard/source/nvrguard \
		$(SRC_DIR)/nvrlog/source/nvrlog \
		$(SRC_DIR)/nvrprodcttest/source/nvrproduct \
		$(SRC_DIR)/nvrproto/source/nvrpui.pb-c \
		$(SRC_DIR)/nvrproto/source/nvrmpu.pb-c \
		$(SRC_DIR)/nvrproto/source/nvrmbnetwork.pb-c \
		$(SRC_DIR)/nvrproto/source/nvrwifimgr.pb-c \
		$(SRC_DIR)/nvrproto/source/nvrlcammccfg.pb-c \
		$(SRC_DIR)/nvrproto/source/nvrispcfg.pb-c \
		$(SRC_DIR)/nvrproto/source/nvrsmtp.pb-c \
		$(SRC_DIR)/nvrproto/source/nvrdev.pb-c \
		$(SRC_DIR)/nvrproto/source/dmsrv.pb-c \
		$(SRC_DIR)/nvrproto/source/nvrrec.pb-c \
		$(SRC_DIR)/nvrproto/source/nvrlog.pb-c \
		$(SRC_DIR)/nvrproto/source/nvralarm.pb-c \
		$(SRC_DIR)/nvrproto/source/ncpevent.pb-c \
		$(SRC_DIR)/nvrproto/source/nvrcap.pb-c \
		$(SRC_DIR)/nvrproto/source/nvrsys.pb-c \
		$(SRC_DIR)/nvrproto/source/aisvapd.pb-c \
		$(SRC_DIR)/nvrproto/source/nvrais.pb-c \
		$(SRC_DIR)/nvrproto/source/nvrnetwork.pb-c \
		$(SRC_DIR)/nvrproto/source/nvrusrmgr.pb-c \
		$(SRC_DIR)/nvrproto/source/nvrerrno.pb-c \
		$(SRC_DIR)/nvrproto/source/ncpmsg.pb-c \
		$(SRC_DIR)/nvrproto/source/nvrosdcfg.pb-c \
		$(SRC_DIR)/nvrproto/source/nvrdhcpserver.pb-c \
		$(SRC_DIR)/nvrproto/source/nvrextdev.pb-c \
		$(SRC_DIR)/nvrproto/source/nvrftp.pb-c \
		$(SRC_DIR)/nvrproto/source/nvrgeo.pb-c \
		$(SRC_DIR)/nvrproto/source/nvrdynamicplugin.pb-c\
		$(SRC_DIR)/nvrproto/source/lcambasicintel.pb-c\
		$(SRC_DIR)/nvrproto/source/nvrbluetooth.pb-c\
		$(SRC_DIR)/nvrproto/source/nvrhttpscfg.pb-c\
		$(SRC_DIR)/nvrpui/source/nvrpui\
		$(SRC_DIR)/nvrpui/source/nvrpui_test\
		$(SRC_DIR)/nvrpui/source/nvrptzctrl\
		$(SRC_DIR)/nvrqueue/source/nvrqueue \
		$(SRC_DIR)/nvrsimcode/simrec \
		$(SRC_DIR)/nvrsimcode/simrec_in\
		$(SRC_DIR)/nvrsmtp/source/nvrsmtp \
		$(SRC_DIR)/nvrsys/source/nvrsys\
		$(SRC_DIR)/nvrsys/source/nvrbrdapi\
		$(SRC_DIR)/nvrsys/source/nvrsys_in\
		$(SRC_DIR)/nvrsys/source/nvrsystime\
		$(SRC_DIR)/nvrsys/source/nvrsysalloc\
		$(SRC_DIR)/nvrsys/source/nvrsystem\
		$(SRC_DIR)/nvrsys/source/nvrsyshealth\
		$(SRC_DIR)/nvrusrmgr/source/nvrusrmgr\
		$(SRC_DIR)/nvrsimcode/simnvrupgrade\
		$(SRC_DIR)/nvrvtductrl/source/nvrvtductrl\
		$(SRC_DIR)/nvrvtductrl/source/nvrvtductrl_test\
		$(SRC_DIR)/nvrftp/source/nvrftp\
		$(SRC_DIR)/nvrsimcode/simdmsrv\
		$(SRC_DIR)/airp/source/airpsimcode\
		$(SRC_DIR)/nvrsimcode/nvrnetwork_simpu\
		$(SRC_DIR)/nvrsimcode/nvrnetwork_in_simpu\
		$(SRC_DIR)/nvrprobe/source/nvrsimprobe\
		$(SRC_DIR)/nvrsimcode/simnvrmpu_in\
		$(SRC_DIR)/nvrsimcode/simnvrmpu\
		$(SRC_DIR)/nvrsimcode/simnetcbb\
		$(SRC_DIR)/nvrsimcode/simnvrpcap\
		$(SRC_DIR)/nvrsimcode/simupnp\
		$(SRC_DIR)/nvrsimcode/simddnsc\
		$(SRC_DIR)/nvrsimcode/simsysdbg\
		$(SRC_DIR)/nvrsimcode/simftpc\
		$(SRC_DIR)/nvrsimcode/simSDEF\
		$(SRC_DIR)/nvrsimcode/simnvrapmlog\
		$(SRC_DIR)/nvrdynamicplugin/source/nvrdynamicplugin\
		$(SRC_DIR)/nvrstitch/source/nvrstitch\
		

## Libraries to include in shared object file
LDFLAGS += -Wl,-soname,$(SO_TARGET)
ifneq ($(SLIBS),) 
LDFLAGS += -Wl,-static
LDFLAGS += $(foreach lib,$(SLIBS),-l$(lib))
endif
## Add driver-specific include directory to the search path

INC_PATH += \
		$(CURDIR)/../../ais/include \
		$(CURDIR)/../../nvrsrv/include \
		$(CURDIR)/../../nvralarm/include \
		$(CURDIR)/../../nvrcap/include \
		$(CURDIR)/../../nvrcfg/include \
		$(CURDIR)/../../nvrdev/include \
		$(CURDIR)/../../nvrguard/include \
		$(CURDIR)/../../nvrlog/include \
		$(CURDIR)/../../nvrmpu/include \
		$(CURDIR)/../../lcamclt/include \
		$(CURDIR)/../../nvrnetwork/include \
		$(CURDIR)/../../nvrpcap/include \
		$(CURDIR)/../../nvrproto/include \
		$(CURDIR)/../../nvrpui/include \
		$(CURDIR)/../../nvrqueue/include \
		$(CURDIR)/../../nvrrec/include \
		$(CURDIR)/../../nvrsmtp/include \
		$(CURDIR)/../../nvrsys/include \
		$(CURDIR)/../../nvrusrmgr/include \
		$(CURDIR)/../../nvrvtductrl/include \
		$(CURDIR)/../../dmsrv/dmbasic/include \
		$(CURDIR)/../../dmsrv/common\
		$(CURDIR)/../../dmsrv/dmsrv_in/include \
		$(CURDIR)/../../dmsrv/nvriscsictl/include \
		$(CURDIR)/../../airp/include \
		$(CURDIR)/../../nvrftp/include\
		$(CURDIR)/../../nvrprobe/include \
		$(CURDIR)/../../nvrstartap/include \
		$(CURDIR)/../../nvrextdevmgr/include \
		$(CURDIR)/../../common \
		$(CURDIR)/../../../10-common/include/app\
		$(CURDIR)/../../../10-common/include/app/lwshelper\
		$(CURDIR)/../../../10-common/include/cbb/sqilte\
		$(CURDIR)/../../../10-common/include/cbb/osp\
		$(CURDIR)/../../../10-common/include/cbb/protobuf \
		$(CURDIR)/../../../10-common/include/cbb/debuglog \
		$(CURDIR)/../../../10-common/include/cbb/appclt \
		$(CURDIR)/../../../10-common/include/cbb/libpcap\
		$(CURDIR)/../../../10-common/include/cbb/mxml\
		$(CURDIR)/../../../10-common/include/cbb/rp \
		$(CURDIR)/../../../10-common/include/cbb/charconversion \
		$(CURDIR)/../../../10-common/include/cbb/mediaswitch \
		$(CURDIR)/../../../10-common/include/cbb/ipcmediactrl \
		$(CURDIR)/../../../10-common/include/cbb/smtp\
		$(CURDIR)/../../../10-common/include/cbb/crc_check\
		$(CURDIR)/../../../10-common/include/cbb/videoalg_ipc\
		$(CURDIR)/../../../10-common/include/cbb/mbnet\
		$(CURDIR)/../../../10-common/include/cbb/ddnsc\
		$(CURDIR)/../../../10-common/include/cbb/upnp\
		$(CURDIR)/../../../10-common/include/cbb/cjson\
		$(CURDIR)/../../../10-common/include/cbb/wifim\
		$(CURDIR)/../../../10-common/include/cbb/ftpc\
		$(CURDIR)/../../../10-common/include/cbb/kdmfileinterface\
		$(CURDIR)/../../../10-common/include/cbb/kdssl-ext\
		$(CURDIR)/../../../10-common/include/cbb/kshield \
		$(CURDIR)/../../../10-common/include/cbb/libwebsockets\
		$(CURDIR)/../../../10-common/include/cbb/libwebsockets/config_file/linux\
		$(CURDIR)/../../../10-common/include/cbb/md5\
		$(CURDIR)/../../../10-common/include/hal\
		$(CURDIR)/../../../10-common/include/hal/drvlib_64 \
		$(CURDIR)/../../../10-common/include/hal/drvlib_64/system\
		$(CURDIR)/../../../10-common/include/hal/drvlib_64/hardware\
		$(CURDIR)/../../../10-common/include/hal/netcbb \
		$(CURDIR)/../../../10-common/include/hal/sysdbg \
		$(CURDIR)/../../../10-common/include/hal/udm \
		$(CURDIR)/../../../10-common/include/service\
		$(CURDIR)/../../../10-common/include/system\
		$(CURDIR)/../../../30-cbb/sqlite/sqlite3/include\
		$(CURDIR)/../../../10-common/include/algapp\
		$(CURDIR)/../../../10-common/include/cbb/natagent\
		$(CURDIR)/../../../10-common/include/cbb/802dot1x\
		$(CURDIR)/../../../10-common/include/cbb/goahead/linux\
		$(CURDIR)/../../../10-common/include/cbb/openssl\
		$(CURDIR)/../../../10-common/include/cbb

ifeq ($(PWLIB_SUPPORT),1)
   INC_PATH += $(PWLIBDIR)/include/ptlib/unix $(PWLIBDIR)/include
endif


LIB_PATH += ../../../10-common/lib/release/simpu

INSTALL_LIB_PATH = ../../../10-common/lib/release/simpu
LDFLAGS += -L$(INSTALL_LIB_PATH)

LDFLAGS += -L$(LIB_PATH)
LDFLAGS += -L$(INSTALL_LIB_WEBRTC)
LDFLAGS += -L$(INSTALL_LIB_MEDIACTRL)


LDFLAGS += -losp
LDFLAGS += -lprotobufc
LDFLAGS += -ldebuglog
LDFLAGS += -lcharconv
LDFLAGS += -lsmtp
LDFLAGS += -lsqlite3wrapper
LDFLAGS += -lcjson
LDFLAGS += -lnvrcrc
LDFLAGS += -lappclt
LDFLAGS += -lappbase
LDFLAGS += -lmxml
LDFLAGS += -lmediaswitch
LDFLAGS += -lnetpacket
LDFLAGS += -lkdmtsps
LDFLAGS += -lsmtp
LDFLAGS += -lkdmfileinterface
LDFLAGS += -lkdmnatagent
LDFLAGS += -llwshelper
LDFLAGS += -lwebsockets
LDFLAGS += -lpubsecstack
LDFLAGS += -lgoaheadhelper
LDFLAGS += -lnvrmd5
LDFLAGS += -lmalloc


include $(COMM_DIR)/common.mk


