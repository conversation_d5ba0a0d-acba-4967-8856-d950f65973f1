###
### Copyright (c) 2004 Keda Telecom, Inc.
###

#########################################################################
###
###  DESCRIPTION:
###    Common definitions for all Makefiles in OSP linux project.
###
#########################################################################

TOP := ..

COMM_DIR := ../../../common/

SRC_DIR := $(TOP)/source
CUR_DIR := ./
NVR_VOB_DIR := $(CUR_DIR)/../../../../

## Name and type of the target for this Makefile
SO_TARGET	      := nvrsgwmcpro

## Define debugging symbols
DEBUG = 0
LINUX_COMPILER = _HIS3531DV200_
PWLIB_SUPPORT = 0
CFLAGS += -funwind-tables
## Object files that compose the target(s)

OBJS := $(SRC_DIR)/rc


## Libraries to include in shared object file
        
ifneq ($(SLIBS),) 
LDFLAGS += -Wl,-static
LDFLAGS += $(foreach lib,$(SLIBS),-l$(lib))
endif
## Add driver-specific include directory to the search path

INC_PATH += $(CUR_DIR)/../include \
            $(CUR_DIR)/../include/common \
	        $(CUR_DIR)/../../common \
	        $(CUR_DIR)/../../../nvrsgwcom/comdef \
		    $(NVR_VOB_DIR)/10-common/include/cbb/protobuf\
			$(NVR_VOB_DIR)/10-common/include/cbb/mxml\
		    $(NVR_VOB_DIR)/10-common/include/system\
		    $(NVR_VOB_DIR)/10-common/include/service\
		    $(NVR_VOB_DIR)/10-common/include/cbb/osp\
		    $(NVR_VOB_DIR)/10-common/include/cbb/debuglog\
			$(NVR_VOB_DIR)/10-common/include/cbb/mediaswitch\
			$(NVR_VOB_DIR)/10-common/include/cbb/appclt\
			$(NVR_VOB_DIR)/10-common/include/cbb/sqilte\
			$(NVR_VOB_DIR)/10-common/include/cbb/rp\
			$(NVR_VOB_DIR)/10-common/include/cbb/mediactrl\
			$(NVR_VOB_DIR)/10-common/include/cbb/h323/foundation\
			$(NVR_VOB_DIR)/10-common/include/cbb/ftpc\
			$(NVR_VOB_DIR)/10-common/include/cbb/btctrl\
			$(NVR_VOB_DIR)/10-common/include/cbb/charconversion\
			$(NVR_VOB_DIR)/10-common/include/appext\
			$(NVR_VOB_DIR)/10-common/include/app\
			$(NVR_VOB_DIR)/10-common/include/hal/drvlib_64\
			$(NVR_VOB_DIR)/10-common/include/hal/drvlib_64/audio\
			$(NVR_VOB_DIR)/10-common/include/hal/drvlib_64/common\
			$(NVR_VOB_DIR)/10-common/include/hal/drvlib_64/dspcci\
			$(NVR_VOB_DIR)/10-common/include/hal/drvlib_64/hardware\
			$(NVR_VOB_DIR)/10-common/include/hal/drvlib_64/system\
			$(NVR_VOB_DIR)/10-common/include/hal/drvlib_64/video\
			$(NVR_VOB_DIR)/10-common/include/cbb/mcav2.0\
			$(NVR_VOB_DIR)/40-service/nvrsys/include\
			$(NVR_VOB_DIR)/40-service/nvrcap/include\
			$(NVR_VOB_DIR)/10-common/include/app/sdscpapp\

CFLAGS += -D_HIS3531DV200_

ifeq ($(PWLIB_SUPPORT),1)
   INC_PATH += $(PWLIBDIR)/include/ptlib/unix $(PWLIBDIR)/include
endif


INSTALL_LIB_PATH = $(NVR_VOB_DIR)/10-common/lib/release/his3531dv200/speprolib
LDFLAGS += -L$(INSTALL_LIB_PATH)
LDFLAGS += -L$(NVR_VOB_DIR)/10-common/lib/release/his3531dv200/
LDFLAGS += -lnvrvtductrl

include $(COMM_DIR)/common_cpp.mk
