syntax = "proto2";

///<视频编码参数
message TPbNvrSgwVidEncParam
{
    optional uint32		enc_type				=1;       	///<编码格式H.264\MJpeg\H.265\SVAC\JEPG
    optional uint32 	frame_rate				=2;    		///<帧率1~30以后支持到60
    optional uint32		res_width				=3;      	///<分辨率宽
    optional uint32		res_height				=4;    		///<分辨率高
    optional uint32		max_key_rate			=5;    		///<最大关键帧间隔，一般75，1~2500
    optional uint32		bit_rate				=6;       	///<编码码率(kbps)，必须8整除，否则自动调整
    optional uint32		rc_mode					=7;        	///<码率控制方式CBR\VBR
    optional uint32		quality					=8;  		///<图像编码质量,最低\较低\低\中等\较高\最高，定码率固定为中等不可变
    optional uint32		vid_enc_profile_id		=9;   		///<视频编码复杂度
    optional uint32		smart_enc				=10;		///<smart编码
    optional uint32		bit_smooth_level		=11;       	///<视频编码平滑度(0~100)
	optional uint32		crop_x		            =12;       	///<裁剪起始x坐标
	optional uint32		crop_y		            =13;       	///<裁剪起始y坐标
	optional uint32		crop_w		            =14;       	///<裁剪宽
	optional uint32		crop_h		            =15;       	///<裁剪高
    optional uint32		is_enc		            =16;       	///<是否需要编码
	optional uint32		vid_id		            =17;       	///<sdscp任务的视频主通道id
	optional uint32		sub_vid_id		        =18;       	///<sdscp任务的视频子通道id
	optional uint32		aud_id		            =19;       	///<sdscp任务的音频主通道id
	optional uint32		sub_aud_id		        =20;       	///<sdscp任务的音频子通道id
}

///<音频编码参数
message TPbNvrSgwAudEncParam
{
    optional uint32 	type					=1;			///<音频编码格式
    optional uint32   	sample_rate				=2;			///<采样率(kHZ)
    optional uint32     aud_enc_vol				=3; 		///<编码音量
    optional uint32     aec						=4;  		///<回音抵消
}

///<视频通道编码参数
message TPbNvrSgwVidEncParamChn
{
    repeated TPbNvrSgwVidEncParam vid_enc_param =1;
}

///<音频通道编码参数
message TPbNvrSgwAudEncParamChn
{
    repeated TPbNvrSgwAudEncParam aud_enc_param =1;
}

message TPbNvrSgwRk3308Ver
{
    optional bytes 	rk3308_ver 				=1;
}


message TPbNvrSgwCfg
{
    repeated TPbNvrSgwVidEncParamChn vid_enc_chn_param 	=1;      	///<视频编码参数
    repeated TPbNvrSgwAudEncParamChn aud_enc_chn_param 	=2;      	///<音频编码参数
    optional bytes 	rk3308_ver 							=3;   		///<rk3308 version
}