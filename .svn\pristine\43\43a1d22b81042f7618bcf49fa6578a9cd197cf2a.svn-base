path="../../10-common/version/compileinfo/nvrunifiedlog_cv2x.txt"
date>>$path

cd ./prj_linux

echo ==============================================
echo =      nvr_unifiedlog_linux for cv2x           =
echo ==============================================

echo "============compile libnvrunifiedlog cv2x============">>../$path

make -j4 -e DEBUG=0 -f makefile_cv2x clean
make -j4 -e DEBUG=0 -f makefile_cv2x 2>>../$path

cp -L -r -f libnvrunifiedlog.so ../../../10-common/lib/release/cv2x/

cd ..