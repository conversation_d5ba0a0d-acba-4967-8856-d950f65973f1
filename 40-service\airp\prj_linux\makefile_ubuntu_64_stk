

TOP := ..

COMM_DIR := ../../common/

SRC_DIR := $(TOP)/source
CURDIR := ./

## Name and type of the target for this Makefile

SO_TARGET	      := airp


## Define debugging symbols
DEBUG = 1
PWLIB_SUPPORT = 0
CFLAGS += -funwind-tables
## Object files that compose the target(s)

OBJS := $(SRC_DIR)/airp_stk

## Libraries to include in shared object file
        
ifneq ($(SLIBS),) 
LDFLAGS += -Wl,-static
LDFLAGS += $(foreach lib,$(SLIBS),-l$(lib))
endif
## Add driver-specific include directory to the search path

INC_PATH += $(CURDIR)/../include \
		$(CURDIR)/../../common \
		$(CURDIR)/../../../30-cbb/sqlite/sqlite3/include\
		$(CURDIR)/../../../10-common/include/cbb/sqilte\
		$(CURDIR)/../../../10-common/include/service \
		$(CURDIR)/../../../10-common/include/cbb/debuglog\
		$(CURDIR)/../../../10-common/include/system\
		$(CURDIR)/../../../10-common/include/cbb/protobuf \
		$(CURDIR)/../../../10-common/include/cbb/osp \
		$(CURDIR)/../../../10-common/include/cbb/charconversion\
		$(CURDIR)/../../../10-common/include/cbb/mediactrl\
		$(CURDIR)/../../../10-common/include/cbb/mediaswitch\
		$(CURDIR)/../../../10-common/include/cbb/rp\
		$(CURDIR)/../../../10-common/include/cbb/kdmfileinterface\
		$(CURDIR)/../../../10-common/include/cbb/algapp\
		$(CURDIR)/../../../10-common/include/app \
		$(CURDIR)/../../../40-service/ais/include\
		$(CURDIR)/../../../40-service/nvrrec/include\
		$(CURDIR)/../../../40-service/nvrsys/include\
		$(CURDIR)/../../../41-service_nvr/stkcltapp/include\
		$(CURDIR)/../../../10-common/include/serviceext2/common\
		
CFLAGS += -D_SKYLATE_



ifeq ($(PWLIB_SUPPORT),1)
   INC_PATH += $(PWLIBDIR)/include/ptlib/unix $(PWLIBDIR)/include
endif


INSTALL_LIB_PATH = ../../../10-common/lib/release/ubuntu_64/lib_ext2
LDFLAGS += -L$(INSTALL_LIB_PATH)
include $(COMM_DIR)/common.mk


