#include "drvlib_api.h"
#include "drvlib_def.h"

int DrvModuleInit(void){return 0;}
int DrvModuleExit(void){return 0;}

int DrvVersionGet(TDrvVersions *ptVers){return 0;}

void DrvLogSetConsoleLevel(int nLevel){return ;}


/*hardware*/

int BrdButtonQueryInfo(TButtonInfo *tInfo){return 0;}
int BrdButtonGetStatus(u32 dwId, u32 *pdwStatus){return 0;}

int BrdEthQueryInfo(TEthInfo *ptInfo){return 0;}
int BrdEthGetLinkStat(u32 dwNo, int *pnLink){return 0;}
int BrdEthGetNegStat(u32 dwNo, int *pnAutoNego, int *pnDuplex, int *pnSpeed){return 0;}
int BrdEthSetNego(u32 dwNo, int nDuplex, int nSpeed){return 0;}


int BrdHwmonQueryInfo(THwmonInfo *ptInfo){return 0;}
int BrdHwmonGetStatus(THwmonStat *ptStatus){return 0;}
int BrdHwmonSetStatus(THwmonStat *ptStatus){return 0;}


int BrdPinfoQuery(TPrdInfo *ptInfo){return 0;}
int BrdPinfoClean(void){return 0;}
int BrdIspdataClean(void){return 0;}
int BrdPinfoUpdate(const TPrdInfo *ptInfo){return 0;}
int BrdPinfoSetHwver(u32 dwHwver){return 0;}
int BrdPinfoSetHwSubver(u32 dwSubVer){return 0;}
int BrdPinfoSetHWID(u32 dwHwid){return 0;}
int BrdPinfoSetPID(u32 dwPid){return 0;}
int BrdPinfoFlag0Ops(int nOps, u32 dwArg){return 0;}
int BrdPinfoFlag1Ops(int nOps, u32 dwArg){return 0;}
int BrdPinfoSetUserdata(u8 *byData, int nLength){return 0;}
int BrdPinfoSetIspdata(u8 *byData, int nOffset, int nCount){return 0;}
int BrdPinfoGetIspdata(u8 *byData, int nOffset, int nCount){return 0;}
int BrdPinfoGetHwSubver(u32 *byData){return 0;}
int BrdPinfoGetAccId(u32 *byData){return 0;}
int BrdPinfoSetAccId(u32 dwAccid){return 0;}
int BrdPinfoGetHsver(u32 *byData){return 0;}
int BrdPinfoSetHsver(u32 dwHsver){return 0;}
int BrdPinfoGetCPU(u32 *byData){return 0;}
int BrdPinfoSetCPU(u32 dwCpu){return 0;}
int BrdPinfoGetSID(u32 *byData){return 0;}
int BrdPinfoSetSID(u32 dwSid){return 0;}
int BrdPinfoGetHver(u32 *byData){return 0;}
int BrdPinfoSetHver(u32 dwHver){return 0;}
int BrdInfoQuery(TBrdInfo *ptInfo){return 0;}
int BrdCpuInfoQuery(TCpuInfo *ptInfo){return 0;}


int BrdLedQueryInfo(TLedInfo *ptInfo){return 0;}
int BrdLedSetStatus(u32 dwId, u32 dwStatus){return 0;}
int BrdLedGetStatus(u32 dwId, u32 *pdwStatus){return 0;}

int BrdSetSpeakerMode(int nMode){return 0;}
int BrdSwitcherCtrl(int nCmd){return 0;}
int BrdFxoCtrl(int devid, int cmd, void *args){return 0;}
int BrdGpioExtCardConfig(int dwNo, int nDir, int nVal){return 0;}
int BrdGpioExtCardGetInput(int dwNo, int nInputNum, int *nVal){return 0;}
int BrdGpioExtCardSetoutput(int dwNo, int nOutputNum, int nVal){return 0;}
int BrdRwLogIC(fpga_rw_param *pParam){return 0;}

int BrdTimeGet(struct tm *tTime){return 0;}
int BrdTimeSet(const struct tm *tTime){return 0;}
int BrdSetRtcToSysClock(void){return 0;}
int BrdSetSysToRtcClock(void){return 0;}


int BrdSerialQueryInfo(TSerialInfo *ptInfo){return 0;}
int BrdSerialOpen(TSerialInfo *ptInfo){return 0;}
int BrdSerialClose(int nFd){return 0;}
int BrdSerialIoctl(int nFd, int nFunc, void *pArgs){return 0;}
int BrdSerialRead(int nFd, u8 *pbyBuff, int nLength){return 0;}
int BrdSerialWrite(int nFd, const u8 *pbyBuff, int nLength){return 0;}
int BrdSerialSetMode(int nMode){return 0;}
int BrdSerialSelConsole(int nCpuId){return 0;}


int BrdOledQueryInfo(TOledInfo *info){return 0;}
int BrdOledSetStatus(u32 id, u32 status){return 0;}
int BrdOledShowChars(u32 id, u32 pos,char *string){return 0;}

int BrdSleep(int nSleep){return 0;}
int BrdSetBurnStart(int nMode){return 0;}
int BrdSetBurnStop(int nMode){return 0;}
int BrdSetReplay(int nMode){return 0;}
int BrdSetLcdBacklight(int nMode){return 0;}
int BrdSetSoftReset(int nMode){return 0;}
int BrdSetShutdown(void){return 0;}
int BrdSetExtReset(void){return 0;}
int BrdHddPowerEn(int devid, int enbale){return 0;}
int BrdHddHeat(int devid, int enbale){return 0;}
int BrdUSBCheckPort(void){return 0;}
int BrdGetBatteryLevel(int *level){return 0;}
int BrdGetChargeState(int *state){return 0;}
int BrdGetOptoCouplerState(int *state){return 0;}
int BrdSetOptoCoupler(int enable){return 0;}
int BrdMCUBurn(int state){return 0;}
int BrdRstCamra(u32 devId){return 0;}


/*dspcci*/
s32 DspCciOpen(u32 dwDspId, TDspCfg *ptDspCfg , TCciParam *ptCciParam, HDspCciObj *pptObj){return 0;}
s32 DspCciClose(HDspCciObj ptObj){return 0;}
s32 DspCciWriteMsg(HDspCciObj ptObj, u32 dwChnl, TDspCciMsgDesc *ptMsgDesc){return 0;}
s32 DspCciReadMsg(HDspCciObj ptObj, u32 dwChnl,u8 *pbyBuf, u32 *pdwSize, s32 nTimeout){return 0;}
s32 DspCciRecvMsg(HDspCciObj ptObj, u32 dwChnl,TDspCciMsgDesc *ptMsgDesc, s32 nTimeout){return 0;}
s32 DspCciRecvMsgDone(HDspCciObj ptObj, u32 dwChnl, TDspCciMsgDesc *ptMsgDesc){return 0;}
s32 DspCciGetDspId(HDspCciObj ptObj){return 0;}
s32 DspCciGetHeartBeat(HDspCciObj ptObj, u32 *pdwCount){return 0;}
s32 DspCciGetErrLog(HDspCciObj ptObj, u32 dwErrId, u32 *pdwErrLog){return 0;}
s32 DspCciClearUpChnl(HDspCciObj ptObj, u32 dwChnl){return 0;}
s32 DspCciGetVer(char *pchVer, u32 dwBufLen){return 0;}
s32 DspCciRegPrtFunc(TDspPrtFunc ptDspPrtFunc){return 0;}
s32 DspCciGetRxMsgNum(HDspCciObj ptObj, u32 dwChnl, u32 *pdwMsgNum){return 0;}
void DspCciPrtEnable(u32 dwDspId, BOOL32 bEnPrt){return ;}
void DspCciInfoShow(u32 dwDspId){return ;}
void DspCciModuleShow(u32 dwDspId){return ;}


/*system*/
int SysSetBootStatus(u32 dwStatus){return 0;}


int SysUpgradeDoUgAll(const char *filename, u32 flag){return 0;}
int SysUpgradeDoUgboot(const char *filename, u32 flag){return 0;}
int SysUpgradeDev(const char *szFileName, u32 nFlags){return 0;}
int SysUpgradeCleanEnv(void){return 0;}
int SysUpgradeReport(u32 *pdwReport){return 0;}
int SysUpgradeGetVerbose(const char *szFileName, u8 *pchVerbose){return 0;}
#ifdef _QCOM_
int SysSwitchSystem(){return 0;}
#else
int SysSwitchSystem(int nSysId){return 0;}
#endif
int SysUpgradeScm(u32 dwDevId, const char *szFileName){return 0;}
int SysLoadFpga(int nForce, const char *szFilenName){return 0;}
int SysProgramFpga(const char *byData, int nLength, int nInProgress){return 0;}
int SysGetPPStat(void){return 0;}

int SysOpenWdGuard(TWdParams *ptParams){return 0;}
int SysCloseWdGuard(void){return 0;}
int SysNoticeWdGuard(void){return 0;}
int SysWdGuardStatusGet(TWdStatus *ptStatus){return 0;}
int SysHwReset(int nNormalReboot){return 0;}
int SysReset(int nRebootMode){return 0;}


void SysFirmwareExit(void){return ;}
int SysFirmwareQuery(TFirmWareInfo *info){return 0;}
int SysFirmwareOpen(u32 id){return 0;}
int SysFirmwareClose(u32 id){return 0;}
int SysFirmwareErase(u32 id, u32 addr, u32 len){return 0;}
int SysFirmwareRead(u32 id, u32 addr, u8 *buf, u32 len){return 0;}
int SysFirmwareWrite(u32 id, u32 addr, u8 *buf, u32 len){return 0;}




/*video*/
int VidInApiMapIntfToVP(u32 dwVP, u32 dwIntf){return 0;}
int VidInApiCtrl(u32 dwIntf, int nCmd, void *pArgs){return 0;}
int VidOutApiSelVidOutSrc(u32 dwDstIntf, u32 dwSrcIntf){return 0;}
int VidOutApiCtrl(u32 dwIntf, int nCmd, void *pArgs){return 0;}

int VidSensorCtrl(int idx, int cmd, void *args){return 0;}

void lens_exit(void){return ;}
int VidLensCtrl(int nIndex, int nCmd, void *pArgs){return 0;}


int PtzCtrl(int idx, int cmd, void *args){return 0;}


/*audio*/
int AudInApiCtrl(u32 dwIntf, int nCmd, void *pArgs){return 0;}
int AudOutApiCtrl(u32 dwIntf, int nCmd, void *pArgs){return 0;}


s32 MSndOpen(u32 dwDevId, s32 nMode, TMSndDevParam *ptParam, HMSndDev *phDev){return 0;}
s32 MSndClose(HMSndDev hDev){return 0;}
s32 MSndRead(HMSndDev hDev, void *pBuf, size_t size, s32 nTimeoutMs){return 0;}
s32 MSndWrite(HMSndDev hDev, void *pData, size_t size, s32 nTimeoutMs){return 0;}
s32 MSndCtrl(HMSndDev hDev, s32 nCmd, void *pArgs){return 0;}
s32 MSndGetVer(char *pchVer, u32 dwBufLen){return 0;}




