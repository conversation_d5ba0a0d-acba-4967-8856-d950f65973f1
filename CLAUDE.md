# CLAUDE.md
talk in chinese.
This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Architecture

This is a large C/C++ embedded system codebase for NVR (Network Video Recorder) software with a modular service-oriented architecture:

### Directory Structure
- **10-common/**: Common headers, libraries, and shared components
  - `include/`: System headers organized by category (app, cbb, hal, nms, service, system, tts)
  - `lib/`: Compiled libraries for different target platforms
- **40-service/**: Core services and daemons (main application modules)
- **41-servicefix/**: Service fixes and patches

### Key Service Components
- **nvrsrv**: Main NVR server service
- **nvralarm**: Alarm management service  
- **nvrcap**: Video capture service
- **nvrcfg**: Configuration management service
- **nvrdev**: Device management service
- **nvrrec**: Recording service
- **nvrnetwork**: Network management service
- **nvrmpu**: Media processing unit service
- **dmsrv**: Disk/storage management service
- **ais**: AI services

### Cross-Platform Support
The codebase supports multiple embedded platforms:
- HiSilicon chipsets (his3536, his3516av200, his3516dv300, his3519av100, his3531d, his3559a)
- ARM-based platforms (ax603a, rk3568, cv2x)
- MLU220, SSC339G, SSR621Q
- Ubuntu 64-bit (for development/testing)

## Build System

### Build Commands
The project uses make-based build system with cross-compilation support:

```bash
# Build all services for Linux
cd 40-service
bash compile_linux.sh

# Build specific service for HiSilicon platform
cd 40-service/nvralarm
bash compile_linux

# Build for specific platform
bash compile_linux_his3536
bash compile_linux_ubuntu_64
```

### Build Structure
- Each service has platform-specific compile scripts: `compile_linux_[platform]`
- Makefiles are in `prj_linux/` directories: `makefile_[platform]`
- Common build settings in `40-service/common/common.mk`
- Cross-compiler toolchains defined per platform in `common.mk`

### Build Output
- Shared libraries (.so) built to `10-common/lib/release/[platform]/`
- Debug builds available with `DEBUG=1` flag
- Libraries follow naming pattern: `lib[servicename].so`

## Protocol Buffers

The project extensively uses Protocol Buffers for inter-service communication:
- Proto files located in `10-common/include/service/protofiles/`
- Auto-generation script: `pbtoc.sh` 
- Generated headers go to `10-common/include/service/`
- Generated source files go to `40-service/proto/source/`

## Development Workflow

### Adding New Services
1. Create service directory under `40-service/[servicename]/`
2. Add `source/`, `include/`, and `prj_linux/` subdirectories
3. Create platform-specific makefiles using existing services as template
4. Update main compile script to include new service

### Cross-Platform Development
- Use compiler conditionals based on `LINUX_COMPILER` defines
- Platform-specific code sections with `#ifdef _[PLATFORM]_`
- Leverage common headers in `10-common/include/` for portability

### Protocol Development
1. Add/modify .proto files in `protofiles/` directory
2. Run `pbtoc.sh` script to regenerate C bindings
3. Update service makefiles if new proto files added
4. Include generated headers: `#include "service/[proto].pb-c.h"`

## Key Dependencies

### Core Libraries
- protobuf-c: Protocol buffer C bindings
- OpenSSL: Cryptographic functions
- SQLite: Database storage
- LibWebSockets: WebSocket communication
- cJSON: JSON parsing

### Platform Libraries
- HiSilicon Media SDK (for HiSilicon platforms)
- Video algorithm libraries (face detection, vehicle recognition)
- Hardware abstraction layer (HAL) libraries

## Important Notes

- This is an embedded system codebase - memory and performance optimization is critical
- Services communicate via protocol buffers and shared memory
- Cross-platform compatibility must be maintained across all supported embedded targets
- Build system requires specific cross-compiler toolchains installed in /opt/ paths
- No traditional unit testing framework - testing done on target hardware