

TOP := ..

COMM_DIR := ../../common/

SRC_DIR := $(TOP)/source
CURDIR := ./

## Name and type of the target for this Makefile

SO_TARGET	      := nvrkdvsend


## Define debugging symbols
DEBUG = 0
LINUX_COMPILER = _HIS3531D_
PWLIB_SUPPORT = 0
CFLAGS += -funwind-tables
## Object files that compose the target(s)

OBJS := $(SRC_DIR)/nvrkdvsend
## Libraries to include in shared object file
    

LIB_PATH := ../../../10-common/lib/release/his3531d/kdvmedianet 
#LIB_PATH += $(TOP)/lib

ifneq ($(SLIBS),) 
LDFLAGS += -Wl,-static
LDFLAGS += $(foreach lib,$(SLIBS),-l$(lib))
endif
## Add driver-specific include directory to the search path

#INC_PATH += $(CURDIR)/../../../10-common/include/cbb/osp\
	    $(CURDIR)/../../../10-common/include/cbb/debuglog\
            $(CURDIR)/../../../10-common/include/system
INC_PATH += $(TOP)/include\
            $(CURDIR)/../../../10-common/include/cbb/kdvmedianet\
            $(CURDIR)/../../../10-common/include/service\
            $(CURDIR)/../../../10-common/include/cbb/protobuf\
            $(CURDIR)/../../../10-common/include/cbb/mediaswitch\
            $(CURDIR)/../../../10-common/include/system\
            $(CURDIR)/../../../10-common/include/cbb/debuglog\
            $(CURDIR)/../../../10-common/include/cbb/appclt\
            $(CURDIR)/../../../10-common/include/cbb/osp\
            $(CURDIR)/../../../10-common/include/app
#CFLAGS += -D_HIS3531D_   -DMNET_KDM 
CFLAGS += -D_HIS3531D_   -DOSP_NETADDR_UNDEF

LIBS += kdvmedianet


ifeq ($(PWLIB_SUPPORT),1)
   INC_PATH += $(PWLIBDIR)/include/ptlib/unix $(PWLIBDIR)/include
endif


INSTALL_LIB_PATH = ../../../10-common/lib/release/his3531d/nvrkdvsend
#LDFLAGS += -L$(INSTALL_LIB_PATH)
include $(COMM_DIR)/common.mk



