/**
* @file 	nvrsgwdecoder.c
* @brief    nvr 隔离网关解码器业务
* <AUTHOR>
* @date 	2020-07-21
* @version  1.0
* @copyright V1.0  Copyright(C) 2019 NVR All rights reserved.
*/

#include <stdio.h>
#include <string.h>
#include <sys/time.h>
#include <pthread.h>
#include "debuglog.h"
#include "nvrcommon.h"
#include "nvrcap.h"
#include "nvrguard.h"
#include "nvrcap_in.h"
#include "nvrsys.h"
#include "nvrvtductrl.h"
#include "nvrvtductrl_in.h"
#include "video/vid_intf.h"
#include "nvrsys_in.h"
#include "nvrsgwcore.h"
#include "nvrsystem.h"
#include "nvrqueue.h"
#include "drvlib_api.h"

typedef struct tagNvrSgwDecStreamInfo
{
    u32 byChnId;
    u32 byChnDecId;
    u32 dwMsInId;
} TNvrSgwStreamDecInfo;

typedef struct tagNvrSgwStreamInfo
{
    char achTaskID[SDSCPPRPTO_RTMP_TASK_ID_BUF_LEN];
    BOOL bHasVideo;
    TNvrSgwStreamDecInfo tVidInfo;
    BOOL bHasAudio;
    TNvrSgwStreamDecInfo tAudInfo;
} TNvrSgwStreamInfo;

typedef struct tagNvrSgwDecChnInfo
{
    u32 byChnId;
    u32 byChnDecId;
    u32 dwMsInId;
    BOOL bVideo;
    BOOL byBindAudio;
    u32 byBindAudChnID;
    u32 byBindAudChnDecID;
    BOOL bOnly;
    BOOL bTest;
} TNvrSgwDecChnInfo;

typedef struct tagNvrSgwDecRecvFrameSave
{
    u8 byRecvChn1;
    u8 byRecvChn2;
    u8 byRecvChnXx1;
    u8 byRecvChnXx2;
    u8 byRecvChnXx1Need;
    u8 byRecvChnXx2Need;
    s8 abyFileName[64];
} tNvrSgwDecRecvFrameSave;

static NVRSTATUS NvrSgwSdscpDecOptCB(u32 dwEventType, void* pEventData, u32 dwEventDataLen, void* pvContext);
static NVRSTATUS NvrSgwDecStreamStart(TNvrSgwStreamInfo *ptDecInfo);
static void NvrSgwDecStreamStop(TNvrSgwStreamInfo *ptDecInfo);
static void NvrSgwDecRtmpTest(u16 wChnId, u16 wEncId, BOOL32 bPush, BOOL32 bAudio, s8 *pchUrl);
static void NvrSgwDecRtmpStop(s8 *pchTaskId);
static void NvrSgwDecRtmpAuto();
static void NvrSgwDecStopAllRtmp();
static void NvrSgwDecStratOneRtmpTask(s8 *chUrl);
static void NvrSgwDecRtmpSyncTest(u16 wChnId, u16 wEncId, u16 wAChnId, u16 wAEncId, u16 wAV, s8 *pchUrl);
void NvrSgwMediaCtrlMosicAdjust();
void NvrSgwMediaCtrlSetChnMosicInvalidNew(u16 wDecChnId, u16 wDecId);
NVRSTATUS NvrSgwModifyRtmpSdscpRetryTask(s8 *achTaskID, u64 dwReTryTime, u32 dwMsg, u32 dwMsgId);

static void SgwDecSdscpTestSW(u8 *p);
static s32 SgwDecSdscpTestSend(ESdscpProtoCmdType eEventType, void* pEventData, u32 dwEventDataLen, u32 *dwMsgId);

static SEMHANDLE g_hSdscpNotifySem;
static TNvrSgwDecMgr g_tNvrSgwDecMgr;
static pthread_mutex_t  mediactrl_set_lock;
static pthread_mutex_t  video_set_lock;
static pthread_mutex_t  audio_set_lock;
static pthread_mutex_t  rtmp_task_lock;
static pthread_mutex_t  sdscp_task_lock;
static HTIMERHANDLE g_hTaskRetryCheckTimer;
static tNvrSgwDecRecvFrameSave g_tTestSveFrame;
static BOOL g_bUseMcaBuf = TRUE;
static u32 g_dwMsCbVidIntervalMs = NVR_SGW_DEC_NET_CB_INTERVAL_MAX_MSTIME;
static u32 g_dwMsCbAudIntervalMs = NVR_SGW_DEC_NET_CB_INTERVAL_MAX_MSTIME;
static s32 g_dwMsCbShowVChnXXX = -1;
static s32 g_dwMsCbShowAChnXXX = -1;
static BOOL g_bUseAvSync = FALSE;
static TNvrSgwRtmpMgr g_tNvrSgwRtmpMgr;
static BOOL g_atNvrSgwRtmpTaskAddFlag[NVR_SGW_MAX_CHN_NUM][NVR_SGW_MAX_CHN_VID_NUM + NVR_SGW_MAX_CHN_AUD_NUM];
static BOOL g_bMediaPrintEnable = FALSE;
BOOL g_bMedaiInitSucc[NVR_SGW_MAX_CHIP_CHN_NUM] = {0xffee, 0xffee};
BOOL g_bMedaiVidInitSucc = 0;
static u32 g_dwCbMcaSendLevel = NVR_SGW_DEC_MCA_TRANS_MAX_MSTIME * 2;
static u32 g_dwCbPutLevel = NVR_SGW_DEC_MAX_MSTIME * 2;
static s32 g_dwSdscpTestClientFd = -1;
static BOOL g_bRtmpNoDataFlag[NVR_SGW_MAX_CHN_NUM][NVR_SGW_MAX_CHN_VID_NUM];
static TSvrMcaVidResolution g_bRtmpNoDataOldRes[NVR_SGW_MAX_CHN_NUM][NVR_SGW_MAX_CHN_VID_NUM];
static u32 g_dwAvSyncAudChn[NVR_SGW_MAX_CHN_NUM][NVR_SGW_MAX_CHN_VID_NUM];

u8 g_sgw_max_chn_num = 4;

static FILE *pfvid = NULL;
static FILE *pfaid = NULL;
static FILE *pfaidLen = NULL;
static s32 g_aSaveAvId[2][2] = {{0, 0},{0, 0}};
static BOOL g_bSaveAvId = FALSE;

static BOOL g_bChnFull[NVR_SGW_MAX_CHN_NUM] = {FALSE,FALSE,FALSE,FALSE};                     ///< 资源满时未开始解码的的数据不再进入处理
/**队列句柄*/
static PTNvrDoubleList g_ptNvrSgwQueue = NULL;
#define NVR_SGW_STREAM_OVERFLOW         10
#define NVR_SGW_STREAM_CHNOVERFLOW      11
#define NVR_SGW_STREAM_START            1
#define NVR_SGW_STREAM_STOP             2
#define NVR_SGW_STREAM_UPDATE           3
#define NVR_SGW_SDSCP_RCV_TIMEOUT       5000

#define BITRATE_ABS(x, y)               ((x) > (y) ? (x) - (y) : (y) - (x))

static EMediaCtrlVidDecMode g_aDecMode[2] = {MEDIACTRL_VDEC_MODE_BALANCE, MEDIACTRL_VDEC_MODE_BALANCE};
static u16 g_SdscpRcvTimeOut = 5000;
#define NVR_SGW_SDSCP_RCV_TIMEOUT_CFG "/usr/config/sdscptimeout.conf"

s32 NvrSgwFlashErrLogCallback(const char *szFormat, ...)
{
    s32 nActLen = 0;
    va_list pvList;

    if (!g_bMediaPrintEnable)
    {
        return 0;
    }

    char *achMsg = NVRALLOC(NVR_MAX_STR1024_LEN*6 + 128);
    if(NULL == achMsg)
    {
        return 0;
    }
    va_start( pvList, szFormat );
    memset(achMsg, 0, NVR_MAX_STR1024_LEN*6 + 128);
    nActLen = vsnprintf((char *)achMsg, LOG_PRINT_MAX_LEN - 1 ,szFormat, pvList);
    if( nActLen <= 0 || nActLen >= LOG_PRINT_MAX_LEN - 1)
    {
    }
    va_end(pvList);
    DebugLogFlash(FLASH_LOG_ERR, "%s", achMsg);
    if(NULL != achMsg)
    {
        NVRFREE(achMsg);
    }
    return 0;
}

s32 NvrSgwFlashNoticeLogCallback(const char *szFormat, ...)
{
    s32 nActLen = 0;
    va_list pvList;

    if (!g_bMediaPrintEnable)
    {
        return 0;
    }

    char *achMsg = NVRALLOC(NVR_MAX_STR1024_LEN*6 + 128);
    if(NULL == achMsg)
    {
        return 0;
    }
    va_start( pvList, szFormat );
    memset(achMsg, 0, NVR_MAX_STR1024_LEN*6 + 128);
    nActLen = vsnprintf((char *)achMsg, LOG_PRINT_MAX_LEN - 1 ,szFormat, pvList);
    if( nActLen <= 0 || nActLen >= LOG_PRINT_MAX_LEN - 1)
    {
    }
    va_end(pvList);
    DebugLogFlash(FLASH_LOG_NOTICE, "%s", achMsg);
    if(NULL != achMsg)
    {
        NVRFREE(achMsg);
    }
    return 0;
}

s32 NvrSgwMemNoticeLogCallback(const char *szFormat, ...)
{
    s32 nActLen = 0;
    va_list pvList;

    if (!g_bMediaPrintEnable)
    {
        return 0;
    }

    char *achMsg = NVRALLOC(NVR_MAX_STR1024_LEN*6 + 128);
    if(NULL == achMsg)
    {
        return 0;
    }
    va_start( pvList, szFormat );
    memset(achMsg, 0, NVR_MAX_STR1024_LEN*6 + 128);
    nActLen = vsnprintf((char *)achMsg, LOG_PRINT_MAX_LEN - 1 ,szFormat, pvList);
    if( nActLen <= 0 || nActLen >= LOG_PRINT_MAX_LEN - 1)
    {

    }
    va_end(pvList);
    DebugLogMem(MEM_LOG_NOTICE_CORE, DEBUG_LOG_MOD_MEDIACTRL, "%s", achMsg);
    if(NULL != achMsg)
    {
        NVRFREE(achMsg);
    }
    return 0;
}

void WriteData2File(s8 *pfileName, u8 *pbyFileData, s32 nLen)
{
    if( NULL == pbyFileData )
        return;

    s8 achFilePath[255] = {0};
    u32 dwWriteLen = 0;
    snprintf(achFilePath,sizeof(achFilePath)-1, "/tmp/%s", pfileName);

    FILE *pf = NULL;
    pf = fopen(achFilePath,"wb+");

    if( NULL != pf )
    {
        while (nLen > 0)
        {
            dwWriteLen = fwrite(pbyFileData, 1, nLen, pf);
            nLen -= dwWriteLen;
        }

        fclose(pf);
    }
}

void NvrSgwDecStreamAdd(s32 vid, s32 aud, s32 dwMsInIdV, s32 dwMsInIdA)
{
    TNvrSgwDecChnInfo tDecInfo;
    SGWPRINTIMP("vid:%d aud:%d %d %d\n", vid, aud, dwMsInIdV, dwMsInIdA);
    if (vid >= 0)
    {
        mzero(tDecInfo);
        tDecInfo.byChnId = vid / 100;
        tDecInfo.byChnDecId = vid % 100;

        if (aud >= 0)
        {
            tDecInfo.byBindAudio = TRUE;
            tDecInfo.byBindAudChnID = aud / 100;
            tDecInfo.byBindAudChnDecID = aud % 100;
        }
        tDecInfo.bVideo = TRUE;
        tDecInfo.dwMsInId = dwMsInIdV;
//        NvrSgwDecStreamStart(&tDecInfo);
    }


    if (aud >= 0)
    {
        mzero(tDecInfo);
        tDecInfo.byChnId = aud / 100;
        tDecInfo.byChnDecId = aud % 100;
        tDecInfo.bVideo = FALSE;
        tDecInfo.dwMsInId = dwMsInIdA;
//        NvrSgwDecStreamStart(&tDecInfo);
    }
}

void NvrSgwDecStreamDel(s32 vid, s32 aud)
{
    TNvrSgwDecChnInfo tDecInfo;
    SGWPRINTIMP("vid:%d aud:%d\n", vid, aud);
    if (vid >= 0)
    {
        mzero(tDecInfo);
        tDecInfo.byChnId = vid / 100;
        tDecInfo.byChnDecId = vid % 100;
//        NvrSgwDecStreamStop(&tDecInfo);
    }

    if (aud >= 0)
    {
        mzero(tDecInfo);
        tDecInfo.byChnId = aud / 100;
        tDecInfo.byChnDecId = aud % 100;
//        NvrSgwDecStreamStop(&tDecInfo);
    }
}

void NvrSgwDecMscbInterval(u32 dwVidInterval, u32 dwAudInterval, s32 nVChnXXX, s32 nAChnXXX)
{
    g_dwMsCbVidIntervalMs = dwVidInterval;
    g_dwMsCbAudIntervalMs = dwAudInterval;
    g_dwMsCbShowVChnXXX = nVChnXXX;
    g_dwMsCbShowAChnXXX = nAChnXXX;

    SGWPRINTIMP("set g_dwMsCbVidIntervalMs:%u, g_dwMsCbAudIntervalMs:%u, g_dwMsCbShowVChnXXX:%d , g_dwMsCbShowAChnXXX:%d \n",
            g_dwMsCbVidIntervalMs, g_dwMsCbAudIntervalMs, g_dwMsCbShowVChnXXX, g_dwMsCbShowAChnXXX);
}

void NvrSgwDecDevClear(s32 nVChn, s32 nDecChnId)
{
    s32 nRet = 0;
    EMediactrlVidDisDev eDev = MEDIACTRL_VDIS_DEV_BT1120;
    eDev = 0 == nVChn ? MEDIACTRL_VDIS_DEV_BT1120 : MEDIACTRL_VDIS_DEV_HDMI1;
    nRet = MediaCtrlVidDisClearWin(eDev, nDecChnId);
    SGWPRINTDBG("clear wim  eDev:%d nDecChnId:%d ret:%d\n", eDev, nDecChnId, nRet);
}

void NvrSgwDecDbgLevel(u32 dwMcaSend, u32 dwPutLevel)
{
    if (dwMcaSend > 0)
    {
        g_dwCbMcaSendLevel = dwMcaSend;
    }

    if (dwPutLevel > 0)
    {
        g_dwCbPutLevel = dwPutLevel;
    }
    SGWPRINTDBG("debug print level  g_dwCbMcaSendLevel:%u g_dwCbPutLevel:%u\n", g_dwCbMcaSendLevel, g_dwCbPutLevel);
}

void NvrSgwSetDecMode(s32 rcMode, s32 epMode)
{
    g_aDecMode[0] = rcMode < MEDIACTRL_VDEC_MODE_MAX ? rcMode : g_aDecMode[0] ;
    g_aDecMode[1] = epMode < MEDIACTRL_VDEC_MODE_MAX ? epMode : g_aDecMode[1] ;

    NvrSgwMediaCtrlMosicAdjust();

    SGWPRINTDBG("dec dev mode rc:%d ep:%d (0:real 1:flu 2:bal)\n", g_aDecMode[0], g_aDecMode[1]);
}

void NvrSgwSetMcInitret(s32 rcAudMode, s32 epudMode, s32 vidMode)
{
    g_bMedaiInitSucc[0] = rcAudMode;
    g_bMedaiInitSucc[1] = epudMode;
    g_bMedaiVidInitSucc = vidMode;

    SGWPRINTDBG("dec dev mc ret vid:%d aud rc:%d ep:%d \n", g_bMedaiVidInitSucc,  g_bMedaiInitSucc[0],  g_bMedaiInitSucc[1]);
}


void NvrSgwDecSaveAv(u32 vid, u32 aid, s32 nCount)
{
    do
    {
        g_aSaveAvId[0][0] = vid;
        g_aSaveAvId[0][1] = nCount;
        g_aSaveAvId[1][0] = aid;
        g_aSaveAvId[1][1] = 0;

        pfvid = fopen("/tmp/vidsave.h264","wb+");
        if( NULL == pfvid )
        {
            break;
        }
        pfaid = fopen("/tmp/audsave.aaclc","wb+");
        if( NULL == pfaid )
        {
           break;
        }
        pfaidLen = fopen("/tmp/audsave.len","wb+");
        if( NULL == pfaidLen )
        {
           break;
        }
        g_bSaveAvId = TRUE;
    } while(0);
    SGWPRINTDBG("stream save vid:%d aid:%d count:%d bsave:%d\n",
            g_aSaveAvId[0][0], g_aSaveAvId[1][0], g_aSaveAvId[0][1], g_bSaveAvId);
}

void NvrSgwFrameSave(u16 wDecDevId, BOOL bVid, TKDFrame *pFrame)
{
    static BOOL bSave = FALSE;
    u32 dwWriteLen = 0;

    if (g_bSaveAvId)
    {
        if (wDecDevId == g_aSaveAvId[0][0] && bVid && !bSave)
        {
            if (0 == g_aSaveAvId[1][1])
            {
                if (pFrame->x.m_tVideoParam.m_bKeyFrame)
                {
                    bSave =TRUE;
                }
            }
        }

        if (bSave)
        {
            if (bVid && pfvid && wDecDevId == g_aSaveAvId[0][0])
            {
                g_aSaveAvId[1][1]++;
                dwWriteLen = fwrite(pFrame->m_pData, 1, pFrame->m_dwDataSize, pfvid);
                if (pFrame->m_dwDataSize != dwWriteLen)
                {
                    SGWPRINTERR("stream save vid:%d  err %d %d\n",
                                g_aSaveAvId[0][0],dwWriteLen, pFrame->m_dwDataSize);
                }
                else
                {
                    SGWPRINTDBG("stream save vid:%d save:%d\n",  g_aSaveAvId[0][0], dwWriteLen);
                }
            }

            if (!bVid && pfaid && wDecDevId == g_aSaveAvId[1][0])
            {
                dwWriteLen = fwrite(pFrame->m_pData, 1, pFrame->m_dwDataSize, pfaid);
                if (pFrame->m_dwDataSize != dwWriteLen)
                {
                    SGWPRINTERR("stream save aid:%d  err %d %d\n",
                                g_aSaveAvId[1][0],dwWriteLen, pFrame->m_dwDataSize);
                }
                else
                {
                    s8 achLen[32] = {0};
                    SGWPRINTDBG("stream save aid:%d save:%d\n",  g_aSaveAvId[1][0], dwWriteLen);
                    sprintf(achLen, "%u\r\n", dwWriteLen);
                    if(pfaidLen)
                        fputs(achLen, pfaidLen);
                }

            }

            if (g_aSaveAvId[1][1] == g_aSaveAvId[0][1])
            {
                g_bSaveAvId = FALSE;
                if (pfvid)
                {
                    fclose(pfvid);
                    pfvid = NULL;
                }
                if (pfaid)
                {
                    fclose(pfaid);
                    pfaid = NULL;
                }
                if (pfaidLen)
                {
                    fclose(pfaidLen);
                    pfaidLen = NULL;
                }
                bSave = FALSE;
                SGWPRINTDBG("stream save over vid:%d aid:%d count:%d bsave:%d\n",
                        g_aSaveAvId[0][0], g_aSaveAvId[1][0], g_aSaveAvId[0][1], g_bSaveAvId);
            }
        }
    }
}

NVRSTATUS NvrSgwPushQueueOpt(u32 dwOptType,u8 byMergerType,u32 dwLen,char *pBuf)
{
    NVRSTATUS eRet = NVR_ERR__OK;
    TNvrDoubleListPushAttr tNodeAttr;

    mzero(tNodeAttr);

    tNodeAttr.byPriority = NVR_QUEUE_NODE_PRIORITY_NORMAL;
    tNodeAttr.byMergerType = byMergerType;
    tNodeAttr.dwType = dwOptType;
    tNodeAttr.pchDataBuf = pBuf;
    tNodeAttr.dwDataLen = dwLen;


    if(NULL != pBuf)
    {
        SGWPRINTFREQ("opttype:%lu,priority:%u,mergertype:%u,datalen:%lu,buf:%s\n",\
        dwOptType,tNodeAttr.byPriority,tNodeAttr.byMergerType,tNodeAttr.dwDataLen,pBuf);
    }
    else
    {
        SGWPRINTFREQ("opttype:%lu,priority:%u,mergertype:%u,datalen:%lu\n",\
        dwOptType,tNodeAttr.byPriority,tNodeAttr.byMergerType,tNodeAttr.dwDataLen);
    }


    eRet = NvrQueuePush(g_ptNvrSgwQueue, &tNodeAttr);
    if(NVR_ERR__OK != eRet)
    {
        SGWPRINTERR("NvrQueuePush failed ret:%d\n",eRet);
    }
    else
    {
        SGWPRINTFREQ("NvrQueuePush type :%lu succ \n",dwOptType);
    }
    return eRet;
}


NVRSTATUS NvrSgwSndSdscpEncInfo(void *ptLayOutEncInfo, void *pvContext)
{
    NVRSTATUS eRet = NVR_ERR__OK;
    pthread_mutex_lock(&sdscp_task_lock);

    eRet = NvrVtduSndSdscpEncInfo(ptLayOutEncInfo, pvContext);

    pthread_mutex_unlock(&sdscp_task_lock);

    return eRet;
}

s32 NvrStyleSetTotype(s32 nStyle)
{
    s32 nType = SDSCPPROTO_WIN_LAYOUT_1;

    if (nStyle < 4)
    {
        nType = SDSCPPROTO_WIN_LAYOUT_1;
    }
    else if (nStyle < 9)
    {
        nType = SDSCPPROTO_WIN_LAYOUT_4;
    }
    else if (nStyle < 16)
    {
        nType = SDSCPPROTO_WIN_LAYOUT_9;
    }
    else if (nStyle < 25)
    {
        nType = SDSCPPROTO_WIN_LAYOUT_16;
    }
    else if (nStyle < 36)
    {
        nType = SDSCPPROTO_WIN_LAYOUT_25;
    }
    else
    {
        nType = SDSCPPROTO_WIN_LAYOUT_36;
    }

    return nType;
}

void NvrSgwDecStyle(u8 s1, u8 s2, u8 s3, u8 s4)
{
    TSdscpProtoReqSetCompStyleParam tSetParam;
    TSdscpProtoRespGetCompStyleParam tGetParam;

    mzero(tSetParam);
    mzero(tGetParam);

    tSetParam.aTypeArray[3] = NvrStyleSetTotype(s4);
    tSetParam.aTypeArray[2] = NvrStyleSetTotype(s3);
    tSetParam.aTypeArray[1] = NvrStyleSetTotype(s2);
    tSetParam.aTypeArray[0] = NvrStyleSetTotype(s1);
    tSetParam.dwChnNum = 4;
    SdscpAppCoreSetCompStyleParam(&tSetParam);

    SdscpAppCoreGetCompStyleParam(&tGetParam);

    SGWPRINTIMP("get style:%d-%d-%d-%d\n", tGetParam.aTypeArray[0], tGetParam.aTypeArray[1],
            tGetParam.aTypeArray[2], tGetParam.aTypeArray[3]);
}

void NvrSgwDecSetMcaBuf(u8 mode)
{
    g_bUseMcaBuf = mode;
    SGWPRINTIMP("g_bUseMcaBuf %d\n", g_bUseMcaBuf);
}

void NvrSgwDecSetMediaPrint(u8 mode)
{
    g_bMediaPrintEnable = mode;
    SGWPRINTIMP("g_bMediaPrintEnable %d\n", g_bMediaPrintEnable);
}


void NvrSgwDecRecvFrameSave(s32 p1, s32 p2, s8 *p3)
{
    g_tTestSveFrame.byRecvChn1 = p1 / 100;
    g_tTestSveFrame.byRecvChn2 = p2 / 100;
    g_tTestSveFrame.byRecvChnXx1 = p1 % 100;
    g_tTestSveFrame.byRecvChnXx2 = p2 % 100;
    snprintf(g_tTestSveFrame.abyFileName, sizeof(g_tTestSveFrame.abyFileName), "%s", p3 ? p3 :"sgwSave");
    g_tTestSveFrame.byRecvChnXx1Need = TRUE;
    g_tTestSveFrame.byRecvChnXx2Need = TRUE;
}

void NvrSgwShowdecSync(s32 p1, s32 p2, s8 *p3)
{
    s32 i = 0;
    TMediaCtrlAVSyncParam tAvsyncParam;
    mzero(tAvsyncParam);
    MediaCtrlGetAVSyncParam(&tAvsyncParam);
    for (i = 0; i < MEDIACTRL_MAX_VID_DEC_NUM; i++)
    {
        if (tAvsyncParam.atAVSyncParam[i].bAVSyncEnable)
        {
            SGWPRINTIMP("get index:%d enable:%d nVDecDevId:%d nADecDevId:%d sync\n", i,
                    tAvsyncParam.atAVSyncParam[i].bAVSyncEnable,
                    tAvsyncParam.atAVSyncParam[i].nVidDecIdx,
                    tAvsyncParam.atAVSyncParam[i].nAudDecIdx);
        }
    }

    {
        g_bUseAvSync = p1;
        SGWPRINTIMP("g_bUseAvSync %d\n", g_bUseAvSync);
    }

}

void NvrSgwShowdecmosic(s32 p1, s32 p2, s8 *p3)
{
    s32 i = 0, j = 0;


    s32 chnMapValue  = 0;

    for (j = 0; j < NVR_SGW_MAX_CHN_NUM; j++)
    {
        SGWPRINTIMP("mosic info \n\t\tbOverlay:%d dwNumberOfWindows:%u \n",
                g_tNvrSgwDecMgr.atMosaicParam[j].bOverlay, g_tNvrSgwDecMgr.atMosaicParam[j].dwNumberOfWindows);
        if (p1 == j)
        {
            s32 anExist[64] = {0};
            for(i = 0 ; i < g_tNvrSgwDecMgr.atMosaicParam[j].dwNumberOfWindows; i++)
            {
                chnMapValue =  g_tNvrSgwDecMgr.atMosaicParam[j].adwChnMap[i];
                if (chnMapValue >= 0)
                {
                    if (anExist[chnMapValue]) {
                        SGWPRINTERR("Duplicate adwChnMap value found: %d\n", chnMapValue);
                    } else {
                        anExist[chnMapValue] = 1;
                    }
                }
                if (p2 == i || p3)
                {
                    SGWPRINTIMP(" dwWidId:%d\n", g_tNvrSgwDecMgr.atMosaicParam[j].atWinList[i].dwWidId );
                    SGWPRINTIMP(" dwPriority:%d\n", g_tNvrSgwDecMgr.atMosaicParam[j].atWinList[i].dwPriority );
                    SGWPRINTIMP(" dwStartX:%d\n", g_tNvrSgwDecMgr.atMosaicParam[j].atWinList[i].dwStartX );
                    SGWPRINTIMP(" dwStartY:%d\n", g_tNvrSgwDecMgr.atMosaicParam[j].atWinList[i].dwStartY );
                    SGWPRINTIMP(" dwWidth:%d\n", g_tNvrSgwDecMgr.atMosaicParam[j].atWinList[i].dwWidth );
                    SGWPRINTIMP(" dwHeight:%d\n", g_tNvrSgwDecMgr.atMosaicParam[j].atWinList[i].dwHeight );
                    SGWPRINTIMP(" bEnableWinBorder:%d\n", g_tNvrSgwDecMgr.atMosaicParam[j].atWinList[i].bEnableWinBorder );
                    SGWPRINTIMP(" dwLeftThickness:%d\n", g_tNvrSgwDecMgr.atMosaicParam[j].atWinList[i].tWinBorder.dwLeftThickness );
                    SGWPRINTIMP(" dwTopThickness:%d\n", g_tNvrSgwDecMgr.atMosaicParam[j].atWinList[i].tWinBorder.dwTopThickness );
                    SGWPRINTIMP(" dwRightThickness:%d\n", g_tNvrSgwDecMgr.atMosaicParam[j].atWinList[i].tWinBorder.dwRightThickness );
                    SGWPRINTIMP(" dwBottomThickness:%d\n", g_tNvrSgwDecMgr.atMosaicParam[j].atWinList[i].tWinBorder.dwBottomThickness );
                    SGWPRINTIMP(" dwColor:%d\n", g_tNvrSgwDecMgr.atMosaicParam[j].atWinList[i].tWinBorder.dwColor );
                    SGWPRINTIMP(" bCrop:%d\n", g_tNvrSgwDecMgr.atMosaicParam[j].atWinList[i].bCrop );
                    SGWPRINTIMP(" wStartX:%d\n", g_tNvrSgwDecMgr.atMosaicParam[j].atWinList[i].tCropRect.wStartX );
                    SGWPRINTIMP(" wStartY:%d\n", g_tNvrSgwDecMgr.atMosaicParam[j].atWinList[i].tCropRect.wStartY );
                    SGWPRINTIMP(" wWidth:%d\n", g_tNvrSgwDecMgr.atMosaicParam[j].atWinList[i].tCropRect.wWidth );
                    SGWPRINTIMP(" wHeight:%d\n", g_tNvrSgwDecMgr.atMosaicParam[j].atWinList[i].tCropRect.wHeight );
                    SGWPRINTIMP(" bFreeze:%d\n", g_tNvrSgwDecMgr.atMosaicParam[j].atWinList[i].bFreeze );
                    SGWPRINTIMP(" eVidDecMode:%d\n", g_tNvrSgwDecMgr.atMosaicParam[j].atWinList[i].eVidDecMode );
                    SGWPRINTIMP(" bFreeze:%d\n", g_tNvrSgwDecMgr.atMosaicParam[j].atWinList[i].bFreeze );
                    SGWPRINTIMP(" adwChnMap:%d\n", g_tNvrSgwDecMgr.atMosaicParam[j].adwChnMap[i] );
                    SGWPRINTIMP(" eVidDisSource:%d\n", g_tNvrSgwDecMgr.atMosaicParam[j].eVidDisSource[i] );
                    SGWPRINTIMP(" bContainedGraphic:%d\n\n", g_tNvrSgwDecMgr.atMosaicParam[j].tDisSrcParam[i].bContainedGraphic );
                }
            }
        }
    }
}
void NvrSgwShowdecInfo()
{
    static TNvrSgwDecMgr s_tNvrSgwDecMgr;
    s32 i = 0, j = 0;

    SGWPRINTINFO("vid:%d  rc audio:%d  ep audio:%d\n", g_bMedaiVidInitSucc, g_bMedaiInitSucc[0], g_bMedaiInitSucc[1]);
    SGWPRINTINFO("dwNodeNum:%u  dwRetryNodeNum:%u\n", g_tNvrSgwRtmpMgr.dwNodeNum, g_tNvrSgwRtmpMgr.dwRetryNodeNum);

    ///<打印RTMP拉流任务信息
    SGWPRINTINFO("taskid\t\tpullvid\tReportStart bRetry\t\t\t\turl\n");
    TNvrSgwRtmpTaskNode *ptNode = NULL;
    pthread_mutex_lock(&rtmp_task_lock);
    do
    {
        ///<找到rtmp任务节点
        ptNode = g_tNvrSgwRtmpMgr.ptHead;
        if(NULL == ptNode)
        {
            break;
        }
        while (ptNode)
        {
            SGWPRINTINFO("%-10s\t  %u-%-u\t    %-u\t      %u\t    %s\n", ptNode->tRtmpTask.achTaskID, 
                ptNode->tRtmpTask.tPullVidID.dwChnId, ptNode->tRtmpTask.tPullVidID.dwXxxID, 
                ptNode->bRepStartedFlg, ptNode->bReTry, ptNode->tRtmpTask.achRtmpUrl);
            ptNode = ptNode->ptNext;
        }
    }while(0);
    pthread_mutex_unlock(&rtmp_task_lock);

    SGWPRINTINFO("taskVid  ISstart  chnxx\t  decid\t   count  type\t fps  CanvasId\t   x     y  width  height\tasync\t    max     Diff  bitrate     taskid\n");
    for (i = 0; i < NVR_SGW_MAX_CHN_NUM; i++)
    {
        for (j = 0; j < NVR_SGW_MAX_CHN_VID_NUM; j++)
        {
            if (g_tNvrSgwDecMgr.aatVidDecInfo[i][j].byValid)
            {
                ///<           vid    start chnxx decid count type fps CanvId  x    y    w    h  async  max diff  bt  taskid
                SGWPRINTINFO("%4d-%02d  %4d  %8x  %6d   %7d   %3d  %4d  %4d   %7d  %4d  %5d  %6d  %10x  %8d  %7d  %7u   %8s\n", i, j,
                        g_tNvrSgwDecMgr.aatVidDecInfo[i][j].byStartDec,
                        g_tNvrSgwDecMgr.aatVidDecInfo[i][j].dwChnIdDecID,
                        g_tNvrSgwDecMgr.aatVidDecInfo[i][j].byRealDecNo + (g_tNvrSgwDecMgr.aatVidDecInfo[i][j].byRealChn / 2) * 32,
                        g_tNvrSgwDecMgr.aatVidDecInfo[i][j].dwFrameCount,
                        g_tNvrSgwDecMgr.aatVidDecInfo[i][j].wMediaType,
                        g_tNvrSgwDecMgr.aatVidDecInfo[i][j].vaInfo.tVideoParam.wFranmeRate,
                        g_tNvrSgwDecMgr.aatVidDecInfo[i][j].byRealChn,
                        g_tNvrSgwDecMgr.aatVidDecInfo[i][j].vaInfo.tVideoParam.wVideoX,
                        g_tNvrSgwDecMgr.aatVidDecInfo[i][j].vaInfo.tVideoParam.wVideoY,
                        g_tNvrSgwDecMgr.aatVidDecInfo[i][j].vaInfo.tVideoParam.wVideoWidth,
                        g_tNvrSgwDecMgr.aatVidDecInfo[i][j].vaInfo.tVideoParam.wVideoHeight,
                        g_tNvrSgwDecMgr.aatVidDecInfo[i][j].dwAvSyncChn,
                        g_tNvrSgwDecMgr.aatVidDecInfo[i][j].dwMaxFramSize,
                        g_tNvrSgwDecMgr.aatVidDecInfo[i][j].dwFrameCount - s_tNvrSgwDecMgr.aatVidDecInfo[i][j].dwFrameCount,
                        g_tNvrSgwDecMgr.aatVidDecInfo[i][j].vaInfo.tVideoParam.dwBitRate,
                        g_tNvrSgwDecMgr.aatVidDecInfo[i][j].achTaskID);
            }
        }
    }

    SGWPRINTINFO("a_chn  no tn  \tstart \tchnxx  \tdecid \t     count\ttype \tSample \tchannel\tBitsPS \tvsync\tmax\tDiff    taskid\n");
    for (i = 0; i < NVR_SGW_MAX_CHN_NUM; i++)
    {
        for (j = 0; j < NVR_SGW_MAX_CHN_AUD_NUM; j++)
        {
            if (g_tNvrSgwDecMgr.aatAudDecInfo[i][j].byValid)
            {
                ///<        a_chn  no   tn   start chnxx decid count type Sample c BitsPS vsync max    Diff
                SGWPRINTINFO("%4d   %2d %2d  \t%4d\t%4x\t%4d\t%10d  \t%4d\t%4d\t%4d\t%4d\t%02x  %6d  \t %d   %8s\n", i, j,
                        g_tNvrSgwDecMgr.wCurAudDecNum[i],
                        g_tNvrSgwDecMgr.aatAudDecInfo[i][j].byStartDec,
                        g_tNvrSgwDecMgr.aatAudDecInfo[i][j].dwChnIdDecID,
                        g_tNvrSgwDecMgr.aatAudDecInfo[i][j].byRealDecNo,
                        g_tNvrSgwDecMgr.aatAudDecInfo[i][j].dwFrameCount,
                        g_tNvrSgwDecMgr.aatAudDecInfo[i][j].wMediaType,
                        g_tNvrSgwDecMgr.aatAudDecInfo[i][j].vaInfo.tAudioParam.dwSample,
                        g_tNvrSgwDecMgr.aatAudDecInfo[i][j].vaInfo.tAudioParam.wChannel,
                        g_tNvrSgwDecMgr.aatAudDecInfo[i][j].vaInfo.tAudioParam.wBitsPerSample,
                        g_tNvrSgwDecMgr.aatAudDecInfo[i][j].dwAvSyncChn,
                        g_tNvrSgwDecMgr.aatAudDecInfo[i][j].dwMaxFramSize,
                        g_tNvrSgwDecMgr.aatAudDecInfo[i][j].dwFrameCount - s_tNvrSgwDecMgr.aatAudDecInfo[i][j].dwFrameCount,
                        g_tNvrSgwDecMgr.aatAudDecInfo[i][j].achTaskID);
            }
        }
    }

    memcpy(&s_tNvrSgwDecMgr, &g_tNvrSgwDecMgr, sizeof(g_tNvrSgwDecMgr));
    SGWPRINTINFO("wTotalVidDecNum:%d  wTotalAudDecNum:%d\n", g_tNvrSgwDecMgr.wTotalVidDecNum, g_tNvrSgwDecMgr.wTotalAudDecNum);
    for (i = 0; i < NVR_SGW_MAX_CHN_NUM; i++)
    {
        SGWPRINTINFO("chn:%d wCurVidDecNum:%d  wCurAudDecNum:%d\n", i, g_tNvrSgwDecMgr.wCurVidDecNum[i], g_tNvrSgwDecMgr.wCurAudDecNum[i]);
    }
}

void NvrSgwMediaCtrlSetChnMosic(u16 wDecChnId, u16 wDecId, BOOL32 bDec)
{
    s32 i = 0;
    s32 nRet = 0;
    EMediactrlVidDisDev eDev = 0 == wDecChnId ? MEDIACTRL_VDIS_DEV_BT1120 : MEDIACTRL_VDIS_DEV_HDMI1;
    s32 nDecDevId = wDecId + wDecChnId * NVR_SGW_MAX_CHN_VID_NUM;

    if ((g_tNvrSgwDecMgr.aatVidDecInfo[wDecChnId][wDecId].vaInfo.tVideoParam.wVideoWidth
            == g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[wDecId].dwWidth)
            && (g_tNvrSgwDecMgr.aatVidDecInfo[wDecChnId][wDecId].vaInfo.tVideoParam.wVideoHeight
                    == g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[wDecId].dwHeight))
    {
        return;
    }

    if (wDecId >=  g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].dwNumberOfWindows)
    {
        SGWPRINTERR("dec id %d over %d\n",  wDecId, g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].dwNumberOfWindows);
        return;
    }

    g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].adwChnMap[wDecId] = nDecDevId;
    g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[wDecId].bCrop = FALSE;
    g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[wDecId].tCropRect.wStartX = 0;
    g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[wDecId].tCropRect.wStartY = 0;
    g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[wDecId].tCropRect.wWidth = 800;
    g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[wDecId].tCropRect.wHeight = 600;
    g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[wDecId].eVidDecMode =  MEDIACTRL_VDEC_MODE_FLUENCY;
    g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[wDecId].dwWidth =
            g_tNvrSgwDecMgr.aatVidDecInfo[wDecChnId][wDecId].vaInfo.tVideoParam.wVideoWidth;
    g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[wDecId].dwHeight =
            g_tNvrSgwDecMgr.aatVidDecInfo[wDecChnId][wDecId].vaInfo.tVideoParam.wVideoHeight;

    SGWPRINTIMP("wDecChnId:%d, eDev:%d, wDecId:%d, nDecDevId:%d, bDec:%d\n", wDecChnId, eDev, wDecId, nDecDevId, bDec);

    s32 anExist[64] = {0};
    for(i = 0 ; i < g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].dwNumberOfWindows; i++)
    {
        s32 chnMapValue =  g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].adwChnMap[i];
        if (chnMapValue >= 0)
        {
            if (anExist[chnMapValue]) {
                SGWPRINTERR("Duplicate adwChnMap value found: %d\n", chnMapValue);
            } else {
                anExist[chnMapValue] = 1;
            }
        }
        if (wDecId != i)
        {
            continue;
        }
        SGWPRINTFREQ(" dwWidId:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].dwWidId );
        SGWPRINTFREQ(" dwPriority:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].dwPriority );
        SGWPRINTFREQ(" dwStartX:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].dwStartX );
        SGWPRINTFREQ(" dwStartY:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].dwStartY );
        SGWPRINTDBG(" dwWidth:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].dwWidth);
        SGWPRINTDBG(" dwHeight:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].dwHeight);
        SGWPRINTFREQ(" bEnableWinBorder:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].bEnableWinBorder );
        SGWPRINTFREQ(" dwLeftThickness:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].tWinBorder.dwLeftThickness );
        SGWPRINTFREQ(" dwTopThickness:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].tWinBorder.dwTopThickness );
        SGWPRINTFREQ(" dwRightThickness:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].tWinBorder.dwRightThickness );
        SGWPRINTFREQ(" dwBottomThickness:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].tWinBorder.dwBottomThickness );
        SGWPRINTFREQ(" dwColor:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].tWinBorder.dwColor );
        SGWPRINTFREQ(" bCrop:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].bCrop );
        SGWPRINTFREQ(" wStartX:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].tCropRect.wStartX );
        SGWPRINTFREQ(" wStartY:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].tCropRect.wStartY );
        SGWPRINTFREQ(" wWidth:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].tCropRect.wWidth );
        SGWPRINTFREQ(" wHeight:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].tCropRect.wHeight );
        SGWPRINTFREQ(" bFreeze:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].bFreeze );
        SGWPRINTFREQ(" eVidDecMode:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].eVidDecMode );
        SGWPRINTFREQ(" bFreeze:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].bFreeze );
        SGWPRINTFREQ(" adwChnMap:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].adwChnMap[i] );
        SGWPRINTFREQ(" eVidDisSource:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].eVidDisSource[i] );
        SGWPRINTFREQ(" bContainedGraphic:%d\n\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].tDisSrcParam[i].bContainedGraphic );
    }

    nRet = MediactrlVidDisSetMosaic(eDev, &g_tNvrSgwDecMgr.atMosaicParam[wDecChnId],
            &g_tNvrSgwDecMgr.atMosaicOutInfo[wDecChnId]);

    SGWPRINTIMP("MediactrlVidDisSetMosaic nRet:%d\n", nRet);
}

void NvrSgwMediaCtrlMosicAdjust()
{
    s32 i = 0, j  = 0;
    TNvrSgwCfg tNvrSgwCfg;
    u32 dwSqrtWin = 1;
    u32 dwWidth = NVR_SGW_MAX_RES_WIDTH, dwHeight = NVR_SGW_MAX_RES_HEIGHT;
    TMediaCtrlVidDisMosaicParam tMosaicParam;
    s32  nDecDevId = -1;
    EMediactrlVidDisDev eDev = MEDIACTRL_VDIS_DEV_BT1120;
    s32 nRet = 0;
    static BOOL bFisrtIn =TRUE;
    static TNvrSgwCfg s_tNvrSgwCfg;

    mzero(tNvrSgwCfg);
    if (bFisrtIn)
    {
        mzero(s_tNvrSgwCfg);
        bFisrtIn = FALSE;
    }

    NvrSgwCfgGetParam(&tNvrSgwCfg);

    for (i = 0; i < NVR_SGW_MAX_CHN_NUM; i++)
    {
//        if (tNvrSgwCfg.adwChnStyle[i] != s_tNvrSgwCfg.adwChnStyle[i])
        {
            /*视频显示参数HDMI*/
            dwSqrtWin = tNvrSgwCfg.adwChnStyle[i];
            mzero(tMosaicParam);

            tMosaicParam.bOverlay = TRUE;
            tMosaicParam.dwNumberOfWindows = dwSqrtWin * dwSqrtWin;
            tMosaicParam.tAspectRatio.eScaleMode = MEDIACTRL_VSCALE_MODE_FULL;
            tMosaicParam.tAspectRatio.dwBgColor = MEDIACTRL_BG_COLOR_BLACK;
            tMosaicParam.outputFPS = 30;
            tMosaicParam.bReserveDecChn = FALSE;

            for(j = 0; j < tMosaicParam.dwNumberOfWindows; j++)
            {
                nDecDevId = j + i * NVR_SGW_MAX_CHN_VID_NUM;
                tMosaicParam.atWinList[j].dwWidId = j;
                tMosaicParam.atWinList[j].dwPriority = 0;
                tMosaicParam.atWinList[j].dwStartX = SGW_ALIGN_BACK((dwWidth/dwSqrtWin) * (j%dwSqrtWin), 2);
                tMosaicParam.atWinList[j].dwStartY = SGW_ALIGN_BACK((dwHeight/dwSqrtWin) * (j/dwSqrtWin), 2);
                tMosaicParam.atWinList[j].dwWidth = SGW_ALIGN_BACK(dwWidth/dwSqrtWin, 2);
                tMosaicParam.atWinList[j].dwHeight = SGW_ALIGN_BACK(dwHeight/dwSqrtWin, 2);
                tMosaicParam.atWinList[j].bEnableWinBorder = TRUE;
                tMosaicParam.atWinList[j].tWinBorder.dwLeftThickness = 2;
                tMosaicParam.atWinList[j].tWinBorder.dwTopThickness = 2;
                tMosaicParam.atWinList[j].tWinBorder.dwRightThickness = 2;
                tMosaicParam.atWinList[j].tWinBorder.dwBottomThickness = 2;
                tMosaicParam.atWinList[j].tWinBorder.dwColor = (0x00FF00);   /* green back groud color */
                tMosaicParam.atWinList[j].bCrop = FALSE;
                tMosaicParam.atWinList[j].tCropRect.wStartX = 0;
                tMosaicParam.atWinList[j].tCropRect.wStartY = 0;
                tMosaicParam.atWinList[j].tCropRect.wWidth = 800;
                tMosaicParam.atWinList[j].tCropRect.wHeight = 600;
                tMosaicParam.atWinList[j].bFreeze = FALSE;
                tMosaicParam.atWinList[j].eVidDecMode = g_aDecMode[0];
                tMosaicParam.adwChnMap[j] = MEDIACTRL_INVALID_CHN;
                tMosaicParam.eVidDisSource[j] = MEDIACTRL_VID_DIS_SOURCE_DEC;
                tMosaicParam.tDisSrcParam[j].bContainedGraphic = FALSE;
            }

            memcpy(&g_tNvrSgwDecMgr.atMosaicParam[i], &tMosaicParam, sizeof(TMediaCtrlVidDisMosaicParam));

            if (i < NVR_SGW_MAX_CHIP_CHN_NUM)
            {
                eDev = 0 == i ? MEDIACTRL_VDIS_DEV_BT1120 : MEDIACTRL_VDIS_DEV_HDMI1;
                nRet = MediactrlVidDisSetMosaic(eDev, &g_tNvrSgwDecMgr.atMosaicParam[i], &g_tNvrSgwDecMgr.atMosaicOutInfo[i]);
                if (0 == nRet)
                {
                    SGWPRINTIMP("MediactrlVidDisSetMosaic nRet:%d\n", nRet);
                }
                else
                {
                    SGWPRINTERR("MediactrlVidDisSetMosaic nRet:%d\n", nRet);
                }
            }

            g_tNvrSgwDecMgr.wCurVidDecChnMaxWidth[i] = SGW_ALIGN_BACK(dwWidth/dwSqrtWin, 2);
            g_tNvrSgwDecMgr.wCurVidDecChnMaxHeight[i] = SGW_ALIGN_BACK(dwHeight/dwSqrtWin, 2);
        }
    }

    for (i = 0; i < NVR_SGW_MAX_CHN_NUM; i++)
    {
        SGWPRINTIMP(" mosic %d dwNumberOfWindows : %u\n", i, g_tNvrSgwDecMgr.atMosaicParam[i].dwNumberOfWindows);
        for(j = 0; j < g_tNvrSgwDecMgr.atMosaicParam[i].dwNumberOfWindows; j++)
        {
            SGWPRINTFREQ(" dwWidId:%d\n", g_tNvrSgwDecMgr.atMosaicParam[i].atWinList[j].dwWidId);
            SGWPRINTFREQ(" dwPriority:%d\n", g_tNvrSgwDecMgr.atMosaicParam[i].atWinList[j].dwPriority );
            SGWPRINTFREQ(" dwStartX:%d\n", g_tNvrSgwDecMgr.atMosaicParam[i].atWinList[j].dwStartX );
            SGWPRINTFREQ(" dwStartY:%d\n", g_tNvrSgwDecMgr.atMosaicParam[i].atWinList[j].dwStartY );
            SGWPRINTFREQ(" dwWidth:%d\n", g_tNvrSgwDecMgr.atMosaicParam[i].atWinList[j].dwWidth );
            SGWPRINTFREQ(" dwHeight:%d\n", g_tNvrSgwDecMgr.atMosaicParam[i].atWinList[j].dwHeight );
            SGWPRINTFREQ(" bEnableWinBorder:%d\n", g_tNvrSgwDecMgr.atMosaicParam[i].atWinList[j].bEnableWinBorder );
            SGWPRINTFREQ(" dwLeftThickness:%d\n", g_tNvrSgwDecMgr.atMosaicParam[i].atWinList[j].tWinBorder.dwLeftThickness );
            SGWPRINTFREQ(" dwTopThickness:%d\n", g_tNvrSgwDecMgr.atMosaicParam[i].atWinList[j].tWinBorder.dwTopThickness );
            SGWPRINTFREQ(" dwRightThickness:%d\n", g_tNvrSgwDecMgr.atMosaicParam[i].atWinList[j].tWinBorder.dwRightThickness );
            SGWPRINTFREQ(" dwBottomThickness:%d\n", g_tNvrSgwDecMgr.atMosaicParam[i].atWinList[j].tWinBorder.dwBottomThickness );
            SGWPRINTFREQ(" dwColor:%d\n", g_tNvrSgwDecMgr.atMosaicParam[i].atWinList[j].tWinBorder.dwColor );
            SGWPRINTFREQ(" bCrop:%d\n", g_tNvrSgwDecMgr.atMosaicParam[i].atWinList[j].bCrop );
            SGWPRINTFREQ(" wStartX:%d\n", g_tNvrSgwDecMgr.atMosaicParam[i].atWinList[j].tCropRect.wStartX );
            SGWPRINTFREQ(" wStartY:%d\n", g_tNvrSgwDecMgr.atMosaicParam[i].atWinList[j].tCropRect.wStartY );
            SGWPRINTFREQ(" wWidth:%d\n", g_tNvrSgwDecMgr.atMosaicParam[i].atWinList[j].tCropRect.wWidth );
            SGWPRINTFREQ(" wHeight:%d\n", g_tNvrSgwDecMgr.atMosaicParam[i].atWinList[j].tCropRect.wHeight );
            SGWPRINTFREQ(" bFreeze:%d\n", g_tNvrSgwDecMgr.atMosaicParam[i].atWinList[j].bFreeze );
            SGWPRINTFREQ(" eVidDecMode:%d\n", g_tNvrSgwDecMgr.atMosaicParam[i].atWinList[j].eVidDecMode );
            SGWPRINTFREQ(" bFreeze:%d\n", g_tNvrSgwDecMgr.atMosaicParam[i].atWinList[j].bFreeze );
            SGWPRINTFREQ(" adwChnMap:%d\n", g_tNvrSgwDecMgr.atMosaicParam[i].adwChnMap[j] );
            SGWPRINTFREQ(" eVidDisSource:%d\n", g_tNvrSgwDecMgr.atMosaicParam[i].eVidDisSource[j] );
            SGWPRINTFREQ(" bContainedGraphic:%d\n\n", g_tNvrSgwDecMgr.atMosaicParam[i].tDisSrcParam[j].bContainedGraphic );
        }
    }

    TNvrSgwCmdMediaCtrlInit tSgwMcaMEdaiCtrlInitParam;

    mzero(tSgwMcaMEdaiCtrlInitParam);
    memcpy(&tSgwMcaMEdaiCtrlInitParam.adwChnStyle[0], &tNvrSgwCfg.adwChnStyle[2], sizeof(tSgwMcaMEdaiCtrlInitParam));
    tSgwMcaMEdaiCtrlInitParam.dwDecMode = g_aDecMode[1];
    nRet = NvrSgwMcaCmdSend(CMD_NVR_MCA_SET_MEIDACTRL_MOSIC_ADJUST, (u8*) &tSgwMcaMEdaiCtrlInitParam, sizeof(tSgwMcaMEdaiCtrlInitParam));
    if (0 != nRet)
    {
        SGWPRINTERR("CMD_NVR_MCA_SET_MEIDACTRL_MOSIC_ADJUST ret:%d\n", nRet);
    }

    MAKE_COMPILER_HAPPY(nDecDevId);
}

void NvrSgwMediaCtrlShowChnMosicParam()
{
    u8 i, wDecChnId;
    for(wDecChnId = 0; wDecChnId < 2; wDecChnId++)
    {
        for(i = 0 ; i < g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].dwNumberOfWindows; i++)
        {
            pthread_mutex_lock(&mediactrl_set_lock);
            SGWPRINTDBG(" dwWidId:%2d x:%4d y:%4d w:%4d h:%4d map:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].dwWidId,
                    g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].dwStartX, g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].dwStartY,
                    g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].dwWidth, g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].dwHeight,
                    g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].adwChnMap[i]);
            SGWPRINTFREQ(" dwWidId:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].dwWidId );
            SGWPRINTFREQ(" dwPriority:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].dwPriority );
            SGWPRINTFREQ(" dwStartX:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].dwStartX );
            SGWPRINTFREQ(" dwStartY:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].dwStartY );
            SGWPRINTFREQ(" dwWidth:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].dwWidth);
            SGWPRINTFREQ(" dwHeight:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].dwHeight);
            SGWPRINTFREQ(" bEnableWinBorder:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].bEnableWinBorder );
            SGWPRINTFREQ(" dwLeftThickness:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].tWinBorder.dwLeftThickness );
            SGWPRINTFREQ(" dwTopThickness:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].tWinBorder.dwTopThickness );
            SGWPRINTFREQ(" dwRightThickness:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].tWinBorder.dwRightThickness );
            SGWPRINTFREQ(" dwBottomThickness:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].tWinBorder.dwBottomThickness );
            SGWPRINTFREQ(" dwColor:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].tWinBorder.dwColor );
            SGWPRINTFREQ(" bCrop:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].bCrop );
            SGWPRINTFREQ(" wStartX:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].tCropRect.wStartX );
            SGWPRINTFREQ(" wStartY:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].tCropRect.wStartY );
            SGWPRINTFREQ(" wWidth:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].tCropRect.wWidth );
            SGWPRINTFREQ(" wHeight:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].tCropRect.wHeight );
            SGWPRINTFREQ(" bFreeze:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].bFreeze );
            SGWPRINTFREQ(" eVidDecMode:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].eVidDecMode );
            SGWPRINTFREQ(" bFreeze:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].bFreeze );
            SGWPRINTFREQ(" adwChnMap:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].adwChnMap[i] );
            SGWPRINTFREQ(" eVidDisSource:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].eVidDisSource[i] );
            SGWPRINTFREQ(" bContainedGraphic:%d\n\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].tDisSrcParam[i].bContainedGraphic );
            pthread_mutex_unlock(&mediactrl_set_lock);
        }
    }
}
void NvrSgwMediaCtrlSetChnMosicNew(TNvrSgwResPosInfo *ptPos, u16 wWidth, u16 wHeight)
{
    s32 i = 0;
    s32 nRet = 0;
    u16 wDecId = 0, nDecDevId = 0, wDecChnId = 0;
    EMediactrlVidDisDev eDev = MEDIACTRL_VDIS_DEV_BT1120;

    wDecChnId = ptPos->nChn;
    wDecId = ptPos->nChnWinIndex;
    nDecDevId = ptPos->nVidDecId;
    eDev = 0 == wDecChnId ? MEDIACTRL_VDIS_DEV_BT1120 : MEDIACTRL_VDIS_DEV_HDMI1;

    if (wDecId >=  g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].dwNumberOfWindows)
    {
        SGWPRINTERR("dec id %d over %d\n",  wDecId, g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].dwNumberOfWindows);
        return;
    }

    g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].adwChnMap[wDecId] = nDecDevId;
    g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[wDecId].bCrop = FALSE;
    g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[wDecId].tCropRect.wStartX = 0;
    g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[wDecId].tCropRect.wStartY = 0;
    g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[wDecId].tCropRect.wWidth = 800;
    g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[wDecId].tCropRect.wHeight = 600;
    g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[wDecId].eVidDecMode =  g_aDecMode[0];
    g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[wDecId].dwStartX = ptPos->x * RES_WIDTH_UNIT - ptPos->byBadd;
    g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[wDecId].dwStartY = ptPos->y * RES_HEIGHT_UNIT;
    g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[wDecId].dwWidth = wWidth;
    g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[wDecId].dwHeight = wHeight;

    SGWPRINTIMP("----- mosaic  set  wRealDecChnId:%d winindex:%d wDecId:%d \n", wDecChnId, wDecId, nDecDevId);

    for(i = 0 ; i < g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].dwNumberOfWindows; i++)
    {
        int j = 0;
        // 检测当前wDecChnId对应的tCropRect矩形是否重叠
        for (j = 0; j < g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].dwNumberOfWindows; j++)
        {
            if (i == j)
                continue; // 跳过自身

            TMediaCtrlRectRegion *rect1 = &g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].tCropRect;
            TMediaCtrlRectRegion *rect2 = &g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[j].tCropRect;

            // 检查矩形是否重叠
            if (rect1->wStartX < rect2->wStartX + rect2->wWidth &&
                rect1->wStartX + rect1->wWidth > rect2->wStartX &&
                rect1->wStartY < rect2->wStartY + rect2->wHeight &&
                rect1->wStartY + rect1->wHeight > rect2->wStartY)
            {

                SGWFLASHNOTICE("Warning: Overlapping rectangles detected for wDecChnId %d between windows %d and %d\n",
                            wDecChnId, i, j);

                // 这里可以添加更多的处理逻辑，比如调整矩形位置或大小
            }
        }

        if (wDecId != i)
        {
            continue;
        }
        SGWPRINTDBG(" dwWidId:%2d x:%4d y:%4d w:%4d h:%4d map:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].dwWidId,
                g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].dwStartX, g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].dwStartY,
                g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].dwWidth, g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].dwHeight,
                g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].adwChnMap[i]);
        SGWPRINTFREQ(" dwWidId:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].dwWidId );
        SGWPRINTFREQ(" dwPriority:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].dwPriority );
        SGWPRINTFREQ(" dwStartX:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].dwStartX );
        SGWPRINTFREQ(" dwStartY:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].dwStartY );
        SGWPRINTFREQ(" dwWidth:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].dwWidth);
        SGWPRINTFREQ(" dwHeight:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].dwHeight);
        SGWPRINTFREQ(" bEnableWinBorder:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].bEnableWinBorder );
        SGWPRINTFREQ(" dwLeftThickness:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].tWinBorder.dwLeftThickness );
        SGWPRINTFREQ(" dwTopThickness:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].tWinBorder.dwTopThickness );
        SGWPRINTFREQ(" dwRightThickness:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].tWinBorder.dwRightThickness );
        SGWPRINTFREQ(" dwBottomThickness:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].tWinBorder.dwBottomThickness );
        SGWPRINTFREQ(" dwColor:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].tWinBorder.dwColor );
        SGWPRINTFREQ(" bCrop:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].bCrop );
        SGWPRINTFREQ(" wStartX:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].tCropRect.wStartX );
        SGWPRINTFREQ(" wStartY:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].tCropRect.wStartY );
        SGWPRINTFREQ(" wWidth:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].tCropRect.wWidth );
        SGWPRINTFREQ(" wHeight:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].tCropRect.wHeight );
        SGWPRINTFREQ(" bFreeze:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].bFreeze );
        SGWPRINTFREQ(" eVidDecMode:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].eVidDecMode );
        SGWPRINTFREQ(" bFreeze:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[i].bFreeze );
        SGWPRINTFREQ(" adwChnMap:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].adwChnMap[i] );
        SGWPRINTFREQ(" eVidDisSource:%d\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].eVidDisSource[i] );
        SGWPRINTFREQ(" bContainedGraphic:%d\n\n", g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].tDisSrcParam[i].bContainedGraphic );
    }

    if (wDecChnId < NVR_SGW_MAX_CHIP_CHN_NUM)
    {
        nRet = MediactrlVidDisSetMosaic(eDev, &g_tNvrSgwDecMgr.atMosaicParam[wDecChnId],
                &g_tNvrSgwDecMgr.atMosaicOutInfo[wDecChnId]);
        if (nRet)
        {
            SGWPRINTERR("MediactrlVidDisSetMosaic err nRet:%d\n", nRet);
        }
    }
}

void NvrSgwMediaCtrlSetChnMosicInvalidNew(u16 wDecChnId, u16 wDecId)
{
    s32 nRet = 0;
    EMediactrlVidDisDev eDev = 0 == wDecChnId ? MEDIACTRL_VDIS_DEV_BT1120 : MEDIACTRL_VDIS_DEV_HDMI1;

    if (wDecId >=  g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].dwNumberOfWindows)
    {
        SGWPRINTERR("dec id %d over %d\n",  wDecId, g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].dwNumberOfWindows);
        return;
    }

    SGWPRINTIMP("----- mosaic clear wRealDecChnId:%d winindex:%d wDecId:%d \n", wDecChnId, wDecId, g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].adwChnMap[wDecId]);
    g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].adwChnMap[wDecId] = MEDIACTRL_INVALID_CHN;
    g_tNvrSgwDecMgr.atMosaicParam[wDecChnId].atWinList[wDecId].eVidDecMode =  MEDIACTRL_VDEC_MODE_REAL;

    if (wDecChnId < NVR_SGW_MAX_CHIP_CHN_NUM)
    {
        nRet = MediactrlVidDisSetMosaic(eDev, &g_tNvrSgwDecMgr.atMosaicParam[wDecChnId],
                &g_tNvrSgwDecMgr.atMosaicOutInfo[wDecChnId]);
        if (nRet)
        {
            SGWPRINTERR("MediactrlVidDisSetMosaic err nRet:%d\n", nRet);
        }
    }
}

static void __NvrSgwDecDealChnDisconnect(u16 wChnId, ENvrStreamDiscnctType eDiscnctType, void* pContext)
{
    char achBuf[NVR_MAX_STR32_LEN] = {0};

    SGWPRINTIMP("chnid:%u,discontype:%d\n",wChnId,eDiscnctType);

    //NvrVtduCtrlDestroyFrameStream
    MAKE_COMPILER_HAPPY(achBuf);
    return;
}

static EMediaCtrlVidCodecType __NvrSgwConVidEncTypeToMediactrlVidDecType(const ENvrVidEncType eVidEncType)
{
    switch(eVidEncType)
    {
        case NVR_VID_TYPE_H264:
            return MEDIACTRL_VID_CODEC_H264;
        case NVR_VID_TYPE_MJPEG:
            return MEDIACTRL_VID_CODEC_MJPEG;
        case NVR_VID_TYPE_H265:
            return MEDIACTRL_VID_CODEC_H265;
        case NVR_VID_TYPE_SVAC:
            return MEDIACTRL_VID_CODEC_SVAC;
        case NVR_VID_TYPE_MPEG4:
            return MEDIACTRL_VID_CODEC_JPEG;
        default:
            return MEDIACTRL_VID_CODEC_H264;
    }
    return MEDIACTRL_VID_CODEC_H264;
}

u32 NvrSgwMediaVidFpsSyncTime(s32 nInSyncTime)
{
    u32 dwSyncTine = 1000;
    if (nInSyncTime < 3000)
    {
        dwSyncTine = 3 * 1000;
    }
    else if (nInSyncTime < 10000)
    {
        dwSyncTine = 10 * 1000;
    }
    else if (nInSyncTime < 30000)
    {
        dwSyncTine = 30 * 1000;
    }
    else
    {
        dwSyncTine = 60 * 1000;
    }
    return dwSyncTine;
}

s32 NvrSgwMediaVidFpsAdjust(s32 nCalcFps)
{
    s32 nFps = 25;
    if (nCalcFps >= 24 && nCalcFps <= 26)
    {
        nFps = 25;
    }
    if (nCalcFps >= 29 && nCalcFps <= 31)
    {
        nFps = 30;
    }
    return nFps;
}

void __NvrSgwStreamSendOverflowMsg(s8 *achTaskID, BOOL bChn)
{
    u8 byStr[NVR_MAX_STR128_LEN] = {0};
    mzero(byStr);
    snprintf((char *)byStr, NVR_MAX_STR128_LEN, "%s", achTaskID);
    if (bChn)
    {
        NvrSgwPushQueueOpt(NVR_SGW_STREAM_CHNOVERFLOW, NVR_QUEUE_NODE_MERGER_NONE, NVR_MAX_STR128_LEN, (char *)byStr);
    }
    else
    {
        NvrSgwPushQueueOpt(NVR_SGW_STREAM_OVERFLOW, NVR_QUEUE_NODE_MERGER_NONE, NVR_MAX_STR128_LEN, (char *)byStr);
    }
}

void __NvrSgwStreamStatSendMsg(s32 nDecChnId, s32 nDecId, BOOL bAud, u32 dwMsgType)
{
    s8 pBuf[NVR_MAX_STR512_LEN] = {0};

    snprintf(pBuf, NVR_MAX_STR512_LEN, "vid:%d, chn:%d, subChn:%d", !bAud, nDecChnId, nDecId);

    NvrSgwPushQueueOpt(dwMsgType, NVR_QUEUE_NODE_MERGER_NONE, sizeof(pBuf), (char *)pBuf);
}

s32 NvrSgwChipArrangeOverChange(s32 nChip)
{
    s32 nRet = 1;
    s32 i = 0, j = 0;
    s32 nChnMin = nChip * NVR_SGW_MAX_CHIP_CHN_NUM;
    s32 nChnMax = nChnMin + NVR_SGW_MAX_CHIP_CHN_NUM;
    u64 timeIn = 0;
    u32 dwMsgId = 0;

    TSdscpProtoReqSetLayOutEncParam tLayOutEncInfo;
    TNvrSgwCmdMediaCtrlStreamOp tDecStreamParam;
    mzero(tDecStreamParam);

    if (nChip > 1 || nChip < 0)
    {
        SGWPRINTDBG("arrange param in err %d\n", nChip);
        return 0;
    }

    mzero(tLayOutEncInfo);

    for (i = 0; i < NVR_SGW_MAX_CHN_NUM; i++)
    {
        for (j = 0; j < NVR_SGW_MAX_CHN_VID_NUM; j++)
        {
            if(nChnMin <= g_tNvrSgwDecMgr.aatVidDecInfo[i][j].byRealChn && g_tNvrSgwDecMgr.aatVidDecInfo[i][j].byRealChn < nChnMax )
            {
                if (g_tNvrSgwDecMgr.aatVidDecInfo[i][j].byStartDec)
                {
                    tLayOutEncInfo.aParamArray[tLayOutEncInfo.dwChnNum].eType = SDSCPPROTO_LAYOUT_ACT_STOP;
                    tLayOutEncInfo.aParamArray[tLayOutEncInfo.dwChnNum].byChnVidId = i;
                    tLayOutEncInfo.aParamArray[tLayOutEncInfo.dwChnNum].bysubChnVidId = j;
                    if (-1 != g_tNvrSgwDecMgr.aatVidDecInfo[i][j].dwAvSyncChn)
                    {
                        tLayOutEncInfo.aParamArray[tLayOutEncInfo.dwChnNum].byChnAudId =
                                (g_tNvrSgwDecMgr.aatVidDecInfo[i][j].dwAvSyncChn >> 8 ) & 0xff;
                        tLayOutEncInfo.aParamArray[tLayOutEncInfo.dwChnNum].bysubChnAudId =
                                g_tNvrSgwDecMgr.aatVidDecInfo[i][j].dwAvSyncChn & 0xff;
                    }
                    tLayOutEncInfo.dwChnNum++;
                    if(nChip == 1)
                    {
                        tDecStreamParam.dwFrameType = 0;
                        tDecStreamParam.wChnId = i;
                        tDecStreamParam.wDecID = j;
                        NvrSgwMcaCmdSend(CMD_NVR_MCA_VID_DEC_RESET, (u8*) &tDecStreamParam, sizeof(tDecStreamParam));
                    }
                }
            }
        }
    }
    SGWPRINTDBG("arrange to send stop %d\n", tLayOutEncInfo.dwChnNum);
    timeIn = NvrSysGetCurTimeMSec();
    if(g_dwSdscpTestClientFd != -1)
    {
        SgwDecSdscpTestSend(SDSCPPROTO_CMD_TYPE_SET_LAYOUT_ENC_PARAM, (void *)&tLayOutEncInfo, sizeof(TSdscpProtoReqSetLayOutEncParam), &dwMsgId);
    }
    else
    {
        NvrSgwSndSdscpEncInfo(&tLayOutEncInfo, (void *)&dwMsgId);
    }
    //nRet = OsApi_SemTakeByTime(g_hSdscpNotifySem, 300);
    //if(nRet)
    //{
        for (i = 0; i < NVR_SGW_MAX_CHN_NUM; i++)
        {
            for (j = 0; j < NVR_SGW_MAX_CHN_VID_NUM; j++)
            {
                if(nChnMin <= g_tNvrSgwDecMgr.aatVidDecInfo[i][j].byRealChn && g_tNvrSgwDecMgr.aatVidDecInfo[i][j].byRealChn < nChnMax )
                {
                    if (g_tNvrSgwDecMgr.aatVidDecInfo[i][j].byStartDec)
                    {
                        g_tNvrSgwDecMgr.aatVidDecInfo[i][j].byStartDec = FALSE;
                        g_tNvrSgwDecMgr.wCurVidDecNum[i] = g_tNvrSgwDecMgr.wCurVidDecNum[i] > 0 ? g_tNvrSgwDecMgr.wCurVidDecNum[i] - 1 : 0;
                        SGWPRINTIMP("%s  ----- mosic clear\n", g_tNvrSgwDecMgr.aatVidDecInfo[i][j].achTaskID);
                        NvrSgwMediaCtrlSetChnMosicInvalidNew(g_tNvrSgwDecMgr.aatVidDecInfo[i][j].byRealChn,
                                g_tNvrSgwDecMgr.aatVidDecInfo[i][j].nDevVidWinId);

                    }
                }
            }
        }
    //}
    //else
    //{

    //}

    SGWMEMNOTICE("arrange stop chip  %d  result:%d take:%llu ms", nChip, nRet, NvrSysGetCurTimeMSec() - timeIn);

    return nRet;
}

void NvrSgwDecUpdateParam(s16 nDecChnId, s16 nDecId)
{
    g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].nVidParamChange += 1;
}

static void __NvrSgwDecFrameVidDateCBNew(void* pData, void* pvContext)
{
    s32 nRet = 0;
    s32 nDecChnId = -1;
    s32 nDecId = -1;
    s32 nDecDevId = -1;
    u64 dwT1[NVR_SGW_MAX_CHN_NUM][NVR_SGW_MAX_CHN_VID_NUM] = {0};
    u64 dwT2[NVR_SGW_MAX_CHN_NUM][NVR_SGW_MAX_CHN_VID_NUM] = {0};
    u64 timeIn[NVR_SGW_MAX_CHN_NUM][NVR_SGW_MAX_CHN_VID_NUM] = {0};
    u64 timeOut[NVR_SGW_MAX_CHN_NUM][NVR_SGW_MAX_CHN_VID_NUM] = {0};
    static u64 time[NVR_SGW_MAX_CHN_NUM][NVR_SGW_MAX_CHN_VID_NUM] = {0,};
    static u64 AvTime[NVR_SGW_MAX_CHN_NUM][NVR_SGW_MAX_CHN_VID_NUM] = {0,};
    static u64 s_u64Fpstime[NVR_SGW_MAX_CHN_NUM][NVR_SGW_MAX_CHN_VID_NUM] = {0,};
    s32 nCalcFps = 0;
    BOOL bResChange = FALSE;
    static BOOL bSendStartEnc[NVR_SGW_MAX_CHN_NUM][NVR_SGW_MAX_CHN_VID_NUM] = {FALSE, };

    if(NULL != pvContext)
    {
        nDecChnId = (((u32)*(u32*)pvContext) >> 8) & 0xff;
        nDecId = ((u32)*(u32*)pvContext) & 0xff;
    }
    else
    {
        SGWPRINTDBG("pvContext is null.\n");
        if(NULL != pData)
        {
            ((TMSFrame *)pData)->MSFreeFrame((TMSFrame *)pData);
        }
        return;
    }
    if (g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byStopDec)
    {
        if(NULL != pData)
        {
            ((TMSFrame *)pData)->MSFreeFrame((TMSFrame *)pData);
        }
        return;
    }

    timeIn[nDecChnId][nDecId] = NvrSysGetCurTimeMSec();
    if(NULL != pData)
    {
        ///<解码器id有效
        do
        {
            TKDFrame *ptFrame = (TKDFrame*) &(((TMSFrame*) pData)->m_tFrame);

            g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wAdjustFps++;
            g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.dwAdjustBitRate += ptFrame->m_dwDataSize;
            if (ptFrame->x.m_tVideoParam.m_bKeyFrame)
            {
                if (g_tTestSveFrame.byRecvChnXx1Need)
                {
                   if (nDecChnId == g_tTestSveFrame.byRecvChn1 && nDecId == g_tTestSveFrame.byRecvChnXx1)
                   {
                       s8 abyFile[128] = {0};
                       snprintf(abyFile, sizeof(abyFile), "%s_%d_%02d_%llu.h264", g_tTestSveFrame.abyFileName, g_tTestSveFrame.byRecvChn1, g_tTestSveFrame.byRecvChnXx1, NvrSysGetCurTimeMSec());
                       WriteData2File(abyFile, ptFrame->m_pData, ptFrame->m_dwDataSize);
                       g_tTestSveFrame.byRecvChnXx1Need = FALSE;
                   }
                }

                if (g_tTestSveFrame.byRecvChnXx2Need)
                {
                   if (nDecChnId == g_tTestSveFrame.byRecvChn2 && nDecId == g_tTestSveFrame.byRecvChnXx2)
                   {
                       s8 abyFile[128] = {0};
                       snprintf(abyFile, sizeof(abyFile), "%s_%d_%02d_%llu.h264", g_tTestSveFrame.abyFileName, g_tTestSveFrame.byRecvChn2, g_tTestSveFrame.byRecvChnXx2, NvrSysGetCurTimeMSec());
                       WriteData2File(abyFile, ptFrame->m_pData, ptFrame->m_dwDataSize);
                       g_tTestSveFrame.byRecvChnXx2Need = FALSE;
                   }
                }

                u32 dwFpsSyncTime = g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.dwAdjustFpsTime;
                u64 u64FpsTime = NvrSysGetCurTimeMSec();
                s32 nTrueCalcVal = 0;

//                SGWPRINTDBG(" now:%llu  old:%llu   diff:%llu \n", u64FpsTime, s_u64Fpstime[nDecChnId][nDecId],
//                        ((u64FpsTime - s_u64Fpstime[nDecChnId][nDecId]) / 1000));
                if ((u64FpsTime - s_u64Fpstime[nDecChnId][nDecId]) > dwFpsSyncTime)
                {

                    if (0 == g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wFranmeRate)
                    {
                        g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wFranmeRate = ptFrame->m_byFrameRate;
                    }
                    else
                    {
                        nCalcFps = g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wAdjustFps;
                        nTrueCalcVal = NvrSgwMediaVidFpsAdjust(nCalcFps / ((u64FpsTime - s_u64Fpstime[nDecChnId][nDecId] + 500) / 1000));
                        if ( g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wFranmeRate != nTrueCalcVal)
                        {
                            g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wFranmeRate = nTrueCalcVal;
                            g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].nVidParamChange += 1;
                            SGWPRINTDBG("%s  bVidParamChange nDecChnId:%2d nDecId:%2d nCalcFps:%2d %2d fps:%2d time diff:%llu %llu sunc:%ums\n",\
                                    g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].achTaskID, nDecChnId, nDecId,\
                                    nCalcFps, nTrueCalcVal, g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wFranmeRate,\
                                    (u64FpsTime - s_u64Fpstime[nDecChnId][nDecId] + 500) / 1000, u64FpsTime,\
                                    g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.dwAdjustFpsTime);
                        }
                    }

                    if (0 == g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.dwBitRate)
                    {
                        g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.dwBitRate = 512;
                    }
                    else
                    {
                        nTrueCalcVal = g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.dwAdjustBitRate
                                * 8 / ((u64FpsTime - s_u64Fpstime[nDecChnId][nDecId]) / 1000) / 1024;

                        if (nTrueCalcVal > 512)
                        {
                            if (-1 != g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].dwAvSyncChn)
                            {
                                s32 nAChn = (g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].dwAvSyncChn >> 8) & 0xff;
                                s32 nASubChn = g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].dwAvSyncChn & 0xff;

                                nTrueCalcVal += g_tNvrSgwDecMgr.aatAudDecInfo[nAChn][nASubChn].vaInfo.tAudioParam.dwSample
                                        * g_tNvrSgwDecMgr.aatAudDecInfo[nAChn][nASubChn].vaInfo.tAudioParam.wChannel
                                        * g_tNvrSgwDecMgr.aatAudDecInfo[nAChn][nASubChn].vaInfo.tAudioParam.wBitsPerSample
                                        / 8 / 1024;
                            }
                            if ( BITRATE_ABS(g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.dwBitRate, nTrueCalcVal) > 300)
                            {
                                SGWPRINTDBG("%s  nDecChnId:%2d nDecId:%2d [total:%u] nCalc:%u  old:%u  time diff:%llu %llu\n",\
                                        g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].achTaskID,\
                                        nDecChnId, nDecId, g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.dwAdjustBitRate,\
                                        nTrueCalcVal,\
                                        g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.dwBitRate,\
                                        (u64FpsTime - s_u64Fpstime[nDecChnId][nDecId]) / 1000, u64FpsTime);
                                g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].nVidParamChange += 2;
                                g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.dwBitRate = nTrueCalcVal;
                            }
                        }
                    }

                    g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.dwAdjustFpsTime = NvrSgwMediaVidFpsSyncTime(dwFpsSyncTime);

                    g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wAdjustFps = 0;
                    g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.dwAdjustBitRate = 0;
                    s_u64Fpstime[nDecChnId][nDecId] = u64FpsTime;
                }
            }

            pthread_mutex_lock(&mediactrl_set_lock);
            if (g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byStartDec)
            {
                if (ptFrame->x.m_tVideoParam.m_wVideoWidth != g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wVideoWidth
                        || ptFrame->x.m_tVideoParam.m_wVideoHeight != g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wVideoHeight )
                {
                    if(g_bRtmpNoDataFlag[nDecChnId][nDecId] && g_bRtmpNoDataOldRes[nDecChnId][nDecId].wHeight == ptFrame->x.m_tVideoParam.m_wVideoHeight && g_bRtmpNoDataOldRes[nDecChnId][nDecId].wWidth == ptFrame->x.m_tVideoParam.m_wVideoWidth)
                    {
                        g_bRtmpNoDataFlag[nDecChnId][nDecId] = 0;
                        g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wVideoWidth = ptFrame->x.m_tVideoParam.m_wVideoWidth;
                        g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wVideoHeight = ptFrame->x.m_tVideoParam.m_wVideoHeight;
                        __NvrSgwStreamStatSendMsg(nDecChnId, nDecId, FALSE, NVR_SGW_STREAM_START);
                    }
                    else
                    {
                        g_bRtmpNoDataFlag[nDecChnId][nDecId] = 0;
                    SGWMEMNOTICE("%s  vid ------  %2d, %2d, dev:%2d dec w:%d h:%d change %d %d is key frame %d",
                            g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].achTaskID, nDecChnId, nDecId, nDecDevId,
                            g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wVideoWidth,
                            g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wVideoHeight,
                            ptFrame->x.m_tVideoParam.m_wVideoWidth, ptFrame->x.m_tVideoParam.m_wVideoHeight,
                            ptFrame->x.m_tVideoParam.m_bKeyFrame);
                    __NvrSgwStreamStatSendMsg(nDecChnId, nDecId, FALSE, NVR_SGW_STREAM_STOP);
                    ///< 释放当前资源，重新申请
                    SGWMEMNOTICE("%s: resmap free prep(x%u-y%u, %ux%u); realchn vid:%d-%d aud:%d-%d uniq:%d\n", g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].achTaskID,
                            g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wVideoX,
                            g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wVideoY,
                            g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wVideoWidth,
                            g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wVideoHeight,
                            g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byRealChn, g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byRealDecNo,
                            g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byRealChn, g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].nDecAudioId,
                            g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].nUniq);
                    NvrSgwStreamDecFree(g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].nUniq);
                    g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].nUniq = 0;
                    g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byStartDec = FALSE;
                    g_tNvrSgwDecMgr.wCurVidDecNum[nDecChnId] = g_tNvrSgwDecMgr.wCurVidDecNum[nDecChnId] > 0 ? g_tNvrSgwDecMgr.wCurVidDecNum[nDecChnId] - 1 : 0;
                    g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wVideoWidth = 0;
                    g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wVideoHeight = 0;
                    if (g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byRealChn < NVR_SGW_MAX_CHIP_CHN_NUM)
                    {
                        SGWPRINTIMP("%s  ----- mosic clear\n", g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].achTaskID);
                        NvrSgwMediaCtrlSetChnMosicInvalidNew(g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byRealChn,
                                g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].nDevVidWinId);
                    }
                    else
                    {
                        TNvrSgwCmdMediaCtrlStreamOp tDecStreamParam;
                        mzero(tDecStreamParam);

                        tDecStreamParam.dwFrameType = 0;
                        tDecStreamParam.wChnId = nDecChnId;
                        tDecStreamParam.wDecID = nDecId;

                        nRet = NvrSgwMcaCmdSend(CMD_NVR_MCA_VID_DEC_RESET, (u8*) &tDecStreamParam, sizeof(tDecStreamParam));
                        SGWMEMNOTICE("%s  ----- mosic clear  mcaMsgId:%u, %u-%u", g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].achTaskID,
                                     tDecStreamParam.dwMsgId, nDecChnId, nDecId);
                    }
                    bResChange = TRUE;
                    }
                }
            }
            pthread_mutex_unlock(&mediactrl_set_lock);

            pthread_mutex_lock(&mediactrl_set_lock);
            nDecDevId = g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byRealDecNo;
            if (!g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byStartDec || g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].nVidParamChange)
            {
                TMediaCtrlVidDecParam tVidDecParam;
                TNvrSgwResPosInfo tPosInfo;

                mzero(tPosInfo);
                mzero(tVidDecParam);

                if (!g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byStartDec)
                {
                #if 0
                    if(!ptFrame->x.m_tVideoParam.m_bKeyFrame)
                    {
                        //回调进来第一帧不是关键帧,则等到关键帧才开始解码
                        pthread_mutex_unlock(&mediactrl_set_lock);
                        SGWPRINTERR("%s  vid ------ dec[%u][%u] first frame is not keyFrame   time: %llu\n",\
                            g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].achTaskID,\
                            nDecChnId, nDecId, timeIn[nDecChnId][nDecId]);
                        ((TMSFrame*) pData)->MSFreeFrame((TMSFrame*) pData);
                        return ;
                    }
                #endif
                    s32 nAudDecID = (((g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].dwAvSyncChn >> 8) & 0xff ) * NVR_SGW_MAX_CHN_AUD_NUM
                            + (g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].dwAvSyncChn & 0xff));
                    if (g_bChnFull[nAudDecID / NVR_SGW_MAX_CHN_AUD_NUM / NVR_SGW_MAX_CHIP_CHN_NUM])
                    {
                        g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byStopDec = TRUE;
                        pthread_mutex_unlock(&mediactrl_set_lock);
                        SGWPRINTERR("%s  vid ------ dec resource full %d %d]\n", g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].achTaskID, nDecChnId, nDecId);
                        ((TMSFrame*) pData)->MSFreeFrame((TMSFrame*) pData);
                        return ;
                    }

                    if (g_tNvrSgwDecMgr.wCurVidDecNum[nDecChnId] >= NVR_SGW_MAX_CHN_VID_NUM)
                    {
                        pthread_mutex_unlock(&mediactrl_set_lock);
                        SGWPRINTERR("%s  vid ------ dec chn full %d %d]\n",\
                                g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].achTaskID,\
                                g_tNvrSgwDecMgr.wCurVidDecNum[nDecChnId], NVR_SGW_MAX_CHN_VID_NUM);
                        ((TMSFrame*) pData)->MSFreeFrame((TMSFrame*) pData);
                        return ;
                    }
                    if (0 == g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wVideoWidth
                            && 0 == g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wVideoHeight)
                    {
                        ///< stream first
                        g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wVideoWidth = ptFrame->x.m_tVideoParam.m_wVideoWidth;
                        g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wVideoHeight = ptFrame->x.m_tVideoParam.m_wVideoHeight;
                    }
#if 1
                    else if (ptFrame->x.m_tVideoParam.m_wVideoWidth != g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wVideoWidth
                        || ptFrame->x.m_tVideoParam.m_wVideoHeight != g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wVideoHeight )
                    {
                        SGWPRINTERR("%s  vid +++++++  %2d, %2d, dev:%2d dec w:%d h:%d change %d %d is key frame %d\n",
                                g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].achTaskID, nDecChnId, nDecId, nDecDevId,
                                g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wVideoWidth,
                                g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wVideoHeight,
                                ptFrame->x.m_tVideoParam.m_wVideoWidth, ptFrame->x.m_tVideoParam.m_wVideoHeight,
                                ptFrame->x.m_tVideoParam.m_bKeyFrame);
                        if(g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byStartDec)
                            __NvrSgwStreamStatSendMsg(nDecChnId, nDecId, FALSE, NVR_SGW_STREAM_STOP);
                        ///< 释放当前资源，重新申请
                        if(g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].nUniq)
                        {
                            SGWMEMNOTICE("%s: resmap free prep(x%u-y%u, %ux%u); realchn vid:%d-%d aud:%d-%d uniq:%d\n", g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].achTaskID,
                                    g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wVideoX,
                                    g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wVideoY,
                                    g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wVideoWidth,
                                    g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wVideoHeight,
                                    g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byRealChn, g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byRealDecNo,
                                    g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byRealChn, g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].nDecAudioId,
                                    g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].nUniq);
                            NvrSgwStreamDecFree(g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].nUniq);
                        }
                        g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].nUniq = 0;
                        g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byStartDec = FALSE;
                        g_tNvrSgwDecMgr.wCurVidDecNum[nDecChnId] = g_tNvrSgwDecMgr.wCurVidDecNum[nDecChnId] > 0 ? g_tNvrSgwDecMgr.wCurVidDecNum[nDecChnId] - 1 : 0;
                        g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wVideoWidth = 0;
                        g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wVideoHeight = 0;
                        if (g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byRealChn < NVR_SGW_MAX_CHIP_CHN_NUM)
                        {
                            SGWPRINTIMP("%s  ----- mosic clear\n", g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].achTaskID);
                            NvrSgwMediaCtrlSetChnMosicInvalidNew(g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byRealChn,
                                    g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].nDevVidWinId);
                        }
                        else
                        {
                            TNvrSgwCmdMediaCtrlStreamOp tDecStreamParam;
                            mzero(tDecStreamParam);

                            tDecStreamParam.dwFrameType = 0;
                            tDecStreamParam.wChnId = nDecChnId;
                            tDecStreamParam.wDecID = nDecId;

                            nRet = NvrSgwMcaCmdSend(CMD_NVR_MCA_VID_DEC_RESET, (u8*) &tDecStreamParam, sizeof(tDecStreamParam));
                            SGWMEMNOTICE("%s  ----- mosic clear  mcaMsgId:%u, %u-%u", g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].achTaskID,
                                         tDecStreamParam.dwMsgId, nDecChnId, nDecId);
                        }
                        bResChange = TRUE;
                    }
#endif

                    tPosInfo.nUniq = g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].nUniq;
                    tPosInfo.w = ptFrame->x.m_tVideoParam.m_wVideoWidth;
                    tPosInfo.h = ptFrame->x.m_tVideoParam.m_wVideoHeight;
                    tPosInfo.nAudDecId = g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].bOnly ? -1
                            : (((g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].dwAvSyncChn >> 8) & 0xff ) * NVR_SGW_MAX_CHN_AUD_NUM
                            + (g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].dwAvSyncChn & 0xff));
                    SGWPRINTIMP("%s  ===== stream [%d][%02d] apply [uniq:%d] vid %s %d=====\n", g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].achTaskID,
                            nDecChnId, nDecId, tPosInfo.nUniq,
                            g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].bOnly ? "" : "and aud", tPosInfo.nAudDecId);
                    SGWMEMNOTICE("%s: resmap calc prep(x%d-y%d, %dx%d); realchn vid:%d-%d aud:%d-%d uniq:%d \n", g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].achTaskID,
                            -1,
                            -1,
                            g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wVideoWidth,
                            g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wVideoHeight,
                            -1, -1,
                            -1, tPosInfo.nAudDecId, g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].nUniq);

                    NvrSgwStreamDecApply(&tPosInfo);
                    if ( 0 == tPosInfo.nUniq)
                    {
                        SGWPRINTERR("%s  ===== vid stream calc fail and notify vid:%d aud:%d =====\n",\
                                g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].achTaskID,\
                                tPosInfo.nVidDecId, tPosInfo.nAudDecId);
                        s32 nChip = 0;
                        if (-1 != tPosInfo.nAudDecId)
                        {
                            mzero(tPosInfo);
                            tPosInfo.nUniq = g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].nUniq;
                            tPosInfo.w = ptFrame->x.m_tVideoParam.m_wVideoWidth;
                            tPosInfo.h = ptFrame->x.m_tVideoParam.m_wVideoHeight;
                            tPosInfo.nAudDecId = g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].bOnly ? -1
                                        : (((g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].dwAvSyncChn >> 8) & 0xff ) * 8
                                        + (g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].dwAvSyncChn & 0xff));
                            nRet = NvrSgwStreamArrange(&tPosInfo, &nChip);
                            if (nRet)
                            {
                                nRet = NvrSgwChipArrangeOverChange(nChip);
                                if (0 == nRet)
                                {
                                    NvrSgwStreamResume(nChip);
                                    nRet = -1;
                                }
                            }
                            else
                            {
                                SGWPRINTERR("%s  ===== arrange fail  =====\n", g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].achTaskID);
                                nRet = -1;
                            }
                        }
                        else
                        {
                            nRet = -1;
                        }

                        if (nRet < 0)
                        {
                            SGWPRINTERR("%s  =====   over flow   =====\n", g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].achTaskID);
                            __NvrSgwStreamSendOverflowMsg(g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].achTaskID, bResChange);
                            g_bChnFull[tPosInfo.nAudDecId/NVR_SGW_MAX_CHN_AUD_NUM / NVR_SGW_MAX_CHIP_CHN_NUM] = TRUE;
                            pthread_mutex_unlock(&mediactrl_set_lock);
                            ((TMSFrame*) pData)->MSFreeFrame((TMSFrame*) pData);
                            return ;
                        }
                    }

                    SGWMEMNOTICE("%s ===== stream calc succ realchn:%d uniq:%u viddecid:%d auddecid:%d winIndex:%d take %llu(%llu)=====",
                            g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].achTaskID,
                            tPosInfo.nChn, tPosInfo.nUniq, tPosInfo.nVidDecId, tPosInfo.nAudDecId, tPosInfo.nChnWinIndex,
                            NvrSysGetCurTimeMSec() - timeIn[nDecChnId][nDecId], timeIn[nDecChnId][nDecId]);

                    nDecDevId = tPosInfo.nVidDecId;
                    g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byRealDecNo = nDecDevId;
                    g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byRealChn = tPosInfo.nChn;
                    g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].nDevVidWinId = tPosInfo.nChnWinIndex;
                    g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].nDecAudioId = tPosInfo.nAudDecId;
                    g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wVideoX = tPosInfo.x * RES_WIDTH_UNIT - tPosInfo.byBadd;
                    g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wVideoY = tPosInfo.y * RES_HEIGHT_UNIT;
                    g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wVideoWidth = ptFrame->x.m_tVideoParam.m_wVideoWidth;
                    g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wVideoHeight = ptFrame->x.m_tVideoParam.m_wVideoHeight;
                    g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wFranmeRate =
                    g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wFranmeRate > 0 ?
                    g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wFranmeRate : ptFrame->m_byFrameRate;
                    g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].nUniq =  tPosInfo.nUniq;
                    SGWMEMNOTICE("%s: resmap calc succ(x%u-y%u, %ux%u); realchn vid:%d-%d aud:%d-%d uniq:%d\n", g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].achTaskID,
                            g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wVideoX,
                            g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wVideoY,
                            g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wVideoWidth,
                            g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wVideoHeight,
                            g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byRealChn, g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byRealDecNo,
                            g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byRealChn, g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].nDecAudioId,
                            g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].nUniq);

                    ///< msg send
                    bSendStartEnc[nDecChnId][nDecId] = TRUE;
                    //__NvrSgwStreamStatSendMsg(nDecChnId, nDecId, FALSE, NVR_SGW_STREAM_START);
                    g_tNvrSgwDecMgr.wCurVidDecNum[nDecChnId]++;
                }

                tVidDecParam.eDecType = __NvrSgwConVidEncTypeToMediactrlVidDecType(
                        NvrSrvConverPayloadToVidEncType(ptFrame->m_byMediaType));
                tVidDecParam.dwFrameRate = g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wFranmeRate > 0 ?
                        g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wFranmeRate : ptFrame->m_byFrameRate;
                tVidDecParam.tVidDecRes.wWidth = ptFrame->x.m_tVideoParam.m_wVideoWidth < 96 ? 96 : ptFrame->x.m_tVideoParam.m_wVideoWidth;
                tVidDecParam.tVidDecRes.wHeight = ptFrame->x.m_tVideoParam.m_wVideoHeight < 96 ? 96 : ptFrame->x.m_tVideoParam.m_wVideoHeight;
                tVidDecParam.tVidDecEffect.bDCI = FALSE;
                tVidDecParam.tVidDecEffect.bNR = FALSE;
                tVidDecParam.tVidDecEffect.bES = TRUE;
                tVidDecParam.eFrameMode = MEDIACTRL_VDEC_FRAME_IPB;

                g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].wMediaType = ptFrame->m_byMediaType;
                g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.dwEncType = (u32)tVidDecParam.eDecType;
                g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wFranmeRate = tVidDecParam.dwFrameRate;
                g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wVideoWidth = tVidDecParam.tVidDecRes.wWidth;
                g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wVideoHeight = tVidDecParam.tVidDecRes.wHeight;



                if (g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byRealChn < NVR_SGW_MAX_CHIP_CHN_NUM)
                {
                    nRet = MediaCtrlVidDecSetParam(nDecDevId, &tVidDecParam);
                    SGWMEMNOTICE("%s RC MediaCtrlVidDecSetParam  vid:%d-%d  DecDevId:%d  (x%u-y%u, w%u-h%u)  ret:%d", g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].achTaskID,
                            nDecChnId, nDecId, nDecDevId,
                            g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wVideoX,
                            g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wVideoY,
                            tVidDecParam.tVidDecRes.wWidth,
                            tVidDecParam.tVidDecRes.wHeight, nRet);
                    if (tPosInfo.nUniq)
                    {
                        NvrSgwMediaCtrlSetChnMosicNew(&tPosInfo, ptFrame->x.m_tVideoParam.m_wVideoWidth, ptFrame->x.m_tVideoParam.m_wVideoHeight);
                    }
                }
                else
                {
                    TNvrSgwCmdMediaCtrlStreamOp tDecStreamParam;
                    mzero(tDecStreamParam);

                    tDecStreamParam.dwFrameType = 0;
                    tDecStreamParam.wChnId = nDecChnId;
                    tDecStreamParam.wDecID = nDecId;

                    tDecStreamParam.x = tPosInfo.x * RES_WIDTH_UNIT - tPosInfo.byBadd;
                    tDecStreamParam.y = tPosInfo.y * RES_HEIGHT_UNIT;
                    tDecStreamParam.w = g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wVideoWidth;
                    tDecStreamParam.h = g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wVideoHeight;

                    tDecStreamParam.nUniq = tPosInfo.nUniq;
                    tDecStreamParam.nChn = tPosInfo.nChn;
                    tDecStreamParam.nVidDecId = tPosInfo.nVidDecId;
                    tDecStreamParam.nAudDecId = tPosInfo.nAudDecId;
                    tDecStreamParam.nChnWinIndex = tPosInfo.nChnWinIndex;

                    memcpy(&tDecStreamParam.tDecInfo, &tVidDecParam, sizeof(tVidDecParam));
                    nRet = NvrSgwMcaCmdSend(CMD_NVR_MCA_VID_DEC_START, (u8*) &tDecStreamParam, sizeof(tDecStreamParam));
                    SGWMEMNOTICE("%s  EP StartDec mcaMsgId:%u  vid:%d-%d  (x%u-y%u, w%u-h%u)", g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].achTaskID,
                            tDecStreamParam.dwMsgId, nDecChnId, nDecId,
                            tDecStreamParam.x, tDecStreamParam.y,
                            tDecStreamParam.tDecInfo.tVidDecRes.wWidth,
                            tDecStreamParam.tDecInfo.tVidDecRes.wHeight);

                }
                SGWPRINTDBG("%s VidDecSet  %2d, %2d, dev:%2d[%d] ret:%d bFpsChange:%d Type:%d, fps:%u, rate:%u w:%d, h:%d, mode:%d, streamID:%d, ts:%u\n",
                        g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].achTaskID,
                        nDecChnId, nDecId, nDecDevId, g_tNvrSgwDecMgr.aatVidDecInfo[ nDecChnId][nDecId].byRealChn, nRet, g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].nVidParamChange,
                        tVidDecParam.eDecType, tVidDecParam.dwFrameRate,
                        g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.dwBitRate, tVidDecParam.tVidDecRes.wWidth,
                        tVidDecParam.tVidDecRes.wHeight, tVidDecParam.eFrameMode, ptFrame->m_byStreamID, ptFrame->m_dwTimeStamp);

                if (g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].nVidParamChange)
                {
                    __NvrSgwStreamStatSendMsg(nDecChnId, nDecId, 0, NVR_SGW_STREAM_UPDATE);
                }

                if (!g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byStartDec
                        && (g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byRealChn < NVR_SGW_MAX_CHIP_CHN_NUM))
                {
                    nRet = MediaCtrlVidDecStart(nDecDevId);
                    if (nRet)
                    {
                        SGWPRINTERR("%s  MediaCtrlVidDecStart %2d, %2d, dev:%2d ret:%2d\n",\
                                g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].achTaskID,\
                                nDecChnId, nDecId, nDecDevId, nRet);
                    }
                }
                g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byStartDec = TRUE;
                g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].nVidParamChange = FALSE;

            }
            pthread_mutex_unlock(&mediactrl_set_lock);

            g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].dwFrameCount++;
            g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].dwMaxFramSize =
                    ptFrame->m_dwDataSize > g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].dwMaxFramSize ?
                            ptFrame->m_dwDataSize : g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].dwMaxFramSize;

            if (g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byStartDec)
            {
                dwT1[nDecChnId][nDecId] = NvrSysGetCurTimeMSec();
                if (g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byRealChn < NVR_SGW_MAX_CHIP_CHN_NUM)
                {
                    nRet = MediaCtrlVidDecPutFrame(nDecDevId, ptFrame);
                    if(0 == nRet)
                    {
                        g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].dwDecFrameCnt++;
                    }
                    else
                    {
                        SGWPRINTERR("%s  vid: %u-%u  MediaCtrlVidDecPutFrame error ret: %d\n", g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].achTaskID, nDecChnId, nDecId, nRet);
                        SGWMEMNOTICE("%s  vid: %u-%u  MediaCtrlVidDecPutFrame error ret: %d", g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].achTaskID, nDecChnId, nDecId, nRet);
                        g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].nVidParamChange += 3;    ///<出现码流分辨率与设置到媒控的参数不一致时,媒控会返回错误码  0x000004,将此标志置为真,则可在下一帧更新参数到媒控
                    }
                }
                else
                {
                    TNvrSgwCmdMediaCtrlFrame tFaramInfo;
                    mzero(tFaramInfo);
                    tFaramInfo.dwFrameType = 0;
                    tFaramInfo.wChnId = nDecChnId;
                    tFaramInfo.wDecID = nDecId;
                    memcpy(&tFaramInfo.tFrame, ptFrame, sizeof(TKDFrame));

                    nRet = NvrSgwMcaFrameSendExt((u8*) &tFaramInfo, sizeof(tFaramInfo) + tFaramInfo.tFrame.m_dwDataSize, 1);
                    ((TMSFrame *)pData)->MSFreeFrame((TMSFrame *)pData);
                }
                dwT2[nDecChnId][nDecId] = NvrSysGetCurTimeMSec();
                if (0 != nRet)
                {
                    SGWPRINTERR("%s  nDecDevId:%d MediaCtrlVidDecPutFrame failed mcret:%d\n",\
                            g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].achTaskID, nDecDevId, nRet);
                }
                else
                {
                    if ((dwT2[nDecChnId][nDecId] - dwT1[nDecChnId][nDecId]) > g_dwCbPutLevel)
                    {
                        SGWPRINTDBG("%s  vid ------ %2d %2d nDecDevId:%2d DecPutFrame succ streamID:%d, size:%u "\
                                "MediaType:%d, FrameRate:%d, Width:%d, Height:%d, put time:%u ms\n",\
                                g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].achTaskID,\
                                nDecChnId, nDecId,\
                                nDecDevId, ptFrame->m_byStreamID, ptFrame->m_dwDataSize, ptFrame->m_byMediaType,\
                                ptFrame->m_byFrameRate,\
                                ptFrame->x.m_tVideoParam.m_wVideoWidth, ptFrame->x.m_tVideoParam.m_wVideoHeight,\
                                (dwT2[nDecChnId][nDecId] - dwT1[nDecChnId][nDecId]));
                    }

                }
                if(bSendStartEnc[nDecChnId][nDecId])
                {
                    __NvrSgwStreamStatSendMsg(nDecChnId, nDecId, FALSE, NVR_SGW_STREAM_START);
                    bSendStartEnc[nDecChnId][nDecId] = FALSE;
                }
            }
        }while(0);
    }

    timeOut[nDecChnId][nDecId] = NvrSysGetCurTimeMSec();
    if (g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].dwFrameCount > 1)
    {
        AvTime[nDecChnId][nDecId] = (AvTime[nDecChnId][nDecId] + (timeIn[nDecChnId][nDecId] - time[nDecChnId][nDecId])) / (g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].dwFrameCount - 1);
    }
    else
    {
        AvTime[nDecChnId][nDecId] = 0;
    }

    if ((timeOut[nDecChnId][nDecId] - timeIn[nDecChnId][nDecId]) > g_dwCbPutLevel
            || (timeIn[nDecChnId][nDecId] - time[nDecChnId][nDecId]) > g_dwMsCbVidIntervalMs)
    {
        if (-1 == g_dwMsCbShowAChnXXX || g_dwMsCbShowVChnXXX == (nDecChnId * 100 + nDecId))
        {
            SGWPRINTFREQ("%s  vid ------ %2d %2d nDecDevId:%2d cb take time:%4llu net interval[%4llu][AT:%llu, count:%u]\n",\
                    g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].achTaskID,\
                    nDecChnId, nDecId, nDecDevId, (timeOut[nDecChnId][nDecId] - timeIn[nDecChnId][nDecId]),\
                    timeIn[nDecChnId][nDecId] - time[nDecChnId][nDecId], AvTime[nDecChnId][nDecId], g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].dwFrameCount);
        }
    }

    time[nDecChnId][nDecId] = timeIn[nDecChnId][nDecId];

    return;
}

static void __NvrSgwDecFrameAudDateCBNew(void* pData, void* pvContext)
{
    s32 nRet = 0;
    s32 nDecChnId = -1;
    s32 nDecId = -1;
    s32 nDecDevId = -1;
    u64 dwT1 = 0, dwT2 = 0;
    u64 timeIn[NVR_SGW_MAX_CHN_NUM][NVR_SGW_MAX_CHN_VID_NUM] = {0};
    u64 timeOut[NVR_SGW_MAX_CHN_NUM][NVR_SGW_MAX_CHN_VID_NUM] = {0};
    static u64 time[NVR_SGW_MAX_CHN_NUM][NVR_SGW_MAX_CHN_VID_NUM] = {0,};
    static u64 AvTime[NVR_SGW_MAX_CHN_NUM][NVR_SGW_MAX_CHN_VID_NUM] = {0,};

    if(NULL != pvContext)
    {
        nDecChnId = (((u32)*(u32*)pvContext) >> 8) & 0xff;
        nDecId = ((u32)*(u32*)pvContext) & 0xff;
    }
    else
    {
        SGWPRINTDBG("pvContext is null.\n");
        if(NULL != pData)
        {
            ((TMSFrame *)pData)->MSFreeFrame((TMSFrame *)pData);
        }
        return;
    }

    if (g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].byStopDec)
    {
        if(NULL != pData)
        {
            ((TMSFrame *)pData)->MSFreeFrame((TMSFrame *)pData);
        }
        return;
    }

    timeIn[nDecChnId][nDecId] = NvrSysGetCurTimeMSec();
    if(NULL != pData)
    {
        ///<解码器id有效
        do
        {
            TKDFrame *frame = &(((TMSFrame*) pData)->m_tFrame);

            if (!g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].byStartDec)
            {
                pthread_mutex_lock(&audio_set_lock);
                if (g_tNvrSgwDecMgr.wCurAudDecNum[nDecChnId] >= NVR_SGW_MAX_CHN_AUD_NUM)
                {
                    pthread_mutex_unlock(&audio_set_lock);
                    SGWPRINTERR("%s  aud ------%d %d decid:%d  dec chn full %d %d]\n",\
                            g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].achTaskID, nDecChnId, nDecId, nDecDevId,\
                            g_tNvrSgwDecMgr.wCurAudDecNum[nDecChnId], NVR_SGW_MAX_CHN_AUD_NUM);
                    ((TMSFrame*) pData)->MSFreeFrame((TMSFrame*) pData);
                    return ;
                }

                if (g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].bOnly)
                {
                    TNvrSgwResPosInfo tPosInfo;
                    mzero(tPosInfo);

                    tPosInfo.nUniq = g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].nUniq;
                    tPosInfo.nAudDecId = g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].nDecAudioId;
                    SGWPRINTIMP("%s  ===== stream [%d][%02d] apply [%d] only aud =====\n",\
                            g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].achTaskID, nDecChnId, nDecId, tPosInfo.nUniq);
                    NvrSgwStreamDecApply(&tPosInfo);
                    if (tPosInfo.nUniq)
                    {
                        SGWPRINTIMP("%s  ===== stream calc succ aud realchn:%d uniq:%u auddecid:%d=====\n",\
                                g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].achTaskID,\
                                tPosInfo.nChn, tPosInfo.nUniq, tPosInfo.nAudDecId);

                        nDecDevId = tPosInfo.nVidDecId;
                        g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].byRealDecNo = tPosInfo.nAudDecId;
                        g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].nUniq = tPosInfo.nUniq;
                    }
                    else
                    {
                        SGWPRINTERR("%s  ===== aud stream calc fail and notify =====\n", g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].achTaskID);
                    }
                }
                else
                {
                    s32 nVChn = (g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].dwAvSyncChn >> 8) & 0xff;
                    s32 nVSubChn = g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].dwAvSyncChn & 0xff;
                    if (nVChn < NVR_SGW_MAX_CHN_NUM && nVSubChn < NVR_SGW_MAX_CHN_VID_NUM)
                    {
                        if (g_tNvrSgwDecMgr.aatVidDecInfo[nVChn][nVSubChn].byStartDec)
                        {
                            g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].byRealDecNo =
                                    g_tNvrSgwDecMgr.aatVidDecInfo[nVChn][nVSubChn].nDecAudioId;
                            g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].nUniq =
                                    g_tNvrSgwDecMgr.aatVidDecInfo[nVChn][nVSubChn].nUniq;
                        }
                        else
                        {
                            pthread_mutex_unlock(&audio_set_lock);
                            SGWPRINTFREQ("%s  aud ------%d %d  wait vid \n", nDecChnId, nDecId, g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].achTaskID);
                            ((TMSFrame*) pData)->MSFreeFrame((TMSFrame*) pData);
                            return ;
                        }
                    }
                }
                g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].byRealChn =
                        g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].byRealDecNo / NVR_SGW_MAX_CHN_AUD_NUM;

                g_tNvrSgwDecMgr.wCurAudDecNum[nDecChnId]++;

                if (g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].byRealChn >= NVR_SGW_MAX_CHIP_CHN_NUM)
                {
                    TNvrSgwCmdMediaCtrlStreamOp tDecStreamParam;
                    mzero(tDecStreamParam);

                    tDecStreamParam.dwFrameType = 0;
                    tDecStreamParam.wChnId = nDecChnId;
                    tDecStreamParam.wDecID = nDecId;

                    tDecStreamParam.nUniq = g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].nUniq;
                    tDecStreamParam.nChn = g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].byRealChn;
                    tDecStreamParam.nAudDecId = g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].byRealDecNo;

                    nRet = NvrSgwMcaCmdSend(CMD_NVR_MCA_AUD_DEC_START, (u8*)&tDecStreamParam, sizeof(tDecStreamParam));
                    SGWPRINTDBG("%s  CMD_NVR_MCA_AUD_DEC_START %2d, %2d, dev:%2d ret:%2d\n",\
                            g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].achTaskID, \
                            nDecChnId, nDecId, g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].byRealDecNo, nRet);
                }

                SGWPRINTDBG("%s  Aud dec chnId:%d, decid:%d[%d], decdev:%d "\
                        "eDecType:%d, Sample:%u, Channel:%d, BitsP:%d, streamID:%d, ts:%u\n",\
                        g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].achTaskID,\
                        nDecChnId, nDecId, g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].byRealDecNo,\
                        g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].byRealChn,\
                        frame->m_byMediaType, frame->x.m_tAudioParam.m_dwSample, frame->x.m_tAudioParam.m_wChannel,\
                        frame->x.m_tAudioParam.m_wBitsPerSample, frame->m_byStreamID, frame->m_dwTimeStamp);

                g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].wMediaType = frame->m_byMediaType;
                g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].vaInfo.tAudioParam.dwSample = frame->x.m_tAudioParam.m_dwSample;
                g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].vaInfo.tAudioParam.wBitsPerSample = frame->x.m_tAudioParam.m_wBitsPerSample;
                g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].vaInfo.tAudioParam.wChannel = frame->x.m_tAudioParam.m_wChannel;
                g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].byStartDec = TRUE;

                pthread_mutex_unlock(&audio_set_lock);
            }

            nDecDevId = g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].byRealDecNo;

            if (nDecDevId < 0)
            {
                SGWPRINTERR("%s  aud ------%d %d decid:%d  is invalid \n", g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].achTaskID, nDecChnId, nDecId, nDecDevId);
                ((TMSFrame*) pData)->MSFreeFrame((TMSFrame*) pData);
                return ;
            }
            g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].dwFrameCount++;
            g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].dwMaxFramSize =
                    frame->m_dwDataSize > g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].dwMaxFramSize ?
                            frame->m_dwDataSize : g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].dwMaxFramSize;
            g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].dwFrameID = frame->m_dwFrameID;

            if (g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].byStartDec)
            {
                dwT1 = NvrSysGetCurTimeMSec();
                if (nDecDevId < NVR_SGW_MAX_AUD_NUM)
                {
                    if (g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].byRealChn < NVR_SGW_MAX_CHIP_CHN_NUM)
                    {
                        nRet = MediaCtrlAudDecPutFrame(nDecDevId, frame);
                        if(0 == nRet)
                        {
                            g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].dwDecFrameCnt++;
                        }
                    }
                    else
                    {
                        TNvrSgwCmdMediaCtrlFrame tFaramInfo;
                        mzero(tFaramInfo);
                        tFaramInfo.dwFrameType = 1;
                        tFaramInfo.wChnId = nDecChnId;
                        tFaramInfo.wDecID = nDecId;
                        memcpy(&tFaramInfo.tFrame, frame, sizeof(TKDFrame));

                        nRet = NvrSgwMcaFrameSendExt((u8*) &tFaramInfo, sizeof(tFaramInfo) + tFaramInfo.tFrame.m_dwDataSize, 1);
                        ((TMSFrame *)pData)->MSFreeFrame((TMSFrame *)pData);
                    }
                }
                dwT2 = NvrSysGetCurTimeMSec();
                if (0 != nRet)
                {
                    SGWPRINTERR("%s  nDecDevId:%d MediaCtrlAudDecPutFrame failed mcret:%d\n",\
                        g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].achTaskID, nDecDevId, nRet);
                }
                else
                {
                    if ((dwT2 - dwT1) > g_dwCbPutLevel)
                    {
                        SGWPRINTDBG("%s  aud ------ %2d %2d decid:%2d MediaCtrlAudDecPutFrame succ take time:%u ms\n",\
                                g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].achTaskID,\
                                nDecChnId, nDecId, nDecDevId, dwT2 - dwT1);
                    }
                }
            }


        }while(0);
    }
    timeOut[nDecChnId][nDecId] = NvrSysGetCurTimeMSec();
    if (g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].dwFrameCount > 1)
    {
        AvTime[nDecChnId][nDecId] = (AvTime[nDecChnId][nDecId] + (timeIn[nDecChnId][nDecId] - time[nDecChnId][nDecId])) / (g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].dwFrameCount - 1);
    }

    if ((timeOut[nDecChnId][nDecId] - timeIn[nDecChnId][nDecId]) > g_dwCbPutLevel
            || (timeIn[nDecChnId][nDecId] - time[nDecChnId][nDecId]) >= g_dwMsCbAudIntervalMs)
    {
        if (-1 == g_dwMsCbShowAChnXXX || g_dwMsCbShowAChnXXX == (nDecChnId * 100 + nDecId))
        {
            SGWPRINTFREQ("%s  aud ------ %2d %2d nDecDevId:%2d cb take time:%4llu net interval[%4llu][AT:%llu, count:%u]\n",\
                    g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].achTaskID,\
                    nDecChnId, nDecId, nDecDevId, (timeOut[nDecChnId][nDecId] - timeIn[nDecChnId][nDecId]),\
                    timeIn[nDecChnId][nDecId] - time[nDecChnId][nDecId], AvTime[nDecChnId][nDecId], g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].dwFrameCount);
        }
    }

    time[nDecChnId][nDecId] = timeIn[nDecChnId][nDecId];
    return;
}

NVRSTATUS NvrSgwDecStreamStart(TNvrSgwStreamInfo *ptDecInfo)
{
    NVRSTATUS eRet = NVR_ERR__ERROR;
    TNvrVtduStreamFrameParams tParam;
    u32 dwOutId = 0, dwPipelineId = 0;
    s32 nDecChnId = 0, nDecId = 0, dwMsInId = 0;
    ENvrStreamPacketType eType = NVR_STREAM_RTMP_TYPE;

    if ( ((ptDecInfo->tVidInfo.byChnId >= NVR_SGW_MAX_CHN_NUM || ptDecInfo->tVidInfo.byChnDecId >= NVR_SGW_MAX_CHN_VID_NUM) && ptDecInfo->bHasVideo)
        || ((ptDecInfo->tAudInfo.byChnId >= NVR_SGW_MAX_CHN_NUM || ptDecInfo->tAudInfo.byChnDecId >= NVR_SGW_MAX_CHN_AUD_NUM) && ptDecInfo->bHasAudio))
    {
        SGWPRINTDBG("param in err \n");
        return eRet;
    }

    if (ptDecInfo->bHasVideo)
    {
        
        nDecChnId = ptDecInfo->tVidInfo.byChnId;
        nDecId = ptDecInfo->tVidInfo.byChnDecId;
        dwMsInId = ptDecInfo->tVidInfo.dwMsInId;
        
        if (g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byValid)
        {
            TNvrSgwStreamInfo tStopDecInfo;
            mzero(tStopDecInfo);

            tStopDecInfo.bHasVideo = TRUE;
            tStopDecInfo.tVidInfo.byChnId = nDecChnId;
            tStopDecInfo.tVidInfo.byChnDecId = nDecId;
            tStopDecInfo.bHasAudio = !g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].bOnly;
            tStopDecInfo.tAudInfo.byChnId = (g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].dwAvSyncChn >> 8 ) & 0xff;
            tStopDecInfo.tAudInfo.byChnDecId = g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].dwAvSyncChn & 0xff;;
            NvrSgwDecStreamStop(&tStopDecInfo);
        }
    }

    if (ptDecInfo->bHasAudio)
    {
        nDecChnId = ptDecInfo->tAudInfo.byChnId;
        nDecId = ptDecInfo->tAudInfo.byChnDecId;
        dwMsInId = ptDecInfo->tAudInfo.dwMsInId;

        if (g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].byValid)
        {
            TNvrSgwStreamInfo tStopDecInfo;
            mzero(tStopDecInfo);

            tStopDecInfo.bHasAudio = TRUE;
            tStopDecInfo.tAudInfo.byChnId = nDecChnId;
            tStopDecInfo.tAudInfo.byChnDecId = nDecId;
            NvrSgwDecStreamStop(&tStopDecInfo);
        }

        ///<从dwMsInId中起帧，进解码
        tParam.eMediaType = NVR_MEDIA_AUDIO;
        tParam.eBrowseytpe = NVR_REQ_FRAME_SND_BY_LOCDEC;
        tParam.wEncId = nDecId;
        tParam.pSndDiscnctCB = __NvrSgwDecDealChnDisconnect;
        tParam.bSetFrameCB = TRUE;
        tParam.tFrameCBParam.tTrackId.dwTrackIndx = 0;
        tParam.tFrameCBParam.tTrackId.eMediaType = NVR_MEDIA_AUDIO;
        tParam.tFrameCBParam.eDataPackFormat = NVR_DATAPACK_FORMAT_FRAME;
        tParam.tFrameCBParam.pfDataCallBackProc = __NvrSgwDecFrameAudDateCBNew;
        tParam.tFrameCBParam.pvContext = (void*) &g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].dwChnIdDecID;
        eRet = NvrVtduCtrlPrepareFrameStream(dwMsInId, eType, &tParam, &dwOutId, &dwPipelineId);
        if (eRet != NVR_ERR__OK)
        {
            SGWPRINTERR("sgw audio get frame failed! eRet:%d, dwChnId:%u, dwDecId:%u\n", eRet, nDecChnId, nDecId);
            return eRet;
        }

        g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].dwChnIdDecID = ((nDecChnId << 8) + nDecId);
        g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].byStopDec = FALSE;
        g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].byValid = TRUE;
        g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].dwMsInId = dwMsInId;
        g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].dwMsOutId = dwOutId;
        g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].dwMsPipeLineId = dwPipelineId;
        g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].nDecAudioId = nDecChnId * NVR_SGW_MAX_CHN_AUD_NUM + nDecId;
        g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].bOnly = !ptDecInfo->bHasVideo;
        if (ptDecInfo->bHasVideo)
        {
            g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].dwAvSyncChn = (ptDecInfo->tVidInfo.byChnId << 8) + ptDecInfo->tVidInfo.byChnDecId;
        }
        SGWPRINTIMP("aud dwChnId:%u decid:%d eType;%d NvrVtduCtrlPrepareFrameStream dwOutId :%u succ aud:%d v:%d %d uniq:%d\n",
                nDecChnId, nDecId, eType, dwOutId, g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].nDecAudioId,
                (g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].dwAvSyncChn >> 8) & 0xff,
                g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].dwAvSyncChn & 0xff,
                g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].nUniq);
        g_tNvrSgwDecMgr.wTotalAudDecNum++;
        strncpy((char *)g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].achTaskID,  ptDecInfo->achTaskID, SDSCPPRPTO_RTMP_TASK_ID_BUF_LEN);

        if (0 == NvrSgwStreamDecAudPrepare(g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].nDecAudioId))
        {
            __NvrSgwStreamSendOverflowMsg(g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].achTaskID, FALSE);
            g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].byStopDec = TRUE;
            if (ptDecInfo->bHasVideo)
            {
                g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byStopDec = TRUE;
            }
        }
        else
        {
            if (g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].bOnly)
            {
                __NvrSgwStreamStatSendMsg(nDecChnId, nDecId, TRUE, NVR_SGW_STREAM_START);
            }
        }

        eRet = NVR_ERR__OK;
    }

    if (ptDecInfo->bHasVideo)
    {
        nDecChnId = ptDecInfo->tVidInfo.byChnId;
        nDecId = ptDecInfo->tVidInfo.byChnDecId;
        dwMsInId = ptDecInfo->tVidInfo.dwMsInId;

        ///<从dwMsInId中起帧，进解码
        tParam.eMediaType = NVR_MEDIA_VEDIO;
        tParam.eBrowseytpe = NVR_REQ_FRAME_SND_BY_LOCDEC;
        tParam.wEncId = nDecId;
        tParam.pSndDiscnctCB = __NvrSgwDecDealChnDisconnect;
        tParam.bSetFrameCB = TRUE;
        tParam.tFrameCBParam.tTrackId.dwTrackIndx = 0;
        tParam.tFrameCBParam.tTrackId.eMediaType = NVR_MEDIA_VEDIO;
        tParam.tFrameCBParam.eDataPackFormat = NVR_DATAPACK_FORMAT_FRAME;
        tParam.tFrameCBParam.pfDataCallBackProc = __NvrSgwDecFrameVidDateCBNew;
        tParam.tFrameCBParam.pvContext = (void*)&g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].dwChnIdDecID;
        eRet = NvrVtduCtrlPrepareFrameStream(dwMsInId, eType, &tParam, &dwOutId, &dwPipelineId);
        if(eRet != NVR_ERR__OK)
        {
            SGWPRINTERR("sgw video get frame failed! eRet:%d, dwChnId:%u, dwDecId:%u\n", eRet, nDecChnId, nDecId);
            return eRet;
        }
        SGWPRINTIMP("vid dwChnId:%u decid:%d eType:%d NvrVtduCtrlPrepareFrameStream dwOutId :%u succ uniq:%d\n",
                nDecChnId, nDecId, eType, dwOutId, g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].nUniq);
        g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].dwChnIdDecID = ((nDecChnId << 8) + nDecId);
        g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byStopDec = FALSE;
        g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byValid = TRUE;
        g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].dwMsInId = dwMsInId;
        g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].dwMsOutId = dwOutId;
        g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].dwMsPipeLineId = dwPipelineId;
        g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wAdjustFps = 0;
        g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.dwAdjustBitRate = 0;
        g_tNvrSgwDecMgr.wTotalVidDecNum++;
        g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].bOnly = !ptDecInfo->bHasAudio;
        if (ptDecInfo->bHasAudio)
        {
            g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].dwAvSyncChn = (ptDecInfo->tAudInfo.byChnId << 8) + ptDecInfo->tAudInfo.byChnDecId;
        }
        else
        {
            g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].dwAvSyncChn = -1;
        }
        g_dwAvSyncAudChn[nDecChnId][nDecId] = g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].dwAvSyncChn;
        strncpy((char *)g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].achTaskID,  ptDecInfo->achTaskID, SDSCPPRPTO_RTMP_TASK_ID_BUF_LEN);

        eRet = NVR_ERR__OK;
    }

    SGWPRINTIMP("strat set over\n");
    return eRet;
}

void NvrSgwMsFree(TMSFrame * ptMsFrame)
{
    if (ptMsFrame)
    {
        if (ptMsFrame->m_tFrame.m_pData)
        {
            NVRFREE(ptMsFrame->m_tFrame.m_pData);
        }

        NVRFREE(ptMsFrame);
    }
}

void NvrSgwDecStreamStop(TNvrSgwStreamInfo *ptDecInfo)
{
    NVRSTATUS eRet = NVR_ERR__OK;
    s32 nRet = 0;
    s32 nDecDevId = -1;
    u32 nDecChnId = 0, nDecId = 0;
    s32 nUniq = 0;

    if (ptDecInfo->bHasVideo)
    {
        nDecChnId = ptDecInfo->tVidInfo.byChnId;
        nDecId = ptDecInfo->tVidInfo.byChnDecId;
        g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byStopDec = TRUE;
        nDecDevId = g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byRealDecNo;

        g_bRtmpNoDataFlag[nDecChnId][nDecId] = 0;
        g_bRtmpNoDataOldRes[nDecChnId][nDecId].wHeight = 0;
        g_bRtmpNoDataOldRes[nDecChnId][nDecId].wWidth = 0;

        ///<具体分配画布信息后，才去做画布的清除等动作
        if (g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].nUniq)
        {
            s32 nRealChn = g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byRealChn;
            if (nRealChn < NVR_SGW_MAX_CHIP_CHN_NUM)
            {
                pthread_mutex_lock(&mediactrl_set_lock);
                NvrSgwMediaCtrlSetChnMosicInvalidNew(nRealChn, g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].nDevVidWinId);
                nRet = MediaCtrlVidDecStop(nDecDevId, TRUE);
                SGWPRINTFREQ("-----MediaCtrlVidDecStop nDecId:%d nDecDevId:%d nRet:%d\n", nDecId, nDecDevId, nRet);
                EMediactrlVidDisDev eDev = MEDIACTRL_VDIS_DEV_BT1120;
                eDev = 0 == nDecChnId ? MEDIACTRL_VDIS_DEV_BT1120 : MEDIACTRL_VDIS_DEV_HDMI1;
                nRet = MediaCtrlVidDisClearWin(eDev, g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].nDevVidWinId);
                SGWPRINTFREQ("-----MediaCtrlVidDisClearWin eDev:%d nDecId:%d nDecDevId:%d nRet:%d\n", eDev, nDecId, nDecDevId, nRet);
                pthread_mutex_unlock(&mediactrl_set_lock);
            }
            else
            {
                TNvrSgwCmdMediaCtrlStreamOp tDecStreamParam;
                mzero(tDecStreamParam);

                tDecStreamParam.dwFrameType = 0;
                tDecStreamParam.wChnId = nDecChnId;
                tDecStreamParam.wDecID = nDecId;

                tDecStreamParam.nUniq = g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].nUniq;
                tDecStreamParam.nChn = nRealChn;
                tDecStreamParam.nVidDecId = nDecDevId;
                tDecStreamParam.nChnWinIndex = g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].nDevVidWinId;

                nRet = NvrSgwMcaCmdSend(CMD_NVR_MCA_VID_DEC_STOP, (u8*) &tDecStreamParam, sizeof(tDecStreamParam));
                SGWPRINTDBG("CMD_NVR_MCA_VID_DEC_STOP %2d, %2d, dev:%2d ret:%2d\n", nDecChnId, nDecId, nDecDevId, nRet);
            }
        }

        SGWPRINTIMP("vid dwChnId:%u decid:%2d [%d] NvrVtduCtrlDestroyFrameStream dwOutId:%u dwMsPipeLineId:%u succ %d\n", nDecChnId,
                nDecId,g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byRealChn,
                g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].dwMsOutId,
                g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].dwMsPipeLineId,
                g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byValid);
        eRet = NvrVtduCtrlDestroyFrameStream(g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].dwMsInId, NVR_MEDIA_VEDIO,
                g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].dwMsOutId,
                g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].dwMsPipeLineId);
        if (eRet != NVR_ERR__OK)
        {
            SGWPRINTERR("vid NvrVtduCtrlDestroyFrameStream failed! eRet:%d\n", eRet);
        }

        g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byValid = FALSE;
        g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].dwFrameCount = 0;
        g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].dwDecFrameCnt = 0;
        g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byRealChn = 0;
        g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].dwAvSyncChn = -1;
        g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byRealDecNo = -1;
        g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].dwMaxFramSize = 0;
        g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wFranmeRate = 0;
        g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.dwAdjustFpsTime = 0;
        g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wVideoWidth = 0;
        g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wVideoHeight = 0;

        nUniq =  g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].nUniq;

        SGWMEMNOTICE("%s: resmap free prep(x%u-y%u, %ux%u); realchn vid:%d-%d aud:%d-%d uniq:%d\n", g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].achTaskID,
                g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wVideoX,
                g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wVideoY,
                g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wVideoWidth,
                g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wVideoHeight,
                g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byRealChn, g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byRealDecNo,
                g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byRealChn, g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].nDecAudioId,
                g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].nUniq);

        g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].nUniq = 0;

        pthread_mutex_lock(&video_set_lock);
        g_tNvrSgwDecMgr.wTotalVidDecNum = g_tNvrSgwDecMgr.wTotalVidDecNum > 0 ? g_tNvrSgwDecMgr.wTotalVidDecNum - 1 : 0;
        if (g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byStartDec)
        {
            g_tNvrSgwDecMgr.wCurVidDecNum[nDecChnId] = g_tNvrSgwDecMgr.wCurVidDecNum[nDecChnId] > 0 ?  g_tNvrSgwDecMgr.wCurVidDecNum[nDecChnId] - 1 : 0;
        }
        g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byStartDec = FALSE;

        if (ptDecInfo->bHasAudio)
        {
            s32 nAudDecID = (((g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].dwAvSyncChn >> 8) & 0xff ) * NVR_SGW_MAX_CHN_AUD_NUM
                            + (g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].dwAvSyncChn & 0xff));
            g_bChnFull[nAudDecID / NVR_SGW_MAX_CHN_AUD_NUM / NVR_SGW_MAX_CHIP_CHN_NUM] = FALSE;    
        }
        else
        {
            g_bChnFull[g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byRealChn / NVR_SGW_MAX_CHIP_CHN_NUM] = FALSE;      
        }

        __NvrSgwStreamStatSendMsg(nDecChnId, nDecId, FALSE, NVR_SGW_STREAM_STOP);
        pthread_mutex_unlock(&video_set_lock);
    }

    if (ptDecInfo->bHasAudio)
    {
        nDecChnId = ptDecInfo->tAudInfo.byChnId;
        nDecId = ptDecInfo->tAudInfo.byChnDecId;
        if (nDecId < NVR_SGW_MAX_CHN_AUD_NUM)
        {
            g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].byStopDec = TRUE;
            nDecDevId = g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].byRealDecNo;
            if (g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byRealChn < NVR_SGW_MAX_CHIP_CHN_NUM)
            {
                TMSFrame *ptMsFrame = NULL;

                ptMsFrame = NVRALLOC(sizeof(TMSFrame));
                if (ptMsFrame)
                {
                    ///<目前媒控内部在没有音频帧输入是会自动推入静音帧
                    ptMsFrame->MSFreeFrame = NvrSgwMsFree;
                    ptMsFrame->m_tFrame.m_byMediaType = 8; ///<MEDIA_TYPE_PCMA
                    ptMsFrame->m_tFrame.m_dwFrameID = g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].dwFrameID + 1;
                    ptMsFrame->m_tFrame.m_byStreamID = 0;
                    ptMsFrame->m_tFrame.m_dwDataSize = 240;
                    ptMsFrame->m_tFrame.x.m_tAudioParam.m_wChannel = 1;
                    ptMsFrame->m_tFrame.x.m_tAudioParam.m_wBitsPerSample = 16;
                    ptMsFrame->m_tFrame.x.m_tAudioParam.m_dwSample = 8000;
                    ptMsFrame->m_tFrame.m_pData = NVRALLOC(ptMsFrame->m_tFrame.m_dwDataSize);
                    if (ptMsFrame->m_tFrame.m_pData)
                    {
                        memset(ptMsFrame->m_tFrame.m_pData, 0x0, ptMsFrame->m_tFrame.m_dwDataSize);
                    }
                    MediaCtrlAudDecPutFrame(nDecDevId, &ptMsFrame->m_tFrame);
                }
            }
            else
            {
                TNvrSgwCmdMediaCtrlStreamOp tDecStreamParam;
                mzero(tDecStreamParam);

                tDecStreamParam.dwFrameType = 1;
                tDecStreamParam.wChnId = nDecChnId;
                tDecStreamParam.wDecID = nDecId;

                tDecStreamParam.nUniq = g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].nUniq;
                tDecStreamParam.nChn = g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].byRealChn;
                tDecStreamParam.nAudDecId = g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].byRealDecNo;

                nRet = NvrSgwMcaCmdSend(CMD_NVR_MCA_AUD_DEC_STOP, (u8*)&tDecStreamParam, sizeof(tDecStreamParam));
                SGWPRINTDBG("CMD_NVR_MCA_AUD_DEC_STOP %2d, %2d, dev:%2d ret:%2d\n", nDecChnId, nDecId,
                        g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].byRealDecNo, nRet);
            }
            SGWPRINTIMP("aud dwChnId:%u decid:%2d [%d] NvrVtduCtrlDestroyFrameStream dwOutId:%u dwMsPipeLineId:%u succ %d\n", nDecChnId,
                    nDecId, g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].byRealChn,
                    g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].dwMsOutId,
                    g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].dwMsPipeLineId,
                    g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].byValid);

            eRet = NvrVtduCtrlDestroyFrameStream(g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].dwMsInId, NVR_MEDIA_AUDIO,
                    g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].dwMsOutId,
                    g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].dwMsPipeLineId);
            if (eRet != NVR_ERR__OK)
            {
                SGWPRINTERR("aud NvrVtduCtrlDestroyFrameStream failed! eRet:%d\n", eRet);
            }

            if (!ptDecInfo->bHasVideo)
            {
                MSRESULT dwMsRet = MSRESULT_NO_ERROR;

                dwMsRet = MSInStop( g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].dwMsInId);
                SGWPRINTDBG("MSInStop chnid:%u,aud:%u msinid:%lu,msret:%d\n",
                        nDecChnId, nDecId, g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].dwMsInId, dwMsRet);
                dwMsRet = MSInRelease(g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].dwMsInId);
                SGWPRINTDBG("MSInRelease chnid:%u,aud:%u msinid:%lu,msret:%d\n",
                        nDecChnId, nDecId, g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].dwMsInId, dwMsRet);

                __NvrSgwStreamStatSendMsg(nDecChnId, nDecId, TRUE, NVR_SGW_STREAM_STOP);
            }

            g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].byValid = FALSE;
            g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].dwFrameCount = 0;
            g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].dwDecFrameCnt = 0;
            g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].byRealChn = 0;
            g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].dwAvSyncChn = -1;
            g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].byRealDecNo = -1;
            g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].dwMaxFramSize = 0;

            nUniq = nUniq ? nUniq : g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].nUniq;
            g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].nUniq = 0;

            pthread_mutex_lock(&audio_set_lock);
            g_tNvrSgwDecMgr.wTotalAudDecNum = g_tNvrSgwDecMgr.wTotalAudDecNum > 0 ? g_tNvrSgwDecMgr.wTotalAudDecNum - 1 : 0;
            if (g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].byStartDec)
            {
                g_tNvrSgwDecMgr.wCurAudDecNum[nDecChnId] = g_tNvrSgwDecMgr.wCurAudDecNum[nDecChnId] > 0 ? g_tNvrSgwDecMgr.wCurAudDecNum[nDecChnId] - 1 : 0;
            }
            g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].byStartDec = FALSE;
            pthread_mutex_unlock(&audio_set_lock);
        }
    }

    NvrSgwStreamDecFree(nUniq);

    MAKE_COMPILER_HAPPY(nDecDevId);
}

#if 0
NvrVtduRegStatusNotifyCB(LcamMgrGBStatusNotifyCB);
void LcamMgrGBStatusNotifyCB(ENvrVtduNotifyCbType eCbType, void *pContext)
{
    ///<needdo 国标 音频呼叫解码通道处理
    if(pContext)
    {
        if(VTDU_NOTIFY_BROWSE == eCbType)
        {
            TNvrVtduBrowseStatus *ptBrowseStatus = (TNvrVtduBrowseStatus *)pContext;
            LCAMMGRDBG("eCbType:%d bStart:%d\n", eCbType, ptBrowseStatus->bStart);
            if (g_bGBLive != ptBrowseStatus->bStart)
            {
                g_bGBLive = ptBrowseStatus->bStart;
                LcamMgrNotifyMediaStat(0, LCAM_MGR_LIVE_GBV_LIVE, (u8)g_bGBLive);
            }
        }
        else if(VTDU_NOTIFY_AUDCALL == eCbType)
        {
            TNvrVtduAudCallStatus *ptAudCallStatus = (TNvrVtduAudCallStatus *)pContext;
            LcamMgrNotifyMediaStat(0, LCAM_MGR_LIVE_GBA_CALL, (u8)ptAudCallStatus->bStart);
        }
    }
}
#endif

#define SDSCPPROTO_RTMP_TASK_ACTION_WAITDEL 400  //任务节点停止待删除状态
NVRSTATUS NvrSgwFindAndMarkStopRtmpTask(s8 *achTaskID)
{
    NVRSTATUS eRet = NVR_ERR__OK;
    TNvrSgwRtmpTaskNode *ptNode = NULL;

    pthread_mutex_lock(&rtmp_task_lock);
    do
    {
        if(!achTaskID)
        {
            eRet = NVR_ERR__ERROR;
            SGWPRINTERR("input param null\n");
            break;
        }

        ///<找到rtmp任务节点
        ptNode = g_tNvrSgwRtmpMgr.ptHead;
        if(NULL == ptNode)
        {
            eRet = NVR_ERR__ERROR;
            break;
        }
        while (ptNode)
        {
            if(!strcmp(achTaskID, ptNode->tRtmpTask.achTaskID))
            {
                //找到该任务节点后,借用RTMP任务动作字段,标记该任务节点已经处于停止待删除状态,
                //若该任务发生重试时,节点未被删除,则可通过判断该状态进而不进行重试
                ptNode->tRtmpTask.eAction = SDSCPPROTO_RTMP_TASK_ACTION_WAITDEL;
                break;
            }
            ptNode = ptNode->ptNext;
        }

    }while(0);

    pthread_mutex_unlock(&rtmp_task_lock);

    return eRet;
}


NVRSTATUS NvrSgwFindAndPopRtmpTask(s8 *achTaskID, TSdscpProtoReqStartRtmpTask *ptTask)
{
    NVRSTATUS eRet = NVR_ERR__OK;
    TNvrSgwRtmpTaskNode *ptNode = NULL;

    pthread_mutex_lock(&rtmp_task_lock);
    do
    {
        if(!achTaskID)
        {
            eRet = NVR_ERR__ERROR;
            SGWPRINTERR("input param null\n");
            break;
        }

        ///<找到rtmp任务节点并从rtmp任务链表中删除
        ptNode = g_tNvrSgwRtmpMgr.ptHead;
        if(NULL == ptNode)
        {
            eRet = NVR_ERR__ERROR;
            break;
        }
        ///<头结点
        if(!strcmp(achTaskID, ptNode->tRtmpTask.achTaskID))
        {
            g_tNvrSgwRtmpMgr.ptHead = ptNode->ptNext;
            if(g_tNvrSgwRtmpMgr.ptTail == ptNode)
            {
                g_tNvrSgwRtmpMgr.ptTail = ptNode->ptNext;
            }
            ptNode->ptNext = NULL;
            g_tNvrSgwRtmpMgr.dwNodeNum--;
        }
        else
        {
            TNvrSgwRtmpTaskNode *p = ptNode;
            ptNode = ptNode->ptNext;
            while(ptNode)
            {
                if(!strcmp(achTaskID, ptNode->tRtmpTask.achTaskID))
                {
                    p->ptNext = ptNode->ptNext;
                    if(g_tNvrSgwRtmpMgr.ptTail == ptNode)
                    {
                        g_tNvrSgwRtmpMgr.ptTail = p;
                    }
                    ptNode->ptNext = NULL;
                    g_tNvrSgwRtmpMgr.dwNodeNum--;
                    break;
                }
                p = ptNode;
                ptNode = ptNode->ptNext;
            }
            if(NULL == ptNode)
            {
                SGWPRINTERR("can't find taskid\n");
                eRet = NVR_ERR__ERROR;
                break;
            }
        }

        if (ptNode->bReTry)
        {
            g_tNvrSgwRtmpMgr.dwRetryNodeNum--;
        }

        if (ptTask)
        {
            memcpy(ptTask, &ptNode->tRtmpTask, sizeof(TSdscpProtoReqStartRtmpTask));
        }

        if (ptNode)
        {
            NVRFREE(ptNode);
        }

    }while(0);

    SGWPRINTIMP("list dwRetryNodeNum:%d, dwNodeNum:%d, %s find task %s \n", g_tNvrSgwRtmpMgr.dwRetryNodeNum, g_tNvrSgwRtmpMgr.dwNodeNum,
            NVR_ERR__ERROR == eRet ? "can't" : "", achTaskID);
    pthread_mutex_unlock(&rtmp_task_lock);

    return eRet;
}

NVRSTATUS NvrSgwPushRtmpRetryTask(TSdscpProtoReqStartRtmpTask *ptTask, BOOL bReTry)
{
    NVRSTATUS eRet = NVR_ERR__OK;
    TNvrSgwRtmpTaskNode *ptNode = NULL;

    pthread_mutex_lock(&rtmp_task_lock);
    do
    {
        if(!ptTask)
        {
            SGWPRINTERR("input param null\n");
            eRet = NVR_ERR__ERROR;
            break;
        }

        ptNode = (TNvrSgwRtmpTaskNode *)NVRALLOC(sizeof(TNvrSgwRtmpTaskNode));
        if(!ptNode)
        {
            SGWPRINTERR("malloc failed\n");
            eRet = NVR_ERR__ERROR;
            break;
        }
        memset(ptNode, 0, sizeof(TNvrSgwRtmpTaskNode));
        memcpy(&ptNode->tRtmpTask, ptTask, sizeof(TSdscpProtoReqStartRtmpTask));

        SGWPRINTIMP("push task, taskid:%s,vid:%d-%d, aud:%d-%d\n", ptNode->tRtmpTask.achTaskID,
                ptNode->tRtmpTask.tPullVidID.dwChnId, ptNode->tRtmpTask.tPullVidID.dwXxxID,
                ptNode->tRtmpTask.tPullAudID.dwChnId, ptNode->tRtmpTask.tPullAudID.dwXxxID);

        ptNode->bPull = TRUE;
        ptNode->bReTry = bReTry;
        ///<加入rtmp管理链表
        if(NULL == g_tNvrSgwRtmpMgr.ptHead)
        {
            g_tNvrSgwRtmpMgr.ptHead = ptNode;
        }
        else
        {
            g_tNvrSgwRtmpMgr.ptTail->ptNext = ptNode;
        }
        g_tNvrSgwRtmpMgr.ptTail = ptNode;
        g_tNvrSgwRtmpMgr.dwNodeNum++;
        if (bReTry)
        {
            g_tNvrSgwRtmpMgr.dwRetryNodeNum++;
        }

    }while(0);
    pthread_mutex_unlock(&rtmp_task_lock);

    return eRet;
}

NVRSTATUS NvrSgwModifyRtmpRetryTask(s8 *achTaskID, BOOL bReTry)
{
    NVRSTATUS eRet = NVR_ERR__OK;
    TNvrSgwRtmpTaskNode *ptNode = NULL;

    pthread_mutex_lock(&rtmp_task_lock);
    do
    {
        if(!achTaskID)
        {
            SGWPRINTERR("input param null\n");
            eRet = NVR_ERR__ERROR;
            break;
        }

        ptNode = g_tNvrSgwRtmpMgr.ptHead;
        while (ptNode)
        {
            if(!strcmp(achTaskID, ptNode->tRtmpTask.achTaskID))
            {
                if (ptNode->bReTry != bReTry && bReTry)
                {
                    g_tNvrSgwRtmpMgr.dwRetryNodeNum++;
                }
                ptNode->bReTry = bReTry;

                break;
            }
            ptNode = ptNode->ptNext;
        }

    }while(0);
    pthread_mutex_unlock(&rtmp_task_lock);

    return eRet;
}

NVRSTATUS NvrSgwModifyRtmpSdscpRetryTask(s8 *achTaskID, u64 dwReTryTime, u32 dwMsg, u32 dwMsgId)
{
    NVRSTATUS eRet = NVR_ERR__OK;
    TNvrSgwRtmpTaskNode *ptNode = NULL;
    BOOL32 bIsDelTask = FALSE;
    s8 chTaskID[SDSCPPRPTO_RTMP_TASK_ID_BUF_LEN];

    pthread_mutex_lock(&rtmp_task_lock);
    do
    {
        if(!achTaskID)
        {
            SGWPRINTERR("input param null\n");
            eRet = NVR_ERR__ERROR;
            break;
        }

        ptNode = g_tNvrSgwRtmpMgr.ptHead;
        if(!ptNode)
        {
            SGWPRINTERR("insert g_tNvrSgwRtmpMgr.ptHead null\n");
        }
        while (ptNode)
        {
            SGWPRINTFREQ("insert, id:%s, num:%d, dwMsg:%u, msgid:%u\n", ptNode->tRtmpTask.achTaskID, g_tNvrSgwRtmpMgr.dwNodeNum, dwMsg, dwMsgId);
            if(!strcmp(achTaskID, ptNode->tRtmpTask.achTaskID))
            {
                if(ptNode->tRtmpTask.eAction == SDSCPPROTO_RTMP_TASK_ACTION_WAITDEL)
                {
                    //收到边界发送的stop指令后默认同步编码器成功(边界同时会给编码器发stop), 上报任务停止成功状态, 删除任务节点
                    bIsDelTask = TRUE;
                    memcpy(chTaskID, ptNode->tRtmpTask.achTaskID, sizeof(chTaskID));
                    SdscpAppRtmpTaskStateReport(ptNode->tRtmpTask.achTaskID, ptNode->tRtmpTask.achRtmpUrl, SDSCPPROTO_RTMP_TASK_STATE_FINISHED, SDSCPPROTO_RTMP_TASK_ERR_REASON_COUNT, NULL);
                    SGWPRINTDBG("task: %s  stop sucess, report to sdscp\n", ptNode->tRtmpTask.achTaskID);
                }
                if(ptNode->dwRepRetryCnt == 2 && (ptNode->dwReReportMsg != NVR_SGW_STREAM_UPDATE || !ptNode->bRepStartedFlg))
                {
                    //该次同步重试了3次,每次耗时3s,此时上报边界任务start/stop超时,同时取消重试、还原解码器资源
                    //stop超时不上报
                    if(ptNode->dwReReportMsg != NVR_SGW_STREAM_STOP)
                    {
                        SdscpAppRtmpTaskStateReport(ptNode->tRtmpTask.achTaskID, ptNode->tRtmpTask.achRtmpUrl, SDSCPPROTO_RTMP_TASK_STATE_ERROR, SDSCPPROTO_RTMP_TASK_ERR_REASON_TMOUT, NULL);
                    }
                    SGWPRINTERR("task:%s  %s outtime, report to sdscp\n", ptNode->tRtmpTask.achTaskID, ptNode->dwReReportMsg == NVR_SGW_STREAM_STOP ? "stop" : "start");
                    ptNode->dwMsgId = 0;
                    ptNode->dwReReportMsg = 0;
                    ptNode->dwReReportTime = 0;
                    ptNode->dwRepRetryCnt = 0;
                    break;
                }
                SGWPRINTDBG("insert info dwMsg:%u[%u], msgid:%u, %llu, dwRepRetryCnt:%u\n", ptNode->dwReReportMsg, dwMsg, dwMsgId, dwReTryTime, ptNode->dwRepRetryCnt);
                ptNode->dwMsgId = dwMsgId;
                if (ptNode->dwReReportMsg != dwMsg)
                {
                    ptNode->dwReReportMsg = dwMsg;
                    ptNode->dwReReportTime = dwReTryTime;
                    ptNode->dwRepRetryCnt = 0;
                }
                else
                {
                    ptNode->dwReReportTime = ptNode->dwReReportTime > 0 ? ptNode->dwReReportTime : dwReTryTime;
                    ptNode->dwRepRetryCnt++;
                }
                break;
            }
            ptNode = ptNode->ptNext;
        }

    }while(0);
    pthread_mutex_unlock(&rtmp_task_lock);
    if(bIsDelTask == TRUE)
    {
        NvrSgwFindAndPopRtmpTask(chTaskID, NULL);
    }

    return eRet;
}

NVRSTATUS NvrSgwModifyRtmpSdscpRetryTaskClear(u32 dwMsgId)
{
    NVRSTATUS eRet = NVR_ERR__ERROR;
    TNvrSgwRtmpTaskNode *ptNode = NULL;
    u64 timeIn = 0;

    pthread_mutex_lock(&rtmp_task_lock);
    do
    {
        timeIn = NvrSysGetCurTimeMSec();
        ptNode = g_tNvrSgwRtmpMgr.ptHead;
        if(!ptNode)
        {
            SGWPRINTIMP("clear g_tNvrSgwRtmpMgr.ptHead null\n");
        }
        while (ptNode)
        {
            SGWPRINTFREQ("clear, id:%s, num:%d, msgid:%u\n", ptNode->tRtmpTask.achTaskID, g_tNvrSgwRtmpMgr.dwNodeNum, dwMsgId);
            if(ptNode->dwMsgId == dwMsgId)
            {
                SGWMEMNOTICE("task: %s recv and clear info dwReReportMsg:%d, msgid:%u, curtime:%llu, reptime:%llu, use time:%llu\n",\
                    ptNode->tRtmpTask.achTaskID, ptNode->dwReReportMsg, ptNode->dwMsgId, timeIn, ptNode->dwReReportTime, (timeIn - ptNode->dwReReportTime));
                if ((s32)(timeIn - ptNode->dwReReportTime) < g_SdscpRcvTimeOut)
                {
                    eRet = NVR_ERR__OK;
                }
                //start、stop同步完成后上报connected和finished状态给边界
                if(!ptNode->bRepStartedFlg && ptNode->dwReReportMsg != NVR_SGW_STREAM_STOP)
                {
                    ptNode->bRepStartedFlg = TRUE;
                    SdscpAppRtmpTaskStateReport(ptNode->tRtmpTask.achTaskID, ptNode->tRtmpTask.achRtmpUrl, SDSCPPROTO_RTMP_TASK_STATE_CONNECTED, SDSCPPROTO_RTMP_TASK_ERR_REASON_COUNT, NULL);
                    SGWPRINTDBG("task: %s  start sucess, report to sdscp\n", ptNode->tRtmpTask.achTaskID);
                }
                else if(ptNode->dwReReportMsg == NVR_SGW_STREAM_STOP)
                {
                    ptNode->bRepStartedFlg = FALSE;  ///<任务发生重试时,会上报stop完成状态给上层,要将此标志置0,以便于start后上报start成功状态。若上层无需感知业务自己的任务重试,则可将此分支删除
                    SdscpAppRtmpTaskStateReport(ptNode->tRtmpTask.achTaskID, ptNode->tRtmpTask.achRtmpUrl, SDSCPPROTO_RTMP_TASK_STATE_FINISHED, SDSCPPROTO_RTMP_TASK_ERR_REASON_COUNT, NULL);
                    SGWPRINTDBG("task: %s  stop sucess, report to sdscp\n", ptNode->tRtmpTask.achTaskID);
                }
                ptNode->dwReReportMsg = 0;
                ptNode->dwReReportTime = 0;
                ptNode->dwMsgId = 0;
                ptNode->dwRepRetryCnt = 0;
                //break;
            }
            ptNode = ptNode->ptNext;
        }

    }while(0);
    pthread_mutex_unlock(&rtmp_task_lock);

    return eRet;
}

NVRSTATUS NvrSgwGetRtmpSdscpRetryTask(TNvrSgwRtmpTaskNode *ptTaskNode)
{
    NVRSTATUS eRet = NVR_ERR__ERROR;
    TNvrSgwRtmpTaskNode *ptNode = NULL;
    u64 dwTime = NvrSysGetCurTimeMSec();

    pthread_mutex_lock(&rtmp_task_lock);
    do
    {
        if(!ptTaskNode)
        {
            SGWPRINTERR("input param null\n");
            break;
        }
        ptNode = g_tNvrSgwRtmpMgr.ptHead;
        while (ptNode)
        {
            if(((dwTime - ptNode->dwReReportTime) > g_SdscpRcvTimeOut) && ptNode->dwReReportMsg && (0 != ptNode->dwReReportTime))
            {
                SGWMEMNOTICE("$$$$$$$$sdscp retry find task, taskid:%s,vid:%d-%d, aud:%d-%d msg:%u time:%llu, take time: %llu\n", ptNode->tRtmpTask.achTaskID,
                        ptNode->tRtmpTask.tPullVidID.dwChnId, ptNode->tRtmpTask.tPullVidID.dwXxxID,
                        ptNode->tRtmpTask.tPullAudID.dwChnId, ptNode->tRtmpTask.tPullAudID.dwXxxID,
                        ptNode->dwReReportMsg, ptNode->dwReReportTime, dwTime - ptNode->dwReReportTime);

                memcpy(ptTaskNode, ptNode, sizeof(TNvrSgwRtmpTaskNode));

                //ptNode->dwReReportMsg = 0;
                ptNode->dwReReportTime = 0;
                //ptNode->dwMsgId = 0;

                eRet = NVR_ERR__OK;
                break;
            }

            ptNode = ptNode->ptNext;
        }
    }
    while (0);
    pthread_mutex_unlock(&rtmp_task_lock);

    return eRet;
}

NVRSTATUS NvrSgwGetRtmpRetryTask(TSdscpProtoReqStartRtmpTask *ptTask, s32 *pnNum)
{
    NVRSTATUS eRet = NVR_ERR__ERROR;
    TNvrSgwRtmpTaskNode *ptNode = NULL;

    pthread_mutex_lock(&rtmp_task_lock);
    do
    {
        if(!ptTask || !pnNum)
        {
            SGWPRINTERR("input param null\n");
            break;
        }
        ptNode = g_tNvrSgwRtmpMgr.ptHead;
        while (ptNode)
        {
            if(ptNode->bReTry)
            {
                ptNode->bReTry = FALSE;
                g_tNvrSgwRtmpMgr.dwRetryNodeNum--;
                break;
            }

            ptNode = ptNode->ptNext;
        }

        //找到重试的任务节点且该节点不处于停止待删除状态时才返回成功
        if (ptNode && SDSCPPROTO_RTMP_TASK_ACTION_WAITDEL != ptNode->tRtmpTask.eAction)
        {
            SGWPRINTIMP("find task, taskid:%s,vid:%d-%d, aud:%d-%d\n", ptNode->tRtmpTask.achTaskID,
                    ptNode->tRtmpTask.tPullVidID.dwChnId, ptNode->tRtmpTask.tPullVidID.dwXxxID,
                    ptNode->tRtmpTask.tPullAudID.dwChnId, ptNode->tRtmpTask.tPullAudID.dwXxxID);

            memcpy(ptTask, &ptNode->tRtmpTask, sizeof(TSdscpProtoReqStartRtmpTask));
            ptNode->bRepStartedFlg = 0;  ///<重试时会停止解码重新启动,此时同步完成仍需上报任务启动状态(防止任务未stop时边界下发相同任务的start)

            eRet = NVR_ERR__OK;
        }

        *pnNum = g_tNvrSgwRtmpMgr.dwRetryNodeNum;
    }
    while (0);
    pthread_mutex_unlock(&rtmp_task_lock);

    SGWPRINTIMP("all list taskid:%d, retry:%d\n", g_tNvrSgwRtmpMgr.dwNodeNum, g_tNvrSgwRtmpMgr.dwRetryNodeNum);

    return eRet;
}

NVRSTATUS NvrSgwPopAllRtmpTask()
{
    NVRSTATUS eRet = NVR_ERR__OK;
    TNvrSgwRtmpTaskNode *ptNode = NULL;
    TNvrSgwRtmpTaskNode *ptTmpNode = NULL;

    pthread_mutex_lock(&rtmp_task_lock);
    do
    {
        ptNode = g_tNvrSgwRtmpMgr.ptHead;
        while (ptNode)
        {
            SGWPRINTDBG("over:%d, %d:%d\n", ptNode->tRtmpTask.achTaskID,
                    g_tNvrSgwRtmpMgr.dwNodeNum, g_tNvrSgwRtmpMgr.dwRetryNodeNum);
            g_tNvrSgwRtmpMgr.ptHead = ptNode->ptNext;
            ptNode->ptNext = NULL;
            g_tNvrSgwRtmpMgr.dwNodeNum--;
            if (ptNode->bReTry)
            {
                g_tNvrSgwRtmpMgr.dwRetryNodeNum--;
            }
            ptTmpNode = ptNode;
            ptNode = g_tNvrSgwRtmpMgr.ptHead;

            NVRFREE(ptNode);
        }
    }
    while (0);
    g_tNvrSgwRtmpMgr.ptTail = NULL;
    pthread_mutex_unlock(&rtmp_task_lock);
    MAKE_COMPILER_HAPPY(ptTmpNode);

    return eRet;
}

void *NvrSgwSdscpRetrytTask(void)
{
    TNvrSgwRtmpTaskNode tTaskNode;

    while (1)
    {
        if ( NVR_ERR__OK == NvrSgwGetRtmpSdscpRetryTask(&tTaskNode))
        {
            s32 nDecChnId = 0, nDecId = 0, dwMsgType = 0;
            BOOL bAud = FALSE;

            bAud = (FALSE == tTaskNode.tRtmpTask.bPullVid) ? TRUE : FALSE;
            if (bAud)
            {
                nDecChnId = tTaskNode.tRtmpTask.tPullAudID.dwChnId;
                nDecId = tTaskNode.tRtmpTask.tPullAudID.dwXxxID;
            }
            else
            {
                nDecChnId = tTaskNode.tRtmpTask.tPullVidID.dwChnId;
                nDecId = tTaskNode.tRtmpTask.tPullVidID.dwXxxID;
            }

            dwMsgType = tTaskNode.dwReReportMsg;
            ///< send msg
            __NvrSgwStreamStatSendMsg(nDecChnId, nDecId, bAud, dwMsgType);
        }
        usleep(50 * 1000);
    }
    OsApi_TaskExit();
    return NULL;
}

void *NvrSgwDealOptTask(void)
{
    NVRSTATUS eRet = NVR_ERR__OK;
    TNvrDoubleListPopAttr tPopAttr;
    TSdscpProtoReqSetLayOutEncParam tEncParam;
    s8 achBuf[NVR_MAX_STR512_LEN] = { 0 };
    s8 achTskId[NVR_MAX_STR64_LEN] = {0};
    u32 dwType = 0;
    s32 nRet = 0;
    u64 timeIn = 0;
    u16 dwLayOutCmdNum = 0;
    u32 dwMsgId = 0;

#ifndef WIN32
    prctl(PR_SET_NAME, "nvrSgwDeal", 0, 0, 0);
#endif

    mzero(tPopAttr);
    tPopAttr.byBlockMode = NVR_QUEUE_POP_NOBLOCK;
    tPopAttr.pchDataBuf = achBuf;
    tPopAttr.dwDataLen = NVR_MAX_STR512_LEN;

    while(TRUE)
    {
        SGWPRINTFREQ("begin to pop\n");
        while(TRUE)
        {
            eRet = NvrQueuePop(g_ptNvrSgwQueue, &tPopAttr);
            if(NVR_ERR__ERROR == eRet)
            {
                break;
            }
            else if(NVR_SGW_STREAM_OVERFLOW == tPopAttr.dwType)
            {
                SGWMEMNOTICE("=== overflow === :%s",achBuf);
                NvrVtduSndSdscpResourceOverflow(achBuf, SDSCPPROTO_NTF_TYPE_OVERFLOW);
            }
            else if(NVR_SGW_STREAM_CHNOVERFLOW == tPopAttr.dwType)
            {
                SGWMEMNOTICE("=== chnoverflow === :%s",achBuf);
                NvrVtduSndSdscpResourceOverflow(achBuf, SDSCPPROTO_NTF_TYPE_CHNOVERFLOW);
            }
            else if(NVR_SGW_STREAM_START <= tPopAttr.dwType || tPopAttr.dwType <= NVR_SGW_STREAM_UPDATE)
            {
                s32 nDecChnId = 0, nDecId = 0, nVid = 0;

                sscanf(achBuf, "vid:%d, chn:%d, subChn:%d", &nVid, &nDecChnId, &nDecId);

                mzero(achTskId);
                dwType = tPopAttr.dwType;

                tEncParam.dwChnNum = 1;
                pthread_mutex_lock(&mediactrl_set_lock);
                if (nVid)
                {
                    tEncParam.aParamArray[dwLayOutCmdNum].eType = dwType - 1;
                    tEncParam.aParamArray[dwLayOutCmdNum].byChnVidId = nDecChnId;
                    tEncParam.aParamArray[dwLayOutCmdNum].bysubChnVidId = nDecId;
                    if (-1 != g_dwAvSyncAudChn[nDecChnId][nDecId])
                    {
                        tEncParam.aParamArray[dwLayOutCmdNum].byChnAudId = (g_dwAvSyncAudChn[nDecChnId][nDecId] >> 8) & 0xff;
                        tEncParam.aParamArray[dwLayOutCmdNum].bysubChnAudId = g_dwAvSyncAudChn[nDecChnId][nDecId] & 0xff;
                    }
                    tEncParam.aParamArray[dwLayOutCmdNum].byslotId = g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byRealChn / NVR_SGW_MAX_CHIP_CHN_NUM;
                    tEncParam.aParamArray[dwLayOutCmdNum].byCanvas = g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byRealChn;
                    tEncParam.aParamArray[dwLayOutCmdNum].tCanvasloc.dwX = g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wVideoX;
                    tEncParam.aParamArray[dwLayOutCmdNum].tCanvasloc.dwY = g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wVideoY;
                    tEncParam.aParamArray[dwLayOutCmdNum].tCanvasarea.dwX = g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wVideoWidth;
                    tEncParam.aParamArray[dwLayOutCmdNum].tCanvasarea.dwY = g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wVideoHeight;
                    tEncParam.aParamArray[dwLayOutCmdNum].dwFPS =  g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.wFranmeRate;
                    tEncParam.aParamArray[dwLayOutCmdNum].dwRate = g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.dwBitRate;
                    tEncParam.aParamArray[dwLayOutCmdNum].dwEncType = g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].vaInfo.tVideoParam.dwEncType;
                    snprintf(achTskId, NVR_MAX_STR64_LEN, "%s", g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].achTaskID);
                }
                else
                {
                    tEncParam.aParamArray[dwLayOutCmdNum].byChnAudId = nDecChnId;
                    tEncParam.aParamArray[dwLayOutCmdNum].bysubChnAudId = nDecId;
                    snprintf(achTskId, NVR_MAX_STR64_LEN, "%s", g_tNvrSgwDecMgr.aatAudDecInfo[nDecChnId][nDecId].achTaskID);
                }
                snprintf((s8*)tEncParam.aParamArray[dwLayOutCmdNum].achTaskID, NVR_MAX_STR64_LEN, "%s", achTskId);
                pthread_mutex_unlock(&mediactrl_set_lock);

                SGWPRINTIMP("[%d]===%s=== %s info:%u %u %u %u %u %u %u\n", dwLayOutCmdNum,
                        NVR_SGW_STREAM_START == dwType ? "start" : (NVR_SGW_STREAM_STOP == dwType ? "stop " : "update"), achTskId,
                        tEncParam.aParamArray[dwLayOutCmdNum].tCanvasloc.dwX, tEncParam.aParamArray[dwLayOutCmdNum].tCanvasloc.dwY,
                        tEncParam.aParamArray[dwLayOutCmdNum].tCanvasarea.dwX, tEncParam.aParamArray[dwLayOutCmdNum].tCanvasarea.dwY,
                        tEncParam.aParamArray[dwLayOutCmdNum].dwFPS, tEncParam.aParamArray[dwLayOutCmdNum].dwRate, tEncParam.aParamArray[dwLayOutCmdNum].dwEncType);
                SGWMEMNOTICE("[%d]%s: %s dec, v:%d-%d a:%d-%d info:(x%u-y%u, %ux%u) FPS:%u Rate:%u EncType:%u slotId:%d Canvas:%d\n", dwLayOutCmdNum,
                achTskId, NVR_SGW_STREAM_START == dwType ? "start" : (NVR_SGW_STREAM_STOP == dwType ? "stop " : "update"),
                tEncParam.aParamArray[dwLayOutCmdNum].byChnVidId, tEncParam.aParamArray[dwLayOutCmdNum].bysubChnVidId,
                tEncParam.aParamArray[dwLayOutCmdNum].byChnAudId, tEncParam.aParamArray[dwLayOutCmdNum].bysubChnAudId,
                tEncParam.aParamArray[dwLayOutCmdNum].tCanvasloc.dwX, tEncParam.aParamArray[dwLayOutCmdNum].tCanvasloc.dwY,
                tEncParam.aParamArray[dwLayOutCmdNum].tCanvasarea.dwX, tEncParam.aParamArray[dwLayOutCmdNum].tCanvasarea.dwY,
                tEncParam.aParamArray[dwLayOutCmdNum].dwFPS, tEncParam.aParamArray[dwLayOutCmdNum].dwRate, tEncParam.aParamArray[dwLayOutCmdNum].dwEncType,
                tEncParam.aParamArray[dwLayOutCmdNum].byslotId, tEncParam.aParamArray[dwLayOutCmdNum].byCanvas);
                dwLayOutCmdNum++;
                if(dwLayOutCmdNum == SDSCPPROTO_VID_ENC_CAP_MAX_NUM)
                    break;
            }
        }
        if(dwLayOutCmdNum)
        {
            tEncParam.dwChnNum = dwLayOutCmdNum;
            dwLayOutCmdNum = 0;
            if(g_dwSdscpTestClientFd != -1)
            {
                nRet = SgwDecSdscpTestSend(SDSCPPROTO_CMD_TYPE_SET_LAYOUT_ENC_PARAM, (void *)&tEncParam, sizeof(TSdscpProtoReqSetLayOutEncParam), &dwMsgId);
            }
            else
            {
                NvrSgwSndSdscpEncInfo((void *)&tEncParam, (void *)&dwMsgId);
            }
            timeIn = NvrSysGetCurTimeMSec();
            for(u8 i = 0; i < tEncParam.dwChnNum; i++)
                NvrSgwModifyRtmpSdscpRetryTask((s8*)tEncParam.aParamArray[i].achTaskID, timeIn, tEncParam.aParamArray[i].eType + 1, dwMsgId);
            //nRet = OsApi_SemTakeByTime(g_hSdscpNotifySem, NVR_SGW_SDSCP_RCV_TIMEOUT);
            SGWMEMNOTICE("Layoutcmd number:%u, Msgid:%u, reptime:%llu\n",
                    tEncParam.dwChnNum, dwMsgId, timeIn);
            mzero(tEncParam);
        }
        usleep(100 * 1000);

    }

    OsApi_TaskExit();
    MAKE_COMPILER_HAPPY(nRet);

    return NULL;
}

s32 NvrSgwSdscpStyleToDevStyle(s32 nStyle)
{
    s32 nDevStyle = 1;
    if (SDSCPPROTO_WIN_LAYOUT_1 == nStyle)
    {
        nDevStyle = 1;
    }
    else if (SDSCPPROTO_WIN_LAYOUT_2 == nStyle)
    {
        nDevStyle = 1;
    }
    else if (SDSCPPROTO_WIN_LAYOUT_4 == nStyle)
    {
        nDevStyle = 2;
    }
    else if (SDSCPPROTO_WIN_LAYOUT_9 == nStyle)
    {
        nDevStyle = 3;
    }
    else if (SDSCPPROTO_WIN_LAYOUT_16 == nStyle)
    {
        nDevStyle = 4;
    }
    else if (SDSCPPROTO_WIN_LAYOUT_25 == nStyle)
    {
        nDevStyle = 5;
    }
    else if (SDSCPPROTO_WIN_LAYOUT_36 == nStyle)
    {
        nDevStyle = 6;
    }

    return nDevStyle;
}

s32 NvrSgwDevStyleToSdscpStyle(s32 nStyle)
{
    s32 nSdscpStyle = 1;
    if (1 == nStyle)
    {
        nSdscpStyle = SDSCPPROTO_WIN_LAYOUT_1;
    }
    else if (2 == nStyle)
    {
        nSdscpStyle = SDSCPPROTO_WIN_LAYOUT_4;
    }
    else if (3 == nStyle)
    {
        nSdscpStyle = SDSCPPROTO_WIN_LAYOUT_9;
    }
    else if (4 == nStyle)
    {
        nSdscpStyle = SDSCPPROTO_WIN_LAYOUT_16;
    }
    else if (5 == nStyle)
    {
        nSdscpStyle = SDSCPPROTO_WIN_LAYOUT_25;
    }
    else if (6 == nStyle)
    {
        nSdscpStyle = SDSCPPROTO_WIN_LAYOUT_36;
    }

    return nSdscpStyle;
}

s32 NvrsgwTaskRetryCheckTimerCB( HTIMERHANDLE dwTimerId, void* param )
{
    NVRSTATUS eRet = NVR_ERR__ERROR;
    TSdscpProtoReqStartRtmpTask tTask;
    s32 nNum = 0;

    mzero(tTask);

    eRet = NvrSgwGetRtmpRetryTask(&tTask,  &nNum);
    if (NVR_ERR__OK == eRet)
    {
        u16 wChnId = 0, wChnXXId = 0;

        if (tTask.bPullVid)
        {
            wChnXXId = tTask.tPullVidID.dwXxxID;
            wChnId = tTask.tPullVidID.dwChnId;
        }
        else
        {
            wChnXXId = tTask.tPullAudID.dwXxxID + NVR_SGW_MAX_CHN_VID_NUM;
            wChnId = tTask.tPullAudID.dwChnId;
        }

        SGWPRINTIMP("retry task vid:%d %d, aud:%d %d %d %d\n", tTask.tPullVidID.dwChnId, tTask.tPullVidID.dwXxxID,
                tTask.tPullAudID.dwChnId, tTask.tPullAudID.dwXxxID, wChnId, wChnXXId);
        SdscpAppCoreStartRtmpTask(&tTask);
    }

    if (nNum > 0)
    {
        SGWPRINTIMP("task wait next\n");
        OsApi_TimerSet(g_hTaskRetryCheckTimer, 100, NvrsgwTaskRetryCheckTimerCB, NULL);
    }
    else
    {
        SGWPRINTIMP("task stop\n");
        OsApi_TimerStop(g_hTaskRetryCheckTimer);
    }
    return 0;
}

NVRSTATUS NvrSgwSdscpDecOptCB(u32 dwEventType, void* pEventData, u32 dwEventDataLen, void* pvContext)
{
    NVRSTATUS eRet = NVR_ERR__ERROR;
    switch (dwEventType)
    {
        case SDSCPPROTO_CMD_TYPE_START_RTMP_TASK:   ///<pvContext 传递vtdu创建号的msinid
        {
            u16 wChnId = 0;
            u16 wEncId = 0;
            u16 wChnXXId = 0;
            TSdscpProtoReqStartRtmpTask *pStartTask = NULL;
            u32 dwMsInId = 0;

            if (dwEventDataLen != sizeof(TSdscpProtoReqStartRtmpTask) || !pEventData)
            {
                SGWPRINTERR("SDSCPPROTO_CMD_TYPE_START_RTMP_TASK data failed! %u--%ld\n", dwEventDataLen, sizeof(TSdscpProtoReqStartRtmpTask));
                break;
            }

            pStartTask = (TSdscpProtoReqStartRtmpTask *)pEventData;
            dwMsInId = ((long long)pvContext) & 0xffffffff;
            if (0xFFFFFFFF ==  dwMsInId ||  0xFFFFFFFE ==  dwMsInId)
            {
                ///< sdscp拉流时由于协议通道定义和vtdu模块的通道的定义有差别，需要先进行通道转换，把4*16的通道转换成64 * 1
                if (pStartTask->bPullVid)
                {
                    wChnId = pStartTask->tPullVidID.dwChnId;
                    wChnXXId = pStartTask->tPullVidID.dwXxxID;
                }
                else
                {
                    wChnId = pStartTask->tPullAudID.dwChnId;
                    wChnXXId = pStartTask->tPullAudID.dwXxxID + NVR_SGW_MAX_CHN_VID_NUM;
                }

                if (0xFFFFFFFF ==  dwMsInId)
                {
                    ///< 第一次进来只标记，方便第二次进来把任务加入链表
                    g_atNvrSgwRtmpTaskAddFlag[wChnId][wChnXXId] = TRUE;
                }

                wChnId = pStartTask->tPullVidID.dwChnId;
                wEncId = pStartTask->tPullVidID.dwXxxID;
                pStartTask->tPullVidID.dwChnId = wChnId * NVR_SGW_MAX_CHN_VID_NUM + wEncId;;
                pStartTask->tPullVidID.dwXxxID = 0;
                SGWPRINTFREQ("sdscp pull vid change %d:%d ->%d:%d  dwMsInId:%x\n",
                        wChnId, wEncId, pStartTask->tPullVidID.dwChnId, pStartTask->tPullVidID.dwXxxID, dwMsInId);
                wChnId = pStartTask->tPullAudID.dwChnId;
                wEncId = pStartTask->tPullAudID.dwXxxID;
                pStartTask->tPullAudID.dwChnId = wChnId * NVR_SGW_MAX_CHN_VID_NUM + wEncId;;
                pStartTask->tPullAudID.dwXxxID = 0;
                SGWPRINTFREQ("sdscp pull aud change %d:%d ->%d:%d\n",
                        wChnId, wEncId, pStartTask->tPullAudID.dwChnId, pStartTask->tPullAudID.dwXxxID);

                break;
            }

            wChnId = pStartTask->tPullVidID.dwChnId;
            wEncId = pStartTask->tPullVidID.dwXxxID;
            pStartTask->tPullVidID.dwChnId = wChnId / NVR_SGW_MAX_CHN_VID_NUM;
            pStartTask->tPullVidID.dwXxxID = wChnId % NVR_SGW_MAX_CHN_VID_NUM;
            wChnId = pStartTask->tPullAudID.dwChnId;
            wEncId = pStartTask->tPullAudID.dwXxxID;
            pStartTask->tPullAudID.dwChnId = wChnId / NVR_SGW_MAX_CHN_VID_NUM;
            pStartTask->tPullAudID.dwXxxID = wChnId % NVR_SGW_MAX_CHN_VID_NUM;

            if (pStartTask->bPullVid)
            {
                wChnId = pStartTask->tPullVidID.dwChnId;
                wChnXXId = pStartTask->tPullVidID.dwXxxID;
            }
            else
            {
                wChnId = pStartTask->tPullAudID.dwChnId;
                wChnXXId = pStartTask->tPullAudID.dwXxxID + NVR_SGW_MAX_CHN_VID_NUM;
            }

            SGWMEMNOTICE("%s: rtmp task start(%s), vid:%d-%d, aud:%d-%d, url:%s, msinid:%3u bvid:%d baud:%d",
                    pStartTask->achTaskID, g_atNvrSgwRtmpTaskAddFlag[wChnId][wChnXXId] ? "new":"retry",
                    pStartTask->tPullVidID.dwChnId, pStartTask->tPullVidID.dwXxxID,
                    pStartTask->tPullAudID.dwChnId,  pStartTask->tPullAudID.dwXxxID,
                    pStartTask->achRtmpUrl, dwMsInId, pStartTask->bPullVid, pStartTask->bPullAud);

            if (g_atNvrSgwRtmpTaskAddFlag[wChnId][wChnXXId])
            {
                NvrSgwPushRtmpRetryTask(pStartTask, FALSE);
                g_atNvrSgwRtmpTaskAddFlag[wChnId][wChnXXId] = FALSE;
            }

            pthread_mutex_lock(&rtmp_task_lock);
            do
            {
                TNvrSgwStreamInfo tStreamDecInfo;
                mzero(tStreamDecInfo);

                strncpy(tStreamDecInfo.achTaskID,  pStartTask->achTaskID, SDSCPPRPTO_RTMP_TASK_ID_BUF_LEN);

                if (pStartTask->bPullVid)
                {
                    tStreamDecInfo.bHasVideo = TRUE;
                    tStreamDecInfo.tVidInfo.byChnId = pStartTask->tPullVidID.dwChnId;
                    tStreamDecInfo.tVidInfo.byChnDecId = pStartTask->tPullVidID.dwXxxID;
                    tStreamDecInfo.tVidInfo.dwMsInId = dwMsInId;
                }

                if (pStartTask->bPullAud)
                {
                    tStreamDecInfo.bHasAudio = TRUE;
                    tStreamDecInfo.tAudInfo.byChnId = pStartTask->tPullAudID.dwChnId;
                    tStreamDecInfo.tAudInfo.byChnDecId = pStartTask->tPullAudID.dwXxxID;
                    tStreamDecInfo.tAudInfo.dwMsInId = dwMsInId;
                }

                eRet = NvrSgwDecStreamStart(&tStreamDecInfo);
                SGWMEMNOTICE("%s: MsIn Prepare %s!", tStreamDecInfo.achTaskID, eRet == NVR_ERR__OK ? "OK" :"Err");
                if( eRet != NVR_ERR__OK)
                {
                    SdscpAppRtmpTaskStateReport(tStreamDecInfo.achTaskID, pStartTask->achRtmpUrl, SDSCPPROTO_RTMP_TASK_STATE_ERROR, SDSCPPROTO_RTMP_TASK_ERR_REASON_CRTDEC, NULL);
                }
            } while(0);
            pthread_mutex_unlock(&rtmp_task_lock);

            break;
        }

        case SDSCPPROTO_CMD_TYPE_STOP_RTMP_TASK:    ///
        {
            u16 wChnId = 0;
            u16 wChnXXId = 0;
            TSdscpProtoReqStartRtmpTask *pStartTask = NULL;

            if (dwEventDataLen != sizeof(TSdscpProtoReqStartRtmpTask) || !pEventData)
            {
                SGWPRINTERR("SDSCPPROTO_CMD_TYPE_STOP_RTMP_TASK data failed! %u--%ld\n", dwEventDataLen, sizeof(TSdscpProtoReqStartRtmpTask));
                break;
            }

            pStartTask = (TSdscpProtoReqStartRtmpTask *)pEventData;

            ///< sdscp拉流时进行通道转换，停止时通道要还原
            wChnId = pStartTask->tPullVidID.dwChnId;
            pStartTask->tPullVidID.dwChnId = wChnId / NVR_SGW_MAX_CHN_VID_NUM;
            pStartTask->tPullVidID.dwXxxID = wChnId % NVR_SGW_MAX_CHN_VID_NUM;
            wChnId = pStartTask->tPullAudID.dwChnId;
            pStartTask->tPullAudID.dwChnId = wChnId / NVR_SGW_MAX_CHN_VID_NUM;
            pStartTask->tPullAudID.dwXxxID = wChnId % NVR_SGW_MAX_CHN_VID_NUM;

            if (pStartTask->bPullVid)
            {
                wChnId = pStartTask->tPullVidID.dwChnId;
                wChnXXId = pStartTask->tPullVidID.dwXxxID;
            }
            else
            {
                wChnId = pStartTask->tPullAudID.dwChnId;;
                wChnXXId = pStartTask->tPullAudID.dwXxxID + NVR_SGW_MAX_CHN_VID_NUM;
            }

            SGWMEMNOTICE("%s: rtmp task stop, vid:%d-%d, aud:%d-%d, url:%s bvid:%d baud:%d",
                    pStartTask->achTaskID, pStartTask->tPullVidID.dwChnId, pStartTask->tPullVidID.dwXxxID,
                    pStartTask->tPullAudID.dwChnId,  pStartTask->tPullAudID.dwXxxID, pStartTask->achRtmpUrl,
                    pStartTask->bPullVid, pStartTask->bPullAud);

            g_atNvrSgwRtmpTaskAddFlag[wChnId][wChnXXId] = FALSE;
            //NvrSgwFindAndPopRtmpTask(pStartTask->achTaskID, NULL);
            NvrSgwFindAndMarkStopRtmpTask(pStartTask->achTaskID);  //将该任务节点标记为停止待删除状态,收到编码器回复的stop成功后再删除

            pthread_mutex_lock(&rtmp_task_lock);
            TNvrSgwStreamInfo tStreamDecInfo;
            mzero(tStreamDecInfo);

            if (pStartTask->bPullVid)
            {
                tStreamDecInfo.bHasVideo = TRUE;
                tStreamDecInfo.tVidInfo.byChnId = pStartTask->tPullVidID.dwChnId;
                tStreamDecInfo.tVidInfo.byChnDecId = pStartTask->tPullVidID.dwXxxID;
            }

            if (pStartTask->bPullAud)
            {
                tStreamDecInfo.bHasAudio = TRUE;
                tStreamDecInfo.tAudInfo.byChnId = pStartTask->tPullAudID.dwChnId;
                tStreamDecInfo.tAudInfo.byChnDecId = pStartTask->tPullAudID.dwXxxID;
            }
            NvrSgwDecStreamStop(&tStreamDecInfo);

            pthread_mutex_unlock(&rtmp_task_lock);

            eRet = NVR_ERR__OK;

            MAKE_COMPILER_HAPPY(pStartTask);
            break;
        }

        case SDSCPPROTO_CMD_TYPE_GET_COMP_STYLE_PARAM:
        {
            TSdscpProtoRespGetCompStyleParam *pGetCompStyleParam = NULL;
            s32 i = 0;
            TNvrSgwCfg tNvrSgwCfg;

            if (dwEventDataLen != sizeof(TSdscpProtoRespGetCompStyleParam))
            {
                SGWPRINTERR("SDSCPPROTO_CMD_TYPE_GET_COMP_STYLE_PARAM data failed! %u--%ld\n", dwEventDataLen, sizeof(TSdscpProtoRespGetCompStyleParam));
                break;
            }

            pGetCompStyleParam = (TSdscpProtoRespGetCompStyleParam *)pEventData;

            NvrSgwCfgGetParam(&tNvrSgwCfg);

            pGetCompStyleParam->dwChnNum = NVR_SGW_MAX_CHN_NUM;
            for (i = 0; i < NVR_SGW_MAX_CHN_NUM; i++)
            {
                pGetCompStyleParam->aTypeArray[i] = NvrSgwDevStyleToSdscpStyle(tNvrSgwCfg.adwChnStyle[i]);
                SGWPRINTDBG("SDSCPPROTO_CMD_TYPE_GET_COMP_STYLE_PARAM t:%u aTypeArray[%d] %d \n",
                        pGetCompStyleParam->dwChnNum, i, pGetCompStyleParam->aTypeArray[i]);
            }

            eRet = NVR_ERR__OK;

            MAKE_COMPILER_HAPPY(pGetCompStyleParam);
            break;
        }

        case SDSCPPROTO_CMD_TYPE_SET_COMP_STYLE_PARAM:
        {
            eRet = NVR_ERR__OK;
            break;

            TSdscpProtoReqSetCompStyleParam *pSetCompStyleParam = NULL;
            s32 i = 0, nStyle = 0;;
            TNvrSgwCfg tNvrSgwCfg;
            BOOL bReboot = FALSE;

            if (dwEventDataLen != sizeof(TSdscpProtoReqSetCompStyleParam))
            {
                SGWPRINTERR("SDSCPPROTO_CMD_TYPE_SET_COMP_STYLE_PARAM data failed! %u--%ld\n", dwEventDataLen, sizeof(TSdscpProtoReqSetCompStyleParam));
                break;
            }

            pSetCompStyleParam = (TSdscpProtoReqSetCompStyleParam *)pEventData;

            NvrSgwCfgGetParam(&tNvrSgwCfg);

            SGWPRINTDBG("SDSCPPROTO_CMD_TYPE_SET_COMP_STYLE_PARAM dwChnNum = %u \n", pSetCompStyleParam->dwChnNum);
            pSetCompStyleParam->dwChnNum = pSetCompStyleParam->dwChnNum > NVR_SGW_MAX_CHN_NUM ? NVR_SGW_MAX_CHN_NUM : pSetCompStyleParam->dwChnNum;

            for (i = 0; i < pSetCompStyleParam->dwChnNum; i++)
            {
                nStyle = NvrSgwSdscpStyleToDevStyle(pSetCompStyleParam->aTypeArray[i]);
                if (tNvrSgwCfg.adwChnStyle[i] != nStyle)
                {
                    bReboot = TRUE;
                    tNvrSgwCfg.adwChnStyle[i] = nStyle;
                }
                SGWPRINTDBG("SDSCPPROTO_CMD_TYPE_SET_COMP_STYLE_PARAM aTypeArray[%d] %d->%d \n", i,
                        pSetCompStyleParam->aTypeArray[i], tNvrSgwCfg.adwChnStyle[i]);
            }

            if (bReboot)
            {
                NvrSgwCfgSetParam(&tNvrSgwCfg);

                sync();
                sleep(1);
                TNvrSysShutDownInfo tReInfo;
                SGWFLASHNOTICE("chaneg style reboot\n");
                snprintf(tReInfo.achOperator, sizeof(tReInfo.achOperator), "%s", "local");
                snprintf(tReInfo.achDescription, sizeof(tReInfo.achDescription), "%s", "sdscp change style");
                NvrSysReboot(&tReInfo);
            }

            eRet = NVR_ERR__OK;
            NvrSgwCfgSetParam(&tNvrSgwCfg);
            NvrSgwMediaCtrlMosicAdjust();

            MAKE_COMPILER_HAPPY(pSetCompStyleParam);
            break;
        }

        case SDSCPPROTO_CMD_TYPE_GET_CHN_LIST:
        {
            s32 i = 0, j = 0;
            TSdscpProtoRespGetChnList *ptGetChnList = NULL;
            TNvrCapRtmpChnList tRtmpChnList;
            TNvrSgwCfg tNvrSgwCfg;
            u32 dwSqrtWin = 1;

            mzero(tRtmpChnList);
            mzero(tNvrSgwCfg);

            ptGetChnList = (TSdscpProtoRespGetChnList *)pEventData;;

            if (dwEventDataLen != sizeof(TSdscpProtoRespGetChnList) || !pEventData)
            {
                SGWPRINTERR("SDSCPPROTO_CMD_TYPE_SET_COMP_STYLE_PARAM data failed! %u--%ld\n", dwEventDataLen, sizeof(TSdscpProtoRespGetChnList));
                break;
            }

            NvrSgwCfgGetParam(&tNvrSgwCfg);

            ptGetChnList->dwSumNum = 0;
            do
            {
                u16 wDecChnId = 0;

                for (i = 0; i < g_sgw_max_chn_num; i++)
                {
                    dwSqrtWin = 4;  ///< 固定16路
                    wDecChnId = i;
//                    wDecChnId = tRtmpChnList.awDecChnId[i];
                    ptGetChnList->atChnInfos[i].dwID = wDecChnId;
                    snprintf(ptGetChnList->atChnInfos[ptGetChnList->dwSumNum].achName, 32, "D%u", wDecChnId);

                    ptGetChnList->atChnInfos[i].dwVidDecCapNum = dwSqrtWin * dwSqrtWin;
                    ptGetChnList->atChnInfos[i].eStatus = SDSCPPROTO_CHN_STATUS_ONLINE;

                    if(!access("/usr/config/ONLYEP", F_OK))
                    {
                        ptGetChnList->atChnInfos[0].eStatus = SDSCPPROTO_CHN_STATUS_IDLE;
                        ptGetChnList->atChnInfos[1].eStatus = SDSCPPROTO_CHN_STATUS_IDLE;
                    }
                    if(!access("/usr/config/ONLYRC", F_OK))
                    {
                        ptGetChnList->atChnInfos[2].eStatus = SDSCPPROTO_CHN_STATUS_IDLE;
                        ptGetChnList->atChnInfos[3].eStatus = SDSCPPROTO_CHN_STATUS_IDLE;
                    }

                    for (j = 0; j < ptGetChnList->atChnInfos[i].dwVidDecCapNum; j++)
                    {
                        if (j < SDSCPPROTO_VID_DEC_CAP_MAX_NUM)
                        {
                            ptGetChnList->atChnInfos[i].atVidDecCapList[j].dwID = j;
                            strncpy((char *)ptGetChnList->atChnInfos[i].atVidDecCapList[j].abyTaskId, (char *)g_tNvrSgwDecMgr.aatVidDecInfo[i][j].achTaskID, SDSCPPRPTO_RTMP_TASK_ID_BUF_LEN);
                            ptGetChnList->atChnInfos[i].atVidDecCapList[j].tMaxResCap.wHeight = NVR_SGW_MAX_RES_HEIGHT / dwSqrtWin;
                            ptGetChnList->atChnInfos[i].atVidDecCapList[j].tMaxResCap.wWidth = NVR_SGW_MAX_RES_WIDTH / dwSqrtWin;
                            ptGetChnList->atChnInfos[i].atVidDecCapList[j].tCurResCap.wHeight = g_tNvrSgwDecMgr.aatVidDecInfo[i][j].vaInfo.tVideoParam.wVideoHeight;
                            ptGetChnList->atChnInfos[i].atVidDecCapList[j].tCurResCap.wWidth = g_tNvrSgwDecMgr.aatVidDecInfo[i][j].vaInfo.tVideoParam.wVideoWidth;
                            ptGetChnList->atChnInfos[i].atVidDecCapList[j].abVidTypeCap[SDSCPPROTO_VID_TYPE_H264] = TRUE;
                            ptGetChnList->atChnInfos[i].atVidDecCapList[j].abVidTypeCap[SDSCPPROTO_VID_TYPE_MJPEG] = TRUE;
                            ptGetChnList->atChnInfos[i].atVidDecCapList[j].abVidTypeCap[SDSCPPROTO_VID_TYPE_H265] = TRUE;
                        }
                    }

                    ptGetChnList->atChnInfos[i].dwAudDecCapNum = NVR_SGW_MAX_CHN_AUD_NUM;
                    for (j = 0; j < ptGetChnList->atChnInfos[i].dwAudDecCapNum; j++)
                    {
                        if (j < SDSCPPROTO_AUD_DEC_CAP_MAX_NUM)
                        {
                            ptGetChnList->atChnInfos[i].atAudDecCapList[j].dwID = j;
                            strncpy((char *)ptGetChnList->atChnInfos[i].atAudDecCapList[j].abyTaskId, (char *)g_tNvrSgwDecMgr.aatAudDecInfo[i][j].achTaskID, SDSCPPRPTO_RTMP_TASK_ID_BUF_LEN);
                            ptGetChnList->atChnInfos[i].atAudDecCapList[j].abAudTypeCap[SDSCPPROTO_AUD_TYPE_PCMA] = TRUE;
                            ptGetChnList->atChnInfos[i].atAudDecCapList[j].abAudTypeCap[SDSCPPROTO_AUD_TYPE_PCMU] = TRUE;
                            ptGetChnList->atChnInfos[i].atAudDecCapList[j].abAudTypeCap[SDSCPPROTO_AUD_TYPE_ADPCM] = TRUE;
                            ptGetChnList->atChnInfos[i].atAudDecCapList[j].abAudTypeCap[SDSCPPROTO_AUD_TYPE_AACLC] = TRUE;
                        }
                    }
                    SGWPRINTIMP("chnlist: %d  dwID:%d name:%s dwVidDecCapNum:%d dwAudDecCapNum:%d\n",
                            i, ptGetChnList->atChnInfos[i].dwID,
                            ptGetChnList->atChnInfos[ptGetChnList->dwSumNum].achName,
                            ptGetChnList->atChnInfos[i].dwVidDecCapNum,
                            ptGetChnList->atChnInfos[i].dwAudDecCapNum);
                    for (j = 0; j < ptGetChnList->atChnInfos[i].dwVidDecCapNum; j++)
                    {
                        SGWPRINTFREQ("vid dwID:%u, wHeight:%u, wWidth:%u\n",
                                ptGetChnList->atChnInfos[i].atVidDecCapList[j].dwID,
                                ptGetChnList->atChnInfos[i].atVidDecCapList[j].tMaxResCap.wHeight,
                                ptGetChnList->atChnInfos[i].atVidDecCapList[j].tMaxResCap.wWidth);
                    }
                    for (j = 0; j < ptGetChnList->atChnInfos[i].dwAudDecCapNum; j++)
                    {
                        SGWPRINTFREQ("aud dwID:%u\n", ptGetChnList->atChnInfos[i].atAudDecCapList[j].dwID);
                    }
                    ptGetChnList->dwSumNum++;
                }
                eRet = NVR_ERR__OK;
            } while (0);

            break;
        }
        case SDSCPPROTO_CMD_TYPE_STOP_ALL_RTMP_TASK:
        {
            SGWPRINTIMP("pop all list %d:%d\n", g_tNvrSgwRtmpMgr.dwNodeNum, g_tNvrSgwRtmpMgr.dwRetryNodeNum);
            NvrSgwPopAllRtmpTask();
            break;
        }

        case SDSCPPROTO_CMD_TYPE_REPORT_RTMP_TASK_STATE:
        {
            TSdscpProtoReqStartRtmpTask tRtmpTask;
            u32 dwEvent = 0;

            if (dwEventDataLen != sizeof(TSdscpProtoReqStartRtmpTask) || !pEventData)
            {
                SGWPRINTERR("rtmp recv data err and notify data failed! %u--%ld\n", dwEventDataLen, sizeof(TSdscpProtoReqStartRtmpTask));
                break;
            }

            if (pvContext)
            {
                dwEvent = ((long long)pvContext) & 0xffffffff;
                SGWPRINTIMP("notify event:%d \n", dwEvent);
            }

            mzero(tRtmpTask);
            memcpy(&tRtmpTask, pEventData, dwEventDataLen);
            if(1014 == dwEvent)
            {
                //新策略:rtmp拉流时中间断流通知编码器无码流继续等待,但不要退送蓝屏
                u16 wChnId, wEncId;
                wChnId = tRtmpTask.tPullVidID.dwChnId / NVR_SGW_MAX_CHN_VID_NUM;
                wEncId = tRtmpTask.tPullVidID.dwChnId % NVR_SGW_MAX_CHN_VID_NUM;
                SGWPRINTIMP("1014 task: vid %d-%d\n", wChnId, wEncId);
                if(g_tNvrSgwDecMgr.aatVidDecInfo[wChnId][wEncId].vaInfo.tVideoParam.wVideoWidth != 0)
                {
                    g_bRtmpNoDataFlag[wChnId][wEncId] = 1;
                    g_bRtmpNoDataOldRes[wChnId][wEncId].wHeight = g_tNvrSgwDecMgr.aatVidDecInfo[wChnId][wEncId].vaInfo.tVideoParam.wVideoHeight;
                    g_bRtmpNoDataOldRes[wChnId][wEncId].wWidth = g_tNvrSgwDecMgr.aatVidDecInfo[wChnId][wEncId].vaInfo.tVideoParam.wVideoWidth;
                    g_tNvrSgwDecMgr.aatVidDecInfo[wChnId][wEncId].vaInfo.tVideoParam.wVideoWidth = 0;
                    g_tNvrSgwDecMgr.aatVidDecInfo[wChnId][wEncId].vaInfo.tVideoParam.wVideoHeight = 0;

                   if (g_tNvrSgwDecMgr.aatVidDecInfo[wChnId][wEncId].bOnly)
                   {
                        g_bChnFull[g_tNvrSgwDecMgr.aatVidDecInfo[wChnId][wEncId].byRealChn / NVR_SGW_MAX_CHIP_CHN_NUM] = FALSE;
                   }
                   else
                   {
                        s32 nAudDecID = (((g_tNvrSgwDecMgr.aatVidDecInfo[wChnId][wEncId].dwAvSyncChn >> 8) & 0xff ) * NVR_SGW_MAX_CHN_AUD_NUM
                            + (g_tNvrSgwDecMgr.aatVidDecInfo[wChnId][wEncId].dwAvSyncChn & 0xff));
                        g_bChnFull[nAudDecID / NVR_SGW_MAX_CHN_AUD_NUM / NVR_SGW_MAX_CHIP_CHN_NUM] = FALSE;    
                   }
                }
                __NvrSgwStreamStatSendMsg(wChnId, wEncId, 0, NVR_SGW_STREAM_STOP);
            }
            else
            {
                SGWMEMNOTICE("%s: pull rtmp event:%u", tRtmpTask.achTaskID, dwEvent);
                NvrSgwModifyRtmpRetryTask(tRtmpTask.achTaskID, TRUE);
                OsApi_TimerSet(g_hTaskRetryCheckTimer, 3000, NvrsgwTaskRetryCheckTimerCB, NULL);
            }

            MAKE_COMPILER_HAPPY(dwEvent);
            break;
        }

        case SDSCPPROTO_CMD_TYPE_SET_LAYOUT_ENC_PARAM:
        {
            TSdscpProtoReqStartRtmpTask *pStartTask = NULL;

            if (dwEventDataLen != sizeof(TSdscpProtoReqStartRtmpTask) || !pEventData)
            {
                SGWPRINTERR("SDSCPPROTO_CMD_TYPE_START_RTMP_TASK data failed! %u--%ld\n", dwEventDataLen, sizeof(TSdscpProtoReqStartRtmpTask));
                break;
            }

            pStartTask = (TSdscpProtoReqStartRtmpTask *)pEventData;

            ESdscpProtoCmdType ECmdType = pStartTask->bPullVid;
            u32 nErrorCode = pStartTask->bPullAud;
            u32 dwMsgId = pStartTask->bPushVid;

            SGWMEMNOTICE("SDSCPPROTO_CMD_TYPE_SET_LAYOUT_ENC_PARAM %u %u MsgId: %u\n", ECmdType, nErrorCode, dwMsgId);
            if(nErrorCode)
            {
                SGWPRINTERR("Enc Report Error: %u  MsgId: %u\n", nErrorCode, dwMsgId);
            }
            if (SDSCPPROTO_CMD_TYPE_SET_LAYOUT_ENC_PARAM == ECmdType)
            {
                eRet = NvrSgwModifyRtmpSdscpRetryTaskClear(dwMsgId);
                if (NVR_ERR__OK == eRet)
                {
                    SGWPRINTFREQ("clear succ %u\n", NvrSysGetCurTimeMSec());
                    //OsApi_SemGive(g_hSdscpNotifySem);
                }
            }

            MAKE_COMPILER_HAPPY(nErrorCode);
            break;
        }
        default:
            break;
    }

    return eRet;
}

s32 NvrSgwMediaCtrlVidDecReleaseCB(s32 nDecChn, TKDFrame *ptFrame)
{
//    SGWPRINTIMP(" cb %p  %u\n", ptFrame, ptFrame->m_dwDataSize);
    TMSFrame *ptMsFrame = (TMSFrame*)ptFrame;
    if (ptMsFrame->MSFreeFrame)
    {
        ptMsFrame->MSFreeFrame(ptMsFrame);
    }
    return 0;
}

s32 NvrSgwMediaCtrlAudDecReleaseCB(s32 nDecChn, TKDFrame *ptFrame)
{
//    SGWPRINTIMP(" cb %p  %u\n", ptFrame, ptFrame->m_dwDataSize);
    TMSFrame *ptMsFrame = (TMSFrame*)ptFrame;
    if (ptMsFrame->MSFreeFrame)
    {
        ptMsFrame->MSFreeFrame(ptMsFrame);
    }
    return 0;
}

NVRSTATUS NvrSgwDecoderMediaCtrlInit()
{
    NVRSTATUS eRet = NVR_ERR__OK;
    s32 nRet = 0, nVRet = -1, nARet = -1;
    s32 i = 0, j = 0;
    TVidStd tVidStdSet;
    TMediaCtrlCapability tCapability;
    TMediaCtrlVidInitInfo tVidInitInfo;
    TMediaCtrlAudInitInfo tAudInitInfo;
    TNvrSgwCfg tNvrSgwCfg;
    u32 dwSqrtWin = 1;
    u32 dwWidth = NVR_SGW_MAX_RES_WIDTH, dwHeight = NVR_SGW_MAX_RES_HEIGHT;

    mzero(tCapability);
    mzero(tVidInitInfo);
    mzero(tAudInitInfo);
    mzero(tNvrSgwCfg);

    tVidStdSet.dwWidth = NVR_SGW_MAX_RES_WIDTH;
    tVidStdSet.dwHeight = NVR_SGW_MAX_RES_HEIGHT;
    tVidStdSet.bProgressive = TRUE;
    tVidStdSet.dwFrameRate = 30;
    tVidStdSet.dwColorSpace = VIDEO_COLORSPACE_YUV422 | VIDEO_STD_MASK_EMBSYNC;

    ///<配置it6801 采集4k hdmi 采集参数
    nRet = VidInApiCtrl(VIDIF_HDMI(0), VIDIF_SET_STD, &tVidStdSet);
    if (nRet != 0)
    {
        SGWPRINTERR("VidInApiCtrl error\n");
    }

    NvrSgwCfgGetParam(&tNvrSgwCfg);

    ///<视频采集能力集
    tCapability.byVidCapChnNum = 0;


    ///<视频解码能力集
    tCapability.byVidDecChnNum = NVR_SGW_MAX_CHN_VID_NUM * 2;
    tCapability.tVidDecCapability.dwVidDecTotalPixel = NVR_SGW_MAX_RES_WIDTH * NVR_SGW_MAX_RES_HEIGHT * NVR_SGW_MEDIA_MAX_VID_CHN_3840_2160_DEC_NUM;
    tCapability.tVidDecCapability.tMaxDecRes.wWidth = NVR_SGW_MAX_RES_WIDTH;
    tCapability.tVidDecCapability.tMaxDecRes.wHeight = NVR_SGW_MAX_RES_HEIGHT;

    ///<视频解码参数
    for(i = 0; i < tCapability.byVidDecChnNum; i++)
    {
        tVidInitInfo.atDecChannelParams[i].tVidDecRes.wWidth = 3840;
        tVidInitInfo.atDecChannelParams[i].tVidDecRes.wHeight = 2160;
        tVidInitInfo.atDecChannelParams[i].eDecType = MEDIACTRL_VID_CODEC_H264;
        tVidInitInfo.atDecChannelParams[i].dwFrameRate = 30;

    }

    ///<视频输出设备的能力集
    tCapability.byVidDisDevNum = 2;
    tCapability.byVoDevNum = 2;

    for (i = 0; i < tCapability.byVoDevNum; i++)
    {
        ///<视频显示参数HDMI
        dwSqrtWin = tNvrSgwCfg.adwChnStyle[i];
        tVidInitInfo.atDisParams[i].eInterfaceMap = 0 == i ? MEDIACTRL_VDIS_DEV_BT1120 : MEDIACTRL_VDIS_DEV_HDMI1;
        tVidInitInfo.atDisParams[i].eDevMap = 0 == i ? MEDIACTRL_VO_DEV_HD0 : MEDIACTRL_VO_DEV_HD1;
        tVidInitInfo.atDisParams[i].tDeviceParams.bEnable = TRUE;
        tVidInitInfo.atDisParams[i].tDeviceParams.eVidDisStandard = MEDIACTRL_VID_STANDARD_3840x2160_30;
        tVidInitInfo.atDisParams[i].tDeviceParams.dwBgColor = MEDIACTRL_BG_COLOR_BLACK;
        tVidInitInfo.atDisParams[i].tDeviceParams.dwBrightness = 50;
        tVidInitInfo.atDisParams[i].tDeviceParams.dwContast = 50;
        tVidInitInfo.atDisParams[i].tDeviceParams.dwSaturation = 50;
        tVidInitInfo.atDisParams[i].tDeviceParams.dwHue = 50;

        tVidInitInfo.atDisParams[i].tMosaicParams.bOverlay = TRUE;
        tVidInitInfo.atDisParams[i].tMosaicParams.dwNumberOfWindows = dwSqrtWin * dwSqrtWin;
        for(j = 0; j < tVidInitInfo.atDisParams[i].tMosaicParams.dwNumberOfWindows; j++)
        {
            tVidInitInfo.atDisParams[i].tMosaicParams.atWinList[j].dwWidId = j;
            tVidInitInfo.atDisParams[i].tMosaicParams.atWinList[j].dwPriority = 0;
            tVidInitInfo.atDisParams[i].tMosaicParams.atWinList[j].dwStartX = SGW_ALIGN_BACK((dwWidth/dwSqrtWin) * (j%dwSqrtWin), 2);
            tVidInitInfo.atDisParams[i].tMosaicParams.atWinList[j].dwStartY = SGW_ALIGN_BACK((dwHeight/dwSqrtWin) * (j/dwSqrtWin), 2);
            tVidInitInfo.atDisParams[i].tMosaicParams.atWinList[j].dwWidth = SGW_ALIGN_BACK(dwWidth/dwSqrtWin, 2);
            tVidInitInfo.atDisParams[i].tMosaicParams.atWinList[j].dwHeight = SGW_ALIGN_BACK(dwHeight/dwSqrtWin, 2);
            tVidInitInfo.atDisParams[i].tMosaicParams.atWinList[j].bEnableWinBorder = TRUE;
            tVidInitInfo.atDisParams[i].tMosaicParams.atWinList[j].tWinBorder.dwLeftThickness = 2;
            tVidInitInfo.atDisParams[i].tMosaicParams.atWinList[j].tWinBorder.dwTopThickness = 2;
            tVidInitInfo.atDisParams[i].tMosaicParams.atWinList[j].tWinBorder.dwRightThickness = 2;
            tVidInitInfo.atDisParams[i].tMosaicParams.atWinList[j].tWinBorder.dwBottomThickness = 2;
            tVidInitInfo.atDisParams[i].tMosaicParams.atWinList[j].tWinBorder.dwColor = (0x00FF00);   ///<green back groud color
            tVidInitInfo.atDisParams[i].tMosaicParams.atWinList[j].bCrop = FALSE;
            tVidInitInfo.atDisParams[i].tMosaicParams.atWinList[j].tCropRect.wStartX = 0;
            tVidInitInfo.atDisParams[i].tMosaicParams.atWinList[j].tCropRect.wStartY = 0;
            tVidInitInfo.atDisParams[i].tMosaicParams.atWinList[j].tCropRect.wWidth = 0;
            tVidInitInfo.atDisParams[i].tMosaicParams.atWinList[j].tCropRect.wHeight = 0;
            tVidInitInfo.atDisParams[i].tMosaicParams.atWinList[j].bFreeze = FALSE;
            tVidInitInfo.atDisParams[i].tMosaicParams.atWinList[j].eVidDecMode = MEDIACTRL_VDEC_MODE_REAL;
            tVidInitInfo.atDisParams[i].tMosaicParams.adwChnMap[j] = MEDIACTRL_INVALID_CHN;
            tVidInitInfo.atDisParams[i].tMosaicParams.eVidDisSource[j] = MEDIACTRL_VID_DIS_SOURCE_DEC;
            tVidInitInfo.atDisParams[i].tMosaicParams.tDisSrcParam[j].bContainedGraphic = FALSE;

        }
        tVidInitInfo.atDisParams[i].tMosaicParams.tAspectRatio.eScaleMode = MEDIACTRL_VSCALE_MODE_FULL;
        tVidInitInfo.atDisParams[i].tMosaicParams.tAspectRatio.dwBgColor = MEDIACTRL_BG_COLOR_GREEN;
        tVidInitInfo.atDisParams[i].tMosaicParams.outputFPS = 30;
        tVidInitInfo.atDisParams[i].tMosaicParams.bReserveDecChn = FALSE;

        memcpy(&g_tNvrSgwDecMgr.atMosaicParam[i], &tVidInitInfo.atDisParams[i].tMosaicParams, sizeof(TMediaCtrlVidDisMosaicParam));
    }

    ///<ARGB8888格式的鼠标参数
    char cursor[24][24][4];
    memset(cursor, 0xff, sizeof(char)*24*24*4);
    char *cursorBmp = (char *)cursor;

    tVidInitInfo.tMouseParams.tCursorInfo.pBmpbuf = cursorBmp;
    tVidInitInfo.tMouseParams.tCursorInfo.ePixelFormat = MEDIACTRL_PIXEL_FORMAT_RGB_8888;
    tVidInitInfo.tMouseParams.tCursorInfo.dwPicWidth = 24;
    tVidInitInfo.tMouseParams.tCursorInfo.dwPicHeight = 24;
    tVidInitInfo.tMouseParams.tBindDevInfo.eVoDev = MEDIACTRL_VDIS_DEV_INVALID;
    tVidInitInfo.tMouseParams.tBindDevInfo.dwDevWidth = 1920;
    tVidInitInfo.tMouseParams.tBindDevInfo.dwDevHeight = 1080;
    tVidInitInfo.tMouseParams.eSensitivity = MEDIACTRL_MOUSE_NORMAL;

    ///<AudCapability
    tCapability.tAudCapCapability.eMode = MEDIACTRL_AUD_PROCESS_MODE_MATRIX;
    tCapability.tAudCapCapability.eSampleRate = MEDIACTRL_AUD_SAMPLE_RATE_16K;
    tCapability.tAudCapCapability.eSampleBits = MEDIACTRL_AUD_SAMPLE_BITS_16;
    tCapability.tAudCapCapability.dwFrameTime = 5;
    tCapability.tAudCapCapability.byAudCapChnNum = 0;
    tCapability.tAudCapCapability.byAudPlyChnNum = 16;

    tCapability.tAudCapCapability.byAudDecChnNum = 16;
    tCapability.tAudCapCapability.byAudEncChnNum = 0;
    tCapability.tAudCapCapability.byAudMixerChnNum = 0;

    tCapability.tAudCapCapability.byAudCapPcmChnNum = 0;
    tCapability.tAudCapCapability.byAudPlyPcmChnNum = 0;

    for (i = 0; i < MEDIACTRL_MAX_AUD_DEC_NUM; i++)
    {
        if (i < tCapability.tAudCapCapability.byAudDecChnNum)
        {
            tAudInitInfo.tAudParam.atDecParam[i].s32DecId = i;
            tAudInitInfo.tAudParam.atDecParam[i].bDecEnable = TRUE;
            tAudInitInfo.tAudParam.atDecParam[i].bMute = FALSE;
            tAudInitInfo.tAudParam.atDecParam[i].byDecVolume = 50;
            tAudInitInfo.tAudParam.atDecParam[i].bVoiceChangerEnable = FALSE;
            tAudInitInfo.tAudParam.atDecParam[i].dwDelayTime = 0;
        }
        else
        {
            tAudInitInfo.tAudParam.atDecParam[i].s32DecId = MEDIACTRL_INVALID_CHN;
        }
    }

    ///<PLAY Module 2
    for (i = 0; i < MEDIACTRL_MAX_AUD_PLY_NUM; i++)
    {
        if (i < tCapability.tAudCapCapability.byAudPlyChnNum)
        {
            tAudInitInfo.tAudParam.atPlyParam[i].s32Interface = MEDIACTRL_AUD_OPTICAL_PORT_OUT(i);
            tAudInitInfo.tAudParam.atPlyParam[i].tLinkInfo.eModule = MEDIACTRL_AUD_MODULE_DEC;
            tAudInitInfo.tAudParam.atPlyParam[i].tLinkInfo.s32Index = i;
            tAudInitInfo.tAudParam.atPlyParam[i].s32PlyGain = 90;
            tAudInitInfo.tAudParam.atPlyParam[i].bMute = FALSE;
            tAudInitInfo.tAudParam.atPlyParam[i].byPlyVolume = 50;
            tAudInitInfo.tAudParam.atPlyParam[i].bAnsEnable = FALSE;
            tAudInitInfo.tAudParam.atPlyParam[i].dwDelayTime = 0;
        }
        else
        {
            tAudInitInfo.tAudParam.atPlyParam[i].s32Interface = MEDIACTRL_INVALID_CHN;
        }
    }

    nVRet = MediaCtrlInit(&tCapability, &tVidInitInfo, NULL);
    if(0 != nVRet)
    {
        SGWPRINTERR("MediaCtrlInit vid fail, ret = %d\n", nVRet);
        printf("MediaCtrlInit vid fail, ret = %d\n", nVRet);
        eRet = NVR_ERR__ERROR;
    }
    else
    {
        MediaCtrlVidDecSetReleaseCallback(NvrSgwMediaCtrlVidDecReleaseCB);
    }
    g_bMedaiVidInitSucc = nVRet;

    nARet = MediaCtrlInit(&tCapability, NULL, &tAudInitInfo);
    if(0 != nARet)
    {
        SGWPRINTERR("MediaCtrlInit aud fail, ret = %d\n", nARet);
        printf("MediaCtrlInit aud fail, ret = %d\n", nARet);
        eRet = NVR_ERR__ERROR;
    }
    else
    {
        MediaCtrlAudDecSetReleaseCallback(NvrSgwMediaCtrlAudDecReleaseCB);
    }

    g_bMedaiInitSucc[0] = nARet;

    SGWFLASHNOTICE("MediaCtrlInit vid %s, aud %s\n", 0 == nVRet ? "succ" : "fail", 0 == nARet ? "succ" : "fail");

    MediaCtrlFlashErrLogSetCB(NvrSgwFlashErrLogCallback);
    MediaCtrlFlashNoticeLogSetCB(NvrSgwFlashNoticeLogCallback);
    MediaCtrlMemNoticeLogSetCB(NvrSgwMemNoticeLogCallback);
    return eRet;
}

NVRSTATUS NvrSgwDecoderSubChipMediaCtrlInit()
{
    s32 nRet = 0;
    TNvrSgwCfg tNvrSgwCfg;
    TNvrSgwCmdMediaCtrlInit tSgwMcaMEdaiCtrlInitParam;

    mzero(tNvrSgwCfg);
    mzero(tSgwMcaMEdaiCtrlInitParam);

    NvrSgwCfgGetParam(&tNvrSgwCfg);

    SGWPRINTIMP("NvrSgwCfgGetParam chn style .%d.%d.\n", tNvrSgwCfg.adwChnStyle[2], tNvrSgwCfg.adwChnStyle[3]);
    memcpy(&tSgwMcaMEdaiCtrlInitParam.adwChnStyle[0], &tNvrSgwCfg.adwChnStyle[2], sizeof(tSgwMcaMEdaiCtrlInitParam));
    nRet = NvrSgwMcaCmdSend(CMD_NVR_MCA_SET_MEIDACTRL_INIT, (u8*)&tSgwMcaMEdaiCtrlInitParam,
            sizeof(tSgwMcaMEdaiCtrlInitParam));
    SGWPRINTIMP("CMD_NVR_MCA_SET_MEIDACTRL_INIT %d .%d.%d.\n", nRet, tSgwMcaMEdaiCtrlInitParam.adwChnStyle[0],
            tSgwMcaMEdaiCtrlInitParam.adwChnStyle[1]);
    printf("CMD_NVR_MCA_SET_MEIDACTRL_INIT ret:%d ...\n", nRet);

    return 0 == nRet ?  NVR_ERR__OK : NVR_ERR__ERROR;
}

void NvrSgwMediaDecoderInterfaceInit()
{
    EMediactrlVidDisDev eDev = MEDIACTRL_VDIS_DEV_BT1120;
    TMediaCtrlVidDisDevParam tVidDisDevParam;
    s32 nRet = 0;
    s32 i =0, j = 0;

    mzero(tVidDisDevParam);

    tVidDisDevParam.bEnable = TRUE;
    tVidDisDevParam.eVidDisStandard = MEDIACTRL_VID_STANDARD_3840x2160_30;
    tVidDisDevParam.dwBgColor = MEDIACTRL_BG_COLOR_BLACK;
    tVidDisDevParam.dwBrightness = 50;
    tVidDisDevParam.dwContast = 50;
    tVidDisDevParam.dwSaturation = 50;
    tVidDisDevParam.dwHue = 50;

    nRet = MediaCtrlVidDisSetDevParam(eDev, &tVidDisDevParam);
    SGWPRINTIMP("-----MediaCtrlVidDisSetDevParam eDev:%d  nRet:%d\n", eDev, nRet);
    eDev = MEDIACTRL_VDIS_DEV_HDMI1;
    nRet = MediaCtrlVidDisSetDevParam(eDev, &tVidDisDevParam);
    SGWPRINTIMP("-----MediaCtrlVidDisSetDevParam eDev:%d  nRet:%d\n", eDev, nRet);

    for (i = 0; i < NVR_SGW_MAX_CHN_NUM; i++)
    {
        for(j = 0; j < g_tNvrSgwDecMgr.atMosaicParam[i].dwNumberOfWindows; j++)
        {
            eDev = i > 0 ? MEDIACTRL_VDIS_DEV_HDMI1 : MEDIACTRL_VDIS_DEV_BT1120;
            MediaCtrlVidDisClearWin(eDev, j);
        }
    }
}

void NvrSgwDecoderParamInit()
{
    s32 i= 0, j = 0;
    mzero(g_tNvrSgwDecMgr);

    for (i = 0; i < NVR_SGW_MAX_CHN_NUM; i++)
    {
        for (j = 0; j < NVR_SGW_MAX_CHN_VID_NUM; j++)
        {
            g_tNvrSgwDecMgr.aatVidDecInfo[i][j].dwAvSyncChn = -1;
        }

        for (j = 0; j < NVR_SGW_MAX_CHN_AUD_NUM; j++)
        {
            g_tNvrSgwDecMgr.aatAudDecInfo[i][j].dwAvSyncChn = -1;
        }
    }
}

static HTIMERHANDLE g_hTaskAutoTestTimer = NULL;

s32 NvrsgwTaskTestTimerCB( HTIMERHANDLE dwTimerId, void* param )
{
    static BOOL bStart = TRUE;

    if (bStart)
    {
        NvrSgwDecRtmpAuto();
        OsApi_TimerSet(g_hTaskAutoTestTimer, 25000, NvrsgwTaskTestTimerCB, NULL);
        bStart = FALSE;
    }
    else
    {
        NvrSgwDecStopAllRtmp();
        OsApi_TimerSet(g_hTaskAutoTestTimer, 5000, NvrsgwTaskTestTimerCB, NULL);
        bStart = TRUE;
    }

    return 0;
}

void NvrSgwDecAutoTimer(BOOL bStart)
{
    if (bStart)
    {
        if (!g_hTaskAutoTestTimer)
        {
            OsApi_TimerNew(&g_hTaskAutoTestTimer);
        }
    }
    else
    {
        if (g_hTaskAutoTestTimer)
        {
            OsApi_TimerDelete(g_hTaskAutoTestTimer);
            g_hTaskAutoTestTimer = NULL;
        }
    }


    if (g_hTaskAutoTestTimer)
    {
        OsApi_TimerSet(g_hTaskAutoTestTimer, 3000, NvrsgwTaskTestTimerCB, NULL);
    }
}

void NvrSgwDecGetMedaiStat()
{
    s32 nRet = 0;
    s32 i = 0, j = 0;
    s32 nDevId = 0;
    TMediaCtrlAudState tAudStat;
    TMediaCtrlVidState tVidStat;

    nRet =  MediaCtrlGetStatus(&tAudStat, &tVidStat);
    if (0 == nRet)
    {
        SGWPRINTDBG("-----MediaCtrlGetStatus\n");

        for (i = 0; i < NVR_SGW_MAX_CHIP_CHN_NUM; i++)
        {
            for (j = 0; j < NVR_SGW_MAX_CHN_AUD_NUM; j++)
            {
                if (g_tNvrSgwDecMgr.aatAudDecInfo[i][j].byValid)
                {
                    nDevId = i * NVR_SGW_MAX_CHN_AUD_NUM + j;
                    SGWPRINTINFO("aud chn:%2d dec:%2d, %2d Rcvjitter:%u, Decjitter:%u, DecAvgRhythm:%u, DecBuf:%u\n",
                            i, j, nDevId,
                            tAudStat.dwMediaRcvjitter[nDevId],
                            tAudStat.dwAudDecjitter[nDevId],
                            tAudStat.dwAudDecAvgRhythm[nDevId],
                            tAudStat.dwAudDecBuf[nDevId]);
                }
            }
        }

        for (i = 0; i < NVR_SGW_MAX_CHIP_CHN_NUM; i++)
        {
            for (j = 0; j < NVR_SGW_MAX_CHN_VID_NUM; j++)
            {
                if (g_tNvrSgwDecMgr.aatVidDecInfo[i][j].byValid)
                {
                    nDevId = i * NVR_SGW_MAX_CHN_VID_NUM + j;
                    SGWPRINTINFO("vid chn:%2d dec:%2d, %2d Decjitter:%u, DecBuf:%u\n",
                            i, j, nDevId,
                            tVidStat.dwVidDecjitter[nDevId],
                            tVidStat.dwVidDecBuf[nDevId]);
                }
            }
        }
    }
}

void NvrSgwDecTaskStatus(u16 vChn, u16 aChn, s8 *pTask)
{
    u16 wVidChn = 0, wVidSubChn = 0;
    u16 wAudChn = 0, wAudSubChn = 0;
    TMediaCtrlAudState tAudStat;
    TMediaCtrlVidState tVidStat;
    s32 nVDevId = 0, nADevId = 0;
    TNvrSgwCmdTaskStatus tSgwMacCmdParam;
    TMediaCtrlAudPowerInfo tPowerInfo;
    s32 nRet = 0;
    s32 i = 0;

    tSgwMacCmdParam.wVidChn = 0xff;
    tSgwMacCmdParam.wAudChn = 0xff;

    if (pTask)
    {

    }
    else
    {
        wVidChn = vChn / 100;
        wVidSubChn = vChn % 100;
        nVDevId = wVidChn * NVR_SGW_MAX_CHN_VID_NUM + wVidSubChn;

        wAudChn = aChn / 100;
        wAudSubChn = aChn % 100;
        nADevId = wAudChn * NVR_SGW_MAX_CHN_AUD_NUM + wAudSubChn;
    }

    MediaCtrlGetStatus(&tAudStat, &tVidStat);

    for (i = 0; i < NVR_SGW_MAX_CHN_AUD_NUM * NVR_SGW_MAX_CHIP_CHN_NUM; i++)
    {
        tPowerInfo.atPlayPower[i].s32Interface = MEDIACTRL_AUD_OPTICAL_PORT_OUT(i);
        tPowerInfo.atDecPower[i].s32DecId = i;
    }
    MediaCtrlAudGetPowerInfo(&tPowerInfo);

    if (wVidChn < NVR_SGW_MAX_CHIP_CHN_NUM)
    {
        SGWPRINTINFO("vid: chn schn start frameCount msin  msout recvj   buff bindaud \n");
        SGWPRINTINFO("     %3d  %2d    %d    %08d   %02d %6d %7d   %d  %03d\n", wVidChn, wVidSubChn,
                g_tNvrSgwDecMgr.aatVidDecInfo[wVidChn][wVidSubChn].byStartDec,
                g_tNvrSgwDecMgr.aatVidDecInfo[wVidChn][wVidSubChn].dwFrameCount,
                g_tNvrSgwDecMgr.aatVidDecInfo[wVidChn][wVidSubChn].dwMsInId,
                g_tNvrSgwDecMgr.aatVidDecInfo[wVidChn][wVidSubChn].dwMsOutId,
                tVidStat.dwVidDecjitter[nVDevId],
                tVidStat.dwVidDecBuf[nVDevId],
                g_tNvrSgwDecMgr.aatVidDecInfo[wVidChn][wVidSubChn].dwAvSyncChn);
    }
    else
    {
        tSgwMacCmdParam.wVidChn = wVidChn;
        tSgwMacCmdParam.wVidSubChn = wVidSubChn;
        tSgwMacCmdParam.dwVidMsIn = g_tNvrSgwDecMgr.aatVidDecInfo[wVidChn][wVidSubChn].dwMsInId;
        tSgwMacCmdParam.dwVidMsOut = g_tNvrSgwDecMgr.aatVidDecInfo[wVidChn][wVidSubChn].dwMsOutId;

        if (wAudChn >= NVR_SGW_MAX_CHIP_CHN_NUM)
        {
            tSgwMacCmdParam.wAudChn = wAudChn;
            tSgwMacCmdParam.wAudSubChn = wAudSubChn;
            tSgwMacCmdParam.dwAudMsIn = g_tNvrSgwDecMgr.aatAudDecInfo[wAudChn][wAudSubChn].dwMsInId;
            tSgwMacCmdParam.dwAudMsOut = g_tNvrSgwDecMgr.aatAudDecInfo[wAudChn][wAudSubChn].dwMsOutId;
        }

        nRet = NvrSgwMcaCmdSend(CMD_NVR_MCA_NOTIFY_TASK_STATUS, (u8*)&tSgwMacCmdParam, sizeof(tSgwMacCmdParam));
        if (0 == nRet)
        {
        }
    }
    if (wAudChn < NVR_SGW_MAX_CHIP_CHN_NUM)
    {
        SGWPRINTINFO("aud: chn schn start frameCount msin  msout recvj   buff  avg  dis decp  playp\n");
        SGWPRINTINFO("     %3d  %2d    %d    %08d   %02d %06d %7d   %d %d %d  %3d   %3d\n", wAudChn, wAudSubChn,
                g_tNvrSgwDecMgr.aatAudDecInfo[wAudChn][wAudSubChn].byStartDec,
                g_tNvrSgwDecMgr.aatAudDecInfo[wAudChn][wAudSubChn].dwFrameCount,
                g_tNvrSgwDecMgr.aatAudDecInfo[wAudChn][wAudSubChn].dwMsInId,
                g_tNvrSgwDecMgr.aatAudDecInfo[wAudChn][wAudSubChn].dwMsOutId,
                tAudStat.dwAudDecjitter[nADevId],
                tAudStat.dwAudDecBuf[nADevId],
                tAudStat.dwAudDecAvgRhythm[nADevId],
                tAudStat.dwAudDecDiscardFrmNum[nADevId],
                tPowerInfo.atDecPower[nADevId].s32Power,
                tPowerInfo.atPlayPower[nADevId].s32Power);
    }
    else
    {
        if (wAudChn != tSgwMacCmdParam.wAudChn)
        {
            tSgwMacCmdParam.wAudChn = wAudChn;
            tSgwMacCmdParam.wAudSubChn = wAudSubChn;

            nRet = NvrSgwMcaCmdSend(CMD_NVR_MCA_NOTIFY_TASK_STATUS, (u8*)&tSgwMacCmdParam, sizeof(tSgwMacCmdParam));
            if (0 == nRet)
            {
            }
        }
    }
}

void NvrSgwDecSysinfo(s8 *pTask)
{
    if (pTask)
    {
        u8 byCmd[1024] = {0};
        sprintf((char *)byCmd, "%s > /tmp/sysinfo", pTask);
        NvrSystem((const char *)byCmd);
    }
}

void NvrSgwDecSdscpMsg(s32 chn, s32 subChn, s32 type)
{

    __NvrSgwStreamStatSendMsg(chn, subChn, FALSE, type);
}

void NvrSgwDecReset(s32 nDecChnId, s32 nDecId, s32 nDelay)
{
    g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byStopDec = TRUE;
    NvrSgwStreamDecFree(g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].nUniq);

    pthread_mutex_lock(&mediactrl_set_lock);
    g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].nUniq = 0;
    g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byStartDec = FALSE;
    if (g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byRealChn < NVR_SGW_MAX_CHIP_CHN_NUM)
    {
        NvrSgwMediaCtrlSetChnMosicInvalidNew(g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byRealChn,
                g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].nDevVidWinId);
    }
    pthread_mutex_unlock(&mediactrl_set_lock);
    usleep(nDelay * 1000);
    g_tNvrSgwDecMgr.aatVidDecInfo[nDecChnId][nDecId].byStopDec = FALSE;
}

void NvrSgwDecSetOnlyChip(u8 ChipId)
{
    if(ChipId == 0)
    {
        //只用主片
        if(!access("/usr/config/ONLYEP", F_OK))
        {
            system("rm -rf /usr/config/ONLYEP");
        }
        system("touch /usr/config/ONLYRC");
    }
    else if(ChipId == 1)
    {
        //只用从片
        if(!access("/usr/config/ONLYRC", F_OK))
        {
            system("rm -rf /usr/config/ONLYRC");
        }
        system("touch /usr/config/ONLYEP");
    }
    else
    {
        //主从都用
        if(!access("/usr/config/ONLYEP", F_OK))
        {
            system("rm -rf /usr/config/ONLYEP");
        }
        if(!access("/usr/config/ONLYRC", F_OK))
        {
            system("rm -rf /usr/config/ONLYRC");
        }
    }
}

static void NvrSgwDecSdscpRcvTimeOutCustom(u16 dwTm)
{
    FILE *pfp = NULL;
    if(dwTm)
    {
        pfp = fopen(NVR_SGW_SDSCP_RCV_TIMEOUT_CFG, "w");
        if(pfp)
        {
            fprintf(pfp, "%hu ms", dwTm);
            fclose(pfp);
        }
    }
    else
    {
        pfp = fopen(NVR_SGW_SDSCP_RCV_TIMEOUT_CFG, "r");
        if(pfp)
        {
            fscanf(pfp, "%hu ms", &g_SdscpRcvTimeOut);
            fclose(pfp);
        }
    }
}

void NvrSgwDecGetChnList()
{
    NVRSTATUS eRet = NVR_ERR__ERROR;
    u32 dwDataLen = sizeof(TSdscpProtoRespGetChnList);
    TSdscpProtoRespGetChnList tGetChnList;
    s32 nChnNum = 0, nChnIndex = 0;
    s32 nLimitNum = 0, i = 0, j = 0;

    eRet = NvrSgwSdscpDecOptCB(SDSCPPROTO_CMD_TYPE_GET_CHN_LIST, (void *)&tGetChnList, dwDataLen, NULL);
    if (NVR_ERR__ERROR == eRet)
    {
        SGWPRINTERR("get chn list err  fail,ret:%d\n", eRet);
    }
    else
    {
        SGWPRINTDBG("dwSumNum %d\n", tGetChnList.dwSumNum);
        nChnNum = tGetChnList.dwSumNum > SDSCPPROTO_CHN_MAX_NUM ? SDSCPPROTO_CHN_MAX_NUM : tGetChnList.dwSumNum;

        for (nChnIndex = 0; nChnIndex < nChnNum; nChnIndex++)
        {
            SGWPRINTDBG("chn info id:%d\n", tGetChnList.atChnInfos[nChnIndex].dwID);
            SGWPRINTDBG("chn info achName:%s\n", tGetChnList.atChnInfos[nChnIndex].achName);
            SGWPRINTDBG("chn info eStatus:%d(0:idle 1:online 2:offline\n", tGetChnList.atChnInfos[nChnIndex].eStatus);
            SGWPRINTDBG("chn info dwVidDecCapNum:%d\n", tGetChnList.atChnInfos[nChnIndex].dwVidDecCapNum);
            nLimitNum = tGetChnList.atChnInfos[nChnIndex].dwVidDecCapNum > SDSCPPROTO_VID_DEC_CAP_MAX_NUM ? SDSCPPROTO_VID_DEC_CAP_MAX_NUM : tGetChnList.atChnInfos[nChnIndex].dwVidDecCapNum;
            for (i = 0;  i < nLimitNum; i++)
            {
                SGWPRINTDBG("chn info atVidDecCapList dwID:%d\n", tGetChnList.atChnInfos[nChnIndex].atVidDecCapList[i].dwID);
                SGWPRINTDBG("chn info atVidDecCapList abyTaskId:%s\n", tGetChnList.atChnInfos[nChnIndex].atVidDecCapList[i].abyTaskId);
                SGWPRINTDBG("chn info atVidDecCapList tMaxResCap wWidth:%d\n", tGetChnList.atChnInfos[nChnIndex].atVidDecCapList[i].tMaxResCap.wWidth);
                SGWPRINTDBG("chn info atVidDecCapList tMaxResCap wHeight:%d\n", tGetChnList.atChnInfos[nChnIndex].atVidDecCapList[i].tMaxResCap.wHeight);
                SGWPRINTDBG("chn info atVidDecCapList tCurResCap wWidth:%d\n", tGetChnList.atChnInfos[nChnIndex].atVidDecCapList[i].tCurResCap.wWidth);
                SGWPRINTDBG("chn info atVidDecCapList tCurResCap wHeight:%d\n", tGetChnList.atChnInfos[nChnIndex].atVidDecCapList[i].tCurResCap.wHeight);
                for (j = 0;  j < SDSCPPROTO_VID_TYPE_COUNT; j++)
                {
                    SGWPRINTDBG("chn info atVidDecCapList abVidTypeCap %d:%d\n", j, tGetChnList.atChnInfos[nChnIndex].atVidDecCapList[i].abVidTypeCap[j]);
                }
            }
            SGWPRINTDBG("chn info dwAudDecCapNum:%d\n", tGetChnList.atChnInfos[nChnIndex].dwAudDecCapNum);
            nLimitNum = tGetChnList.atChnInfos[nChnIndex].dwAudDecCapNum > SDSCPPROTO_AUD_DEC_CAP_MAX_NUM ? SDSCPPROTO_AUD_DEC_CAP_MAX_NUM : tGetChnList.atChnInfos[nChnIndex].dwAudDecCapNum;
            for (i = 0;  i < nLimitNum; i++)
            {
                SGWPRINTDBG("chn info atAudDecCapList dwID:%d\n", tGetChnList.atChnInfos[nChnIndex].atAudDecCapList[i].dwID);
                SGWPRINTDBG("chn info atAudDecCapList abyTaskId:%s\n", tGetChnList.atChnInfos[nChnIndex].atAudDecCapList[i].abyTaskId);
                for (j = 0;  j < SDSCPPROTO_AUD_TYPE_COUNT; j++)
                {
                    SGWPRINTDBG("chn info atAudDecCapList abAudTypeCap %d:%d\n", j, tGetChnList.atChnInfos[nChnIndex].atAudDecCapList[i].abAudTypeCap[j]);
                }
            }

            SGWPRINTDBG("============================================================\n");
        }
    }
}

static s32 NvrSgwDecUpdateE2()
{
    s32 nRet = 0;
    u32 adwData[2];
    TPrdInfo tPrdInfo;
    memset(&tPrdInfo, 0x0, sizeof(tPrdInfo));
    mzero(adwData);

    //查询主片E2
    nRet = BrdPinfoQuery(&tPrdInfo);
    if (0 != nRet)
    {
        SGWPRINTERR("BrdPinfoQuery fail,ret:%d\n", nRet);
    }

    SGWPRINTDBG("BrdPinfoQuery pid:0x%x hid:0x%x\n",  tPrdInfo.dwPid, tPrdInfo.dwHwId);
    adwData[0] = tPrdInfo.dwPid;
    adwData[1] = tPrdInfo.dwHwId;

    NvrSgwMcaCmdSend(CMD_NVR_MCA_UPDATE_E2, (u8*)adwData, sizeof(adwData));
    if( nRet != 0)
    {
    	printf("McaWriteCmdmsg nRet:%d\n",nRet );
    }

    return nRet;
}


static void NvrSgwDecVoChnDump(u8 byLayerId, u8 byChnId,u8 byNum);

static NVRSTATUS NvrSgwDecExtOpt(u16 byOptType, void *pParam)
{
    switch(byOptType)
    {
        case 1:
        {
            SGWMEMNOTICE("External operation reset FPGA");
            NvrSgwResetFpga();
            sprintf((s8*)pParam, "Reset FPGA OK!\r\n");
        }break;
        case 2:
        {
            SGWMEMNOTICE("External operation reboot");
            TNvrSysShutDownInfo tReInfo;
            mzero(tReInfo);
            snprintf(tReInfo.achOperator, sizeof(tReInfo.achOperator), "%s", "local");
            snprintf(tReInfo.achDescription, sizeof(tReInfo.achDescription), "%s", "External operation reboot");
            sprintf((s8*)pParam, "Reboot....\r\n");

            NvrSysReboot(&tReInfo);
        }break;
        case 3:
        {
            if(NULL == pParam)
                return NVR_ERR__ERROR;
            u8 i = 0;
            s8 abyBufBak[128];
            TMediaCtrlAudPowerInfo tPowerInfo;
            BOOL32 bEpChkSuc = 0;
            FILE *pFileHandle = NULL;
            mzero(abyBufBak);
            mzero(tPowerInfo);

            //查询从片mc_power
            remove("/tmp/sysinfo");
            NvrSgwMcaCmdSend(CMD_NVR_MCA_EP_MCPOWER, &i, sizeof(i));
            //等待结果,3s后没有则退出
            while(i < 6)
            {
                if(!access("/tmp/sysinfo", F_OK))
                {
                    bEpChkSuc = 1;
                    break;
                }
                i++;
                usleep(500 * 1000);
            }

            //获取主片mc_power
            for (i = 0; i < NVR_SGW_MAX_CHN_AUD_NUM * NVR_SGW_MAX_CHIP_CHN_NUM; i++)
            {
                tPowerInfo.atPlayPower[i].s32Interface = MEDIACTRL_AUD_OPTICAL_PORT_OUT(i);
                tPowerInfo.atDecPower[i].s32DecId = i;
            }
            MediaCtrlAudGetPowerInfo(&tPowerInfo);

            //拼接字符串
            sprintf(pParam, "============== PLAY POWER ==============\r\n");
            for(i = 0; i < NVR_SGW_MAX_CHN_AUD_NUM * NVR_SGW_MAX_CHIP_CHN_NUM; i++)
            {
                snprintf(abyBufBak, sizeof(abyBufBak), "Interface %#x %d\r\n", tPowerInfo.atPlayPower[i].s32Interface, tPowerInfo.atPlayPower[i].s32Power);
                strcat(pParam, abyBufBak);
            }
            pFileHandle = fopen("/tmp/sysinfo", "r");
            if(pFileHandle)
            {
                while(fgets(abyBufBak, sizeof(abyBufBak), pFileHandle))
                {
                    s8 *token = strstr(abyBufBak, "Interface ");
                    if(token)
                    {
                        strcat(pParam, token);
                    }
                }
                fclose(pFileHandle);
                pFileHandle = NULL;
            }
            strcat(pParam, "============== DEC POWER ==============\r\n");
            for(i = 0; i < NVR_SGW_MAX_CHN_AUD_NUM * NVR_SGW_MAX_CHIP_CHN_NUM; i++)
            {
                snprintf(abyBufBak, sizeof(abyBufBak), "INDEX %d %d\r\n", tPowerInfo.atDecPower[i].s32DecId, tPowerInfo.atDecPower[i].s32Power);
                strcat(pParam, abyBufBak);
            }
            pFileHandle = fopen("/tmp/sysinfo", "r");
            if(pFileHandle)
            {
                while(fgets(abyBufBak, sizeof(abyBufBak), pFileHandle))
                {
                    s8 *token = strstr(abyBufBak, "INDEX ");
                    if(token)
                    {
                        strcat(pParam, token);
                    }
                }
                fclose(pFileHandle);
                pFileHandle = NULL;
            }
            SGWMEMNOTICE("mc_power:\n%s\n", pParam);
        }break;
        default:
        {
            sprintf((s8*)pParam, "Param Error\r\n");
        }break;
    }
    return NVR_ERR__OK;
}

NVRSTATUS NvrSgwDecoderInit()
{
    NVRSTATUS eRet = NVR_ERR__OK;
    TNvrCapRtmpChnList tRtmpChnList;
    mzero(tRtmpChnList);
    NvrCapGetCapParam(NVR_CAP_ID_RTMP_CHNLIST, (void *)&tRtmpChnList);
    g_sgw_max_chn_num = tRtmpChnList.wDecChnNum;

    SGWPRINTIMP("decoder init begin ...\n");

    if( !OsApi_SemBCreate(&g_hSdscpNotifySem) )
    {
        SGWPRINTERR("create g_hSdscpNotifySem failed\n");
        return NVR_ERR__ERROR;
    }
    OsApi_SemTake(g_hSdscpNotifySem);

    pthread_mutex_init(&mediactrl_set_lock, NULL);
    pthread_mutex_init(&video_set_lock, NULL);
    pthread_mutex_init(&audio_set_lock, NULL);
    pthread_mutex_init(&rtmp_task_lock, NULL);
    pthread_mutex_init(&sdscp_task_lock, NULL);

    NvrSgwDecSdscpRcvTimeOutCustom(0);

    OsApi_TimerNew(&g_hTaskRetryCheckTimer);

    mzero(g_tNvrSgwRtmpMgr);
    mzero(g_atNvrSgwRtmpTaskAddFlag);

    if(g_sgw_max_chn_num == 4)
    {
        NvrSgwMcaInit();
    }
    else
    {
        g_bMedaiInitSucc[1] = 0;
    }

    //同步从片系统时间
    TNvrSgwSysTime tTime;
    gettimeofday(&tTime.tv, &tTime.tz);
    NvrSgwMcaCmdSend(CMD_NVR_MCA_SETTIME_SYNC, (u8*)&tTime, sizeof(tTime));

    //检查从片E2
    NvrSgwDecUpdateE2();

    NvrSgwUpgradeCheck();

    NvrSgwCfgInit();

    NvrSgwDecoderParamInit();

    NvrSgwDecoderSubChipMediaCtrlInit();

    NvrSgwDecoderMediaCtrlInit();

    NvrVtduSetSdscpCB(NvrSgwSdscpDecOptCB);

    NvrVtduSetSgwOptCB(NvrSgwDecExtOpt);

    NvrSgwMediaCtrlMosicAdjust();

    NvrSgwMediaDecoderInterfaceInit();

    NvrSgwResBitMapInit();

    NvrQueueCreate(&g_ptNvrSgwQueue);
    //创建任务处理线程
    if ((TASKHANDLE)NULL == OsApi_TaskCreate((void*)NvrSgwDealOptTask, "nvrSgwDeal", NVR_TASK_COMMON_PRIORITY, 1024<<10, 0, 0, NULL))
    {
        SGWFLASHERR("NvrSgwDealOptTask create failed\n");
        return NVR_ERR__ERROR;
    }

    if ((TASKHANDLE)NULL == OsApi_TaskCreate((void*)NvrSgwSdscpRetrytTask, "SgwRetryDeal", NVR_TASK_COMMON_PRIORITY, 1024<<10, 0, 0, NULL))
    {
        SGWFLASHERR("NvrSgwSdscpRetrytTask create failed\n");
        return NVR_ERR__ERROR;
    }

    OsApi_RegCommand( "decinfo", (void *)NvrSgwShowdecInfo, "show dec info");
    OsApi_RegCommandEx( "decmosic", (void *)NvrSgwShowdecmosic, "show dec mosic info", "iis");
    OsApi_RegCommandEx( "decsync", (void *)NvrSgwShowdecSync, "show av syncinfo", "iis");
    OsApi_RegCommandEx( "decsave", (void *)NvrSgwDecRecvFrameSave, "save recv frame ", "iis");
    OsApi_RegCommandEx( "decsadd", (void *)NvrSgwDecStreamAdd, "add rtsp msout ", "iiii");
    OsApi_RegCommandEx( "decsdel", (void *)NvrSgwDecStreamDel, "del rtsp msout ", "iii");
    OsApi_RegCommandEx( "decstyle", (void *)NvrSgwDecStyle, "set dec style ", "iiii");
    OsApi_RegCommandEx( "deccbi", (void *)NvrSgwDecMscbInterval, "set dec ms cb interval ", "iiii");
    OsApi_RegCommand( "decmb", (void *)NvrSgwDecSetMcaBuf, "set g_bUseMcaBuf ");
    OsApi_RegCommand( "decmcp", (void *)NvrSgwDecSetMediaPrint, "set g_bMediaPrintEnable ");
    OsApi_RegCommandEx( "decrto", (void *)NvrSgwDecStratOneRtmpTask, "dec rtmp test ", "s");
    OsApi_RegCommandEx( "decrtb", (void *)NvrSgwDecRtmpTest, "dec rtmp test ", "iiiis");
    OsApi_RegCommandEx( "decrts", (void *)NvrSgwDecRtmpStop, "dec rtmp stop ", "s");
    OsApi_RegCommand( "decrta", (void *)NvrSgwDecRtmpAuto, "dec rtmp audo ");
    OsApi_RegCommand( "decrte", (void *)NvrSgwDecStopAllRtmp, "dec rtmp stop all");
    OsApi_RegCommandEx( "decrtav", (void *)NvrSgwDecRtmpSyncTest, "dec rtmp pluu aud vid all", "iiiiis");
    OsApi_RegCommandEx( "deccc", (void *)NvrSgwDecDevClear, "dec clear win ", "ii");
    OsApi_RegCommandEx( "decdbg", (void *)NvrSgwDecDbgLevel, "dec set cb sned level ", "ii");
    OsApi_RegCommandEx( "decsaveav", (void *)NvrSgwDecSaveAv, "dec save av stream ", "iii");
    OsApi_RegCommandEx( "decmode", (void *)NvrSgwSetDecMode, "dec set dec mode ", "ii");
    OsApi_RegCommandEx( "decmcret", (void *)NvrSgwSetMcInitret, "dec set mc init ret ", "iii");
    OsApi_RegCommandEx( "decrtest", (void *)NvrSgwDecAutoTimer, "dec auto test ", "i");
    OsApi_RegCommand( "decmest", (void *)NvrSgwDecGetMedaiStat, "dec get media stat");
    OsApi_RegCommandEx( "decst", (void *)NvrSgwDecTaskStatus, "dec task status ", "iis");
    OsApi_RegCommandEx( "sgwsys", (void *)NvrSgwDecSysinfo, "cmd  ", "s");
    OsApi_RegCommandEx( "decre", (void *)NvrSgwDecReset, "cmd vid redec ", "iii");
    OsApi_RegCommandEx( "decmsg", (void *)NvrSgwDecSdscpMsg, "cmd layoutcmd msg", "iii");
    OsApi_RegCommandEx( "decdump", (void *)NvrSgwDecVoChnDump, "vo_chn_dump layer chn cnt", "iii");
    OsApi_RegCommandEx( "sdscptest", (void *)SgwDecSdscpTestSW, "sdscptest", "s");
    OsApi_RegCommand( "decchip", NvrSgwDecSetOnlyChip, "dec set chip enable(0-RC 1-EP other-BOTH)");
    OsApi_RegCommand( "decshowmosic", NvrSgwMediaCtrlShowChnMosicParam, "dec set chip enable(0-RC 1-EP other-BOTH)");
    OsApi_RegCommandEx( "dectmout", NvrSgwDecSdscpRcvTimeOutCustom, "dec set layout sync timeout time (ms)", "i");
    OsApi_RegCommand( "getchn", (void *)NvrSgwDecGetChnList, "dec get chn list info");
    return eRet;
}

void NvrSgwDecVoChnDump(u8 byLayerId, u8 byChnId,u8 byNum)
{
    s8 abyCmd[NVR_MAX_STR512_LEN];
    u32 dwWidth = 0, dwHeight = 0, dwLayerId, dwChnId;

    if(byLayerId >= NVR_SGW_MAX_CHN_NUM || byChnId >= 16 || byNum <= 0 || byNum > 100)
    {
        OspPrintf(1,0,"SgwDecVoDump error,byLayerId %u, byChnId %u, byNum %u\n", byLayerId, byChnId, byNum);
        return;
    }

    memset(abyCmd,0,NVR_MAX_STR512_LEN);

    sprintf(abyCmd, "rm -rf /tmp/vo%u_%u.yuv", byLayerId, byChnId);
    NvrSystem(abyCmd);

    OspPrintf(1,0,"SgwDecVoDump byLayerId %u, byChnId %u,  byNum %u\n",byLayerId, byChnId, byNum);

    if(byLayerId < NVR_SGW_MAX_CHIP_CHN_NUM)
    {
        sprintf(abyCmd, "cp /usr/bin/tools/vo_chn_dump /tmp;cd /tmp;./vo_chn_dump %u %u %u", byLayerId, byChnId, byNum);
        NvrSystem(abyCmd);
        OspPrintf(1,0,"SgwDecVoDump abyCmd1 %s\n",abyCmd);

        //
        memset(abyCmd,0,NVR_MAX_STR512_LEN);
        memcpy(abyCmd, "/tmp", 4);
         DIR *dir;
        struct dirent *ptr;
        if((dir=opendir(abyCmd)) == NULL)
        {
            OspPrintf(1,0,"Open dir:%s error...\n",abyCmd);
        }
        else
        {
            while ((ptr=readdir(dir)) != NULL)
            {
                OspPrintf(1,0,"file name:  %s\n", ptr->d_name);
                if(strncmp(ptr->d_name,"vo_layer", 8)==0)
                {
                    sscanf(ptr->d_name, "vo_layer%u_chn%u_%dx%d_p", &dwLayerId, &dwChnId, &dwWidth, &dwHeight);
                    break;
                }
            }
            closedir(dir);
        }

        memset(abyCmd,0,NVR_MAX_STR512_LEN);
        sprintf(abyCmd, "mv /tmp/vo_layer*.yuv /tmp/vo%u_%u_%dx%d.yuv", byLayerId, byChnId, dwWidth, dwHeight);
        NvrSystem(abyCmd);
        OspPrintf(1,0,"SgwEncDump abyCmd2 %s\n",abyCmd);
    }
    else
    {
        u32 dwdata = 0;
        dwdata = (byLayerId << 8) | (byChnId << 4) | byNum;
        s32 nRet = NvrSgwMcaCmdSend(CMD_NVR_MCA_DEC_VO_DUMP, (u8*)&dwdata, sizeof(u32));
        if( nRet != 0)
        {
            printf("McaWriteCmdmsg nRet:%d\n",nRet );
        }
    }
}

void NvrSgwDecRtmpSyncTest(u16 wChnId, u16 wEncId, u16 wAChnId, u16 wAEncId, u16 wAV, s8 *pchUrl)
{
    TSdscpProtoReqStartRtmpTask tTask;
    mzero(tTask);

    snprintf(tTask.achTaskID, SDSCPPRPTO_RTMP_TASK_ID_BUF_LEN, "pull%u%u", wChnId, wEncId);
    tTask.eAction = SDSCPPROTO_RTMP_TASK_ACTION_PULL;
    tTask.bPullVid = wAV / 10;
    tTask.tPullVidID.dwChnId = wChnId;
    tTask.tPullVidID.dwXxxID = wEncId;
    tTask.bPullAud = wAV % 10;
    tTask.tPullAudID.dwChnId = wAChnId;
    tTask.tPullAudID.dwXxxID = wAEncId;

    snprintf(tTask.achRtmpUrl, SDSCPPROTO_RTMP_URL_BUF_LEN, "%s", pchUrl);

    SdscpAppCoreStartRtmpTask(&tTask);

}

extern BOOL32 g_byManualPullRtmpTest;
void NvrSgwDecRtmpTest(u16 wChnId, u16 wEncId, BOOL32 bPush, BOOL32 bAudio, s8 *pchUrl)
{
    static u32 dwTaskCnt = 0;
    if(bAudio && wEncId >=8)
    {
        NVRVTDUIMP(3, "Error: Audio EncId must less than 8!\n");
        return;
    }

    TSdscpProtoReqStartRtmpTask tTask;
    mzero(tTask);
    if(bPush)
    {
        snprintf(tTask.achTaskID, SDSCPPRPTO_RTMP_TASK_ID_BUF_LEN, "push%u%u", wChnId, wEncId);
        tTask.eAction = SDSCPPROTO_RTMP_TASK_ACTION_PUSH;
        tTask.bPushVid = TRUE;
        tTask.tPushVidID.dwChnId = wChnId;
        tTask.tPushVidID.dwXxxID = wEncId;
        tTask.bPushAud = bAudio;
        tTask.tPushAudID.dwChnId = wChnId;
        tTask.tPushAudID.dwXxxID = wEncId;
    }
    else
    {
        snprintf(tTask.achTaskID, SDSCPPRPTO_RTMP_TASK_ID_BUF_LEN, "pull%u%u_%u", wChnId, wEncId, dwTaskCnt++);
        tTask.eAction = SDSCPPROTO_RTMP_TASK_ACTION_PULL;
        tTask.bPullVid = TRUE;
        tTask.tPullVidID.dwChnId = wChnId;
        tTask.tPullVidID.dwXxxID = wEncId;
        tTask.bPullAud = bAudio;
        tTask.tPullAudID.dwChnId = wChnId;
        tTask.tPullAudID.dwXxxID = wEncId;
        //g_byManualPullRtmpTest = 1;
    }
    snprintf(tTask.achRtmpUrl, SDSCPPROTO_RTMP_URL_BUF_LEN, "%s", pchUrl);

    SdscpAppCoreStartRtmpTask(&tTask);

}

void NvrSgwDecRtmpStop(s8 *pchTaskId)
{
    TSdscpProtoReqStopRtmpTask tTask;
    mzero(tTask);

    snprintf(tTask.achTaskID,SDSCPPRPTO_RTMP_TASK_ID_BUF_LEN,"%s", pchTaskId);
    SdscpAppCoreStopRtmpTask(&tTask);
}

void NvrSgwDecRtmpAuto()
{
    FILE *fp;
    char buf[1024];
    s32 i = 0, j = 0, k = 0;;
    s32 nAuto = 0;
    s32 wChnId[2] = {0};
    s32 wEncId[2] = {0};
    BOOL32 bPush = 0;
    BOOL32 bAudio = 0;
    BOOL32 bStyleAdjust = 0;
    s8 chUrl[10][128] = {0};
    BOOL32 bDoList = 0;
    s32 nAudCount = 0;
    BOOL32 bAudioIn = 0;

    fp = fopen("/usr/bin/rtmp_test.list", "r");
    if (fp == NULL)
    {
        printf("open /usr/bin/rtmp_test.list file error\n");
        NVRVTDUIMP(3, "open /usr/bin/rtmp_test.list file error\n");
        return;
    }

    fgets (buf, 1024, fp);
    mzero(buf);
    fgets (buf, 1024, fp);
    SGWPRINTIMP("get buf:%s", buf);
    //2:0:0:0:2:1:1:1
    sscanf(buf, "%d:%d:%d:%d:%d:%d:%d:%d", &nAuto, &wChnId[0], &wChnId[1], &wEncId[0], &wEncId[1], &bPush, &bAudio, &bStyleAdjust);

    SGWPRINTIMP("auto info: num:%d chnid:%d-%d enc:%d-%d push:%d audio:%d bStyleAdjust:%d\n",
            nAuto, wChnId[0], wChnId[1], wEncId[0], wEncId[1], bPush, bAudio, bStyleAdjust);

    if(bStyleAdjust)
    {
        if (wEncId[1] >= g_tNvrSgwDecMgr.atMosaicParam[i].dwNumberOfWindows)
        {
            wEncId[1] = g_tNvrSgwDecMgr.atMosaicParam[i].dwNumberOfWindows - 1;
        }
    }

    mzero(buf);
    if (nAuto)
    {
        for (i = 0; i < nAuto; i++)
        {
            fgets (buf, 1024, fp);
            sscanf(buf, "%s", chUrl[i]);
            mzero(buf);
        }

        for (i = wChnId[0]; i <= wChnId[1]; i++)
        {
            if(bStyleAdjust)
            {
                wEncId[1] = wEncId[1] > (g_tNvrSgwDecMgr.atMosaicParam[i].dwNumberOfWindows - 1) ?
                        (g_tNvrSgwDecMgr.atMosaicParam[i].dwNumberOfWindows - 1) : wEncId[1];
            }
            nAudCount = 0;
            for (j = wEncId[0]; j <= wEncId[1]; j++)
            {
                if (bAudio)
                {
                    bAudioIn = nAudCount < NVR_SGW_MAX_CHN_AUD_NUM ? 1 : 0;
                    nAudCount++;
                }
                SGWPRINTIMP(" == rtmpstart %d %d %d %d %s \n", i, j, bPush, bAudioIn, chUrl[k]);
                NvrSgwDecRtmpTest(i, j, bPush, bAudioIn, chUrl[k]);
                k++;
                k = k % nAuto;
            }
        }
    }
    else
    {
        while (fgets(buf, 1024, fp) != NULL)
        {
            if (strstr(buf, "use"))
            {
                bDoList = TRUE;
            }

            if (bDoList && !strstr(buf, "use"))
            {
                //2:0:0"0:rtmp://10.67.73.151:1935/live/stream
                sscanf(buf, "%d:%d:%d:%d:%s", wChnId, wEncId, &bPush, &bAudio, chUrl[0]);
                SGWPRINTIMP("get info: %d %d %d %d %s \n", wChnId[0], wEncId[0], bPush, bAudio, chUrl[0]);
                NvrSgwDecRtmpTest(wChnId[0], wEncId[0], bPush, bAudio, chUrl[0]);
            }

            mzero(buf);
        }
    }

    fclose(fp);

    SGWPRINTIMP("start do over \n");
}

void NvrSgwDecStratOneRtmpTask(s8 *chUrl)
{
    s32 i = 0, j = 0;
    s32 nVidId = -1, nAudId = -1;
    s32 bAud = 0;

    for (i = 0; i < NVR_SGW_MAX_CHN_NUM; i++)
    {
        for (j = 0; j < NVR_SGW_MAX_CHN_VID_NUM; j++)
        {
            if (!g_tNvrSgwDecMgr.aatVidDecInfo[i][j].byValid)
            {
                nVidId = j;
                break;
            }
        }
        if (j < 0)
        {
            continue;
        }

        for (j = NVR_SGW_MAX_CHN_AUD_NUM - 1; j >= 0; j--)
        {
            if (!g_tNvrSgwDecMgr.aatAudDecInfo[i][j].byValid)
            {
                nAudId = j;
                bAud = 1;
                break;
            }
        }

        if (nVidId >= 0)
        {
            if (chUrl)
            {
                TSdscpProtoReqStartRtmpTask tTask;
                mzero(tTask);
                {
                    snprintf(tTask.achTaskID, SDSCPPRPTO_RTMP_TASK_ID_BUF_LEN, "pull%d%d%d", i, nVidId, nAudId);
                    tTask.eAction = SDSCPPROTO_RTMP_TASK_ACTION_PULL;
                    tTask.bPullVid = TRUE;
                    tTask.tPullVidID.dwChnId = i;
                    tTask.tPullVidID.dwXxxID = nVidId;
                    tTask.bPullAud = bAud;
                    tTask.tPullAudID.dwChnId = bAud ? i : 0;
                    tTask.tPullAudID.dwXxxID = bAud ? nAudId : 0;
                }
                snprintf(tTask.achRtmpUrl, SDSCPPROTO_RTMP_URL_BUF_LEN, "%s", chUrl);

                SdscpAppCoreStartRtmpTask(&tTask);
                return ;
            }
        }
    }

}
void NvrSgwDecStopAllRtmp()
{
    SGWPRINTIMP("begin do \n");
    SdscpAppCoreStopAllRtmpTask();
    SGWPRINTIMP("do over \n");
}

//生产测试时,编解码器模拟sdscp服务--client
#define SDSCPTESTPORT 19130

static HTIMERHANDLE g_hSdscpTestRcvTimer[4][16] = {NULL};
static pthread_mutex_t  SdscpTest_lock;
static TASKHANDLE g_hFrameDataTask = NULL;

static void *SgwDecSdscpTestRecv()
{
    s32 nRet = 0;
    TSdscpProtoReqStartRtmpTask tStartTask;
    u32 dwDataLen = sizeof(TSdscpProtoReqStartRtmpTask);

    while(TRUE)
    {
        if(g_dwSdscpTestClientFd == -1)
        {
            break;
        }
        nRet = recv(g_dwSdscpTestClientFd, &tStartTask, dwDataLen, 0);
        if(dwDataLen == nRet)
            NvrSgwSdscpDecOptCB(SDSCPPROTO_CMD_TYPE_SET_LAYOUT_ENC_PARAM, (void *)&tStartTask, dwDataLen, NULL);
        else if(nRet == 0)
        {
            SGWPRINTERR("[line:%d]recv error: %d!\n", __LINE__, nRet);
            break;
        }
    }
    g_hFrameDataTask = NULL;
    return NULL;
}

static void _SgwDecSdscpTestSend(HTIMERHANDLE dwTimerId, void* param)
{
    //pthread_mutex_lock(&SdscpTest_lock);
    s32 nRet;
    u32 dwBufLen = sizeof(u32) + sizeof(ESdscpProtoCmdType) + sizeof(TSdscpProtoReqSetLayOutEncParam) + sizeof(u32);
    u8 *abyBuf = (u8 *)param;
    nRet = send(g_dwSdscpTestClientFd, abyBuf, dwBufLen, 0);
    if(nRet != dwBufLen)
    {
        SGWPRINTERR("[line:%d]send failed!\n", __LINE__);
        return ;
    }

    usleep(10*1000);
    NVRFREE(abyBuf);
    pthread_mutex_unlock(&SdscpTest_lock);
}

static s32 SgwDecSdscpTestSend(ESdscpProtoCmdType eEventType, void* pEventData, u32 dwEventDataLen, u32 *dwMsgId)
{
    //pthread_mutex_lock(&SdscpTest_lock);
    s32 nRet = 0;
    u32 dwBufLen;
    u8 *abyBuf = NULL;
    static u32 dwMsgId_s;
    u32 dwChnId, dwEncId;
    u32 dwDelayTimMs;
    TSdscpProtoReqSetLayOutEncParam *ptEncParam = (TSdscpProtoReqSetLayOutEncParam *)pEventData;
    dwChnId = ptEncParam->aParamArray[0].byChnVidId;
    dwEncId = ptEncParam->aParamArray[0].bysubChnVidId;

    dwBufLen = sizeof(dwEventDataLen) + sizeof(eEventType) + dwEventDataLen + sizeof(u32);
    abyBuf = (u8*)NVRALLOC(dwBufLen);
    if(abyBuf == NULL)
    {
        SGWPRINTERR("[line:%d]NVRALLOC failed!\n", __LINE__);
        return -1;
    }

    *dwMsgId = ++dwMsgId_s;
    //组装消息体
    memcpy(abyBuf, &dwEventDataLen, sizeof(dwEventDataLen));
    memcpy(abyBuf + sizeof(dwEventDataLen), &eEventType, sizeof(eEventType));
    memcpy(abyBuf + sizeof(dwEventDataLen) + sizeof(eEventType), pEventData, dwEventDataLen);
    memcpy(abyBuf + sizeof(dwEventDataLen) + sizeof(eEventType) + dwEventDataLen, &dwMsgId_s, sizeof(u32));

    srand(dwMsgId_s);
    dwDelayTimMs = rand() % 51 + 1;  ///<1-51 ms
    FILE *fp = fopen("/usr/config/delay", "r");
    if(fp)
    {
        fscanf(fp, "%d ms", &dwDelayTimMs);
        fclose(fp);
    }
    dwDelayTimMs = dwDelayTimMs == 0 ? 1 : dwDelayTimMs;
    pthread_mutex_lock(&SdscpTest_lock);
    OspTimerSet(g_hSdscpTestRcvTimer[dwChnId][dwEncId], dwDelayTimMs, (void*)_SgwDecSdscpTestSend, (void *)abyBuf);
    return nRet;
}

static s32 SgwDecSdscpTestCon(s8 *pParam)
{
    s32 dwFd = -1;
    struct sockaddr_in seraddr;

    bzero(&seraddr,sizeof(seraddr));

    dwFd = socket(AF_INET,SOCK_STREAM, 0);
    if(dwFd < 0)
    {
        SGWPRINTERR("[line:%d]socket error: %s\n", __LINE__, strerror(errno));
        return dwFd;
    }
    
    //s32 dwFlags = 0;
    //dwFlags = fcntl(dwFd, F_GETFL, 0);
    //dwFlags |= O_NONBLOCK;
    //fcntl(dwFd, F_SETFL, dwFlags);  //设置为非阻塞模式

    seraddr.sin_family = AF_INET;
    seraddr.sin_port = htons(SDSCPTESTPORT);
    inet_pton(AF_INET, (LPCSTR)pParam, &seraddr.sin_addr);
    if(connect(dwFd, (struct sockaddr *)&seraddr, sizeof(seraddr))==-1)
    {
        SGWPRINTERR("connect() error: %s\n", strerror(errno));
        close(dwFd);
        dwFd = -1;
    }
    else
    {
        //创建接收Sdscptest数据线程
        g_hFrameDataTask = OspTaskCreate( SgwDecSdscpTestRecv, "gSdscpRcvData", 110 ,1<<20 , 0 , 0 , NULL);
        if ( NULL == g_hFrameDataTask)
        {
            SGWPRINTERR("OspTaskCreate gSdscpRcvData failed !\n");
            return NVR_ERR__ERROR;
        }
    }

    return dwFd;
}

static void SgwDecSdscpTestStart(void *pParam)
{
    static s8 abyIp[16];
    u8 dwChnId, dwEncId;

    if(pParam && g_dwSdscpTestClientFd == -1)
    {
        sprintf(abyIp, "%s", (s8*)pParam);
        SGWPRINTERR("server IP: %s\n", abyIp);
        g_dwSdscpTestClientFd = SgwDecSdscpTestCon(abyIp);
    }

    for(dwChnId = 0; dwChnId < 4; dwChnId++)
    {
        for(dwEncId = 0; dwEncId < 16; dwEncId++)
        {
            if (0 != OspTimerNew(&g_hSdscpTestRcvTimer[dwChnId][dwEncId]))
            {
                SGWPRINTERR(" create g_hSdscpTestRcvTimer failed\n");
            }
        }
    }
    pthread_mutex_init(&SdscpTest_lock, NULL);
}

static void SgwDecSdscpTestStop()
{
    if(g_dwSdscpTestClientFd != -1)
    {
        close(g_dwSdscpTestClientFd);
        g_dwSdscpTestClientFd = -1;
        if(g_hFrameDataTask)
        {
            OspTaskTerminate(g_hFrameDataTask);
            g_hFrameDataTask = NULL;
        }
        pthread_mutex_destroy(&SdscpTest_lock);
    }
}

static void SgwDecSdscpTestSW(u8 *p)
{
    if(p)
    {
        SgwDecSdscpTestStart(p);
    }
    else
    {
        SgwDecSdscpTestStop();
    }
}

