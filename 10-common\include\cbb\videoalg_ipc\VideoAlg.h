#ifndef _VIDEO_ALG_H_
#define _VIDEO_ALG_H_

#include "kdvtype.h"

#ifdef __cplusplus
extern "C" {
#endif


#ifdef  __WINDOWS__
#define _PLATFORM_PREFIX_ __declspec(dllexport)
#else
#define _PLATFORM_PREFIX_ 
#endif



//#######################################################################################################################
//    所有项目公共的定义, 不能改
//  新项目在 <函数调用信息返回码> 和 <算法类型> 后面继续添加
//
//
//#######################################################################################################################

#define MAX_IMAGE_INFO 50   // 输入图像最大帧数
#define MAX_DETECT_INFO 30  // 人脸检测输出最大人脸个数
#define MAX_MODEL_NUM 50    // 加载模型的最大个数
#define TARGET_DETECTED_MAXNUM 40
#define MULTI_DETECTED_MAXNUM 100
#define MAX_HUMAN_DETECT_INFO 64  // 人形检测输出最大人形个数
#define ABNORM_OBJECT_MAXNUM 10        //场景最大异常目标个数
#define MAX_TRIPLINE_DETECT_NUM 4       //绊线最大个数
// 人脸去重：哈希匹配算法
#define HASHSIZE 16          // 哈希匹配算法中人脸图像缩放后的大小16x16
#define MAXHASH (HASHSIZE*HASHSIZE*3)   // 哈希匹配算法中匹配3通道的图像
#define MAXHASH_SINGLECHANNEL (HASHSIZE*HASHSIZE)  // 哈希匹配算法中匹配1通道的图像
#define FEATURE_SIZE_PER_IMAGE_MENJIN 128*4
#define FEATURE_SIZE_PER_IMAGE_MENJIN28 640*4
#define FEATURE_DIMENSION_V2_MENJIN 512*4
#define FEATURE_DIMENSION 128
#define FEATURE_DIMENSION_V2 256
#define DETECTAREA_MAXNUM 4                //绘制区域最大个数

// 函数调用信息返回码
typedef enum tagIMGRetCode
{
    SUCCESS_GPUALG = 0,
    ERR_ALGORITHM_TYPE,
    ERR_FORMAT_TYPE,
    ERR_INIT_DEVICE,
    ERR_CREATE_HOST_BUFFER,
    ERR_CREATE_GPU_BUFFER,
    ERR_CREATE_HANDLE,
    ERR_CREATE_KERNELS,
    ERR_MAP_IMAGE,
    ERR_UNMAP_IMAGE,
    ERR_SET_PARAMETERS,
    ERR_PROCESS_KERNELS,
    ERR_RELEASE_RESOURCE,
    ERR_ALGORITHM_INIT,
    ERR_BIN_OPEN,
    ERR_JPEG_ERR,
    ERR_KEDA_VIDA_INIT,
    ERR_LOAD_MODEL,
    ERR_IMG_DEFOUCS,
    ERR_INPUT_BUF_NULL,
    ERR_INPUT_WIDTH_HEIGHT,
    ERR_SNPE_EXECUTE_FALSE,
    ERR_CREATE_SNPE_TENSOR_BUFFER,
    ERR_SNPE_CONTAINER,
    ERR_SNPE_BUILDER,
    ERR_NO_SUCH_TYPE,
    ERR_SNPE_RUNTIME_UNSUPPORT,
    ERR_KEDA_VIDA_ERR,
    ERR_PARAM_INVALID,
    ERR_ALG_HANDLE,
}EMIMGRetCode;


// 算法类型
typedef enum tagIMGAlgType
{
    BARRELCORRECTION = 0,           // 畸变校正
    LOWLIGHTDENOISE,                // 3D降噪
    CARMODEL,                        // 车型识别
    CARCOLOR,                        // 车颜色识别
    DETECTION,                        // 检测相关
    CARPLATEDETECT,                 // 车牌检测
    CARPLATERECOG,                  // 车牌识别
    MANDETECT,                      // 人脸检测(自动)
    MANUALMANDETECT,                // 人脸检测(手动)
    ISPADJUST,                        // 根据人脸亮度申请ISP调节
    CARPLATEDLDETECT,               // 深度学习车牌检测
    VIVODETECT,                     // 活体检测
    MANDETECTFASTGRAB,
    MANDETECTTRACK,                 // 人脸检测跟踪
    MANFACECOMPAR,                    // 人脸比对
    MANFACEFEATURE,                    // 人脸特征提取
    JPEGTONV12,                        //jpeg解码器
    MOVINGOBJDETECT,
    PERSONCOUNT,                    // 精确人数统计
    PERSONCNT,                        // 快速精确人数统计
    MANUALMANDETECT_DSP,
    MANDETECT_DSP,
    CARPLATEDLDETECT_TF,
    CARPLATEDLDETECT_BAHRAIN,        //巴林车牌检测
    JPEGRESIZE,
    CARPLATERECOG_BAHRAIN,          //巴林车牌识别
    CARPLATEDLDETECT_DSP,           //车牌检测（DSP）
    CARPLATEDLDETECT_POINT,         //车牌检测（关键点）
    MANFACEFEATURE_DSP,
    CARPLATERECOG_CLS,             //车牌分类识别
    CARPLATERECOG_LITTLE,          //车牌识别(小模型)
    MANDETECTTRACK_NNIE,            //NNIE_人脸检测
    MANUALMANDETECT_NNIE_Moon50U,   //MOON50U人脸检测
    HUMANDETECT_NNIE_Moon50U,               //人形检测
    MANFACEFEATURE_NNIE_Moon50U,    //MOON50U人脸识别
    KEYPOINTDETECT_NNIE_Moon50U,
    HUMANDETETCT_TL,                //NNIE_交通信号灯立柱行人检测项目
    MANUALMANDETECT_NNIE_SVR,
    VIVODETECT_DSP,
    MANUALMANDETECT_TL,             //NNIE_交通信号灯立柱项目人脸检测
    CARUMBRELLA_CLS,                //是否加装伞架
    MANUALMANDETECT_NNIE_Moon50U_4K,//MOON50U 4K人脸检测
    MULTIDETECT_NNIE_IPC6X5,      //第五代智能ipc----多目标检测
    MANDETECT_BUS,                // 行车记录仪_公交车
    CARPARKING_DETECT,           //违停车检
    MANUALMANDETECTTRACK_NNIE_Moon50U, //视讯人脸检测追踪
    CARPLATEDLDETECT_NNIE_CS,
    CARKEYPOINTDETECT_NNIE_CS,
    DARKENHANCE_NNIE_CAR,      //低照增强-车辆卡口 分辨率支持为 4096*2176
    CARUMBRELLA_NNIE,           //伞具加装
    CARPLATEDLDETECT_NNIE,      // 全车牌检测
    CARKEYPOINTDETECT_NNIE,     //车牌角点回归
    CARKPLATECLASS_NNIE,        //车牌分类
    CARPLATERECOG_ONELINE_BGRHWC, //单行车牌识别---输入pattern为BGRHWC(160*48)
    CARPLATERECOG_TWOLINE_BGRHWC, //双行车牌识别---输入pattern为BGRHWC(128*64)
    HUMANDETECT_NNIE_SVR_InquiryRoom,           //审讯留置室人形检测
    TWOCLASSIFICATION_NNIE_4GMONITORBALL,    // 4G球二分类
    MANUALMANDETECT_NNIE_VAC,       // 900W车卡 人脸检测
    MANFACEFEATURE_NNIE_VAC,        // 900W车卡 特征提取
    KEYPOINTDETECT_NNIE_VAC,        // 900W车卡 关键点
    CARPLATEDLDETECT_NNIE_NV21,    // 4G球车牌检测NNIE实现
    CARKEYPOINTDETECT_NNIE_NV21,   // 4G球车牌回归NNIE实现
    MANUALMANDETECT_NNIE_Moon50U_MULTIRESOLUTION,
    MANUALMANDETECT_NNIE_K5_FisheyeLens,  //鱼眼人脸检测
    VIVODETECT_NNIE,                     //NNIE活体检测
    INTELLIGENTTRACK_NNIE,      //单球跟踪
    MANUALMANDETECT_NNIE_K5_FisheyeLens_Library,  //鱼眼人脸检测识别--入库
    JPEGTONV21,                    // JPG转NV21格式
    DARKENHANCE_NNIE_MAN,       // 低照增强-人员卡口 分辨率支持参见具体项目结构体
    PEOPLE_COUNTING_K5_FISHEYE, // K5鱼眼人数统计
    HUMANDETECT,                     //油田场景人员检测
    MANFACEFEATURE_NNIE_Moon50U_4K,   ////MOON50U 4K人脸识别   
    KEYPOINTDETECT_NNIE_Moon50U_4K,    
    MANUALMANDETECT_NNIE_Moon50U_MULTIRESOLUTION_4K,
    CARPLATERECOG_NNIE,    // 车牌识别NNIE版本实现（mzp wjx 128*48）
    MANDETECT_PASS,      // 4G球离线布控——PAD端判断人脸是否符合入库要求
    UNSHARPMASK,        //反锐化掩模
    TWOCLASSIFICATION_NIGHT_NNIE_4GMONITORBALL,    // 4G球二分类夜间模式
    DARKENHANCE_NNIE_EPOLICE, //电警NNIE低照
    MANFACEFEATURE_NNIE_Moon50U_Track, //MOON50U 人脸识别-追踪版本
    CARPLATERECOG_CLS_NNIE, //4G球NNIE车牌分类识别模块
    CARPLATEDLDETECT_NNIE_INCAR,  // ITS_v3 1111 xiexu 车牌检测256*256 先检车再检车牌 FPN
    REDLIGHTDETECT_NNIE_ITS,  //交通信号灯描红
    CARPLATERAW2RGB, //车辆卡口-过曝车牌raw重建
    CARPLATEDLDETECT_CPU, //dsj_v2 caffe版本的车牌检测+角点回归
    CARPLATERECOG_TF,    // 车牌识别 tf 版本，大模型，不分单双行，128*48
    CARPLATERECOG_TF_SMALL,    // 车牌识别 tf 版本，小模型，不分单双行，128*48
    HELMETDETECT_NNIE,
    INFRARED_MEETING_ROOM_DETECT,    // 红外会议室感知人进入
    DARKENHANCENNIE_CAR_GRAY_GAUSS,  // 车卡电警使用NNIE计算灰度和高斯图模型
    MASKS_NNIE_4GMONITORBALL,   // 4G球口罩检测
    DARKENHANCE_NNIE_4Gball, //4G 球超微光
    DARKSMOKE_BG_NNIE,  // 黑烟车项目
    INTELLIGENTTRACKCAR_NNIE,//车辆跟踪
    CARPLATERECOGVIDA,//车牌识别-vida
    CARKPLATECLASSVIDA,//车牌分类-vida
    CARKEYPOINTDETECTVIDA,//车牌回归-vida
    CARPLATEDETECTVIDA,//车牌检测-vida
    DARKENHANCE_NNIE_VIDEO,//视讯超微光
    MANUALFACEDETECT_CLS,           //NNIE_交通信号灯立柱项目人脸二分类
    EYE_GAZE_MOON90H,            //视讯眼神交互
    MANUALMANDETECT_NNIE_MOON90H, // 网呈1080P方案
    MANFACEFEATURE_NNIE_MOON90H, //
    DARKENHANCE_CPU_VIDEO,        // 超微光视讯 CPU调用部分
    DARKENHANCE_GPU_VIDEO,        // 超微光视讯 GPU调用部分
    CARPLATERECOG_CLS_DSP,      //车牌分类和识别（DSP）
    MANUALMASKCLS,              //口罩和墨镜检测    
    MANUALMANDETECT_NNIE_NVR, //
    MANFACEFEATURE_NNIE_NVR, //
    MATTING_NNIE_VIDEO,        // MATTING视讯 NNIE调用部分
    MATTING_GPU_VIDEO,        // MATTING视讯 GPU调用部分
    MANUALFACEPOSE_TL,      //行人立柱人脸姿态分
    STANDUPDECT,                    //  起立检测
    MANUALMANDETECT_NNIE_MOON90H_4K, //  网呈4K方案
    THROWINGDETECT,  //抛洒物检测
    HELMETDET_NNIE, // 非机动车头盔检测
    FOGALARM_NNIE, //雾天能见度等级
    MANUALMANDETECT_NNIE_SVR_3531D,//3531D 人脸检测
    PERSONCOUNT_3531D,//3531D 人数检测
    COLORMASKCODEC,// 红绿灯描红主从板传输的mask编解码器
    POSE_DECTECT_3531D,//姿态检测
    ROBE_DECTECT_3531D,//法袍检测
    DEVIANT_DECTECT_3531D,//异常行为检测
    DEVIANT_DECTECT_CL_3531D,//异常行为分类
    MANNEDNONMOTOR_NNIE, //非机动车载人
    SID_AMBA, // amba 初始demo宏
    CARPLATEDETECT_AMBA,// amba 车牌检测demo
    CARKPLATECLASS_AMBA, // amba 车牌分类
    CARKPLATEALIGN_AMBA, // amba 车牌对齐
    CARPLATERECOG_AMBA, // amba 车牌识别
    HELMETDET_AMBA,// amba 非机动车头盔检测
    MANNEDNONMOTOR_AMBA,//amba 非机动车载人
    ANTIREFLECTION_NNIE,//  车窗去反射
    TRAFFICPLATE_DETECT_NNIE_ITS,  //交通灯灯牌检测
    VIDEO_DIAG_3531D,
    DARKENHANCE_CAR_AMBA,
    DARKENHANCE_EPOLICE_AMBA,
    MANUALMANDETECT_MOON51_AMBA, //amba 人脸检测
    MANFACEFEATURE_MOON51_AMBA, //amba 特征提取
    MANUALMANDETECT_MOON51_AMBA_4K,
    MANFACEFEATURE_MOON51_AMBA_4K,
    MANUALMANDETECTTRACK_MOON51_AMBA,//amba 人头检测
    HUMANDETECT_MOON51_AMBA,//amba 人形检测
    MULTIDETECT_AMBA,
    CARPLATEDLDETECT_INDONESIA_NNIE, //印尼车牌检测
    INDONESIACARPLATERECOG_NNIE, // 印尼车牌识别
    MANDETECT_AMBA,  //4G球人脸检测带跟踪
    MANFACEFEATURE_AMBA_4GBALL,  //4G球人脸检测识别
    MANUALMANTRACK_TL,      //行人跟踪
    SMALLDETECT_NNIE,
    ALARMTYPE_NNIE,
    BALINCARPLATEDETECT_NNIE, // 巴林车牌检测hisi
    BALINCARPLATECLS_NNIE, // 巴林车牌分类hisi
    BALINCARPLATERECOG_NNIE,//巴林车牌识别hisi
    UAECARDETECT,//阿联酋车辆检测(车载用)
    UAELPRRECOGCARBOX,//阿联酋车牌识别(车载用) 车辆box内检测并识别号牌
    CARPLATEDETECT_AMBA_4GBALL,  //4G球车牌检测(安霸)
    CARPLATERECOG_AMBA_4GBALL,   //4G球车牌识别(安霸) 
    MANUALMANDETECTTRACK_NNIE_Moon50U_4K,
    SUZHOUPLATERECOG_NNIE, // 苏州非机动车车牌识别
    SUZHOUPLATECLS_NNIE, // 苏州非机动车车牌分类
    SUZHOUPLATEDETECT_NNIE, // 苏州非机动车车牌检测
    SUZHOUPLATEALIGN_NNIE, // 苏州非机动车车牌对齐
    INTELLIGENTTRACKCAR,
    BALINCARKEYPOINTDETECT_NNIE, //巴林车牌对齐
    TRAFFICLIGHTDETECT,  //交通信号灯描色
    TRAFFICPLATEDETECT,  //交通灯牌检测
    UAELPRDET,  //阿联酋车牌检测
    UAELPRREG,  //阿联酋车牌识别
    CHN_LPRDET,   //国内车牌版本检测 不区分平台
    CHN_LPRALI,   //国内车牌版本对齐 不区分平台
    CHN_LPRCLS,   //国内车牌版本分类 不区分平台
    CHN_LPRREG,   //国内车牌版本识别 不区分平台
    SID_CAR,      //超微光车卡 不区分平台
    SID_EPOLICE,  //超微光电警 不区分平台
    SID_VIDEO,    //视频超微光 不区分平台
    SRR,
    TWOCLASSIFICATION_INTELLIGENTTRACK,    //教师跟踪二分类
    INTELLIGENTTRACK, //amba教师跟踪
    INDONESIA_MOTOR_LPRDET,   // 印度尼西亚摩托车车牌检测 不区分平台
    INDONESIA_MOTOR_LPRALI,   // 印度尼西亚摩托车车牌对齐 不区分平台
    INDONESIA_MOTOR_LPRCLS,   // 印度尼西亚摩托车车牌分类 不区分平台
    INDONESIA_MOTOR_LPRREG,   // 印度尼西亚摩托车车牌识别 不区分平台	
	MOTCARTRACK,              // 车辆多目标跟踪
}EMIMGAlgType;

//输入图像BGR格式像素内交错存放的图像数据
typedef enum tagImageFormat
{
    IMGALG_NV12 = 0,
    IMGALG_BGR,
    IMGALG_NV21,
}EMImageFormat;

// 版本等信息
typedef struct tagVersionInfo
{
    u8 u8AlgVersionInfo[200];      // 算法版本号
    u8 u8OpenclVersionInfo[200];   // OpenCL版本号
    u8 u8PlatformInfo[200];        // 平台信息
}TVersionInfo;


// 输入图像信息结构体
typedef struct tagImageBuffer
{
    u8* pu8ImageDataY;     // 输入/输出图像Y分量指针
    u8* pu8ImageDataU;     // 输入/输出图像U量指针，和图像格式相关，如果为NV12，则为UV分量指针
    u8* pu8ImageDataV;     // 输入/输出图像V分量指针，和图像格式相关，如果为NV12，则无效
    u32 u32Width;            // 图像宽度
    u32 u32Height;           // 图像高度
    u32 u32PitchY;           // 输入/输出图像Y分量跨度
    u32 u32PitchUV;          // 输入/输出图像UV分量跨度
    u32 u32Flag;             // 输出图像有效标识，1为有效，0为无效
    s32 fd;                           // ION buffer id
    EMImageFormat emFormat;           // 图像格式
    void* pvbuffertag;                  //与该TImageBuffer对应tag（每个TImageBuffer都唯一对应，用于释放）
}TImageBuffer;


// 输入输出图像信息结构体
typedef struct tagImageInfo
{
    TImageBuffer tImageBuffer[MAX_IMAGE_INFO];     // 图像数据，可能有多个
    void* pvImageInfo;                             // 和图像数据对应的信息
    u32 u32FreeBufferNumber;                          // 待free的TImageBuffer数目
    void* pvFreeTags[MAX_IMAGE_INFO];               // 待free的TImageBuffer对应的pvbuffertag组成的数组    
}TImageInfo;


typedef struct tagManDetectOpen //调用open接口时读取的模型bin文件
{
    EMImageFormat emFormat;
    s8 s8GPUBinPath[200];
    s8 s8ManDetectBinPath[MAX_MODEL_NUM][200];
    u32 u32SdCardFlag;      //0:不开打印，1：开启tf卡打印
    char  as8SdcardPath[400];
}TKEDAManDetectOpen;


typedef struct tagManmodelROI    //人员检测的范围（是否需要使用待确认）
{
    u32 u32RoiX;
    u32 u32RoiY;
    u32 u32RoiWidth;
    u32 u32RoiHeight;
    float platecls_threshold; // 获取业务设置的分类阈值
}TManDetectROI;    
    

typedef struct//输出人员检测结果对应的坐标信息
{
    s32 up;                
    s32 down;
    s32 left;
    s32 right;
}fbox;


typedef struct//检测人脸的关键点坐标
{
    float x[5];
    float y[5];
}ldm;

typedef struct//检测人脸的关键点坐标
{
    float x[12];
    float y[12];
}ldm12;

typedef struct tagScoreInfo
{
    float PoseScore;     // 姿态
    float OccScore;      // 遮挡
    float ExpScore;      // 表情
    float ImgBriScore;   // 亮度
    float ImgCstScore;  // 对比度
    float ImgNoiseScore; // 清晰度
}TScoreInfo;


typedef struct tagFaceInfo
{
    fbox bbox;        //  人脸边框
    ldm landmark;    //  关键点
    float prob;      //  概率
}StuFaceInfo;
    

typedef struct tagFaceDetectionInfo
{
    s32 FaceCount;            // 人脸个数
    StuFaceInfo FacePosi[MAX_IMAGE_INFO];      // 人脸信息
    TScoreInfo FaceScore[MAX_IMAGE_INFO];    // 人脸得分
}TFaceDetectionInfo;


typedef struct  //输出人员检测结果对应的TImageBuffer的tag以及对应坐标信息
{
    s32 up;
    s32 down;
    s32 left;
    s32 right;
    float score;
    void* pvbuffertag;
}TRectMan;
    
typedef struct tagManDetectOutput
{
       u32 u32ManDetectNumber;               // 输出检测出的人员数量
    TRectMan tManRectOut[MAX_DETECT_INFO];
}TManDetectOutputInfo;

/*=====================车牌检测=================================*/
//OPEN info for pvOpen in IMGVideoAlgOpen
// 车牌检测推荐区域（分为上中下和全图四种，其中上中下均约为原图的一半高度区域）
// 为手持设备新添加两种模式，分别为HANDHELD_FOCUS（图像中心：横向5/7，纵向5/7空间） 和 HANDHELD_W_5_7_H_3_5（图像中偏下局部区域:横向占5/7纵向占3/5空间）
// 根据测试需求增加：HANDHELD_FOCUS_PLUS在HANDHELD_FOCUS区域基础上，左右下三个方向各拓宽约50像素
// 需要说明的是上述除了FULL_PIC模式外其他均为1080P固定参数。FULL_PIC模式为全图，与输入分辨率无关
typedef enum tagCarPlateDetectRegion
{             
    DOWN=0,
    MIDDLE,                   
    UP,                   
    FULL_PIC,
    HANDHELD_FOCUS,
    HANDHELD_W_5_7_H_3_5,
    CUSTOM,
    HANDHELD_FOCUS_PLUS,
    CUSTOM_PIXEL_INPUT_PEOCESS,
    TF_HANDHELD,
}EMCarPlateDetectRegion;

/*仅配合上述CUSTOM模式使用，在自定义区间内进行车牌检测*/
/*自定义区间为经过下述裁剪后剩下的矩形区域*/
typedef struct
{
    s32 Height_CutUp;                // 高度方向，上部分被裁剪去掉的高度百分比/这里满像素对应100
    s32 Height_CutDown;                // 高度方向，下部分被裁剪去掉的高度百分比/这里满像素对应100
    s32 Width_CutLeft;                // 宽度方向，左侧部分被裁剪去掉的宽度百分比/这里满像素对应100
    s32 Width_CutRight;                // 宽度方向，右侧部分被裁剪去掉的宽度百分比/这里满像素对应100
}TRECT_CUSTOM;

typedef struct tagCarPlateDetectOpen
{
    u32 u32Prob;                   //用户设置置信度参数
    EMCarPlateDetectRegion emPlateRegion;    //车牌检测推荐区域
    TRECT_CUSTOM rectCustom;                //当且仅当EMCarPlateDetectRegion使用CUSTOM宏时该结构体才会被解析。

    /*add for deeplearning method*/
    EMImageFormat emFormat;
    s8 s8GPUBinPath[200];
    s8 s8CarPlateDLDetectBinPath[3][200];
    s32 nnie_id;
}TKEDACarPlateDetectOpen;

////////////////////////主要使用头盔检测包含多目标//////////////// add by xx 6/28
typedef enum tagHelmetMulTarget
{
    Helmet = 0,      //头盔
    NoHelmet,        //未带头盔

}EMHelmetMulTarget;

typedef struct  //输出检测结果对应的TImageBuffer的tag以及对应坐标信息
{
    s32 up;
    s32 down;
    s32 left;
    s32 right;
    float score;
    EMHelmetMulTarget MulTarget;
    void* pvbuffertag;
}TRectHelmetMultiObject;

typedef struct //输出结构体
{
    u32 u32MultiDetectNumber;               // 输出多目标检测个数
    TRectHelmetMultiObject tMultiRectOut[MULTI_DETECTED_MAXNUM];//多目标坐标
}THelmetMultiDetectOutputInfo;


//INPUT for TImageInfo.pvImageInfo
/*暂留*/

//OUTPUT for TImageInfo.pvImageInfo
//车牌检测相关输出的信息

/*兼容传统检测算法结构体*/
typedef struct
{
    s32 up;                    // 区域上部 Y 轴坐标
    s32 down;                // 区域下部 Y 轴坐标
    s32 left;                // 区域左部 X 轴坐标
    s32 right;                // 区域右部 X 轴坐标
}TRECT;
typedef struct  
{
    TRECT plateRect;
    float plateProb;
    s32 plateColor;
    s32 angle;
}TPLATEOUTPUT;
typedef struct tagVehicleProjetInfo            //20170804
{
    s32 Flag;                                  //1:需要调用车牌识别，0：不需调用车牌识别
    s32 plateNum;                               //检测出的牌照数量     
    TPLATEOUTPUT plate[40];                       //牌照属性 
} TVehicleProjetInfo;

/*深度学习检测算法结构体：Attention！其中TRECT_DL结构体中的up/down/left/right四个量在现有深度学习检测方法中不使用、暂保留*/
typedef struct
{
    s32 x;
    s32 y;
}TPlatePoint;
typedef struct
{
    s32 up;                    // 区域上部 Y 轴坐标(暂不使用)
    s32 down;                // 区域下部 Y 轴坐标(暂不使用)
    s32 left;                // 区域左部 X 轴坐标(暂不使用)
    s32 right;                // 区域右部 X 轴坐标(暂不使用)
    TPlatePoint pt[4];      // 4个角点的坐标x、y值(顺序依次为左上、右上、左下、右下)
}TRECT_DL;
typedef struct  
{
    TRECT_DL plateRectDL;
    float plateProb;
    s32 plateColor;
    s32 angle;
    //s32 plateluma;
    void* pvbuffertag;
}TPLATEOUTPUT_DL;
typedef struct tagVehicleProjetInfo_DL           //20171031
{
    s32 Flag;                                  //1:需要调用车牌识别，0：不需调用车牌识别
    s32 plateNum;                               //检测出的牌照数量                  
    TPLATEOUTPUT_DL plate[40];                   //牌照属性 
} TVehicleProjetInfo_DL;


/*=====================雾天分类=================================*/

/*
noFog     = 0  //没有雾
lightFog  = 1  //轻雾
Fog       = 2  //雾
heavyFog  = 3  //大雾
denseFog  = 4  //浓雾
strongFog = 5 //强浓雾
*/

typedef struct tagFogAlarmOutput
{
    unsigned int u32Visibility;              // 能见度
}TFogAlarmOutput;
/*=====================雾天分类=================================*/


/*=====================非机动车载人=================================*/

/*
u32cls  = 0  //没载人
u32cls  = 1  //载人
*/

typedef struct tagMannedNonmotorOutput
{
    unsigned int u32cls;              // 载人
}TMannedNonmotorOutput;
/*=====================非机动车载人=================================*/

/*=====================车牌相关-vida=================================*/
/*=====================1-车牌检测-vida=================================*/
typedef struct tagCarPlateDetectCpuOpen
{
    EMImageFormat emFormat;
    s8 aas8CarPlateDetectBinPath[200];//
}TKEDACarPlateDetectCpuOpen;
/*=====================2-车牌回归-vida=================================*/
typedef struct tagCarPlateKeyPointCpuOpen
{
    EMImageFormat emFormat;
    s8 aas8CarPlateKeyPointBinPath[200];//
}TKEDACarPlateKeyPointCpuOpen;

/*=====================3-车牌分类-vida=================================*/
typedef struct tagCarPlateClassCpuOpen
{
    EMImageFormat emFormat;
    s8 aas8CarPlateClassBinPath[200];//
}TKEDACarPlateClassCpuOpen;

/*=====================4-车牌识别-vida=================================*/

typedef enum tagCarPlateRecogModelType   //add by wjx 0912
{
    BigCarPlateRecogModel = 0,  // 车牌识别大模型  耗时56ms
    SmallCarPlateRecogModel,    // 车牌识别小模型 耗时12ms
    TractorCarPlateRecogModel,  // 农用车识别模型 也支持国内车牌
}EMCarPlateRecogModelType;


typedef struct tagCarPlateRecogCpuOpen
{
    EMImageFormat emFormat;
    s8 aas8CarPlateRecogBinPath[2][200];//0--大模型 1--小模型
    EMCarPlateRecogModelType emCarPlateRecogModelType;  //车牌识别模型
}TKEDACarPlateRecogCpuOpen;
/*=====================车牌相关-vida=================================*/

//OPEN info for pvOpen in IMGVideoAlgOpen
typedef struct tagCarPlateRecogOpen
{
    EMImageFormat emFormat;
    s8 s8GPUBinPath[200];
    s8 s8CarPlateRecogBinPath[200];
    s8 aas8CarPlateClsRecogBinPath[2][200];
    s32 MultiFrameFilterOn;    //多帧图像中出现相同车牌的过滤器：当设置为1时，如果后一帧检测框与前一帧iou大于0.9，则不作识别;0时，每帧都做识别
}TKEDACarPlateRecogOpen;


typedef struct tagCarPlateRecog4GBallNnieOpen   
{
    EMImageFormat emFormat;
    s32 MultiFrameFilterOn;    //多帧图像中出现相同车牌的过滤器：当设置为1时，如果后一帧检测框与前一帧iou大于0.9，则不作识别;0时，每帧都做识别
    s32 nnie_id;    //指定nnie编号
    EMCarPlateRecogModelType emCarPlateRecogModelType;  //车牌识别模型

}TKEDACarPlateRecog4GBallNnieOpen;


typedef struct tagCarPlateRecog4GBall981NnieOpen   
{
    EMImageFormat emFormat;
    s32 MultiFrameFilterOn;    //多帧图像中出现相同车牌的过滤器：当设置为1时，如果后一帧检测框与前一帧iou大于0.9，则不作识别;0时，每帧都做识别
    s32 nnie_id;    //指定nnie编号
    EMCarPlateRecogModelType emCarPlateRecogModelType;  //车牌识别模型
    s32 s32PlateListMultiReportLength; // 多帧上报过滤缓冲的长度
    s32 s32PlateListFilterRepeatLength;  // 车牌去重过滤的缓冲长度

}TKEDACarPlateRecog4GBall981NnieOpen;

//INPUT for TImageInfo.pvImageInfo
//待识别的车牌检测相关信息(结果可直接来自CarPlateDetect.h中对应结构体TVehicleProjetInfo)

typedef struct
{
    s32 x;
    s32 y;
}TPlatePointRecog;
typedef struct
{
    s32 up;                // 区域上部 Y 轴坐标
    s32 down;                // 区域下部 Y 轴坐标
    s32 left;                // 区域左部 X 轴坐标
    s32 right;                // 区域右部 X 轴坐标
    TPlatePointRecog pt[4];       //坐标
}TPLATERECT;
typedef struct tagCarPlatetoRecogINFO
{
    TPLATERECT plateRect;
    float plateProb;
    s32 plateColor;
    s32 angle;
      void* pvbuffertag;        //为了与车牌检测输出结构体一致，加了这个指针，暂不使用。
}TCarPlatetoRecogInfo;
typedef struct tagCarPlatestoRecogINFO         //20170804
{
    s32 Flag;                                  //1:需要调用车牌识别，0：不需调用车牌识别
    s32 plateNum;                               //识别出的牌照数量                  
    TCarPlatetoRecogInfo plate[40];               //牌照属性 
} TCarPlatestoRecogInfo;
typedef struct tagCarPlatestoRecogMoreINFO         //20170804
{
    s32 ManualFlag;                                  //1:手动识别模式；0:自动识别模式
    TCarPlatestoRecogInfo tCarPlatestoRecogInfo;     //多个牌照属性结构体 
    
    u32 u32Unrepeated;                               // 内部车牌识别去重开关 0 关闭 1 打开
    u32 u32FrameFilter;                              // 内部车牌多帧过滤（识别两帧结果相同才上报） 0 关闭 1 打开
    u32 u32TimeInterval;                             // 重复车牌上报的时间间隔 单位 秒(s)
} TCarPlatestoRecogMoreInfo;

typedef struct tagCarPlates981toRecogMoreINFO         //20200815 by wjx
{
    s32 ManualFlag;                                  // 1:手动识别模式；0:自动识别模式
    TCarPlatestoRecogInfo tCarPlatestoRecogInfo;     // 多个牌照属性结构体 
    // s32 s32PlateListMultiReportLength;                  // 多帧上报过滤缓冲的长度  应当小于等于open时设置的缓冲长度  若等于0则关闭多帧上报过滤
    // s32 s32PlateListFilterRepeatLength;              // 车牌去重过滤的缓冲长度 应当小于等于open时设置的缓冲长度 若等于0则关闭车牌去重
    // s32 s32CleanPlateListBuffer;                     //    传入 1 清除两个车牌过滤缓冲区内的信息

    u32 u32Unrepeated;                               // 内部车牌识别去重开关 0 关闭 1 打开
    u32 u32FrameFilter;                              // 内部车牌多帧过滤（识别两帧结果相同才上报） 0 关闭 1 打开
    u32 u32TimeInterval;                             // 重复车牌上报的时间间隔 单位 秒(s)
    
} TCarPlates981toRecogMoreInfo;

//OUTPUT for TImageInfo.pvImageInfo
//车牌识别相关信息
typedef struct  
{
    s32 Flag;                                   //1:该车牌可正确识别，0：车牌识别结果不可用（识别结果不满足语义规则，故pvCarPlateRecogNumber输出unrecognized）, -1：车牌类型未知（不是当前识别模型支持的车牌类型，故pvCarPlateRecogNumber输出unknown）
    u8 pvCarPlateRecogNumber[200];    //车牌号码字符串
    u8 pvCarPlateColor[200];            //车牌颜色字符串
    float score;                                //该车牌识别可信度评分
}TPLATERECOG;
typedef struct tagCarPlateRecogInfo
{
    s32 plateNum;                               //用于识别的牌照数量（与上述车牌检测中的检测出的牌照数量相同）
    TPLATERECOG pvCarPlateRecog[40];
} TCarPlateRecogInfo;


/*=====================教室人员检测=================================*/
#define PERSONMAXCOUNT 512        //最多人数，默认512
#define POINTMAXCOUNT 30        //待检测区域的点，最多30个
#define POLYGONMAXCOUNT 4        //待检测区域，最多4个

typedef struct
{
    u32 x0;    //框的左上角坐标
    u32 y0;
    u32 x1;    //框的右下角坐标
    u32 y1;
}PersonCountRect;        //输出人员框的Rect

typedef struct
{
    u32 x;
    u32 y;
}PersonCountPoint;        //输入待检测区域的点坐标
            
typedef struct
{
    PersonCountPoint m_Points[POINTMAXCOUNT];    //某个待检测区域的点，需按顺序，能组成凸多边形
    u32 m_PointCnt;                    //某个待检测区域的点数量
}PersonCountPolygon;        //输入待检测区域

typedef struct
{
    u32 m_PolygonCnt;
    PersonCountPolygon *m_Polygons;    //输入待检测区域
    float score_threshold;  //分数阈值
}PersonCountMask;        //输入一幅图数个待检测区域的掩膜

//====================== PersonCount Input =======================
typedef struct
{    
    u32 m_PersonMinSize;                //检测人员的最小尺寸阈值，默认12
    u32 m_PersonMaxSize;                //检测人员的最大尺寸阈值，默认1000
    double m_Confidence;                        //检测人员的最低置信度，默认0.2
    char m_Path_PersonCount_model[500];            //路径信息，人数统计model
    char m_Path_PersonCount_prototxt[500];        //路径信息，人数统计prototxt
}PersonCountInPut;                                //人数统计的输入

//====================== PersonCount Output =======================
typedef struct
{
    u8 m_PersonCount;                        //对外输出人员数
    PersonCountRect m_PersonRect[PERSONMAXCOUNT];    //对外输出每个人员的框
    float m_PersonConf[PERSONMAXCOUNT];                //对外输出每个人员的置信度
}PersonCountOutPut;    
/*=====================教室人员检测=================================*/

/*=====================教室人脸检测=================================*/
typedef struct tagManDetectinput    //人员检测的范围（是否需要使用待确认）
{
    u32 u32RoiX;
    u32 u32RoiY;
    u32 u32RoiWidth;
    u32 u32RoiHeight;
    u32 u32ModelFlag;//0:其他  1：实时码流
    u32 ISPthreshold;
}TManDetectinput_edu;

typedef struct
{
    s32 up;
    s32 down;
    s32 left;
    s32 right;
    float score;
    u32 lightness;//0:亮度不足  1：亮度满足要求
    float x[5];
    float y[5];
    void* pvbuffertag;
    int vivo;
}TRectMan_edu;

typedef struct 
{
    u32 u32ManDetectNumber;               
    TRectMan_edu tManRectOut[MAX_DETECT_INFO];
}TManDetectOutputInfo_edu;
/*=====================教室人脸检测=================================*/

/*=====================是否加装伞架=================================*/
//OPEN info for pvOpen in IMGVideoAlgOpen
typedef struct tagCarUmbrellaClsOpen
{
    EMImageFormat emFormat;
    s8 s8CarUmbrellaClsBinPath[200];
}TKEDACarUmbrellaClsOpen;

typedef struct tagCarUmbrellaClsROI  // 是否加装伞架 roi info (input)
{
    unsigned int u32RoiX;
    unsigned int u32RoiY;
    unsigned int u32RoiWidth;
    unsigned int u32RoiHeight;
}TCarUmbreallaClsROI;

//输出是否加装伞架枚举
typedef enum tagIfUmbrella
{
    NO_Umbrella = 0,
    IS_Umbrella,
}EMIfUmbrella;

typedef struct //Umbrella输出结构体
{
    EMIfUmbrella emIfUmbrella;             
}TUmbrellaOutputInfo;
/*=====================是否加装伞架=================================*/

/*=====================违停车检=================================*/

//OPEN info for pvOpen in IMGVideoAlgOpen
typedef struct tagCarParkingDetectOpen
{
    EMImageFormat emFormat;
    s8 s8CarParkingDetectBinPath[200];
}TKEDACarParkingDetectOpen;

typedef struct tagCarParkingROI  
{
    unsigned int u32RoiX;
    unsigned int u32RoiY;
    unsigned int u32RoiWidth;
    unsigned int u32RoiHeight;
    float conf_threshold;
}TCarParkingROI;

/*=====================违停车检=================================*/



/*=====================车牌分类=================================*/
// //OPEN info for pvOpen in IMGVideoAlgOpen
// typedef struct tagCarPlateClsOpen
// {
//     EMImageFormat emFormat;
//     s8 s8CarPlateClsBinPath[200];
// }TKEDACarPlateClsOpen;

typedef struct tagCarPlateClsROI
{
    unsigned int u32RoiX;
    unsigned int u32RoiY;
    unsigned int u32RoiWidth;
    unsigned int u32RoiHeight;
    float platecls_threshold; //获取业务设置的阈值
}TCarPlateClsROI;

//车牌类别
typedef enum tagIfPlateCls
{
    Not_Plate = 0,
    Single_Plate,
    Double_Plate,
}EMPlateCls;

typedef struct 
{
    EMPlateCls emPlateCls;             
}TPlateClsOutputInfo;

/*安霸*/
typedef struct tagCarPlateClsOpen
{
    s8 s8ModelPath[1024];
}TKEDACarPlateClsOpen;

typedef struct tagCarPlateClsMoreInfo
{
    float threshold; //获取业务设置的阈值，
    u8 u8priority;    // 业务优先级

}TKEDACarPlateClsMoreInfo;

/*安霸*/

/*=====================车牌分类=================================*/
/*=====================车牌对齐=================================*/
/*安霸*/
typedef struct tagCarPlateAlignOpen
{
    s8 s8ModelPath[1024];
}TKEDACarPlateAlignOpen;

typedef struct tagCarPlateAlignMoreInfo
{
    u8 u8priority;    // 业务优先级

}TKEDACarPlateAlignMoreInfo;
/*安霸*/
typedef struct tagCarPlateAlignOutputInfo
{
    TPlatePoint pt[4]; 
}TCarPlateAlignOutputInfo;

/*安霸*/

/*=====================车牌对齐=================================*/

/*=====================车牌识别=================================*/
/*安霸*/
typedef struct tagCarPlateRecogOpenAmba
{
    s8 s8ModelPath[1024];
}TKEDACarPlateRecogOpenAmba;

typedef struct tagCarPlateRecogMoreInfoAmba
{
    u8 u8priority;    // 业务优先级

}TKEDACarPlateRecogMoreInfoAmba;
/*安霸*/
typedef struct tagCarPlateRecogOutputInfoAmba
{
    float fOutput[8];
}TCarPlateRecogOutputInfoAmba;

/*安霸*/

/*=====================车牌识别=================================*/


/***************************functions************************************/

/*=========================================================================
函 数 名： IMGVideoAlgInit
功    能： 设备平台初始化
算法实现： 无
参    数：
返 回 值： 返回函数调用信息
===========================================================================*/
_PLATFORM_PREFIX_ EMIMGRetCode IMGVideoAlgInit();

/*=========================================================================
函 数 名： IMGVideoAlgRelease
功    能： 设备平台释放
算法实现： 无
参    数：
返 回 值： 返回函数调用信息
===========================================================================*/
 _PLATFORM_PREFIX_ EMIMGRetCode IMGVideoAlgRelease();

/*=========================================================================
函 数 名： IMGVideoAlgOpen
功    能： 算法初始化
算法实现： 无
参    数： pvHandle                    算法句柄[in]
emIMGAlgType                算法类型[in]
pvOpen                      初始化结构体指针[in]
返 回 值： 返回函数调用信息
===========================================================================*/
 _PLATFORM_PREFIX_ EMIMGRetCode IMGVideoAlgOpen(void** pvHandle,
                                                EMIMGAlgType emIMGAlgType,
                                               void* pvOpen);



/*=========================================================================
函 数 名： IMGVideoAlgProcess
功    能： 算法处理
算法实现： 无
参    数： pvHandle                    算法句柄[in]
           emIMGAlgType                算法类型[in]
           ptInputInfo                 输入数据结构体指针[in]
           ptOutputInfo                输出数据结构体指针[out]
返 回 值： 返回函数调用信息
===========================================================================*/    
_PLATFORM_PREFIX_ EMIMGRetCode IMGVideoAlgProcess(void* pvHandle,
                                                   EMIMGAlgType emIMGAlgType,
                                                 TImageInfo* ptInputInfo,
                                                 TImageInfo* ptOutputInfo);


/*=========================================================================
函 数 名： IMGVideoAlgClose
功    能： 算法资源释放
算法实现： 无
参    数： pvHandle                    算法句柄[in]
emIMGAlgType                算法类型[in]
返 回 值： 返回函数调用信息
===========================================================================*/
 _PLATFORM_PREFIX_ EMIMGRetCode IMGVideoAlgClose(void* pvHandle,
                                                EMIMGAlgType emIMGAlgType);


/*=========================================================================
函 数 名： GetVersionInfo
功    能： 获取版本等信息
算法实现： 无
参    数： emIMGAlgType               算法类型[in]
ptVersionInfo              版本信息结构体[out]
返 回 值： 无返回信息
===========================================================================*/
 _PLATFORM_PREFIX_ void GetVersionInfo(EMIMGAlgType emIMGAlgType,
                                      TVersionInfo* ptVersionInfo);




 /*=========================================================================
 函 数 名： IMGVideoAlgSetTarBriForDetect
 功    能： 算法处理
 算法实现： 无
 参    数： pvHandle                    算法句柄[in]
 emIMGAlgType                算法类型[in]
 ptInputInfo                 输入数据结构体指针[in]
 ptOutputInfo                输出数据结构体指针[out]
 返 回 值： 返回函数调用信息
 ===========================================================================*/
 _PLATFORM_PREFIX_ EMIMGRetCode IMGVideoAlgSetTarBriForDetect(void* pvHandle,
     EMIMGAlgType emIMGAlgType,
     TImageInfo* ptInputInfo,
     TImageInfo* ptOutputInfo);

 /*=========================================================================
 函 数 名： IMGVideoAlgSetTarBriForReg
 功    能： 算法处理
 算法实现： 无
 参    数： pvHandle                    算法句柄[in]
 emIMGAlgType                算法类型[in]
 ptInputInfo                 输入数据结构体指针[in]
 ptOutputInfo                输出数据结构体指针[out]
 返 回 值： 返回函数调用信息
 ===========================================================================*/
 _PLATFORM_PREFIX_ EMIMGRetCode IMGVideoAlgSetTarBriForReg(void* pvHandle,
     EMIMGAlgType emIMGAlgType,
     TImageInfo* ptInputInfo,
     TImageInfo* ptOutputInfo);










//#######################################################################################################################
//    各项目的定义, 按项目需要。
//    如果有功能完全相同的结构定义，可以重复使用；若有区别，请以项目名为后缀重新定义，不可修改其他项目使用的结构体
//
//  
//
//
//#######################################################################################################################



//#######################################################################################################################
//  执法记录仪项目
//  
//  
//  
//  
//  
typedef struct tagManmodelInput    //人员检测的范围（是否需要使用待确认）
{
    u32 u32RoiX;
    u32 u32RoiY;
    u32 u32RoiWidth;
    u32 u32RoiHeight;
    s32 GrabMode; //抓拍模式 0：排查模式 1：巡逻模式 U3弃用
    u32 u32MinFace; //要求上传的最小人脸尺寸
    float minScore; //要求上传的最小人脸姿态分
    u32 u32Isdeduplication; //人脸去重配置，0：不去重 1：去重
    u32 u32Isdeblur; //是否去模糊 0：不去模糊，清晰模糊人脸都上传 1：去模糊，只上传清晰图片
    float fFeatureThreshold; //特征比对阈值，范围0~1
}TManDetectInput_ZFJLY;
typedef  struct//执法记录仪项目ISP联调的输出结构体
{
    s32 startadjustFlag;//0:人脸未满足要求继续等待1:人脸满足要求开始调节
    s32 lightness;//当前计算得到的人脸亮度
    s32 AdjustValue;//目标亮度需要调节的数值
}ISPadjustOutputInfo_ZFJLY;


typedef struct//执法记录仪项目活体检测的输出结构体
{
    s32 vivoFlag;
}VivoDetectOutputInfo_ZFJLY;

typedef struct  //输出人员检测结果对应的TImageBuffer的tag以及对应坐标信息
{
    s32 up;
    s32 down;
    s32 left;
    s32 right;
    float x[5];
    float y[5];
    float score;
    float clearness;
    s32 RectArea;
    s32 ValidFaceFlag;
    void* pvbuffertag;
    u32 mask;
}TRectMan_ZFJLY;


typedef struct //执法记录仪人脸抓拍输出结构体
{
    u32 u32ManDetectNumber;               // 输出检测出的人员数量
    TRectMan_ZFJLY tManRectOut[MAX_DETECT_INFO];
    u32 CurDetectFaceNum;
    StuFaceInfo CurDetectFace[MAX_DETECT_INFO];
}TManDetectOutputInfo_ZFJLY;

//*****************DSJ特征提取******************
typedef struct 
{
    EMImageFormat emFormat;
    s8 s8GPUBinPath[200];
    s8 s8ManRecogBinPath[200];
    u32 DimOfFeature;
}TKEDAManFaceFeatureOpen_ZFJLY;

typedef struct tagManFaceFeatureinfo_ZFJLY   //人员检测的范围（是否需要使用待确认）
{
    u32 u32RoiX;
    u32 u32RoiY;
    u32 u32RoiWidth;
    u32 u32RoiHeight;
    float x[5];
    float y[5];
}TManFaceFeatureInfo_ZFJLY;

typedef struct tagManFaceFeatureOutput_ZFJLY
{
    void* pFeatureBuffer;
}TManFaceFeatureOutputInfo_ZFJLY;

//*****************DSJ_SSR超分******************
typedef struct
{
    EMImageFormat emFormat;
}TKEDASRROpen;

typedef struct 
{
    u32 u32RoiX;
    u32 u32RoiY;
    u32 u32RoiWidth;
    u32 u32RoiHeight;
}TProcessInputSRR;

typedef struct
{
    u32 u32OutWidth;
    u32 u32OutHeight;  
    u32 u32OutChannel;
    u8* puOutImgBuffer;
    u32 u32OutImgBufferSize;
}TProcessOutputSRR;


//*****************DSJ人脸比对******************
#define MAX_VER_NUM_DSJ 10  //人脸比对最多选取数目
typedef struct tagManfacecomparInputInfo_ZFJLY
{
    float * pInputOneFeature;
    float * pInputFeatureSet;
    s32     NumOfFeatureSet;
    s32     NumOfVerification; //top K
    s32     DimOfFeature; //特征维度
    float   Threshold;
}TManFaceComparInputInfo_ZFJLY;

typedef struct tagManFaceComparOutput_ZFJLY
{
    float   VerScore[MAX_VER_NUM_DSJ]; // 相似度
    s32     Verified[MAX_VER_NUM_DSJ];   // 是否通过
    s32     MaxScoreID[MAX_VER_NUM_DSJ];
}TManFaceComparOutputInfo_ZFJLY;

//rgb活体
typedef struct tagManFaceVivoOpen_ZFJLY 
{
    u32 u32SdCardFlag;      //0:不开打印，1：开启tf卡打印
    char  as8SdcardPath[400];
}TManFaceVivoOpen_ZFJLY;


typedef struct tagManFaceVivoinfo_ZFJLY    //人员检测的范围（是否需要使用待确认）
{
    u32 u32RoiX; //ROI区域起始X坐标
    u32 u32RoiY;//ROI区域起始Y坐标
    u32 u32RoiWidth; //ROI区域宽度
    u32 u32RoiHeight; //ROI区域高度
    float x[5]; //人脸关键点x坐标
    float y[5]; //人脸关键点Y坐标
    float Threshold; //活体检测阈值
       s32 up;          //人脸坐标
    s32 down;      
      s32 left;
      s32 right;
}TManFaceVivoInfo_ZFJLY;

typedef struct tagManFaceVivoOutput_ZFJLY
{
    int vivo; //1:活体 0：假体
}TManFaceVivoOutputInfo_ZFJLY;

//#######################################################################################################################
//  门禁项目
//  
//  
//  
//  
//  
//  
/* ============门禁项目人脸检测===============*/
typedef struct tagManmodelinput    //人员检测的范围（是否需要使用待确认）
{
    u32 u32RoiX;
    u32 u32RoiY;
    u32 u32RoiWidth;
    u32 u32RoiHeight;
    u32 u32ModelFlag;//0:其他  1：实时码流
    u32 ISPthreshold;
    u32 u32MaskOpenFlag;     //口罩检测开关，1：开，0：关
}TManDetectinput_menjin;    
typedef struct
{
    s32 up;
    s32 down;
    s32 left;
    s32 right;
    float score;
    u32 lightness;//0:亮度不足  1：亮度满足要求
    float x[5];
    float y[5];
    void* pvbuffertag;
    int vivo;
    int mask;
}TRectMan_menjin;

typedef struct 
{
    u32 u32ManDetectNumber;               
    TRectMan_menjin tManRectOut[MAX_DETECT_INFO];
    u32 u32UploadNum;
    u32 au32FrameNum[MAX_DETECT_INFO];
    u32 au32DeleteNum[MAX_DETECT_INFO];
}TManDetectOutputInfo_menjin;


/* ============门禁项目人脸比对===============*/
typedef struct tagManFaceComparOpen
{
    EMImageFormat emFormat;
}TKEDAManFaceComparOpen;

typedef struct tagManfacecomparInputInfo    //
{
    float * pInputOneFeature;
    float * pInputFeatureSet;
    s32     NumOfFeatureSet;
    float   Threshold;
    s32     s32CompareMOdeFlag;
    u32     DimOfFeature; 
    int     mask;
}TManFaceComparInputInfo;

typedef struct tagManFaceComparOutput
{
    float   VerScore; // 相似度
    s32     Verified;   // 是否通过
    s32     MaxScoreID;
}TManFaceComparOutputInfo;


/* ============门禁项目特征提取===============*/
typedef struct tagManFaceFeatureOpen
{
    EMImageFormat emFormat;
    s8 s8GPUBinPath[200];
    s8 s8ManRecogBinPath[200];
    s8 s8ManRecogBinPath28[200];
    u32 DimOfFeature;
    u32 u32SdCardFlag;      //0:不开打印，1：开启tf卡打印
    char  as8SdcardPath[400];
}TKEDAManFaceFeatureOpen;

// roi info (input)
typedef struct tagManFaceFeatureinfo    //人员检测的范围（是否需要使用待确认）
{
    u32 u32RoiX;
    u32 u32RoiY;
    u32 u32RoiWidth;
    u32 u32RoiHeight;
    float x[5];
    float y[5];
    u32 u32ModelFlag;
}TManFaceFeatureInfo;

typedef struct tagManFaceFeatureOutput
{
    void* pFeatureBuffer;
}TManFaceFeatureOutputInfo;

/* ============门禁项目rgb活体===============*/
typedef struct tagManFaceVivoOpen 
{
    u32 u32SdCardFlag;      //0:不开打印，1：开启tf卡打印
    char  as8SdcardPath[400];
}TManFaceVivoOpen_menjin;


typedef struct tagManFaceVivoinfo    //人员检测的范围（是否需要使用待确认）
{
    u32 u32RoiX;
    u32 u32RoiY;
    u32 u32RoiWidth;
    u32 u32RoiHeight;
    float x[5];
    float y[5];
    float Threshold;
    s32 up;          //人脸坐标
    s32 down;
    s32 left;
    s32 right;
}TManFaceVivoInfo;

typedef struct tagManFaceVivoOutput
{
    int vivo;
}TManFaceVivoOutputInfo;/* ============门禁项目JPG2NV12===============*/
typedef struct tagJPEGDecoderOpen
{
    EMImageFormat emFormat;
    s8 s8JPEGPath[200];
  
}TKEDAJPEGDecoderOpen;

    
typedef struct tagJPEGDecoderinfo
{
    s32 width;
    s32 height;
    char filename[200];
}TJPEGDecoderinfo;

//#######################################################################################################################
//  行车仪项目
//  
//  
//  
//  
//  

typedef struct  //调用open接口时读取的模型bin文件
{
    EMImageFormat emFormat;
    s8 s8GPUBinPath[200];
    s8 s8ManDetectBinPath[MAX_MODEL_NUM][200];
    u32 u32SdCardFlag;      //0:不开打印，1：开启tf卡打印
    char  as8SdcardPath[400];
    u32 u32DimOfFeature; //特征模式下，获取模型输出特征维度信息
}TKEDAManDetectOpen_ivdr;

typedef struct     
{
    u32 u32RoiX;
    u32 u32RoiY;
    u32 u32RoiWidth;
    u32 u32RoiHeight;
    u32 u32ModelFlag;//0:其他  1：实时码流
    u32 ISPthreshold;
    u32 u32DissaThreshold;
    u32 u32DimOfFeature;
    u32 u32IsFeatureMode;//0:去重模式  1：特征提取模式
    u32 u32MinFace;
    float minScore;
    float x[5];
    float y[5];
}TManDetectinput_ivdr;    
typedef struct 
{
    s32 width;
    s32 height;
    char filename[200];
    unsigned char * imagebuffer;
    s32 buffersize;
    s32 isfilemode;
}TJPEGDecoderinfo_ivdr;

typedef struct
{
    s32 up;
    s32 down;
    s32 left;
    s32 right;
    float score;
    u32 lightness;//0:亮度不足  1：亮度满足要求
    float x[5];
    float y[5];
    u32 mask;
    void* pvbuffertag;
    int vivo;
}TRectMan_ivdr;

typedef struct 
{
    u32 u32ManDetectNumber;               
    TRectMan_ivdr tManRectOut[MAX_DETECT_INFO];
    void* pFeatureBuffer;
    u32 u32FeatureBufferSize;
}TManDetectOutputInfo_ivdr;

//#######################################################################################################################
//  uc120 windows项目
//  
//  
//  
//  
//  
typedef struct tagIspFaceWeightInfo
{
    int heightMargin;
    int widthMargin;
    int height;
    int width;
}TIspFaceWeightInfo;

typedef struct     //人员检测的范围（是否需要使用待确认）
{
    u32 u32RoiX;
    u32 u32RoiY;
    u32 u32RoiWidth;
    u32 u32RoiHeight;
    u32 u32ModelFlag;//0:身份证 1:抓拍图
    u32 ISPthreshold;
}TManDetectinput_uc120;


typedef enum
{
    DETECT_MODE,
    RECONGNIZE_MODE
}EMISPAdjustMode;
typedef struct
{
    s32 up;
    s32 down;
    s32 left;
    s32 right;
    float score;
    u32 lightness;//0:亮度不足  1：亮度满足要求
    float x[5];
    float y[5];
    void* pvbuffertag;
    int vivo;
}TRectMan_uc120;
typedef struct
{
    u32 u32ManDetectNumber;
    TRectMan_uc120 tManRectOut[MAX_DETECT_INFO];
    u32 u32UploadNum;
    u32 au32FrameNum[MAX_DETECT_INFO];
    u32 au32DeleteNum[MAX_DETECT_INFO];
    TIspFaceWeightInfo WeightPosi;
    s32 MaxTargetBright;
    s32 MinTargetBright;
    EMISPAdjustMode TargetBrightMode;// 0:检测模式；1:识别模式
    float RegionBrightScore;
}TManDetectOutputInfo_uc120;
//#######################################################################################################################
//  星辰项目
//  
//  
//  
//  
// 

typedef struct     
{
    u32 u32RoiX;
    u32 u32RoiY;
    u32 u32RoiWidth;
    u32 u32RoiHeight;
    u32 u32ModelFlag;
    u32 ISPthreshold;
}TManDetectinput;

typedef struct
{
    s32 s32Vivo;
    u32 u32ManDetectNumber;
}TVivoDetectOutputInfo;


//#######################################################################################################################
//  4G布控球项目
//  
//  
//  
//  
//  
typedef struct tagManDetectTrackParam
{
    float f32ScoreThreshold;    // 分数阈值
    s32 l32UploadInterval;        // 低于阈值时上传间隔
    s32 l32FullFrameInterval;    // 全帧检测间隔
    s32 l32Fps;                    // 每秒传多少帧
    TManDetectROI tManRoi;
}TManDetectTrackParam;

typedef struct tagManDetectParam
{
    float f32ScoreThreshold;    // 分数阈值
    TManDetectROI tManRoi;
    s32 l32OccupiedFrameBuff;
    s32 l32IsUploadFaceGreatThanThreshold;
    s32 s32NightModeFlag;
    s32 s32MinFaceWidth;
    s32 s32MinFaceHeight;
    s32 s32Minlightness;
    s32 s32Maxlightness;
    s32 s32OpenMaskDetectFlag;
    u8 u8priority;

    s32 s32DisappearFrameThreshold; //人脸消失帧数 
    float f32CompareThreshold;        //人脸比对阈值
    float f32AspectRatioThreshold;  //人脸纵横比阈值
    float f32EyeDistanceThreshold; //人脸眼距阈值
    float f32DayScoreFilter;      //白天姿态分阈值
    float f32NightScoreFilter;    //夜间姿态分阈值
}TManDetectParam;

typedef struct tagRectMan4GBall
{
    s32 up;
    s32 down;
    s32 left;
    s32 right;
    float score;
    s32 ValidFaceFlag;
    void *pvbuffertag;
    float *pfFeatureBuffer;
    s32 s32MaskFlag;
}TRectMan_4GBall;

typedef struct tagManDetectOutputInfo4GBall
{
    u32 u32ManDetectNumber;
    TRectMan_4GBall tManRectOut[MAX_DETECT_INFO];
}TManDetectOutputInfo_4GBall;

typedef struct tagManFaceFeatureOutput4GBall
{
    float *pfFeatureBuffer;
    u32 u32ExtractFlag;
    float f32PoseScore;
}TManFaceFeatureOutputInfo_4GBall;

typedef struct tagManfacecompareInputInfo4GBall
{
    float *pfInputOneFeature;
    float *pfInputFeatureSet;
    s32    s32NumOfFeatureSet;
    float  f32Threshold;
    u32    u32DimOfFeature; 
}TManFaceCompareInputInfo_4GBall;

typedef struct tagManFaceCompareOutput4GBall
{
    float f32VerScore;  // 相似度
    s32   s32Verified;  // 是否通过
    s32   s32MaxScoreID;
}TManFaceCompareOutputInfo_4GBall;

typedef struct tagKEDAManDetectNnieOpen4GBall
{
    EMImageFormat emFormat;
    s32 s32NnieID;
}TKEDAManDetectNnieOpen_4GBall;

typedef struct tagManDetectOpen4GBall
{
    s8 s8FaceDetPath[255];    //人脸检测bin路径
    s8 s8ClsPath[255];        //classify bin路径
    s8 s8MskPath[255];        //mask bin路径
    s8 s8FeaPath[255];        //feature bin路径
}TManDetectOpen_4GBall;

typedef struct tagManFaceFeatureOpen4GBall
{
    s8 s8FaceDetPath[255];
    s8 s8ClsPath[255];
    s8 s8FeaPath[255];
}TManFaceFeatureOpen_4GBall;



//#######################################################################################################################
// P2人证核验项目
//  
//  
//
//
//*****************P2人脸检测******************
#define MAX_VER_NUM 10  //人脸比对最多选取数目
typedef struct  //输出人员检测结果对应的TImageBuffer的tag以及对应坐标信息
{
    s32 up;
    s32 down;
    s32 left;
    s32 right;
    float x[5];
    float y[5];
    float score;
    float clearness;
    s32 RectArea;
    s32 ValidFaceFlag;
    void* pvbuffertag;
}TRectMan_P2;

typedef struct //P2人脸检测输出结构体
{
    u32 u32ManDetectNumber;               // 输出检测出的人员数量
    TRectMan_P2 tManRectOut[MAX_DETECT_INFO];
    u32 CurDetectFaceNum;
    StuFaceInfo CurDetectFace[MAX_DETECT_INFO];
}TManDetectOutputInfo_P2;

//*****************P2特征提取******************
typedef struct 
{
    EMImageFormat emFormat;
    s8 s8GPUBinPath[200];
    s8 s8ManRecogBinPath[200];
}TKEDAManFaceFeatureOpen_P2;

typedef struct tagManFaceFeatureinfo_   //人员检测的范围（是否需要使用待确认）
{
    u32 u32RoiX;
    u32 u32RoiY;
    u32 u32RoiWidth;
    u32 u32RoiHeight;
    float x[5];
    float y[5];
}TManFaceFeatureInfo_P2;

typedef struct tagManFaceFeatureOutput_P2
{
    void* pFeatureBuffer;
}TManFaceFeatureOutputInfo_P2;

//*****************P2人脸比对******************
typedef struct tagManfacecomparInputInfo_P2
{
    float * pInputOneFeature;
    float * pInputFeatureSet;
    s32     NumOfFeatureSet;
    s32     NumOfVerification; //top K
    s32     DimOfFeature; //特征维度
    float   Threshold;
}TManFaceComparInputInfo_P2;

typedef struct tagManFaceComparOutput_P2
{
    float   VerScore[MAX_VER_NUM]; // 相似度
    s32     Verified[MAX_VER_NUM];   // 是否通过
    s32     MaxScoreID[MAX_VER_NUM];
}TManFaceComparOutputInfo_P2;


//*****************P2图片缩放******************
typedef struct 
{
    char s8InputFile[200];
    char s8OutputFile[200];
    int  s32OutWidth;
    int  s32OutHeight;
}TResizeInputInfo_P2;


//#######################################################################################################################
//Mini半球项目 
/* ============人脸检测===============*/
typedef struct    //人员检测的范围（是否需要使用待确认）
{
    u32 u32RoiX;
    u32 u32RoiY;
    u32 u32RoiWidth;
    u32 u32RoiHeight;
    s32 GrabMode;
}TManDetectInput_Mini;
typedef  struct//ISP联调的输出结构体
{
    s32 startadjustFlag;//0:人脸未满足要求继续等待1:人脸满足要求开始调节
    s32 lightness;//当前计算得到的人脸亮度
    s32 AdjustValue;//目标亮度需要调节的数值
}ISPadjustOutputInfo_Mini;

typedef struct//活体检测的输出结构体
{
    s32 vivoFlag;
}VivoDetectOutputInfo_Mini;

typedef struct  //输出人员检测结果对应的TImageBuffer的tag以及对应坐标信息
{
    s32 up;
    s32 down;
    s32 left;
    s32 right;
    float x[5];
    float y[5];
    float score;
    float clearness;
    s32 RectArea;
    s32 ValidFaceFlag;
    void* pvbuffertag;
}TRectMan_Mini;

typedef struct //人脸抓拍输出结构体
{
    u32 u32ManDetectNumber;               // 输出检测出的人员数量
    TRectMan_Mini tManRectOut[MAX_DETECT_INFO];
    u32 CurDetectFaceNum;
    StuFaceInfo CurDetectFace[MAX_DETECT_INFO];
}TManDetectOutputInfo_Mini;

/* ============特征提取===============*/
typedef struct 
{
    EMImageFormat emFormat;
    s8 s8GPUBinPath[200];
    s8 s8ManRecogBinPath[200];
    s8 s8ManRecogBinPath28[200];
}TKEDAManFaceFeatureOpen_Mini;

// roi info (input)
typedef struct     //人员检测的范围（是否需要使用待确认）
{
    u32 u32RoiX;
    u32 u32RoiY;
    u32 u32RoiWidth;
    u32 u32RoiHeight;
    float x[5];
    float y[5];
    u32 u32ModelFlag;
}TManFaceFeatureInfo_Mini;

typedef struct 
{
    void* pFeatureBuffer;
}TManFaceFeatureOutputInfo_Mini;

/* ============人脸比对===============*/
typedef struct 
{
    EMImageFormat emFormat;
}TKEDAManFaceComparOpen_Mini;

typedef struct 
{
    float * pInputOneFeature;
    float * pInputFeatureSet;
    s32     NumOfFeatureSet;
    float   Threshold;
    s32     s32CompareMOdeFlag;
}TManFaceComparInputInfo_Mini;

typedef struct 
{
    float   VerScore; // 相似度
    s32     Verified;   // 是否通过
    s32     MaxScoreID;
}TManFaceComparOutputInfo_Mini;

//**************************************************************************************************


/********************视讯MOON50U项目********************/
typedef struct tagKEDAManDetectNnieOpen
{
    EMImageFormat emFormat;
    s8 s8ModelPath[2][255];
    s8 s8PriorboxPath[255];
}TKEDAManDetectNnieOpen;

typedef struct
{
    u8 *pu8ManNnieModel;
    s64 s64ModelSize;
}TManModelAddr_MOON50U;

typedef struct tagKEDADetectNnieOpen_MOON50U
{
    int nnie_id;
    TManModelAddr_MOON50U tManModelAddr[MAX_MODEL_NUM];
}TKEDADetectNnieOpen_MOON50U;

/* ============人脸检测===============*/ 
//roi info (input)
typedef struct tagManmodelROI_NNIE    //人员检测的范围（是否需要使用待确认）
{
    u32 u32RoiX;
    u32 u32RoiY;
    u32 u32RoiWidth;
    u32 u32RoiHeight;
    float Region_Weight;
    float Location_Weight;
    float *LocWeight;
    u64 au64PhyAddr[3];
    u32 Flag;      //追踪restart标记
    u32 AE_Flag;      //AE亮度调试开关（ISP）
    u32 AWB_Flag;     //AWB亮度调试开关（ISP）
    u32 u32DeviceId; //设备ID
    u8 u8priority;
    u32 GrabMode;
    s32 fd;
}TManDetectROI_NNIE;

//AF 注册回调
typedef void (*FImgMOONDetectOCB) (void *ptMOONOutput, u32 u32DeviceId);  
u32 ImgMOONDetectOCBRegister(FImgMOONDetectOCB pCallback, u32 u32DeviceId); 

//AF 注册回调输出结构体
typedef struct 
{
    void *ptDetectOutput;
    void *ptKeypointOutput;
    void *pFeatureBuffer;
    u32 u32DeviceId; //设备ID
}TMOONDetectOutput;


//output info 
typedef struct  //输出人员检测结果对应的TImageBuffer的tag以及对应坐标信息
{
    s32 up;
    s32 down;
    s32 left;
    s32 right;
    int label;
    float score;
    float AverageLight;
    s32 s32MaxFaceMark;
    void* pvbuffertag;
}TRectMan_NNIE;

typedef struct //执法记录仪人脸抓拍输出结构体
{
    u32 u32ManDetectNumber;               // 输出检测出的人员数量
    TRectMan_NNIE tManRectOut[MAX_DETECT_INFO];
    float LumaRatio;
}TManDetectOutputInfo_NNIE;

/* ============特征提取===============*/ 
//roi info (input)
typedef struct tagManFaceFeatureinfo_NNIE    //人员检测的范围（是否需要使用待确认）
{
    u32 u32RoiX;
    u32 u32RoiY;
    u32 u32RoiWidth;
    u32 u32RoiHeight;
    float score;
    float f32PoseScoreThresh;
    s32 s32MaxFaceMark;
    s32 u32DeviceId; //设备ID
    u32 AF_Flag;      //AF智能聚焦开关（ISP）
    u8 u8priority;
    u64 au64PhyAddr[3];
}TManFaceFeatureInfo_NNIE;
//output
typedef struct tagManFaceFeatureOutput_NNIE
{
    void* pFeatureBuffer;
    u32 Flag;
    float PoseScore;
}TManFaceFeatureOutputInfo_NNIE;

/* ============人形检测===============*/ 
//output info
typedef struct  //输出人形检测结果对应的TImageBuffer的tag以及对应坐标信息
{
    s32 up;
    s32 down;
    s32 left;
    s32 right;
    float score;
    int trackid;
    int track_state;
    void* pvbuffertag;
}TRectHuman;

typedef struct //humandetect输出结构体
{
    u32 u32HumanDetectNumber;               // 输出检测出的人员数量
    TRectHuman tHumanRectOut[MAX_HUMAN_DETECT_INFO];
}THumanDetectOutputInfo;

typedef struct  //输出人脸检测结果对应的TImageBuffer的tag以及对应坐标信息
{
    s32 up;
    s32 down;
    s32 left;
    s32 right;
    float score;
    s32 mask_glass_flags;
    void* pvbuffertag;
    s32 x[5];
    s32 y[5];
}TRectFace;

typedef struct //输出结构体
{
    u32 u32FaceDetectNumber;               // 输出检测出的人脸数量
    TRectFace tFaceRectOut[MAX_DETECT_INFO];
}TFaceDetectOutputInfo;

typedef struct
{
    u32 u32HumanDetectNumber;
    TRectHuman tHumanRect[MAX_DETECT_INFO];
}THumanTrackInfo;


//################################################################################################
//
//******************** MOON90H 网呈 ********************
//
typedef struct tagKEDADetectNnieOpen_MOON90H
{
    int nnie_id;
    EMImageFormat emFormat;
}TKEDADetectNnieOpen_MOON90H;

typedef struct tagManInput    //人员检测的范围（是否需要使用待确认）
{
    TManDetectROI tManRoi;
    u32 GrabMode;
    u64 au64PhyAddr[3];
}TManDetectROI_MOON90H;

// MOON90H-1080P输出结构体
typedef struct //人脸检测结果
{
    fbox bbox;        //  人脸边框
    float prob;      //  概率
    // void* pvbuffertag;
}TRectMan_MOON90H;

typedef struct //输出人脸检测结果对应的TImageBuffer的tag以及对应坐标信息
{
    u32 u32ManDetectNumber;               // 输出检测出的人员数量
    TRectMan_MOON90H tManRectOut[MAX_DETECT_INFO];
}TManDetectOutputInfo_MOON90H;

// MOON90H-4K输出结构体
typedef struct //人脸检测及关键点结果
{
    fbox bbox;        //  人脸边框
    ldm12 landmark;    //  12关键点
    float prob;      //  概率
    int dispose_glasses; // 处理眼镜标志 戴眼镜情况下镜框上边界距离眼皮较远时可以进行矫正处理

    fbox bboxL;        //  左眼镜边框
    fbox bboxR;        //  右眼镜边框
    int with_glasses; // 戴眼镜标志

}TRectMan_4K_MOON90H;

typedef struct //输出人脸检测结果对应的TImageBuffer的tag以及对应坐标、关键点信息
{
    u32 u32ManDetectNumber;               // 输出检测出的人员数量
    TRectMan_4K_MOON90H tManRectOut[MAX_DETECT_INFO];
}TManDetectOutputInfo_4K_MOON90H;

// 眼神交互
// roi info (input)
typedef struct tagManEyeGazeCorInfo_NNIE
{
    TManDetectOutputInfo_4K_MOON90H *tManRectInfo;
    unsigned long long u32CurFrameStamp;
}TManEyeGazeCorInfo_MOON90H;


//################################################################################################
//
//******************** MOON51i 安霸 ********************
//

// roi info (input)
typedef struct tagManFaceFeatureInArrayInfo
{
    u32 u32ManDetectNumber;               // 检测人员数量
    TRectMan_NNIE tManRectROI[MAX_DETECT_INFO]; 
    u64 au64PhyAddr[3];
    float f32PoseScoreThresh;
    u8 u8priority;

    s32 u32DeviceId; //设备ID
    u32 AF_Flag;      //AF智能聚焦开关（ISP）

}TManFaceFeatureInArrayInfo;

// feature (output)
typedef struct tagManFaceFeatureOutArrayInfo
{
    TManFaceFeatureOutputInfo_NNIE *tManRecogInfo;
    
}TManFaceFeatureOutArrayInfo;


//################################################################################################
//
//******************** NVR ********************
//
typedef struct tagKEDADetectNnieOpen_NVR
{
    int nnie_id;
    EMImageFormat emFormat;
}TKEDADetectNnieOpen_NVR;

typedef struct tagManInput_NVR    //人员检测的范围（是否需要使用待确认）
{
    TManDetectROI tManRoi;
    u64 au64PhyAddr[3];
}TManDetectROI_NVR;

typedef struct
{
    s32 up;
    s32 down;
    s32 left;
    s32 right;
    float score;
    void* pvbuffertag;
}TRectMan_NVR;

typedef struct //输出人脸检测结果对应的TImageBuffer的tag以及对应坐标信息
{
    u32 u32ManDetectNumber;               // 输出检测出的人员数量
    TRectMan_NVR tManRectOut[MAX_DETECT_INFO];
}TManDetectOutputInfo_NVR;


//################################################################################################
//
//********************行人立柱TL********************
//
typedef struct tagKEDADetectNnieOpen_TL
{
    int nnie_id;
    int mask_glasses_flag;
}TKEDADetectNnieOpen_TL;

typedef struct tagHumanTLOpen
{
    s8 s8PriorboxPath[1024];                                                      //boxBin路径 
    s8 s8ModelPath[1024];                                                         //第一个ModelBin路径
    s8 s8Model2Path[1024];                                                         //第二个ModelBin路径
    u8 u8TwoClassFlag;                                                             //是否加二分类模块标志
}TKEDAHUMANTLOpen;

//output info
typedef struct  //输出人形检测结果对应的TImageBuffer的tag以及对应坐标信息
{
    s32 up;
    s32 down;
    s32 left;
    s32 right;
    float score;
    int trackid;
    int track_state;
    void* pvbuffertag;
    int classId;
}TRectHuman_TL;

typedef struct //humandetect输出结构体
{
    u32 u32HumanDetectNumber;               // 输出检测出的人员数量
    TRectHuman_TL tHumanRectOut[MAX_HUMAN_DETECT_INFO];//检测输出结构体
}THumanDetectOutputInfo_TL;

typedef struct
{
    TPlatePoint pt[4];      // 4个角点的坐标x、y值(顺序依次为左上、右上、右下、左下)
    u32 humanHeight;        //行人高度
    double angle;              //roi角度
}THUMANDETECT_ROI;

typedef struct
{
    TPlatePoint pt[4];      // 4个角点的坐标x、y值(顺序依次为左上、右上、右下、左下)
}THUMANFACEDETECT_ROI;//人脸检测

///////////////安全帽检测////////////

typedef struct tagKEDAHelMetDetectNnieOpen
{
    int nnie_id;
}TKEDAHelMetDetectNnieOpen;
typedef enum tagHelMetTarget
{
    safety_hat,
    no_hat,
}EMHelMetTarget;
typedef struct  //输出人形检测结果对应的TImageBuffer的tag以及对应坐标信息
{
    s32 up;
    s32 down;
    s32 left;
    s32 right;
    float score;
    // s32 s16ObjectsIndex; //目标所属类别
    EMHelMetTarget HelMetTarget;
    void* pvbuffertag;
}TRectHelMetObject;
typedef struct //HelMetdetect输出结构体
{
    u32 u32HelMetDetectNumber;               // 输出多目标检测个数
    TRectHelMetObject tHelMetRectOut[50];//多目标坐标
}THelMetDetectOutputInfo;

////////////////抛洒物检测///////////////

#define MAX_NUM_OUTPUT_SINGLE_OBJECT 100     // 最多输出的目标数量
#define MAX_NUM_THROWING_OBJECT      50      // 最多抛洒物数量
typedef struct tagSingleObject
{
    unsigned int dwBox[4]; // 结构化检测坐标x1, y1, x2, y2
    unsigned int dwClassNum;//结构化检测类别
} TSingleObject;

typedef struct tagThrowingDetectInput
{
    unsigned long long fwTimeStamp;                             // 时间戳
    unsigned int dwNumObjects;                                  // 目标数量
    TSingleObject atSingleObject[MAX_NUM_OUTPUT_SINGLE_OBJECT]; // 目标信息
} TThrowingDetectInput;

typedef struct tagThrowingObject
{
    float fPoint[2];    // 抛洒物 中心位置
} TThrowingObject;

typedef struct tagThrowingDetectOutput
{
    unsigned long long fwTimeStamp;                             // 时间戳
    int bIsThrowing;                                           // 是否抛洒物
    unsigned int dwThrowingNum;                                 // 抛洒物数量
    TThrowingObject atThrowingObject[MAX_NUM_THROWING_OBJECT];  // 抛洒物信息
}TThrowingDetectOutput;
////////////////抛洒物检测///////////////
///////////////安全帽检测////////////

////////////////////////黑烟车nnie----检测////////////////
//OPEN info for pvOpen in IMGVideoAlgOpen
typedef struct tagKEDADarkDetectNnieOpen
{
    s32 nnie_id;
    s32 s32Height; //输入图像大小的高度
    s32 s32Width;  //输入图像大小的宽度
}TKEDADarkDetectNnieOpen;


//INPUT for TImageInfo.pvImageInfo
typedef struct tagDarkDetectROI    //黑烟车检测随影输入图像信息
{
    u32 u32RoiX[MAX_IMAGE_INFO]; //每一帧结构化检测每个目标x坐标，最大目标数暂定50？
    u32 u32RoiY[MAX_IMAGE_INFO]; //每一帧结构化检测每个目标y坐标
    u32 u32RoiWidth[MAX_IMAGE_INFO]; //每一帧结构化检测每个目标宽度
    u32 u32RoiHeight[MAX_IMAGE_INFO]; //每一帧结构化检测每个目标高度
    u32 u32TruckFlag[MAX_IMAGE_INFO]; //每一帧结构化检测每个目标对应是否为黄牌车或者卡车的标志位
    u32 u32VideoFlag; //输入图像是否为最后一帧图像标志位
    u32 u32MultiDetectNum;//每一帧结构化目标检测的目标总数
}TDarkDetectROI;

//OUTPUT for TImageInfo.pvImageInfo
typedef enum tagDarkSmokeType
{
    IS_DARKSMOKE,
    NOT_DARKSMOKE,
}EMDarkSmokeType;
typedef struct 
{
    float f32DarkSmokeScore;    //检测为黑烟车的帧数/黄牌车帧数的概率，用于业务外部调试和感受
    EMDarkSmokeType emDarkSmokeType;//算法内部判断输出是否为黑烟车结果
}TDarkSmokeOutputInfo;//黑烟车检测输出结构体

////////////////////////黑烟车nnie----检测////////////////

////////////////////////视频结构化----多目标检测////////////////
typedef struct tagKEDAMultiDetectNnieOpen
{
    int nnie_id;
}TKEDAMultiDetectNnieOpen;
typedef enum tagMulTarget
{
    car = 0,
    human,
    Motorcycle,
    electromobile,
    bike,
    electrotricycle,
}EMMulTarget;
typedef struct  //输出人形检测结果对应的TImageBuffer的tag以及对应坐标信息
{
    s32 up;
    s32 down;
    s32 left;
    s32 right;
    float score;
    // s32 s16ObjectsIndex; //目标所属类别
    EMMulTarget MulTarget;
    void* pvbuffertag;
}TRectMultiObject;
typedef struct //multidetect输出结构体
{
    u32 u32MultiDetectNumber;               // 输出多目标检测个数
    TRectMultiObject tMultiRectOut[MULTI_DETECTED_MAXNUM];//多目标坐标
}TMultiDetectOutputInfo;


////////////////////////全车牌检测////////////////
typedef struct tagKEDACarPlateNnieOpen
{
    int nnie_id;
}TKEDACarPlateNnieOpen;

typedef struct  //输出车牌检测结果对应的TImageBuffer的tag以及对应坐标信息
{
    s32 up;
    s32 down;
    s32 left;
    s32 right;
    float score;

    void* pvbuffertag;
}TRectPlateObject;
typedef struct 
{
    u32 u32CarPlateNumber;               // 输出车牌检测个数
    TRectPlateObject tCarPlateRectOut[MULTI_DETECTED_MAXNUM];
}TCarPlateOutputInfo;


typedef enum tagBayerPatternType
{
    RGGB = 0,            //人员卡口支持 20190821
    GBRG,                //车辆卡口支持 20190505
    GRBG,
    BGGR,
}EMBayerPatternType;



////////////////////////低照增强-人员卡口////////////////


////////////////////////低照增强-视讯////////////////
//OPEN info for pvOpen in IMGVideoAlgOpen
typedef struct tagKEDADarkEnhanceVideoNnieOpen
{
    EMBayerPatternType emBayerPatternType;    //raw的bayer格式
    s32 s32RawWidth;                        //输入的raw的宽度
    s32 s32RawHeight;                        //输入的raw的高度
    s32 s32RawBitwidth;                        //输入的raw的有效位宽
    s32 s32BlackLevel;                        //黑电平
    s32 s32NnieId;                            //指定nnie编号
}TKEDADarkEnhanceVideoNnieOpen;

//INPUT for TImageInfo.pvImageInfo
typedef struct tagDarkEnhanceVideoNnieMoreINFO
{    
    s32* ps32PhyAddrNnieData;            // s32类型的nnie输入输出 物理地址
    s32* ps32VirAddrNnieData;            // s32类型的nnie输入输出 虚拟地址
    u16* pu16rawinput;                    // u16 raw 输入
}TDarkEnhanceVideoNnieMoreInfo;



typedef struct tagDarkEnhanceVideoGPUInputINFO  
{    
    s32* ps32PhyAddrNnieData;            // s32类型的nnie输出 物理地址
    s32* ps32VirAddrNnieData;            // s32类型的nnie输出 虚拟地址
    // 以下参数仅超微光GPU处理部分传入
    s32 s32DmaBufNum;                   // DMA 映射返回的下标

    // AE机制
    u32 u32AEFlags;                     // 是否开启超微光AE机制 0 关闭  1 开启
    float f32AEWeight;                    // 当前帧计算出来的Scale权重 默认设置0.2
    float f32ScaleRatio;                // 该帧使用的增强系数
    float f32TargetLux;                    // 目标亮度 0-255
    float f32LuxToLerance;                // 目标亮度容忍度 默认5 
    u32 u32frameStep;                    // AE 间隔
    float f32GammaRatioA;                // Gamma参数A
    float f32GammaRatioB;                // Gamma参数B

    s32 s32Flip;                         //          s32Flip   s32Mirror
    s32 s32Mirror;                       //正常显示     0          0   
                                        //水平镜像     0          1
                                        //垂直镜像     1          0 
                                        //180翻转      1          1


}TDarkEnhanceVideoGPUInputInfo;


typedef struct tagDarkEnhanceVideoMoreINFO
{
    float f32ScaleRatio;    //低照增强的幅度
    //float f32BlendRatio;    //与isp处理图融合时所占的权重
    //u8* pu8ImageISP;        //用来融合的isp处理后图像， 格式为 RGB CHW  视讯暂时未使用
    u16* pu16Raw;            //原始RAW数据-宽高在open时已指定
}TDarkEnhanceVideoMoreInfo;

typedef struct tagDarkEnhanceVideoGpuMoreINFO
{    
    u8 *pu8VirAddGpuOut4kData;        // GPU 输出的4K 数据 带物理地址和虚拟地址   目前无法实时算法不支持4k输出
    u8 *pu8PhyAddGpuOut4kData;

    u8 *pu8VirAddGpuOut1080pData;    // GPU 输出的1080p 数据 带物理地址和虚拟地址
    u8 *pu8PhyAddGpuOut1080pData;

    float f32ScaleRatio;                // AE调节后的增强系数，需要传给下一帧的CPU前处理
}TDarkEnhanceVideoGpuMoreInfo;

//OUTPUT for TImageInfo.pvImageInfo
typedef struct //DarkEnhanceCar输出结构体
{
    u8* pu8ImgNetOutput;        //用来存放网络输出的图像， 格式为 RGB HWC
    u8* pu8ImgResizeOutput;        //用来存放Resize后的图像， 格式为 RGB HWC
}TDarkEnhanceVideoOutputInfo;


//------------------------按计算资源切分-------------------------//
typedef struct tagKEDADarkEnhanceVideoPreprocess
{
    EMBayerPatternType emBayerPatternType;    //raw的bayer格式
    s32 s32RawWidth;                        //输入的raw的宽度
    s32 s32RawHeight;                        //输入的raw的高度
    s32 s32RawBitwidth;                        //输入的raw的有效位宽
    s32 s32BlackLevel;                        //黑电平

    float f32ScaleRatio;    //低照增强的幅度

    // unsigned short int* pu16rawinput;       //输入的raw图像指针
    // s32 *ps32NnieInput;                        // 预处理结果  即NNIE的输入

}TKEDADarkEnhanceVideoPreprocess;

typedef struct tagKEDADarkEnhanceVideoNnieprocessOpen
{

    s32 s32NnieId;      // nnie id

}TKEDADarkEnhanceVideoNnieprocessOpen;





//------------------------按计算资源切分-------------------------//

////////////////////////低照增强-视讯////////////////


//////////////////////// 视讯Matting //////////////////////////////////

typedef struct tagKEDAMattingVideoNnieprocessOpen
{
    s32 s32NnieId;          // nnie id

}TKEDAMattingVideoNnieprocessOpen;



typedef struct tagMattingVideoNnieMoreINFO
{        
    s32* ps32PhyAddrNnieData;                        // s32类型的nnie输入输出 物理地址
    s32* ps32VirAddrNnieData;                        // s32类型的nnie输入输出 虚拟地址
    u8*  pu8SrcImgNV21;                                 // 输入图像  NV21数据

}TMattingVideoNnieMoreInfo;



typedef struct tagMattingVideoGPUMoreINFO
{        
    s32 s32GpuAlphaDmaBufNum;          // NNIE 输出结果Dmabuf 映射地址
    // s32 s32GpuSrcYUVDmaBufNum;      // GPU 输入原图的YUV Dmabuf 映射地址  1920 *1080
    // s32 s32GpuDstYUVDmaBufNum;      // GPU 输出图像的YUV Dmabuf 映射地址  1920 *1080
    // s32 s32GpuBackGYUVDmaBufNum;      // GPU 输入背景的YUV Dmabuf 映射地址  1920 *1080

    u8 *pu8SrcYuv;                       // 输入原图的指针 1920*1080*1.5 u8
    u8 *pu8BackYuv;                   // 输入背景图的指针 1920*1080*1.5 u8

    u8* pu8PhyAddrMattingData;        // u8类型背景替换/虚化输出 物理地址  1920*1080*1.5 u8
    u8* pu8VirAddrMattingData;        // u8类型背景替换/虚化输出 虚拟地址 1920*1080*1.5 u8

    s32 s32Flag;                      // 开关   设置 1 开启 背景虚化 设置 0 开启 背景替换 

}TMattingVideoGPUMoreInfo;

//////////////////////// 视讯Matting //////////////////////////////////



typedef enum tagResolutionType
{
    W1920H1080 = 0,        //宽1920 高1080
    W320H320,            //宽320 高320
    W640H640,            //宽640 高640
    W1000H2000,            //宽1000 高2000   
    W2560H1440,
}EMResolutionType;

typedef enum tagDeviceType   //设备
{
    IPC126 = 0,        // 200W硬件
    IPC146 = 1,        // 400W硬件
    IPC186 = 2,        // 800W硬件
    IPC186_334 = 3,    // 800W硬件 imx334传感器设备
    IPC522 = 4,        // 4G球3559A 超微光设备 OV 传感器
    IPC522_Mars,       // 4G球3559A 超微光设备 mars 传感器
}EMDeviceType;
//OPEN info for pvOpen in IMGVideoAlgOpen
typedef struct tagKEDADarkEnhanceManNnieOpen
{
    EMBayerPatternType emBayerPatternType;    //raw的bayer格式 人卡目前支持RGGB的pattern
    s32 s32RawWidth;                        //输入的raw的宽度
    s32 s32RawHeight;                        //输入的raw的高度
    s32 s32RawBitwidth;                        //输入的raw的有效位宽
    // s32 s32BlackLevel;                        //黑电平
    s32 s32NnieId;                            //指定nnie编号
    EMResolutionType emResolutionType;        //开启的分辨率模式（每次open仅支持一种分辨率模式，支持类型列表见enum列表）
    EMDeviceType emDeviceType;              // 开启设备类型
}TKEDADarkEnhanceManNnieOpen;


typedef struct tagKEDADarkEnhance4GballNnieOpen
{
    s32 s32RawWidth;                        //输入的raw的宽度
    s32 s32RawHeight;                        //输入的raw的高度
    s32 s32RawBitwidth;                        //输入的raw的有效位宽
    s32 s32NnieId;                            //指定nnie编号
    EMDeviceType emDeviceType;              // 开启设备类型 目前4G球对应老版本 IPC522 和更换传感器后的 IPC522_Mars 设备
}TKEDADarkEnhance4GballNnieOpen;

// typedef enum tagCarPlateRecogModelType   //add by wjx 0912
// {
//     BigCarPlateRecogModel = 0,  //车牌识别大模型  耗时56ms
//     SmallCarPlateRecogModel,    // 车牌识别小模型 耗时12ms
// }EMCarPlateRecogModelType;

/********** zhulin lpr 接口使用 add by zsy **********/
typedef enum tagCarPlateRecogModelMODELSIZE   //add by wjx 0912
{
    SmallCarPlateRecogModelNnie = 0,        // 车牌识别小模型 
    BigCarPlateRecogModelNnie,              // 车牌识别大模型
}EMCarPlateRecogMODELSIZE;

typedef struct tagKEDACarplateForLprHandle
{    
    EMCarPlateRecogMODELSIZE emLprCarDetectionModelSize;      // 车牌检测模型Size
    EMCarPlateRecogMODELSIZE emLprCarRecogModelSize;          // 车牌识别模型Size
    void *ptCarPlateDetectionNnieHandle;          // 车牌检测模型
    void *ptCarPlateClsNnieHandle;              // 车牌分类模型
    void *ptCarPlateAligNnieHandle;              // 车牌对齐模型
    void *ptCarPlateRecogNnieHandle;              // 车牌识别模型
}KEDACarplateForLprHandle;

/********** zhulin lpr 接口使用 add by zsy **********/

typedef struct tagKEDACarPlateRecogNnieopen
{
    s32 nnie_id;    //指定nnie编号
    EMCarPlateRecogModelType emCarPlateRecogModelType;  //车牌识别模型
}TKEDACarPlateRecogNnieopen;

//INPUT for TImageInfo.pvImageInfo
typedef struct tagDarkEnhanceManROI
{
    u32 u32RoiX;
    u32 u32RoiY;
    u32 u32RoiWidth;
    u32 u32RoiHeight;
}TDarkEnhanceManROI;
typedef struct tagDarkEnhanceManMoreINFO
{
    float f32BlendGainMin;    //  融合时设定的Gain最小值   小于该值融合输出为ISP的结果    10 
    float f32BlendGainMax;    //  融合时设定的Gain最大值   大于该值融合输出全为网络输出   20
    float f32ScaleGainMin;    //  模型推理时设定的Gain最小值 小于该值Y_target = Y_isp     30 
    float f32ScaleGainMax;    //  模型推理时设定的Gain最大值  大于该值Y_target 设定为50   36
    float s32BrightnessRatio; //  控制推理时autoratio的大小  该值越大输出图像越亮         1.0
    float f32ISPGain;       //ISP 增益Gain的值
    float f32UnsharpRatio;  //反锐化掩膜ratio
    s32 s32BlackLevel;        //黑电平
    float f32BlendRatio;    //与isp处理图融合时所占的权重
    u8* pu8ImageISP;        //用来融合的isp处理后图像， 格式为 RGB CHW
    u16* pu16Raw;            //原始RAW数据-宽高在open时已指定
    TDarkEnhanceManROI tDarkEnhanceManROI;    //待增强的roi区域

    u32 u32ScaleRatioFlag;  //  1  开启自适应模式 0 开启手动模式 手动模式下使用f32ScaleRatio 参数传入增强幅度， 自适应情况下内部自动判断
    float f32ScaleRatio;    //低照增强的幅度
    u8 u8TargetBrightness;  // 目标亮度 0-255 默认设置120

}TDarkEnhanceManMoreInfo;



typedef struct tagDarkEnhance4GballMoreINFO
{
    s32 s32BlackLevel;        //黑电平
    float f32ScaleRatio;    //低照增强的幅度
    float f32BlendRatio;    //与isp处理图融合时所占的权重
    u8* pu8ImageISP;        //用来融合的isp处理后图像， 格式为 RGB CHW
    u16* pu16Raw;            //原始RAW数据-宽高在open时已指定
}TDarkEnhance4GballMoreInfo;

//OUTPUT for TImageInfo.pvImageInfo
typedef struct //DarkEnhanceMan输出结构体
{
    u8* pu8ImgNetOutput;        //用来存放网络输出的图像， 格式为 RGB CHW
    u8* pu8ImgBlendOutput;        //用来存放融合了isp处理后的图像， 格式为 RGB CHW
    float f32AutoScaleRatio;    //自适应低照增强的幅度
    float f32AutoBlendRatio;    //自适应与isp处理图融合时所占的权重
}TDarkEnhanceManOutputInfo;

typedef struct //DarkEnhance4G输出结构体
{
    u8* pu8ImgBlendOutput;        //用来存放融合了isp处理后的图像， 格式为 RGB CHW
}TDarkEnhance4GOutputInfo;





////////////////////////反锐化掩模////////////////

typedef enum tagUnsharpKernelType   //add by wjx 01030
{
    LumaGaussMask = 0,  // 高斯大核
    ChromaGaussMask,    // 高斯小核
}EMUnsharpKernelType;

typedef struct tagUnsharpMaskINFO
{
    float f32HighFreqRatio;    //叠加的高频信息比例
    EMUnsharpKernelType emUnsharpKernelType; // unsharp 核
}TUnsharpMaskInfo;

////////////////////////RAW2RGB 车辆卡口-过曝车牌raw重建////////////////
//INPUT for TImageInfo.pvImageInfo
typedef struct
{
    s32 l32X;
    s32 l32Y;
}TPoint_xy;

typedef struct tagRAW2RGBINFO
{
    EMBayerPatternType emBayerPatternType;    //raw的bayer格式
    s32 s32PlateRawWidth;                    //输入车牌的raw的宽度
    s32 s32PlateRawHeight;                    //输入车牌的raw的高度

    s32 s32PlateRawX;                        //输入车牌的raw相对拓展区域的左上角X左边
    s32 s32PlateRawY;                        //输入车牌的raw相对拓展区域的左上角Y左边

    s32 s32ExtendRawWidth;                    //输入扩展区域的raw的宽度
    s32 s32ExtendRawHeight;                    //输入扩展区域的raw的高度

    s32 s32ExtendRawX;                        //输入扩展区域相对原图 左上角X
    s32 s32ExtendRawY;                        //输入扩展区域相对原图 左上角Y

    s32 s32RawBitwidth;                        //输入的raw的有效位宽
    u16 u16BlackLevel;                        //黑电平

    u8 u8sat_level;                            //强度调节范围 1-10 ，默认为5。 1级饱和度最弱接近灰色，10级 饱和度最强。 
    u8 u8GR_in;                                // 抓拍手动白平衡增益值 R通道 （1-100）
    u8 u8GB_in;                                // 抓拍手动白平衡增益值 B通道 （1-100）

    u8 *pu8yuv;                             // 输入的YUV
    u16* pu16PlateRaw;                        //车牌RAW数据
    u16* pu16ExtendRaw;                        //扩展区域RAW数据

    s32 s32RGBWidth;                        // 原图宽
    s32 s32RGBHeight;                        // 原图高
    u8 u8color_flag;                        //车牌颜色分类 1：蓝牌 2：黄牌  3：纯色  4：混合新能源 黄绿牌
    TPoint_xy atKeyPt[4];                   //车牌关键点坐标 右下，左下，左上，右上
}TRAW2RGBInfo;

//OUTPUT for TImageInfo.pvImageInfo
typedef struct //RAW2RGB输出结构体
{
    u8 color_flag;         // 判断出的颜色信息
    u8* pu8ImgOutput;        //用来存放网络输出的图像， 格式为 RGB CHW
    float f32exratio_row;     // 上下扩展的比例
    float f32exratio_col;        // 左右扩展的比例
}TRAW2RGBOutputInfo;


#define DEBUG 1
#define RELEASE 0
#define STATUS RELEASE

////////////////////////长沙非机动车牌检测////////////////
typedef struct tagCSPlateDetectOpen
{
    int nnie_id;
}TCSPlateDetectOpen;
/*输入为300*300的非机动车bgr*/
/*输出结构体复用现有车牌检测的输出结构体TVehicleProjetInfo_DL*/
/*输出为车牌4个角点的坐标4个角点的坐标x、y值(顺序依次为左上、右上、右下、左下)*/



/********************humandetect_nnie 审讯留置室数人头********************/

typedef struct tagKEDADetectNnieOpen_InquiryRoom
{
    int nnie_id;
    s8 s8HumanDetectModelPath[MAX_MODEL_NUM][200];
}TKEDADetectNnieOpen_SVR_InquiryRoom;

typedef struct tagHumanROI    //人员检测的范围（是否需要使用待确认）
{
    u32 u32RoiX;
    u32 u32RoiY;
    u32 u32RoiWidth;
    u32 u32RoiHeight;
}THumanDetectROI;

typedef struct  //输出人形检测结果对应的TImageBuffer的tag以及对应坐标信息
{
    s32 up;
    s32 down;
    s32 left;
    s32 right;
    float score;
    void* pvbuffertag;
}TRectHuman_NNIE;

typedef struct //humandetect输出结构体
{
    u32 u32HumanDetectNumber;               // 输出检测出的人员数量
    TRectHuman_NNIE tHumanRectOut[MAX_HUMAN_DETECT_INFO];
}THumanDetectOutInfo_SVR_InquiryRoom;

/**************************humandetect_nnie 审讯留置室数人头 end***********************************************/

//#######################################################################################################################
//
//
//900万车辆卡口人脸识别海思平台项目 
//
//
/* ============人脸检测===============*/
typedef struct tagKEDADetectNnieOpen_VAC
{
    EMImageFormat emFormat;
    int nnie_id;
}TKEDADetectNnieOpen_VAC;

typedef struct tagManmodelinput_VAC    //人员检测的范围（是否需要使用待确认）
{
    u32 u32RoiX;
    u32 u32RoiY;
    u32 u32RoiWidth;
    u32 u32RoiHeight;
}TManDetectROI_VAC;
    
typedef struct
{
    s32 up;
    s32 down;
    s32 left;
    s32 right;
    float score;
    void* pvbuffertag;
}TRectMan_VAC;

typedef struct 
{
    u32 u32ManDetectNumber;               
    TRectMan_VAC tManRectOut[MAX_DETECT_INFO];
}TManDetectOutputInfo_VAC;

//#######################################################################################################################



///////////////////////////////////////////////////////////////
/*****************视讯K2鱼眼相机人脸检测识别************************/
typedef struct
{
    u8 *pu8ManNnieModel;
    s64 s64ModelSize;
}TManModelAddr_K5;

typedef struct tagJPEGDecoderinfo_K5
{
    s32 s32width;
    s32 s32height;
    s32 s32JpegSize;
    u8 *pu8JpegBuffer;
}TJPEGDecoderinfo_K5;

typedef struct tagKEDAManDetectNnieOpen_K5
{
    EMImageFormat emFormat;
    TManModelAddr_K5 tManModelAddr[MAX_MODEL_NUM];
    // s8 s8ManNnieBinPath[MAX_MODEL_NUM][200];
}TKEDAManDetectNnieOpen_K5;

typedef struct tagManmodelROI_fisheye    //人员检测的范围（是否需要使用待确认）
{
    u32 u32RoiX;
    u32 u32RoiY;
    u32 u32RoiWidth;
    u32 u32RoiHeight;
    u32 u32FisheyeMinidx;
    u32 u32FisheyeMaxidx;
    u32 u32MinFaceSize;
    /*feature*/
    int Flag;     //0: 人脸检测 1：人脸特征提取+人脸比对
    float f32PoseScoreThresh;  //姿态分阈值
    /*compare*/
    float* pInputFeatureSet;   //底库特征
    s32     NumOfFeatureSet;    //底库特征数
    float   Threshold;          //人脸比对阈值
    u64* au64PhyAddr;
}TManDetectROI_fisheye;

typedef struct  //输出人员检测结果对应的TImageBuffer的tag以及对应坐标信息
{
    TPlatePoint pt[4];      // 4个角点的坐标x、y值(顺序依次为左上、右上、左下、右下)
    float score;
    float PoseScore;
    /*feature*/
    // float Feature[256]; //底库特征
    /*compare*/
    float   VerScore; // 相似度
    s32     Verified;   // 是否通过
    s32     MaxScoreID;
    
    void* pvbuffertag;
}TRectMan_NNIE_fisheye;

typedef struct
{
    u32 u32ManDetectNumber;               // 输出检测出的人员数量
    TRectMan_NNIE_fisheye tManRectOut[MAX_DETECT_INFO];
}TManDetectOutputInfo_NNIE_fisheye;

typedef struct 
{
    s32 up;
    s32 down;
    s32 left;
    s32 right;
    float score;
    int Flag;    //判断特征是否生成成功
    void* pFeatureBuffer;
}TManFaceFeatureOutputInfo_NNIE_fisheye;

///////////////////////////////////////////////////////////////

//#######################################################################################################################
//人脸认证项目 
/* ============人脸检测===============*/
typedef struct    //人员检测的范围
{
    u32 u32RoiX;
    u32 u32RoiY;
    u32 u32RoiWidth;
    u32 u32RoiHeight;
    u32 ISPthreshold;
    u32 u32ModelFlag;//0:其他  1：实时码流
}TManDetectInput_FV;

typedef struct  //输出人员检测结果对应的TImageBuffer的tag以及对应坐标信息
{
    s32 up;
    s32 down;
    s32 left;
    s32 right;
    u32 lightness;    //0:亮度不足  1：亮度满足要求
    float x[5];
    float y[5];
    float score;
    void *pvbuffertag;
}TRectMan_FV;

typedef struct //人脸抓拍输出结构体
{
    u32 u32ManDetectNumber;               // 输出检测出的人员数量
    TRectMan_FV tManRectOut[MAX_DETECT_INFO];
}TManDetectOutputInfo_FV;

/* ============活体检测===============*/
typedef struct//活体检测的输出结构体
{
    s32 vivoFlag;    //是否为活体，1：是，0：不是
}TVivoDetectOutputInfo_FV;

/* ============特征提取===============*/
typedef struct     //人员检测的范围
{
    u32 u32RoiX;
    u32 u32RoiY;
    u32 u32RoiWidth;
    u32 u32RoiHeight;
    float x[5];
    float y[5];
}TManFaceFeatureInfo_FV;

typedef struct 
{
    void *pFeatureBuffer;   //特征值
}TManFaceFeatureOutputInfo_FV;

/* ============人脸比对===============*/
typedef struct 
{
    float *pInputOneFeature;   
    float *pInputFeatureSet;
    s32     NumOfFeatureSet;
    float   Threshold;     //比对阈值
}TManFaceComparInputInfo_FV;

typedef struct 
{
    float   VerScore;    // 相似度
    s32     Verified;    // 是否通过 0：不通过，1：通过
    s32     MaxScoreID;
}TManFaceComparOutputInfo_FV;
//#######################################################################################################################


//********************单球跟踪_3519A********************/


typedef struct
{
    TManDetectROI *tman_roi;      //roi区域坐标
    float fZoomTime;
    u32 u32CurRatio;             //当前倍率
    float f32ObjectRatio;        //zoom拉动时目标比例上限
    float f32moveDist;           //运动距离判断
    int startFrame;
    int detFrame;                //跟踪多少帧停止
    int detFailFrame;            //跟踪过程中漏检多少帧停止
    double minDist;              // 最小检测半径 
    u8 u8priority;               //优先级
}TINTTRACKParam;

typedef struct 
{
    s16 s16FirstFrameFlag;     //第一帧的标志0:不跟踪，1：点对点，2：V，3：delatV, 4.回到初始跟踪位置
    double u32PixelX;               //s16FirstFrameFlag=1时，点对点方式（160ms内）把目标拉到画面中心
    double u32PixelY;
    float f32score;
    
    u32 u32ZoomValue;
    float f32ZoomFlag;         //变倍标志(0:不变倍，非零:实际zoom值)
    
    u32 l32HorDrection;       //水平方向  （s16FirstFrameFlag=0）
    u32 l32VerDrection;       //垂直方向  （s16FirstFrameFlag=0）
    double u32PixelDiffHor;      //速度模式 水平像素差  （s16FirstFrameFlag=0）
    double u32PixelDiffVer;      //速度模式 垂直像素差   （s16FirstFrameFlag=0）
    double u32Time;              //时间延时
    u32 u32width;              //输出宽信息
    u32 u32height;             //输出高信息

    u32 u32Reserved; 
}TIntelligentTrackOutputInfo;


//#######################################################################################################################


//#######################################################################################################################


//*******************车辆单球跟踪_3519A********************/

typedef struct 
{
    s16 s16FirstFrameFlag;     //第一帧的标志 0:不跟踪，1：点对点，2：V，3：delatV, 4.回到初始跟踪位置
    double u32PixelX;               //s16FirstFrameFlag=1时，点对点方式（160ms内）把目标拉到画面中心
    double u32PixelY;
    float f32score;
    
    u32 u32ZoomValue;
    float f32ZoomFlag;         //变倍标志(0:不变倍，非零:实际zoom值)
    
    u32 l32HorDrection;       //水平方向  （s16FirstFrameFlag=0）
    u32 l32VerDrection;       //垂直方向  （s16FirstFrameFlag=0）
    double u32PixelDiffHor;      //速度模式 水平像素差  （s16FirstFrameFlag=0）
    double u32PixelDiffVer;      //速度模式 垂直像素差   （s16FirstFrameFlag=0）
    double u32Time;              //时间延时
    u32 u32width;              //输出宽信息
    u32 u32height;             //输出高信息

    u32 u32Reserved;
    u32 u32MultiDetectNumber;               // 输出目标检测个数
    TRectMultiObject tMultiRectOut[MULTI_DETECTED_MAXNUM];//所有目标坐标

}TIntelligentTrackCarOutputInfo;


//#######################################################################################################################

//********************交通信号灯描红项目********************/

#define REDLIGHTDETECT_MAX_ROI_NUM 6  //ROI区域最多个数
#define REDLIGHTDETECT_MAX_LIGHT_NUM_EACH_ROI 6  //ROI区域内最多灯牌数

//输入图像BGR格式像素内交错存放的图像数据
typedef enum tagRedLightType
{
    REDTYPE_PEDESTRIAN = 0,
    REDTYPE_ARROW_TURN_LEFT,
    REDTYPE_ARROW_TURN_RIGHT,
    REDTYPE_ARROW_TURN_AROUND,
    REDTYPE_ARROW_STRIGHT,
    REDTYPE_ROUND_MOTOR_VEHICLE,
    REDTYPE_NON_MOTOR_VEHICLE,
    REDTYPE_SINGLE_NUMBER_PLATE,
}EMRedLightType;

typedef struct tagTime
{
    s32 s32hour;
    s32 s32minute;
    s32 s32second;
}TKEDATime;

// 输入图像信息结构体
typedef struct tagImageBuffer_TL
{
    u8* pu8ImageDataY;     // 输入/输出图像Y分量指针
    u8* pu8ImageDataU;     // 输入/输出图像U量指针，和图像格式相关，如果为NV12，则为UV分量指针
    u8* pu8ImageDataV;     // 输入/输出图像V分量指针，和图像格式相关，如果为NV12，则无效
    u32 u32RoiX;           //相对于原图的ROI区域坐标
    u32 u32RoiY;           //相对于原图的ROI区域坐标
    u32 u32Width;            // ROI图像宽度
    u32 u32Height;           // ROI图像高度
    u32 u32PitchY;           // 输入/输出图像Y分量跨度
    u32 u32PitchUV;          // 输入/输出图像UV分量跨度
    u32 u32Flag;             // 输出图像有效标识，1为有效，0为无效
    s32 fd;                           // ION buffer id
    EMImageFormat emFormat;           // 图像格式 
    s32 s32RawWidth;         //raw图宽度
    s32 s32RawHeight;        //raw图高度
    void *pvRaw;            //RAW图指针
    void *pvbuffertag;        //与该TImageBuffer对应tag（每个TImageBuffer都唯一对应，用于释放）
    s32 s32RawPitch;         //raw图步长
}TImageBuffer_TL;

typedef struct tagTrafficLightOpen
{
    EMImageFormat emFormat;
    s32 nnie_id;
    s8 s8RedLightDetectModelPath[MAX_MODEL_NUM][200];
	s8 s8PriorboxPath[MAX_MODEL_NUM][200];
    s32 min_size[REDLIGHTDETECT_MAX_ROI_NUM][REDLIGHTDETECT_MAX_LIGHT_NUM_EACH_ROI];
    s32 ROINum;
    s32 LightBoardNum[REDLIGHTDETECT_MAX_ROI_NUM];
}TKEDATrafficLightOpen;

typedef struct tagTrafficLightROI    //交通灯灯牌输入信息
{
    u32  u32FlagRedLight;                 //0，表示没有灯；1，表示有红灯；2，表示有绿灯
    u32  u32FlagColorRed;                 //是否做交通灯描色操作，0表示不做，1表示做描红，2表示做描绿，3表示描红或描绿都做
    u32  u32LevelDehalationRed;           //是否做高斯去光晕操作，1,2,3,4,5,6,7表示不同的去光晕等级
    u32  u32LevelDehalationGreen;         //是否做高斯去光晕操作，1,2,3,4,5,6,7表示不同的去光晕等级
    u32  u32DisLevelDehalationRed;        //去光晕范围等级，0表示不做，1,2,3,4表示不同的去光晕范围,等级越高，范围越大
    u32  u32DisLevelDehalationGreen;      //去光晕范围等级，0表示不做，1,2,3,4表示不同的去光晕范围,等级越高，范围越大
    u32  u32ColourRedLevel;               //描色等级，1：正红色 2：橘色 3：粉色 0：不描色 4:南京红
    u32  u32ColourGreenLevel;             //描色等级，1：正绿色 2：淡绿色 3：青蓝色  0：不描色 4:南京绿
    EMRedLightType  emredlight_type;      //灯类别        
    u32 u32RoiX;                          //灯牌相对于ROI区域坐标
    u32 u32RoiY;                          //灯牌相对于ROI区域坐标
    u32 u32RoiWidth;                      //灯牌宽度
    u32 u32RoiHeight;                     //灯牌高度
    void *pvExternBGR;                          //灯牌扩边BGR图指针
    s32 s32ExternX;                       //灯牌扩边相对于ROI区域坐标
    s32 s32ExternY;                       //灯牌扩边相对于ROI区域坐标
    s32 s32ExternWidth;                   //扩边灯牌宽度
    s32 s32ExternHeight;                  //扩边灯牌高度
    u8 *seedMarkFinalOut;
    u32  u32FlagStdNumberLight;       //0，表示没有数字小灯；1，表示有数字小灯
    u8 *seedMarkFinalOutStdNumL;
    u32 u32FlagStdPlateYellow;       //0，表示不描黄；1,表示描黄
}TTrafficLightROI;    

typedef struct tagROIRegionInfo  //ROI区域输入信息
{
    u32 u32TrafficLightNumber;   //交通灯灯牌的个数
    u32 u32FlagGaussianDehalation;  //目前废弃
    TTrafficLightROI tTrafficLightRect[REDLIGHTDETECT_MAX_LIGHT_NUM_EACH_ROI];//交通灯灯牌区域坐标
    TImageBuffer_TL tInputData;
}TTrafficLightROIRegionInfo;

typedef struct tagTrafficLightInput    //输入信息
{
    TKEDATime tKedaTime;          //实时时间
    u32 u32ROIRegionNumber;   //ROI区域的个数
    TTrafficLightROIRegionInfo tROIRegionInfo[REDLIGHTDETECT_MAX_ROI_NUM];   
    u32 u32FlagDayNight;    //日夜切换开关，0表示白天；1表示夜晚
	u8 u8priority;
}TTrafficLightInputInfo;

typedef struct tagRedLightRECT   //红灯输出信息
{
    s32 up;                    // 区域上部 Y 轴坐标
    s32 down;                // 区域下部 Y 轴坐标
    s32 left;                // 区域左部 X 轴坐标
    s32 right;                // 区域右部 X 轴坐标
    s32 up_src;                    // 区域上部 Y 轴坐标
    s32 down_src;                // 区域下部 Y 轴坐标
    s32 left_src;                // 区域左部 X 轴坐标
    s32 right_src;                // 区域右部 X 轴坐标
    s32 up_0;                // 左边缓冲区 Y 轴坐标
    s32 down_0;                // 左边缓冲区 Y 轴坐标
    s32 left_0;                // 左边缓冲区 X 轴坐标
    s32 right_0;            // 左边缓冲区 X 轴坐标
    s32 up_1;                // 右边缓冲区 Y 轴坐标
    s32 down_1;                // 右边缓冲区 Y 轴坐标
    s32 left_1;                // 右边缓冲区 X 轴坐标
    s32 right_1;            // 右边缓冲区 X 轴坐标
    s32 s32RegionLeft;
    s32 s32RegionTop;
    s32 s32RegionStride;
    s32 s32GrayRegionLeft;
    s32 s32GrayRegionTop;
    s32 s32GrayRegionStride;
    s32 s32CentX;               //灯的中心点
    s32 s32CentY;               //灯的中心点
    s32 s32Centxy;
    float score;
    u32  u32FlagDetected;       //是否检测到灯,0:表示未检测到灯，1:表示检测到灯
    u32  u32FlagColored;          //表示灯的颜色，0:表示没有颜色，1:表示红色，2表示绿色，3表示黄色
    u32  u32Area;            //灯的面积
    u32  u32Flag;            //写入缓冲区的标识
    u32  u32GrewFlag;        //生长标识
    u32  u32IspFlag;         //isp信息标识
    u32  u32CleanFlag;       //清除标识
    s32  s32mean;   
    u8  u8meanR;   
    u8  u8meanG;   
    u8  u8meanB;   
    s32 s32MeanR_f;
    s32 s32MeanG_f;
    s32 s32MeanB_f;
    s32 s32MeanR_g;
    s32 s32MeanG_g;
    s32 s32MeanB_g;
    s32 s32WhiteArea;
    s32 s32BlackArea;
    EMRedLightType  emredlight_type;     
    u8 *seedMarkFinal;
    u8 *seedMarkFinalOut;
    u32  u32FlagStdNumL;       //0，表示数字灯未描色；1，数字灯表示数字灯描色
    s32 up_StdNumL;                // 数字灯 Y 轴坐标
    s32 down_StdNumL;                //  数字灯Y 轴坐标
    s32 left_StdNumL;                //  数字灯X 轴坐标
    s32 right_StdNumL;            //  数字灯 X 轴坐标
    u8 *seedMarkFinalOutStdNumL; //数字灯的 mask
}TRedLightRECT;


typedef struct tagTrafficPlateRECT   //灯牌输出信息
{
    s32 up;                    // 区域上部 Y 轴坐标
    s32 down;                // 区域下部 Y 轴坐标
    s32 left;                // 区域左部 X 轴坐标
    s32 right;                // 区域右部 X 轴坐标
    
    float score;
    u32  u32FlagDetected;       //是否检测到灯,0:表示未检测到灯，1:表示检测到灯
}TTrafficPlateRECT;

typedef struct tagROIRegionOut  //ROI区域输出信息
{
    u32 u32DetectNumber;   //ROI区域内检测出的红灯数
    TRedLightRECT tRedLightOut[REDLIGHTDETECT_MAX_LIGHT_NUM_EACH_ROI]; // 检测出的红灯信息

    u32 u32FlagTPCredible;//ROI区域灯牌输出信息是否可信
    u32 u32FlagTPUpdate;//ROI区域灯牌输出信息可信的前提下，建议更新灯牌信息
    u32 u32TPDetectNumber;   //ROI区域内检测出的灯牌数
    TTrafficPlateRECT tTrafficPlateOut[REDLIGHTDETECT_MAX_LIGHT_NUM_EACH_ROI]; // 灯牌信息

    void* pOutBuffer;   //输出BGR数据流
    void *pvbuffertag;  //保留参数
}TTrafficLightROIRegionOut;

typedef struct tagTrafficLightOutputInfo
{
    u32 u32RedLightDetectNumber;            // 检测出的红灯总数
    TTrafficLightROIRegionOut tROIRegionOut[REDLIGHTDETECT_MAX_ROI_NUM];    
}TTrafficLightOutputInfo;



//************大区域灯牌检测***********
typedef struct tagTrafficPlateROIRegionInfo  //ROI区域输入信息
{
    u32 u32TrafficLightNumber;   //交通灯灯牌的个数
    TTrafficLightROI tTrafficPlateRect[REDLIGHTDETECT_MAX_LIGHT_NUM_EACH_ROI];//交通灯灯牌区域坐标
    TImageBuffer_TL tInputData;
}TTrafficPlateROIRegionInfo;

typedef struct tagTrafficPlateInput    //输入信息
{
    TKEDATime tKedaTime;          //实时时间
    u32 u32ROIRegionNumber;   //ROI区域的个数
    TTrafficPlateROIRegionInfo tROIRegionInfo[REDLIGHTDETECT_MAX_ROI_NUM];    
	u8 u8priority;
}TTrafficPlateInputInfo;


typedef struct tagTPROIRegionOut  //ROI区域输出信息
{    
    u32 u32FlagTPCredible;//ROI区域灯牌输出信息是否可信
    u32 u32FlagTPUpdate;//ROI区域灯牌输出信息可信的前提下，建议更新灯牌信息
    u32 u32TPDetectNumber;   //ROI区域内检测出的灯牌数
    TTrafficPlateRECT tTrafficPlateOut[REDLIGHTDETECT_MAX_LIGHT_NUM_EACH_ROI]; // 灯牌信息

    void* pOutBuffer;   //输出BGR数据流
    void *pvbuffertag;  //保留参数
}TTrafficPlateROIRegionOut;
typedef struct tagTrafficPlateOutputInfo
{
    u32 u32FlagTPComplete;//大区域检测算法是否完成，0，正在进行；1，完成
    u32 u32TrafficPlateNumber;            // 检测出的红灯总数
    TTrafficPlateROIRegionOut tROIRegionOut[REDLIGHTDETECT_MAX_ROI_NUM];    
}TTrafficPlateOutputInfo;

//********************交通信号灯描红项目结束********************/



////////////////////////低照增强-车辆卡口////////////////
// 安霸SID
typedef struct tagSIDOpen
{
    
    s32 s32RawWidth;                                                      //输入的raw的宽度
    s32 s32RawHeight;                                                     //输入的raw的高度
    s32 s32BlackLevel;                                                    //黑电平
    s32 s32RawBitwidth;                                                      //输入的raw的有效位宽

}TKEDASIDOpen;

typedef struct tagSIDMoreInfo
{
    
    u16 *pu16Raw;    
    float f32ScaleRatio;  
    u8 u8priority;    

}TKEDASIDMoreInfo;


typedef struct tagSIDOutputInfo
{

    u8 *pu8ImageOutput;   

}TKEDASIDOutputInfo;



//OPEN info for pvOpen in IMGVideoAlgOpen
typedef struct tagKEDADarkEnhanceCarNnieOpen
{
    EMBayerPatternType emBayerPatternType;    //raw的bayer格式
    s32 s32RawWidth;                        //输入的raw的宽度
    s32 s32RawHeight;                        //输入的raw的高度
    s32 s32RawBitwidth;                        //输入的raw的有效位宽
    s32 s32BlackLevel;                        //黑电平
    s32 s32NnieId;                            //指定nnie编号
}TKEDADarkEnhanceCarNnieOpen;


typedef struct tagKEDADarkEnhanceCarOpen
{
    EMBayerPatternType emBayerPatternType;    //raw的bayer格式
    s32 s32RawWidth;                        //输入的raw的宽度
    s32 s32RawHeight;                        //输入的raw的高度
    s32 s32RawBitwidth;                        //输入的raw的有效位宽
    s32 s32BlackLevel;                        //黑电平
    s8 s8SIDModelPath[1024];                 //amba加密SID模型路径
    s8 s8GaussModelPath[1024];                 //amba加密Gauss模型路径
    u8 u8UseExtraMem;                        //1 使用预留内存 0 不使用
	s8 s8HdrModelPath[1024];                 //amba加密Hdr模型路径

}TKEDADarkEnhanceCarOpen;

typedef struct tagDarkEnhanceRedLightROI   //红灯输出信息
{
    u32 u32LevelDehalation;     // 是否做高斯去光晕操作，0表示不做，1,2,3,4表示不同的去光晕等级
    u32 u32DisLevelDehalation;  // 去光晕范围等级参数 1,2,3,4表示不同的去光晕范围,等级越高，范围越大
    
    // 在界面上画的灯牌坐标
    s32 LeftupX;                // 左上角区域 X 轴坐标 
    s32 LeftupY;                // 左上角区域 Y 轴坐标 
    s32 RightdownX;                // 右下角区域 X 轴坐标 
    s32 RightdownY;                // 右下角区域 Y 轴坐标 

    u32  u32ColourRedLevel;       //描色等级，1：正红色 2：橘色 3：粉色 0：不描色
    u32  u32ColourGreenLevel;     //描色等级，1：正绿色 2：淡绿色 3：青蓝色  0：不描色
    
    // *****************描红输出的信息************************
    
    // 以下坐标为 内部检测的精确的灯坐标信息  ---需要相对于原图坐标

    s32 LightLeftupX;                // 左上角区域 X 轴坐标 
    s32 LightLeftupY;                // 左上角区域 Y 轴坐标 
    s32 LightRightdownX;            // 右下角区域 X 轴坐标 
    s32 LightRightdownY;            // 右下角区域 Y 轴坐标 

    u32 u32ProcessedFlag;        // 算法处去光晕描红已经做过了 则该处Flag为1 ，否则为0  --存在算法处无法做去光晕描红而超微光处需要做的特殊情况（如非国标灯牌）
    
    u32 u32FlagColored;          //表示灯的颜色，0:表示没有颜色，1:表示红色，2表示绿色，3表示黄色
    
    u8* pu8Mask;
    u32 u32LightType;              // 0 表示圆灯 1 表示数字灯 2表示单独数字灯牌
    u32 u32IspFlag;                //isp信息标识


    // *****************描红输出的信息************************

}TDarkEnhanceRedLightROI;

//INPUT for TImageInfo.pvImageInfo
typedef struct tagDarkEnhanceCarMoreINFO
{
    float f32ScaleRatio;    //低照增强的幅度
    float f32BlendRatio;    //目前用于反锐化增强系数 0.0-1.0; 0.0时不开启反锐化
    u8* pu8ImageISP;        //用来融合的isp处理后图像， 格式为 RGB CHW
    u16* pu16Raw;            //原始RAW数据-宽高在open时已指定
    u32 u32rawPitch;
    u8* pu8ISPNV21;            //用来融合的isp图像， 格式为 YUV NV21
    s32 s32CcmLevel;        //饱和度等级 0 - 9
    
    u32 u32RoiNum;          // 实时流传出的检测出的 mask 数量
    TDarkEnhanceRedLightROI tRedLightIn[REDLIGHTDETECT_MAX_ROI_NUM*2]; //框选传的灯牌区域信息 数量限制为实时流描红处可能传出的最大mask 数量

    u8 u8priority; // 安霸模型推理优先级
}TDarkEnhanceCarMoreInfo;

//OUTPUT for TImageInfo.pvImageInfo
typedef struct //DarkEnhanceCar输出结构体
{
    u8* pu8ImgNetOutput;        //用来存放网络输出的图像， 格式为 RGB CHW
    u8* pu8ImgBlendOutput;        //用来存放融合了isp处理后的图像， 格式为 RGB CHW
}TDarkEnhanceCarOutputInfo;

typedef struct tagKEDAColorMaskCodecOpen
{
    u32 u32Codec;                                 // 0 启用编码器 将u8 01 表示的mask  编码为比特位01 表示的mask; 
                                                  //1 启用解码器 将比特位01 表示的mask解码为u8 01 表示的mask
}TKEDAColorMaskCodecOpen;


//---------------------------------------------------------------- 超微光车卡电警----------------------------------------------------------------

//IPC7201教师跟踪项目相关结构体
//最大的ROI个数
#define EPTZ_MAX_ROI_NUM 1

//屏蔽区域的个数
#define EPTZ_MAX_MASK_ROI_NUM 5

//最大上下讲台区域的个数
#define EPTZ_MAX_ROSTRUM_NUM 3

//最大输出的目标个数
#define EPTZ_MAX_OBJ_NUM 15

//特写转动的最小灵敏度和最大灵敏度
#define EPTZ_MIN_SENSITIVE -5
#define EPTZ_MAX_SENSITIVE 5

//讲台单目标是否在ppt里面的状态信息
typedef enum EEptzPPT
{
    EM_EPTZ_IN_PPT = 0,                         //讲台单目标在PPT里面
    EM_EPTZ_NOT_IN_PPT                          //讲台单目标不在PPT里面
}EEptzPPT;

//单目标的跟踪状态
typedef enum EEptzTrackStatus
{
    EM_EPTZ_TRACK_NORMAL = 0,                    //讲台单目标且正常跟踪
    EM_EPTZ_TRACK_LR,                            //讲台单目标从左边或右边走出讲台区域
    EM_EPTZ_TRACK_DOWN,                          //讲台单目标走下讲台
    EM_EPTZ_TRACK_MULTI,                         //讲台出现多目标
    EM_EPTZ_TRACK_LOST                           //讲台单目标丢失
}EEptzTrackStatus;

//
typedef enum
{
    EM_TEACHERTRACH_MASK_TYPE = 101,        //教师跟踪MASK是否有效扩展参数类型
}TTeacherTrackParamReservedType;

typedef struct  
{
    s32 s32StartX;
    s32 s32StartY;
    s32 s32Width;
    s32 s32Height;    
}TRect;

typedef struct
{
    u32 u32ReservedType; //EM_TEACHERTRACH_MASK_TYPE
	u32 au32MaskValid[EPTZ_MAX_MASK_ROI_NUM];
}TMaskValid;

//初始化输入参数结构体
typedef struct
{
    s32 s32ImgWidth;                              //图像宽度
    s32 s32ImgHeight;                             //图像高度
    s32 as32LeftBord[EPTZ_MAX_ROSTRUM_NUM];       //下讲台范围左边界    
    s32 as32RightBord[EPTZ_MAX_ROSTRUM_NUM];      //下讲台范围右边界    
    s32 as32DownLineY[EPTZ_MAX_ROSTRUM_NUM];      //下讲台线的Y坐标     
    s32 s32PodiumNum;                             //下讲台范围个数      (目前仅支持三个下讲台区域)
    s32 s32MaskNum;                               //屏蔽区域的个数      (目前最多支持5个屏蔽区域)
    s32 s32ROINum;                                //ROI区域的个数       (目前仅支持一个roi区域)
    TRect atROI[EPTZ_MAX_ROI_NUM];                //预设ROI区域         
    TRect atMaskROI[EPTZ_MAX_MASK_ROI_NUM];       //屏蔽区域            
    TRect tRostrum;                               //讲台位置            
    TRect tPPTRect;                               //播放PPT的幕布的位置 
    s32 s32FrameRate;                             //帧率(帧/秒)
    u32 u32Reserved;                              //保留字段
    float f32MaskThresh;                          //屏蔽区域IOU阈值
}TTeacherTrackParam;

//初始化输入参数结构体
typedef struct
{
	s32 s32ImgWidth;                              //图像宽度
	s32 s32ImgHeight;                             //图像高度
	s32 as32LeftBord[EPTZ_MAX_ROSTRUM_NUM];       //下讲台范围左边界    
	s32 as32RightBord[EPTZ_MAX_ROSTRUM_NUM];      //下讲台范围右边界    
	s32 as32DownLineY[EPTZ_MAX_ROSTRUM_NUM];      //下讲台线的Y坐标     
	s32 s32PodiumNum;                             //下讲台范围个数      (目前仅支持三个下讲台区域)
	s32 s32MaskNum;                               //屏蔽区域的个数      (目前最多支持5个屏蔽区域)
	s32 s32ROINum;                                //ROI区域的个数       (目前仅支持一个roi区域)
	TRect atROI[EPTZ_MAX_ROI_NUM];                //预设ROI区域         
	TRect atMaskROI[EPTZ_MAX_MASK_ROI_NUM];       //屏蔽区域            
	TRect tRostrum;                               //讲台位置            
	TRect tPPTRect;                               //播放PPT的幕布的位置 
	s32 s32FrameRate;                             //帧率(帧/秒)
	u64 u64Reserved;                              //保留字段
	float f32MaskThresh;                          //屏蔽区域IOU阈值
}TTeacherTrackParamAmba;

typedef struct tagTKEDATeacherTrackingOpen
{
    TTeacherTrackParam tTeacherTrackParam;
}TKEDATeacherTrackingOpen;

//amba
typedef struct tagTKEDATeacherTrackOpen
{
    TTeacherTrackParamAmba tTeacherTrackParam;
	s8 s8ModelPath[MAX_MODEL_NUM][255];                 //ModelBin路径
}TKEDATeacherTrackOpen;

//amba
typedef struct tagTKEDATeacherFeatureOpen
{
	s8 s8ModelPath[MAX_MODEL_NUM][255];                 //ModelBin路径
}TKEDATeacherFeatureOpen;

//amba
typedef struct tagTKEDATeacherDetectOpen
{
	s8 s8ModelPath[MAX_MODEL_NUM][255];                 //ModelBin路径
	s8 s8PriorboxPath[MAX_MODEL_NUM][255];              //Priorbox bin路径
}TKEDATeacherDetectOpen;

typedef struct
{
    u64 u64PhyAddrY; //Y物理地址
    u64 u64PhyAddrU; //U物理地址
    u64 u64PhyAddrV; //V物理地址
    u32 u32Reserved; //保留参数
}TTeacherTrackInput;

//amba
typedef struct
{
    u64 u64PhyAddrY; //Y物理地址
    u64 u64PhyAddrU; //U物理地址
    u64 u64PhyAddrV; //V物理地址
    u64 u64Reserved; //保留参数
	u8 u8priority[MAX_MODEL_NUM];   //网络优先级
}TTeacherTrackAmbaInput;

typedef struct
{
    TRect tHumanRect;                            //输出目标位置
    EEptzPPT eEptzPPT;                           //讲台单目标是否在ppt中的状态
    EEptzTrackStatus eEptzTrackStatus;           //讲台单目标的跟踪状态
    u32 u32Reserved;                             //保留参数
}TTeacherTrackOutput;

/* ============人脸检测===============*/
typedef struct    //人员检测的范围
{
    u32 u32RoiX;
    u32 u32RoiY;
    u32 u32RoiWidth;
    u32 u32RoiHeight;
    u32 ISPthreshold;
    u32 u32ModelFlag;//0:其他  1：实时码流
}TManDetectInput_TeacherTrack;

typedef struct    //人员检测的范围
{
	u32 u32RoiX;
	u32 u32RoiY;
	u32 u32RoiWidth;
	u32 u32RoiHeight;
	u32 ISPthreshold;
	u32 u32ModelFlag;//0:其他  1：实时码流
	u8 u8priority[MAX_MODEL_NUM];   //网络优先级
}TManDetectInputTeacherTrackAmba;

typedef struct  //输出人员检测结果对应的TImageBuffer的tag以及对应坐标信息
{
    s32 up;
    s32 down;
    s32 left;
    s32 right;
    u32 lightness;    //0:亮度不足  1：亮度满足要求
    float x[5];
    float y[5];
    float score;
    void *pvbuffertag;
}TRectMan_TeacherTrack;

typedef struct //人脸抓拍输出结构体
{
    u32 u32ManDetectNumber;               // 输出检测出的人员数量
    TRectMan_TeacherTrack tManRectOut[MAX_DETECT_INFO];
}TManDetectOutputInfo_TeacherTrack;

/* ============特征提取===============*/
typedef struct     //人员检测的范围
{
    u32 u32RoiX;
    u32 u32RoiY;
    u32 u32RoiWidth;
    u32 u32RoiHeight;
    float x[5];
    float y[5];
}TManFaceFeatureInfo_TeacherTrack;

//amba
typedef struct     //人员检测的范围
{
	u32 u32RoiX;
	u32 u32RoiY;
	u32 u32RoiWidth;
	u32 u32RoiHeight;
	float x[5];
	float y[5];
	u8 u8priority[MAX_MODEL_NUM];   //网络优先级
}TManFaceFeatureTeacherTrackAmba;

typedef struct 
{
    void *pFeatureBuffer;   //特征值
}TManFaceFeatureOutputInfo_TeacherTrack;

/* ============人脸比对===============*/
typedef struct 
{
    float *pInputOneFeature;   
    float *pInputFeatureSet;
    s32     NumOfFeatureSet;
    float   Threshold;     //比对阈值
    s32 DimOfFeature;
    s32 NumOfVerification;
}TManFaceComparInputInfo_TeacherTrack;

typedef struct 
{
    float   VerScore;    // 相似度
    s32     Verified;    // 是否通过 0：不通过，1：通过
    s32     MaxScoreID;
}TManFaceComparOutputInfo_TeacherTrack;



typedef struct tagStudentDetectObject_NNIE
{              //人脸检测的handl    
    int nnie_id;    
}TKEDAStudent_room_NNIE;


//// AMBA ////
// 初始化模型地址
typedef struct tagCarplateOpen
{
    s8 s8PriorboxPath[1024];                                                      //boxBin路径 
    s8 s8ModelPath[1024];                                                         //ModelBin路径
    u32 u32SrcHeight;                                                             //输入的高
    u32 u32SrcWidth;                                                              //输入的宽
}TKEDACARPLATEOpen;

// 输入信息
typedef struct tagmodelROI
{
    u32 u32RoiX;
    u32 u32RoiY;
    u32 u32RoiWidth;
    u32 u32RoiHeight;
    float platecls_threshold; // 获取业务设置的分类阈值
    u8 u8priority; // 优先级
    void* pvbuffertag;
}TDetectROI;

//输出信息
typedef struct tagDetectMoreInfo 
{
    s32 x_min;
    s32 y_min;
    s32 x_max;
    s32 y_max;
    s32 label;
    float score;
}TKEDADetectMoreInfo;

typedef struct tagDetectOutputInfo
{
    void* pvbuffertag;
    u32 u32DetNumber; // 输出目标个数
    TKEDADetectMoreInfo tRectOut[MULTI_DETECTED_MAXNUM];
}TKEDADetectOutputInfo;

// amba 非机动车载人
typedef struct tagNonmotorMannedOpen
{
    s8 s8ModelPath[1024];
}TKEDANonmotorMannedOpen;




////////////////////////车窗去反射////////////////////////
typedef struct tagAntiReflectNnieOpen
{
    int nnie_id;
}TAntiReflectNnieOpen;

//车窗去反射的区域
typedef struct tagAntiReflectRoi    
{
    u32 u32RoiX;
    u32 u32RoiY;
    u32 u32RoiWidth;
    u32 u32RoiHeight;
}TAntiReflectRoiInfo;

//输出结构体
typedef struct 
{
    u8* pu8ImgNetOutput;//用来存放网络输出的图像， 格式为 RGB HWC
}TAntiReflectOutputInfo;

////////////////////////车窗去反射////////////////////////
//点坐标
typedef struct
{
    s16 s16X;
    s16 s16Y;
}TPoint_alarm;

typedef struct
{
    TPoint_alarm tStartPoint; //绊线起点
    TPoint_alarm tEndPoint;    //绊线终点
    TPoint_alarm tRefA;        //参考点A
    TPoint_alarm tRefB;        //参考点B
    s16 s16TripLineID;  //绊线ID
    u8 u8TripLineType;  // #绊线类型 0(A->B); 1(B->A); 2(A & B)
}TImgDetectLine;

typedef struct  
{
    s16 s16Width;        //图像高度
    s16 s16Height;        //图像宽度
    s16 s16TripLineDetectNum;                                      //绊线个数
    TImgDetectLine atDetectTripLine[MAX_TRIPLINE_DETECT_NUM];      //绊线具体参数
    u8 u8SenseLevel;
}TTripLineInParam;


//运动目标位置信息
typedef struct
{
    s16 s16Left;                     //目标的左边界
    s16 s16Right;                    //目标的右边界
    s16 s16Top;                      //目标的上边界
    s16 s16Bottom;                   //目标的下边界
    u32 u32Area;                     //目标前景像素数
    u32 u32RectArea;                 //目标的面积
    s8 s8EffectFlag;                 //异常目标有效性标志（INVALID：无效，EFFECTIVE：有效） 是否需要？
    s16 s16AreaIndex;                //区域编号
    s16 s16ObjectsIndex;             //目标编号
}TMovingObject;
typedef struct                                                 
{
    s8 as8AlarmFlag[MAX_TRIPLINE_DETECT_NUM];                                    //该帧检测结果（报警或不报警）
    s16 s16TargetNum;                                                    //异常运动目标数   
    TMovingObject atAbnormTargets[ABNORM_OBJECT_MAXNUM];                                     //异常运动目标          
}TTripLineDetectOutput;

//ROI检测区域参数
typedef struct
{
    s16 s16ID;
    s16 s16VertexNum;
    TPoint_alarm atVertex[ABNORM_OBJECT_MAXNUM + 1];
}TImgDetectArea;

typedef struct  
{
    s16 s16Width;        //图像高度
    s16 s16Height;        //图像宽度
    s16 s16DetectAreaNum;    //检测区域数
    TImgDetectArea atDetectArea[DETECTAREA_MAXNUM];  //检测区域
    //u8 u8SenseLevel;    //灵敏度
}TEnterAreaInParam;
    
typedef struct                                                 
{
    s8 as8AlarmFlag[DETECTAREA_MAXNUM];                                    //该帧检测结果（报警或不报警）
    s16 s16TargetNum;                                                    //运动目标数   
    TMovingObject atAbnormTargets[ABNORM_OBJECT_MAXNUM];                                    //异常运动目标         
}TEnterAreaDetectOutput;

typedef struct  
{
    s16 s16Width;        //图像高度
    s16 s16Height;        //图像宽度
    s16 s16DetectAreaNum;    //检测区域数
    TImgDetectArea atDetectArea[DETECTAREA_MAXNUM];  //检测区域
    u8 u8SenseLevel;    //灵敏度
    u8 u8TrigTimeThr;   
}TInvadeAreaInParam;

typedef struct                                                 
{
    s8 as8AlarmFlag[DETECTAREA_MAXNUM];                                    //该帧检测结果（报警或不报警）
    s16 s16TargetNum;                                                    //异常运动目标数   
    TMovingObject atAbnormTargets[ABNORM_OBJECT_MAXNUM];                                     //异常运动目标          
}TInvadeAreaDetectOutput;

typedef struct                                                 
{
    s8 as8AlarmFlag[DETECTAREA_MAXNUM];                                    //该帧检测结果（报警或不报警）
    s16 s16TargetNum;                                                    //异常运动目标数   
    TMovingObject atAbnormTargets[ABNORM_OBJECT_MAXNUM];                                     //异常运动目标          
}TLeaveAreaDetectOutput;

typedef struct  
{
    s16 s16Width;        //图像高度
    s16 s16Height;        //图像宽度
    s16 s16DetectAreaNum;    //检测区域数
    TImgDetectArea atDetectArea[DETECTAREA_MAXNUM];  //检测区域
    //u8 u8SenseLevel;    //灵敏度
}TLeaveAreaInParam;

typedef struct alarm_type_select
{
    u32 tripline_flag; // 警戒线标记，0不使用，1使用
    TTripLineInParam* tripLineParam;
    void *tripLineDetect;

    u32 enterarea_flag; // 进去区域，0不使用，1使用
    TEnterAreaInParam *ptEnterAreaInParam;
    void *enterareaDetect;

    u32 invadearea_flag; // 进去区域，0不使用，1使用
    TInvadeAreaInParam *ptInvadeAreaInParam;
    void *invadeareaDetect;

    u32 leavearea_flag; // 进去区域，0不使用，1使用
    TLeaveAreaInParam *ptleaveAreaInParam;
    void *leaveareaDetect;
    
}ALARM_type_select;

typedef struct alarm_output
{
    TLeaveAreaDetectOutput *LeaveArea_output;
    TInvadeAreaDetectOutput *InvadeArea_output;    
    TEnterAreaDetectOutput *EnterArea_output;
    TTripLineDetectOutput *TripLine_output;
}ALARM_output;

typedef struct //执法记录仪人脸抓拍输出结构体
{
    u32 u32ManDetectNumber;            // 输出检测出的人员数量
    TRectMan_NNIE tManRectOut[MAX_DETECT_INFO];
    float LumaRatio;
    ALARM_output *alarm_output;
}TManDetectOutputInfo_alarm;

////////////////////////阿联酋车辆检测(车载用)////////////////////////
//OPEN
typedef struct tagUAECARDETECTOpen
{
    EMImageFormat emFormat;//图像格式 RGB NV12之类的
    u32 u32imgWidth;//图像分辨率配置(涉及到内存的分配)
    u32 u32imgHeight;
}TUAECARDETECTOpen;

//PROCESS INPUT
//process输入算法roi区域，参考结构体TManDetectROI
//在引用后通过tInputInfo.pvImageInfo指针传入

//PROCESS OUTPUT
//输出结构体
typedef struct tagUAECarRectScore
{
    s32 left;//单个车辆检出框的坐标信息(相对原图)
    s32 top;
    s32 right;
    s32 bottom;
    float score;//该车辆检出框的置信度
}TUAECarRectScore;//对单个车辆检出框的描述

typedef struct 
{
    s32 CarNum;//全图一共检出的车辆数                       
    TUAECarRectScore icvCarInfo[30];//对单个车辆检出框的描述,支持上限30个车辆框输出
}TUAECARDETECTOutputInfo;
////////////////////////阿联酋车辆检测(车载用)////////////////////////

//////////////////////////阿联酋车牌检测识别(车载用)////////////////////////
//OPEN
typedef struct tagUAELPRRECOGOpen
{
    EMImageFormat emFormat;      //图像格式 RGB NV12之类的
    u32 u32imgWidth;             //图像分辨率配置(涉及到内存的分配)
    u32 u32imgHeight;
    s32 s32PltMultiReportLen;    // 多帧上报过滤缓冲的长度
    s32 s32PltFilterRepeatLen;   // 车牌去重过滤的缓冲长度
}TUAELPRRECOGOpen;

//PROCESS INPUT
//在引用后通过tInputInfo.pvImageInfo指针传入
typedef struct tagUAECARROI
{
    s32 left;              //单个车辆检出框的坐标信息(相对原图)
    s32 top;
    s32 right;
    s32 bottom;
}TUAECARROI;//需要进行车牌检测识别的车辆框的区域信息

typedef struct tagUAELPRRECOGINFO
{
    TUAECARROI tCarRoiInfo;                   // 待识别号牌的车辆区域 
    //去重的配置参数
    s32 s32PltMultiReportLen;             // 多帧上报过滤缓冲的长度  应当小于等于open时设置的缓冲长度  若等于0则关闭多帧上报过滤
    s32 s32PltFilterRepeatLen;            // 车牌去重过滤的缓冲长度  应当小于等于open时设置的缓冲长度 若等于0则关闭车牌去重
    s32 s32CleanBuf;                      // 传入 1 清除两个车牌过滤缓冲区内的信息
    //检出车牌宽高限制参数配置-->宽高限制有一个不满足即内部过滤<--
    u32 u32PltWLimit;                     // 检测出的车牌的宽限制
    u32 u32PltHLimit;                      // 检测出的车牌的高限制
    float f32ScoreThreshold;               // 车牌得分过滤阈值
    u32 u32TimeInterval;                   // 重复车牌上报的时间间隔 单位 秒(s)
}TUAELPRRECOGInfo;

//PROCESS OUTPUT
//输出结构体
typedef struct  
{       
    s32 Flag; //flag 1 表示 车牌有效 0 表示无效
    s32 left; //车牌坐标
    s32 top;
    s32 right;
    s32 bottom; 
    float score;                //该车牌可信度评分
    u8 pvPlateType[200];        //车牌类型 即 小号
    u8 pvPlateCode[200];       //车牌大号字符串
    u8 pvPlateColor[200];        //车牌颜色字符串
    u8 pvPlateCountry[200];        //车牌对应国家
}TUAELPRRECOGOutputInfo;
////////////////////////阿联酋车牌检测识别(车载用)////////////////////////

///////////////////////4G球安霸版本车牌检测识别相关////////////////////////
typedef struct tagPltDet4GOpen
{
    EMImageFormat emFormat;
    s8 s8PltDetPriorboxPath[1024];   // 车牌检测模型bin文件路径
    s8 s8PltDetModelPath[1024];         // 车牌检测后处理bin文件路径
    s8 s8PltAlignModelPath[1024];    // 车牌对齐模型bin文件路径
    u32 u32SrcWidth;
    u32 u32SrcHeight;
    // u32 u32Prob;                   //用户设置置信度参数
}TKEDAPlateDet4GOpen;

typedef struct tagPltRecog4GOpen
{
    EMImageFormat emFormat;
    s8 s8PltClsModelPath[1024];          // 车牌分类模型bin文件路径
    s8 s8PltRecogModelPath[1024];         // 车牌识别处理bin文件路径
    u32 u32SrcWidth;
    u32 u32SrcHeight;
    // u32 u32Prob;                   //用户设置置信度参数
}TKEDAPlateRecog4GOpen;

typedef struct     
{
    u32 u32RoiX;
    u32 u32RoiY;
    u32 u32RoiWidth;
    u32 u32RoiHeight;        // ROI  过滤参数

    u8 u8priority;           // 优先级 安霸推理用
}TKEDAPlateDetect4GParam;    

typedef struct  
{
    TCarPlatestoRecogInfo tCarPlatestoRecogInfo;     // 车牌检测结果
    u32 u32Unrepeated;                               // 内部车牌识别去重开关 0 关闭 1 打开
    u32 u32FrameFilter;                              // 内部车牌多帧过滤（识别两帧结果相同才上报） 0 关闭 1 打开
    u8 u8priority;                                   // 优先级 安霸推理用
    u32 u32TimeInterval;                             // 重复车牌上报的时间间隔 单位 秒(s)
} TKEDAPlateRecog4GParam;
///////////////////////4G球安霸版本车牌检测识别相关////////////////////////

typedef struct tagKEDAIntelligenceBallOpen
{
	int nnie_id;
	s8 s8DetPath[255];      //检测模型bin路径
	u32 u32SrcWidth;
	u32 u32SrcHeight;
}TKEDAIntelligenceBallOpen;


typedef struct 
{

    s8 s8SIDModelPath[1024];                 //加密SID模型路径
    s8 s8HDRModelPath[1024];                 //加密HDR模型路径

}TKEDASIDVideoOpen;   //智能交通 视频超微光相关open 结构体

typedef struct 
{
    s8 s8ModelPath[5][1024];
}TKEDAModelPathOpen;  //算法模型载入不论平台


#ifdef __cplusplus
}
#endif

#endif 