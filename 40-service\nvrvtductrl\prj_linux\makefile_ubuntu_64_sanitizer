

TOP := ..

COMM_DIR := ../../common/

SRC_DIR := $(TOP)/source
CURDIR := ./

## Name and type of the target for this Makefile

SO_TARGET	      := nvrvtductrl


## Define debugging symbols
DEBUG = 0
PWLIB_SUPPORT = 0
FSANITIZE = 1
CFLAGS += -funwind-tables
## Object files that compose the target(s)

OBJS := $(SRC_DIR)/nvrvtductrl\
        $(SRC_DIR)/nvrvtductrl_test
## Libraries to include in shared object file
        
ifneq ($(SLIBS),) 
LDFLAGS += -Wl,-static
LDFLAGS += $(foreach lib,$(SLIBS),-l$(lib))
endif
## Add driver-specific include directory to the search path

INC_PATH += $(CURDIR)/../include \
		$(CURDIR)/../../common \
		$(CURDIR)/../../nvrcfg/include \
		$(CURDIR)/../../nvrpui/include \
		$(CURDIR)/../../nvrcap/include \
		$(CURDIR)/../../nvrnetwork/include\
		$(CURDIR)/../../../10-common/include/service \
		$(CURDIR)/../../../10-common/include/cbb/debuglog\
		$(CURDIR)/../../../10-common/include/system\
		$(CURDIR)/../../../10-common/include/cbb/protobuf \
		$(CURDIR)/../../../10-common/include/cbb/osp \
		$(CURDIR)/../../../10-common/include/cbb/charconversion\
		$(CURDIR)/../../../10-common/include/cbb/appclt\
		$(CURDIR)/../../../10-common/include/app\
		$(CURDIR)/../../../10-common/include/cbb/mediaswitch\
		$(CURDIR)/../../../10-common/include/cbb/kdssl-ext\
		$(CURDIR)/../../../10-common/include/cbb/natagent\
		$(CURDIR)/../../../10-common/include/cbb/\
		$(CURDIR)/../../../10-common/include/cbb/openssl\
		$(CURDIR)/../../../10-common/include/cbb/libwebsockets\
		$(CURDIR)/../../../10-common/include/cbb/libwebsockets/config_file/linux\
		$(CURDIR)/../../../10-common/include/app/lwshelper

CFLAGS += -D_SKYLATE_ -D__MRTC__



ifeq ($(PWLIB_SUPPORT),1)
   INC_PATH += $(PWLIBDIR)/include/ptlib/unix $(PWLIBDIR)/include
endif


INSTALL_LIB_PATH = ../../../10-common/lib/debug/ubuntu_64_sanitizer
LDFLAGS += -L$(INSTALL_LIB_PATH)
include $(COMM_DIR)/common.mk


