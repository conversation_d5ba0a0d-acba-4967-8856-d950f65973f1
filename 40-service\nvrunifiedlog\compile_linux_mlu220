path="../../10-common/version/compileinfo/nvrunifiedlog_mlu220.txt"
date>>$path

cd ./prj_linux

echo ==============================================
echo =      nvr_unifiedlog_linux for mlu220           =
echo ==============================================

echo "============compile libnvrunifiedlog mlu220============">>../$path

make -j4 -e DEBUG=0 -f makefile_mlu220 clean
make -j4 -e DEBUG=0 -f makefile_mlu220 2>>../$path

cp -L -r -f libnvrunifiedlog.so ../../../10-common/lib/release/mlu220/

cd ..
