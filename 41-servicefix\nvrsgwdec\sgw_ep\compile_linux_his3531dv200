path="../../../10-common/version/compileinfo/nvrcustcap_his3531dv200.txt"
date>>$path

cd ./prj_linux

echo ==============================================
echo =    sgw_epapp_linux for his3531dv200     =
echo ==============================================

echo "============compile epapp his3531dv200============">>../$path

make -e DEBUG=0 -f makefile_his3531dv200 clean
make -e DEBUG=0 -f makefile_his3531dv200 

date
cp -L -r -f epapp ../../../../10-common/version/release/his3531dv200/bin_sgw


echo "epapp over"
echo ==============================================
echo =    sgw_ep_pkg_linux for his3531dv200     =
echo ==============================================
./compile_linux_his3531dv200
cd ..


