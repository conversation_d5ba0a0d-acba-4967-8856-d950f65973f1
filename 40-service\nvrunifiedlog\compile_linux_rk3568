path="../../10-common/version/compileinfo/nvrunifiedlog_rk3568.txt"
date>>$path

cd ./prj_linux

echo ==============================================
echo =      nvr_unifiedlog_linux for rk3568           =
echo ==============================================

echo "============compile libnvrunifiedlog rk3568============">>../$path

make -e DEBUG=0 -f makefile_rk3568 clean
make -e DEBUG=0 -f makefile_rk3568 2>>../$path

cp -L -r -f libnvrunifiedlog.so ../../../10-common/lib/release/rk3568/

cd ..
