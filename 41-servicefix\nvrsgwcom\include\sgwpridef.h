/**
* @file 	sgwpridef.h
* @brief    nvr 隔离网关编解码器业务
* <AUTHOR>
* @date 	2020-12-17
* @version  1.0
* @copyright V1.0  Copyright(C) 2019 NVR All rights reserved.
*/

#ifndef _SGW_PRI_DEF_H_
#define _SGW_PRI_DEF_H_

#ifdef __cplusplus
extern "C" {
#endif
#include "mediactrl.h"
#include "sdscpapp_coreinterface.h"  ///<sdscp协议接口,app定义，业务实现
#include "sdscpproto_endec.h"
#include "mediaswitch.h"
#include "mca_lib.h"
#include "debuglog.h"
#include "nvrdef.h"

#define SGWPRINTDBG(args, ...) \
    if (1)  \
    {\
        DebugLogPrint(DEBUG_LOG_MOD_FIX_CORE, LOG_LEVEL_DEBUG, "%s " args, _NVRFUN_, ##__VA_ARGS__);\
    }\

#define SGWPRINTIMP(args, ...)  \
        if (1)  \
        {\
            DebugLogPrint(DEBUG_LOG_MOD_FIX_CORE, LOG_LEVEL_IMP, "%s " args, _NVRFUN_, ##__VA_ARGS__);\
        }\

#define SGWPRINTERR(args, ...)  \
        if (1)  \
        {\
            DebugLogPrint(DEBUG_LOG_MOD_FIX_CORE, LOG_LEVEL_ERR, "%s " args, _NVRFUN_, ##__VA_ARGS__);\
        }\

#define SGWPRINTFREQ(args, ...)  \
        if (1)  \
        {\
            DebugLogPrint(DEBUG_LOG_MOD_FIX_CORE, LOG_LEVEL_TEMP, "%s " args, _NVRFUN_, ##__VA_ARGS__);\
        }\

#define SGWFLASHERR(args, ...)          LogFlash(FLASH_LOG_ERR, "FIX", args, ##__VA_ARGS__)
#define SGWFLASHNOTICE(args, ...)       LogFlash(FLASH_LOG_NOTICE, "FIX", args, ##__VA_ARGS__)
#define SGWMEMNOTICE(args, ...)         LogMem(MEM_LOG_NOTICE_CORE, DEBUG_LOG_MOD_FIX_CORE, "FIX", args, ##__VA_ARGS__)

#define SGW_ASSERT(p) \
if (NULL == p)			\
{	\
    SGWPRINTERR("[%s]%s assert failed(line:%d)\n", __FILE__,__FUNCTION__, __LINE__);	\
	return NVR_ERR__ASSERT;												\
}	\



#ifdef __cplusplus
}
#endif

#endif



