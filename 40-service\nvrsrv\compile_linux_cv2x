#!/bin/bash
path="../../10-common/version/compileinfo/nvrsrv_cv2.txt"

rm -rf $path

cd ../nvrupdate/
./compile_linux_cv2x
cd -


#/////////////////////////////////////////////////////////////////////////////////
#[1]、获取版本信息
PKG_VERSION=""
filename='../../10-common/include/service/nvrsys.h'
while read line
do
#1、判断是否包含 "#define"
#用空串替换"#define"
lineLen=${#line};
line=${line/"#define"/};
lineNoDefineLen=${#line};

#2、用空串替换"#define"后长度有变化，说明"#define"存在
if [ "${lineLen}" -ne "${lineNoDefineLen}" ];
then 
#用空串替换"NVR_SYS_SOFT_VER"
line=${line/"NVR_SYS_SOFT_VER"/};
lineNoVerLen=${#line};

#用空串替换"NVR_SYS_SOFT_VER"后长度有变化，说明"NVR_SYS_SOFT_VER"存在
if [ "${lineNoVerLen}" -ne "${lineNoDefineLen}" ];
then 

	#去掉""
	line=${line#*'"'};
	line=${line%'"'*};
	PKG_VERSION=${line};
	break;

fi #if [ "${lineNoVerLen}" -ne "${lineNoDefineLen}" ];

fi #if [ "${lineLen}" -ne "${lineNoDefineLen}" ];

done < ${filename}

PKG_VERSION="${PKG_VERSION}" #在pkg包头中加入版本信息 v7中v为小写
echo ${PKG_VERSION}
export PKG_VERSION


cd prj_cv2x



./compile_linux_cv2x_fix

./compile_linux_cv2x_ptz_djiuav
./compile_linux_cv2x_ptz_vehicle
./compile_linux_cv2x_ptz_dual
./compile_linux_cv2x_ptz