

TOP := ..

COMM_DIR := ../../common/

SRC_DIR := $(TOP)/source
CURDIR := ./

## Name and type of the target for this Makefile

APP_TARGET	      := nvrsrv


## Define debugging symbols
DEBUG = 0
DO_UPX = 0
LINUX_COMPILER = _AX603A_
PWLIB_SUPPORT = 0
CFLAGS += -funwind-tables
CFLAGS += -D__MRTC__
CFLAGS += -D_AX603A_
## Object files that compose the target(s)



OBJS := $(SRC_DIR)/nvrsrv\
## Libraries to include in shared object file
LIB_PATH += $(TOP)/../../10-common/lib/release/ax603a
LIB_PATH += $(TOP)/../../10-common/lib/release/ax603a/audlib
LIB_PATH += $(TOP)/../../10-common/lib/release/ax603a/webrtc


LIBS +=	nvrcfg nvrlog nvrcap nvrcustcap nvrusrmgr nvrnetwork nvrsys nvrproto protobufc sqlite3wrapper malloc debuglog netcbb ddnsc upnpc drv pthread nvrgeo nvrcoi \
	sysdbg goaheadhelper appbase charconv appclt nvrpui nvrvtductrl nvrmpu nvrpcap nvrftp httpclient mxml ghttp go rtspclient kdmposa osp netpacket kdmtsps kdvencrypt mediaswitch stdc++ nvrqueue nvralarm nvrdev nvrupgrade nvrcrc ftpc\
	dmsrv nvrrec rpdata rp kdmfileinterface nvrguard nvrsmtp smtp airp ais algctrl dl SDEF pubsecstack curl nvrunifiedlog\
	pcap\
	m\
	kdmssl\
	kdssl-ext\
	kdmcrypto\
	cjson\
	udm\
	uuid\
	blkid\
	nvrproduct\
	kdmnatagent\
	rpdownload\
	nvrstitch\
	lwshelper\
	websockets\
	nvrmd5\
    #heapcheck\
    #tcmalloc
ifneq ($(SLIBS),) 
LDFLAGS += -Wl,-static
LDFLAGS += $(foreach lib,$(SLIBS),-l$(lib))
endif




## Add driver-specific include directory to the search path

INC_PATH += $(CURDIR)/../include \
		$(CURDIR)/../../common \
		$(CURDIR)/../../nvrcfg/include \
		$(CURDIR)/../../nvrpui/include \
		$(CURDIR)/../../nvrvtductrl/include \
		$(CURDIR)/../../nvrusrmgr/include \
		$(CURDIR)/../../nvrcap/include \
		$(CURDIR)/../../nvrnetwork/include \
		$(CURDIR)/../../nvrsys/include \
		$(CURDIR)/../../nvrdev/include \
		$(CURDIR)/../../nvralarm/include \
		$(CURDIR)/../../nvrlog/include \
		$(CURDIR)/../../nvrguard/include \
		$(CURDIR)/../../nvrsmtp/include \
		$(CURDIR)/../../nvrmpu/include \
		$(CURDIR)/../../nvrpcap/include \
		$(CURDIR)/../../airp/include \
		$(CURDIR)/../../nvrextdevmgr/include \
		$(CURDIR)/../../../10-common/include/service \
		$(CURDIR)/../../../10-common/include/cbb/debuglog\
		$(CURDIR)/../../../10-common/include/cbb/mbnet\
		$(CURDIR)/../../../10-common/include/system\
		$(CURDIR)/../../../10-common/include/hal/sysdbg\
		$(CURDIR)/../../../10-common/include/cbb/protobuf \
		$(CURDIR)/../../../10-common/include/cbb/osp \
		$(CURDIR)/../../../10-common/include/cbb/sqilte \
		$(CURDIR)/../../../10-common/include/cbb/appclt\
		$(CURDIR)/../../../10-common/include/cbb/mediaswitch\
		$(CURDIR)/../../../10-common/include/cbb/kshield\
		$(CURDIR)/../../../10-common/include/app\
		$(CURDIR)/../../../10-common/include/hal/drvlib\
		$(CURDIR)/../../../10-common/include/hal/drvlib/system

#CFLAGS += -fno-builtin-malloc -fno-builtin-calloc -fno-builtin-realloc -fno-builtin-free

ifeq ($(PWLIB_SUPPORT),1)
   INC_PATH += $(PWLIBDIR)/include/ptlib/unix $(PWLIBDIR)/include
endif


INSTALL_NO_UPX_APP_PATH := ../../../10-common/version/release/ax603a/bin_noupx
INSTALL_APP_PATH := ../../../10-common/version/release/ax603a/bin
LDFLAGS += -L$(INSTALL_LIB_PATH)
include $(COMM_DIR)/common.mk





