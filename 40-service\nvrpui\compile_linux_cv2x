path="../../10-common/version/compileinfo/nvrlib_cv2x.txt"
date>>$path

cd ./prj_linux

echo ==============================================
echo =      nvr_pui_linux for cv2x           =
echo ==============================================

echo "============compile libnvrpui cv2x============">>../$path

make -e DEBUG=0 -f makefile_cv2x clean
make -e DEBUG=0 -f makefile_cv2x 2>>../$path

cp -L -r -f libnvrpui.so ../../../10-common/lib/release/cv2x/

cd ..
