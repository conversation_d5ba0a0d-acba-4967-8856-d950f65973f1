/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: rcenccfg.proto */

#ifndef PROTOBUF_C_rcenccfg_2eproto__INCLUDED
#define PROTOBUF_C_rcenccfg_2eproto__INCLUDED

#include <protobuf-c/protobuf-c.h>

PROTOBUF_C__BEGIN_DECLS

#if PROTOBUF_C_VERSION_NUMBER < 1000000
# error This file was generated by a newer version of protoc-c which is incompatible with your libprotobuf-c headers. Please update your headers.
#elif 1002001 < PROTOBUF_C_MIN_COMPILER_VERSION
# error This file was generated by an older version of protoc-c which is incompatible with your libprotobuf-c headers. Please regenerate this file with a newer version of protoc-c.
#endif


typedef struct _TPbNvrSgwVidEncParam TPbNvrSgwVidEncParam;
typedef struct _TPbNvrSgwAudEncParam TPbNvrSgwAudEncParam;
typedef struct _TPbNvrSgwVidEncParamChn TPbNvrSgwVidEncParamChn;
typedef struct _TPbNvrSgwAudEncParamChn TPbNvrSgwAudEncParamChn;
typedef struct _TPbNvrSgwRk3308Ver TPbNvrSgwRk3308Ver;
typedef struct _TPbNvrSgwCfg TPbNvrSgwCfg;


/* --- enums --- */


/* --- messages --- */

/*
 * /<视频编码参数
 */
struct  _TPbNvrSgwVidEncParam
{
  ProtobufCMessage base;
  /*
   * /<编码格式H.264\MJpeg\H.265\SVAC\JEPG
   */
  protobuf_c_boolean has_enc_type;
  uint32_t enc_type;
  /*
   * /<帧率1~30以后支持到60
   */
  protobuf_c_boolean has_frame_rate;
  uint32_t frame_rate;
  /*
   * /<分辨率宽
   */
  protobuf_c_boolean has_res_width;
  uint32_t res_width;
  /*
   * /<分辨率高
   */
  protobuf_c_boolean has_res_height;
  uint32_t res_height;
  /*
   * /<最大关键帧间隔，一般75，1~2500
   */
  protobuf_c_boolean has_max_key_rate;
  uint32_t max_key_rate;
  /*
   * /<编码码率(kbps)，必须8整除，否则自动调整
   */
  protobuf_c_boolean has_bit_rate;
  uint32_t bit_rate;
  /*
   * /<码率控制方式CBR\VBR
   */
  protobuf_c_boolean has_rc_mode;
  uint32_t rc_mode;
  /*
   * /<图像编码质量,最低\较低\低\中等\较高\最高，定码率固定为中等不可变
   */
  protobuf_c_boolean has_quality;
  uint32_t quality;
  /*
   * /<视频编码复杂度
   */
  protobuf_c_boolean has_vid_enc_profile_id;
  uint32_t vid_enc_profile_id;
  /*
   * /<smart编码
   */
  protobuf_c_boolean has_smart_enc;
  uint32_t smart_enc;
  /*
   * /<视频编码平滑度(0~100)
   */
  protobuf_c_boolean has_bit_smooth_level;
  uint32_t bit_smooth_level;
  /*
   * /<裁剪起始x坐标
   */
  protobuf_c_boolean has_crop_x;
  uint32_t crop_x;
  /*
   * /<裁剪起始y坐标
   */
  protobuf_c_boolean has_crop_y;
  uint32_t crop_y;
  /*
   * /<裁剪宽
   */
  protobuf_c_boolean has_crop_w;
  uint32_t crop_w;
  /*
   * /<裁剪高
   */
  protobuf_c_boolean has_crop_h;
  uint32_t crop_h;
  /*
   * /<是否需要编码
   */
  protobuf_c_boolean has_is_enc;
  uint32_t is_enc;
  /*
   * /<sdscp任务的视频主通道id
   */
  protobuf_c_boolean has_vid_id;
  uint32_t vid_id;
  /*
   * /<sdscp任务的视频子通道id
   */
  protobuf_c_boolean has_sub_vid_id;
  uint32_t sub_vid_id;
  /*
   * /<sdscp任务的音频主通道id
   */
  protobuf_c_boolean has_aud_id;
  uint32_t aud_id;
  /*
   * /<sdscp任务的音频子通道id
   */
  protobuf_c_boolean has_sub_aud_id;
  uint32_t sub_aud_id;
};
#define TPB_NVR_SGW_VID_ENC_PARAM__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&tpb_nvr_sgw_vid_enc_param__descriptor) \
    , 0,0, 0,0, 0,0, 0,0, 0,0, 0,0, 0,0, 0,0, 0,0, 0,0, 0,0, 0,0, 0,0, 0,0, 0,0, 0,0, 0,0, 0,0, 0,0, 0,0 }


/*
 * /<音频编码参数
 */
struct  _TPbNvrSgwAudEncParam
{
  ProtobufCMessage base;
  /*
   * /<音频编码格式
   */
  protobuf_c_boolean has_type;
  uint32_t type;
  /*
   * /<采样率(kHZ)
   */
  protobuf_c_boolean has_sample_rate;
  uint32_t sample_rate;
  /*
   * /<编码音量
   */
  protobuf_c_boolean has_aud_enc_vol;
  uint32_t aud_enc_vol;
  /*
   * /<回音抵消
   */
  protobuf_c_boolean has_aec;
  uint32_t aec;
};
#define TPB_NVR_SGW_AUD_ENC_PARAM__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&tpb_nvr_sgw_aud_enc_param__descriptor) \
    , 0,0, 0,0, 0,0, 0,0 }


/*
 * /<视频通道编码参数
 */
struct  _TPbNvrSgwVidEncParamChn
{
  ProtobufCMessage base;
  size_t n_vid_enc_param;
  TPbNvrSgwVidEncParam **vid_enc_param;
};
#define TPB_NVR_SGW_VID_ENC_PARAM_CHN__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&tpb_nvr_sgw_vid_enc_param_chn__descriptor) \
    , 0,NULL }


/*
 * /<音频通道编码参数
 */
struct  _TPbNvrSgwAudEncParamChn
{
  ProtobufCMessage base;
  size_t n_aud_enc_param;
  TPbNvrSgwAudEncParam **aud_enc_param;
};
#define TPB_NVR_SGW_AUD_ENC_PARAM_CHN__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&tpb_nvr_sgw_aud_enc_param_chn__descriptor) \
    , 0,NULL }


struct  _TPbNvrSgwRk3308Ver
{
  ProtobufCMessage base;
  protobuf_c_boolean has_rk3308_ver;
  ProtobufCBinaryData rk3308_ver;
};
#define TPB_NVR_SGW_RK3308_VER__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&tpb_nvr_sgw_rk3308_ver__descriptor) \
    , 0,{0,NULL} }


struct  _TPbNvrSgwCfg
{
  ProtobufCMessage base;
  /*
   * /<视频编码参数
   */
  size_t n_vid_enc_chn_param;
  TPbNvrSgwVidEncParamChn **vid_enc_chn_param;
  /*
   * /<音频编码参数
   */
  size_t n_aud_enc_chn_param;
  TPbNvrSgwAudEncParamChn **aud_enc_chn_param;
  /*
   * /<rk3308 version
   */
  protobuf_c_boolean has_rk3308_ver;
  ProtobufCBinaryData rk3308_ver;
};
#define TPB_NVR_SGW_CFG__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&tpb_nvr_sgw_cfg__descriptor) \
    , 0,NULL, 0,NULL, 0,{0,NULL} }


/* TPbNvrSgwVidEncParam methods */
void   tpb_nvr_sgw_vid_enc_param__init
                     (TPbNvrSgwVidEncParam         *message);
size_t tpb_nvr_sgw_vid_enc_param__get_packed_size
                     (const TPbNvrSgwVidEncParam   *message);
size_t tpb_nvr_sgw_vid_enc_param__pack
                     (const TPbNvrSgwVidEncParam   *message,
                      uint8_t             *out);
size_t tpb_nvr_sgw_vid_enc_param__pack_to_buffer
                     (const TPbNvrSgwVidEncParam   *message,
                      ProtobufCBuffer     *buffer);
TPbNvrSgwVidEncParam *
       tpb_nvr_sgw_vid_enc_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   tpb_nvr_sgw_vid_enc_param__free_unpacked
                     (TPbNvrSgwVidEncParam *message,
                      ProtobufCAllocator *allocator);
/* TPbNvrSgwAudEncParam methods */
void   tpb_nvr_sgw_aud_enc_param__init
                     (TPbNvrSgwAudEncParam         *message);
size_t tpb_nvr_sgw_aud_enc_param__get_packed_size
                     (const TPbNvrSgwAudEncParam   *message);
size_t tpb_nvr_sgw_aud_enc_param__pack
                     (const TPbNvrSgwAudEncParam   *message,
                      uint8_t             *out);
size_t tpb_nvr_sgw_aud_enc_param__pack_to_buffer
                     (const TPbNvrSgwAudEncParam   *message,
                      ProtobufCBuffer     *buffer);
TPbNvrSgwAudEncParam *
       tpb_nvr_sgw_aud_enc_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   tpb_nvr_sgw_aud_enc_param__free_unpacked
                     (TPbNvrSgwAudEncParam *message,
                      ProtobufCAllocator *allocator);
/* TPbNvrSgwVidEncParamChn methods */
void   tpb_nvr_sgw_vid_enc_param_chn__init
                     (TPbNvrSgwVidEncParamChn         *message);
size_t tpb_nvr_sgw_vid_enc_param_chn__get_packed_size
                     (const TPbNvrSgwVidEncParamChn   *message);
size_t tpb_nvr_sgw_vid_enc_param_chn__pack
                     (const TPbNvrSgwVidEncParamChn   *message,
                      uint8_t             *out);
size_t tpb_nvr_sgw_vid_enc_param_chn__pack_to_buffer
                     (const TPbNvrSgwVidEncParamChn   *message,
                      ProtobufCBuffer     *buffer);
TPbNvrSgwVidEncParamChn *
       tpb_nvr_sgw_vid_enc_param_chn__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   tpb_nvr_sgw_vid_enc_param_chn__free_unpacked
                     (TPbNvrSgwVidEncParamChn *message,
                      ProtobufCAllocator *allocator);
/* TPbNvrSgwAudEncParamChn methods */
void   tpb_nvr_sgw_aud_enc_param_chn__init
                     (TPbNvrSgwAudEncParamChn         *message);
size_t tpb_nvr_sgw_aud_enc_param_chn__get_packed_size
                     (const TPbNvrSgwAudEncParamChn   *message);
size_t tpb_nvr_sgw_aud_enc_param_chn__pack
                     (const TPbNvrSgwAudEncParamChn   *message,
                      uint8_t             *out);
size_t tpb_nvr_sgw_aud_enc_param_chn__pack_to_buffer
                     (const TPbNvrSgwAudEncParamChn   *message,
                      ProtobufCBuffer     *buffer);
TPbNvrSgwAudEncParamChn *
       tpb_nvr_sgw_aud_enc_param_chn__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   tpb_nvr_sgw_aud_enc_param_chn__free_unpacked
                     (TPbNvrSgwAudEncParamChn *message,
                      ProtobufCAllocator *allocator);
/* TPbNvrSgwRk3308Ver methods */
void   tpb_nvr_sgw_rk3308_ver__init
                     (TPbNvrSgwRk3308Ver         *message);
size_t tpb_nvr_sgw_rk3308_ver__get_packed_size
                     (const TPbNvrSgwRk3308Ver   *message);
size_t tpb_nvr_sgw_rk3308_ver__pack
                     (const TPbNvrSgwRk3308Ver   *message,
                      uint8_t             *out);
size_t tpb_nvr_sgw_rk3308_ver__pack_to_buffer
                     (const TPbNvrSgwRk3308Ver   *message,
                      ProtobufCBuffer     *buffer);
TPbNvrSgwRk3308Ver *
       tpb_nvr_sgw_rk3308_ver__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   tpb_nvr_sgw_rk3308_ver__free_unpacked
                     (TPbNvrSgwRk3308Ver *message,
                      ProtobufCAllocator *allocator);
/* TPbNvrSgwCfg methods */
void   tpb_nvr_sgw_cfg__init
                     (TPbNvrSgwCfg         *message);
size_t tpb_nvr_sgw_cfg__get_packed_size
                     (const TPbNvrSgwCfg   *message);
size_t tpb_nvr_sgw_cfg__pack
                     (const TPbNvrSgwCfg   *message,
                      uint8_t             *out);
size_t tpb_nvr_sgw_cfg__pack_to_buffer
                     (const TPbNvrSgwCfg   *message,
                      ProtobufCBuffer     *buffer);
TPbNvrSgwCfg *
       tpb_nvr_sgw_cfg__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   tpb_nvr_sgw_cfg__free_unpacked
                     (TPbNvrSgwCfg *message,
                      ProtobufCAllocator *allocator);
/* --- per-message closures --- */

typedef void (*TPbNvrSgwVidEncParam_Closure)
                 (const TPbNvrSgwVidEncParam *message,
                  void *closure_data);
typedef void (*TPbNvrSgwAudEncParam_Closure)
                 (const TPbNvrSgwAudEncParam *message,
                  void *closure_data);
typedef void (*TPbNvrSgwVidEncParamChn_Closure)
                 (const TPbNvrSgwVidEncParamChn *message,
                  void *closure_data);
typedef void (*TPbNvrSgwAudEncParamChn_Closure)
                 (const TPbNvrSgwAudEncParamChn *message,
                  void *closure_data);
typedef void (*TPbNvrSgwRk3308Ver_Closure)
                 (const TPbNvrSgwRk3308Ver *message,
                  void *closure_data);
typedef void (*TPbNvrSgwCfg_Closure)
                 (const TPbNvrSgwCfg *message,
                  void *closure_data);

/* --- services --- */


/* --- descriptors --- */

extern const ProtobufCMessageDescriptor tpb_nvr_sgw_vid_enc_param__descriptor;
extern const ProtobufCMessageDescriptor tpb_nvr_sgw_aud_enc_param__descriptor;
extern const ProtobufCMessageDescriptor tpb_nvr_sgw_vid_enc_param_chn__descriptor;
extern const ProtobufCMessageDescriptor tpb_nvr_sgw_aud_enc_param_chn__descriptor;
extern const ProtobufCMessageDescriptor tpb_nvr_sgw_rk3308_ver__descriptor;
extern const ProtobufCMessageDescriptor tpb_nvr_sgw_cfg__descriptor;

PROTOBUF_C__END_DECLS


#endif  /* PROTOBUF_C_rcenccfg_2eproto__INCLUDED */
