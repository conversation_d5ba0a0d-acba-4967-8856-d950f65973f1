#!/bin/bash
#set -x
path="../../../10-common/version/compileinfo/nvrlib_ssc339g.txt"
date>>$path

module_name=$(basename $PWD |awk -F'_' '{print $1}')
proj_dir=prj_linux

echo ==============================================
echo =      ${module_name}_linux for ssc339g =
echo ==============================================

echo "============compile lib$module_name ssc339g============">>$path

make -C $proj_dir -e DEBUG=1 -f makefile_ssc339g clean all 2>&1 1>/dev/null |tee -a $path


