path="../../10-common/version/compileinfo/nvrlib_ssc339g.txt"
date>>$path

cd ./prj_linux

echo ==============================================
echo =      nvr_vtdu_linux for ssc339g           =
echo ==============================================

echo "============compile libnvrvtductrl ssc339g============">>../$path

make -e DEBUG=0 -f makefile_ssc339g clean
make -e DEBUG=0 -f makefile_ssc339g 2>>../$path

cp -L -r -f libnvrvtductrl.so ../../../10-common/lib/release/ssc339g/

cd ..
