/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: nvrlcammccfg.proto */

/* Do not generate deprecated warnings for self */
#ifndef PROTOBUF_C__NO_DEPRECATED
#define PROTOBUF_C__NO_DEPRECATED
#endif

#include "nvrlcammccfg.pb-c.h"
void   tpb_nvr_lcam_mc_vid_enc_param__init
                     (TPbNvrLcamMcVidEncParam         *message)
{
  static TPbNvrLcamMcVidEncParam init_value = TPB_NVR_LCAM_MC_VID_ENC_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_lcam_mc_vid_enc_param__get_packed_size
                     (const TPbNvrLcamMcVidEncParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_vid_enc_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_lcam_mc_vid_enc_param__pack
                     (const TPbNvrLcamMcVidEncParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_vid_enc_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_lcam_mc_vid_enc_param__pack_to_buffer
                     (const TPbNvrLcamMcVidEncParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_vid_enc_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrLcamMcVidEncParam *
       tpb_nvr_lcam_mc_vid_enc_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrLcamMcVidEncParam *)
     protobuf_c_message_unpack (&tpb_nvr_lcam_mc_vid_enc_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_lcam_mc_vid_enc_param__free_unpacked
                     (TPbNvrLcamMcVidEncParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_vid_enc_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_lcam_mc_vid_enc_param_chn__init
                     (TPbNvrLcamMcVidEncParamChn         *message)
{
  static TPbNvrLcamMcVidEncParamChn init_value = TPB_NVR_LCAM_MC_VID_ENC_PARAM_CHN__INIT;
  *message = init_value;
}
size_t tpb_nvr_lcam_mc_vid_enc_param_chn__get_packed_size
                     (const TPbNvrLcamMcVidEncParamChn *message)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_vid_enc_param_chn__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_lcam_mc_vid_enc_param_chn__pack
                     (const TPbNvrLcamMcVidEncParamChn *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_vid_enc_param_chn__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_lcam_mc_vid_enc_param_chn__pack_to_buffer
                     (const TPbNvrLcamMcVidEncParamChn *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_vid_enc_param_chn__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrLcamMcVidEncParamChn *
       tpb_nvr_lcam_mc_vid_enc_param_chn__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrLcamMcVidEncParamChn *)
     protobuf_c_message_unpack (&tpb_nvr_lcam_mc_vid_enc_param_chn__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_lcam_mc_vid_enc_param_chn__free_unpacked
                     (TPbNvrLcamMcVidEncParamChn *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_vid_enc_param_chn__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_lcam_mc_aud_in_param__init
                     (TPbNvrLcamMcAudInParam         *message)
{
  static TPbNvrLcamMcAudInParam init_value = TPB_NVR_LCAM_MC_AUD_IN_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_lcam_mc_aud_in_param__get_packed_size
                     (const TPbNvrLcamMcAudInParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_aud_in_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_lcam_mc_aud_in_param__pack
                     (const TPbNvrLcamMcAudInParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_aud_in_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_lcam_mc_aud_in_param__pack_to_buffer
                     (const TPbNvrLcamMcAudInParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_aud_in_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrLcamMcAudInParam *
       tpb_nvr_lcam_mc_aud_in_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrLcamMcAudInParam *)
     protobuf_c_message_unpack (&tpb_nvr_lcam_mc_aud_in_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_lcam_mc_aud_in_param__free_unpacked
                     (TPbNvrLcamMcAudInParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_aud_in_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_lcam_mc_aud_enc_param__init
                     (TPbNvrLcamMcAudEncParam         *message)
{
  static TPbNvrLcamMcAudEncParam init_value = TPB_NVR_LCAM_MC_AUD_ENC_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_lcam_mc_aud_enc_param__get_packed_size
                     (const TPbNvrLcamMcAudEncParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_aud_enc_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_lcam_mc_aud_enc_param__pack
                     (const TPbNvrLcamMcAudEncParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_aud_enc_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_lcam_mc_aud_enc_param__pack_to_buffer
                     (const TPbNvrLcamMcAudEncParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_aud_enc_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrLcamMcAudEncParam *
       tpb_nvr_lcam_mc_aud_enc_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrLcamMcAudEncParam *)
     protobuf_c_message_unpack (&tpb_nvr_lcam_mc_aud_enc_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_lcam_mc_aud_enc_param__free_unpacked
                     (TPbNvrLcamMcAudEncParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_aud_enc_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_lcam_mc_aud_enc_param_chn__init
                     (TPbNvrLcamMcAudEncParamChn         *message)
{
  static TPbNvrLcamMcAudEncParamChn init_value = TPB_NVR_LCAM_MC_AUD_ENC_PARAM_CHN__INIT;
  *message = init_value;
}
size_t tpb_nvr_lcam_mc_aud_enc_param_chn__get_packed_size
                     (const TPbNvrLcamMcAudEncParamChn *message)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_aud_enc_param_chn__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_lcam_mc_aud_enc_param_chn__pack
                     (const TPbNvrLcamMcAudEncParamChn *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_aud_enc_param_chn__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_lcam_mc_aud_enc_param_chn__pack_to_buffer
                     (const TPbNvrLcamMcAudEncParamChn *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_aud_enc_param_chn__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrLcamMcAudEncParamChn *
       tpb_nvr_lcam_mc_aud_enc_param_chn__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrLcamMcAudEncParamChn *)
     protobuf_c_message_unpack (&tpb_nvr_lcam_mc_aud_enc_param_chn__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_lcam_mc_aud_enc_param_chn__free_unpacked
                     (TPbNvrLcamMcAudEncParamChn *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_aud_enc_param_chn__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_lcam_mc_aud_dec_param__init
                     (TPbNvrLcamMcAudDecParam         *message)
{
  static TPbNvrLcamMcAudDecParam init_value = TPB_NVR_LCAM_MC_AUD_DEC_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_lcam_mc_aud_dec_param__get_packed_size
                     (const TPbNvrLcamMcAudDecParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_aud_dec_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_lcam_mc_aud_dec_param__pack
                     (const TPbNvrLcamMcAudDecParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_aud_dec_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_lcam_mc_aud_dec_param__pack_to_buffer
                     (const TPbNvrLcamMcAudDecParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_aud_dec_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrLcamMcAudDecParam *
       tpb_nvr_lcam_mc_aud_dec_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrLcamMcAudDecParam *)
     protobuf_c_message_unpack (&tpb_nvr_lcam_mc_aud_dec_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_lcam_mc_aud_dec_param__free_unpacked
                     (TPbNvrLcamMcAudDecParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_aud_dec_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_lcam_mc_aud_dec_param_chn__init
                     (TPbNvrLcamMcAudDecParamChn         *message)
{
  static TPbNvrLcamMcAudDecParamChn init_value = TPB_NVR_LCAM_MC_AUD_DEC_PARAM_CHN__INIT;
  *message = init_value;
}
size_t tpb_nvr_lcam_mc_aud_dec_param_chn__get_packed_size
                     (const TPbNvrLcamMcAudDecParamChn *message)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_aud_dec_param_chn__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_lcam_mc_aud_dec_param_chn__pack
                     (const TPbNvrLcamMcAudDecParamChn *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_aud_dec_param_chn__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_lcam_mc_aud_dec_param_chn__pack_to_buffer
                     (const TPbNvrLcamMcAudDecParamChn *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_aud_dec_param_chn__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrLcamMcAudDecParamChn *
       tpb_nvr_lcam_mc_aud_dec_param_chn__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrLcamMcAudDecParamChn *)
     protobuf_c_message_unpack (&tpb_nvr_lcam_mc_aud_dec_param_chn__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_lcam_mc_aud_dec_param_chn__free_unpacked
                     (TPbNvrLcamMcAudDecParamChn *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_aud_dec_param_chn__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_lcam_mc_aud_aec_param__init
                     (TPbNvrLcamMcAudAecParam         *message)
{
  static TPbNvrLcamMcAudAecParam init_value = TPB_NVR_LCAM_MC_AUD_AEC_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_lcam_mc_aud_aec_param__get_packed_size
                     (const TPbNvrLcamMcAudAecParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_aud_aec_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_lcam_mc_aud_aec_param__pack
                     (const TPbNvrLcamMcAudAecParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_aud_aec_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_lcam_mc_aud_aec_param__pack_to_buffer
                     (const TPbNvrLcamMcAudAecParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_aud_aec_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrLcamMcAudAecParam *
       tpb_nvr_lcam_mc_aud_aec_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrLcamMcAudAecParam *)
     protobuf_c_message_unpack (&tpb_nvr_lcam_mc_aud_aec_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_lcam_mc_aud_aec_param__free_unpacked
                     (TPbNvrLcamMcAudAecParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_aud_aec_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_lcam_mc_aud_aec_param_chn__init
                     (TPbNvrLcamMcAudAecParamChn         *message)
{
  static TPbNvrLcamMcAudAecParamChn init_value = TPB_NVR_LCAM_MC_AUD_AEC_PARAM_CHN__INIT;
  *message = init_value;
}
size_t tpb_nvr_lcam_mc_aud_aec_param_chn__get_packed_size
                     (const TPbNvrLcamMcAudAecParamChn *message)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_aud_aec_param_chn__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_lcam_mc_aud_aec_param_chn__pack
                     (const TPbNvrLcamMcAudAecParamChn *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_aud_aec_param_chn__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_lcam_mc_aud_aec_param_chn__pack_to_buffer
                     (const TPbNvrLcamMcAudAecParamChn *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_aud_aec_param_chn__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrLcamMcAudAecParamChn *
       tpb_nvr_lcam_mc_aud_aec_param_chn__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrLcamMcAudAecParamChn *)
     protobuf_c_message_unpack (&tpb_nvr_lcam_mc_aud_aec_param_chn__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_lcam_mc_aud_aec_param_chn__free_unpacked
                     (TPbNvrLcamMcAudAecParamChn *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_aud_aec_param_chn__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_lcam_mc_aud_mix_param__init
                     (TPbNvrLcamMcAudMixParam         *message)
{
  static TPbNvrLcamMcAudMixParam init_value = TPB_NVR_LCAM_MC_AUD_MIX_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_lcam_mc_aud_mix_param__get_packed_size
                     (const TPbNvrLcamMcAudMixParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_aud_mix_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_lcam_mc_aud_mix_param__pack
                     (const TPbNvrLcamMcAudMixParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_aud_mix_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_lcam_mc_aud_mix_param__pack_to_buffer
                     (const TPbNvrLcamMcAudMixParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_aud_mix_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrLcamMcAudMixParam *
       tpb_nvr_lcam_mc_aud_mix_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrLcamMcAudMixParam *)
     protobuf_c_message_unpack (&tpb_nvr_lcam_mc_aud_mix_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_lcam_mc_aud_mix_param__free_unpacked
                     (TPbNvrLcamMcAudMixParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_aud_mix_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_lcam_mc_aud_mix_param_chn__init
                     (TPbNvrLcamMcAudMixParamChn         *message)
{
  static TPbNvrLcamMcAudMixParamChn init_value = TPB_NVR_LCAM_MC_AUD_MIX_PARAM_CHN__INIT;
  *message = init_value;
}
size_t tpb_nvr_lcam_mc_aud_mix_param_chn__get_packed_size
                     (const TPbNvrLcamMcAudMixParamChn *message)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_aud_mix_param_chn__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_lcam_mc_aud_mix_param_chn__pack
                     (const TPbNvrLcamMcAudMixParamChn *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_aud_mix_param_chn__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_lcam_mc_aud_mix_param_chn__pack_to_buffer
                     (const TPbNvrLcamMcAudMixParamChn *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_aud_mix_param_chn__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrLcamMcAudMixParamChn *
       tpb_nvr_lcam_mc_aud_mix_param_chn__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrLcamMcAudMixParamChn *)
     protobuf_c_message_unpack (&tpb_nvr_lcam_mc_aud_mix_param_chn__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_lcam_mc_aud_mix_param_chn__free_unpacked
                     (TPbNvrLcamMcAudMixParamChn *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_aud_mix_param_chn__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_lcam_mc_rect_num__init
                     (TpbNvrLcamMcRectNum         *message)
{
  static TpbNvrLcamMcRectNum init_value = TPB_NVR_LCAM_MC_RECT_NUM__INIT;
  *message = init_value;
}
size_t tpb_nvr_lcam_mc_rect_num__get_packed_size
                     (const TpbNvrLcamMcRectNum *message)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_rect_num__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_lcam_mc_rect_num__pack
                     (const TpbNvrLcamMcRectNum *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_rect_num__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_lcam_mc_rect_num__pack_to_buffer
                     (const TpbNvrLcamMcRectNum *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_rect_num__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TpbNvrLcamMcRectNum *
       tpb_nvr_lcam_mc_rect_num__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TpbNvrLcamMcRectNum *)
     protobuf_c_message_unpack (&tpb_nvr_lcam_mc_rect_num__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_lcam_mc_rect_num__free_unpacked
                     (TpbNvrLcamMcRectNum *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_rect_num__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_lcam_mc_mdparam_chn__init
                     (TPbNvrLcamMcMDParamChn         *message)
{
  static TPbNvrLcamMcMDParamChn init_value = TPB_NVR_LCAM_MC_MDPARAM_CHN__INIT;
  *message = init_value;
}
size_t tpb_nvr_lcam_mc_mdparam_chn__get_packed_size
                     (const TPbNvrLcamMcMDParamChn *message)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_mdparam_chn__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_lcam_mc_mdparam_chn__pack
                     (const TPbNvrLcamMcMDParamChn *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_mdparam_chn__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_lcam_mc_mdparam_chn__pack_to_buffer
                     (const TPbNvrLcamMcMDParamChn *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_mdparam_chn__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrLcamMcMDParamChn *
       tpb_nvr_lcam_mc_mdparam_chn__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrLcamMcMDParamChn *)
     protobuf_c_message_unpack (&tpb_nvr_lcam_mc_mdparam_chn__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_lcam_mc_mdparam_chn__free_unpacked
                     (TPbNvrLcamMcMDParamChn *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_mdparam_chn__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_lcam_mc_overlay_param_chn__init
                     (TPbNvrLcamMcOverlayParamChn         *message)
{
  static TPbNvrLcamMcOverlayParamChn init_value = TPB_NVR_LCAM_MC_OVERLAY_PARAM_CHN__INIT;
  *message = init_value;
}
size_t tpb_nvr_lcam_mc_overlay_param_chn__get_packed_size
                     (const TPbNvrLcamMcOverlayParamChn *message)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_overlay_param_chn__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_lcam_mc_overlay_param_chn__pack
                     (const TPbNvrLcamMcOverlayParamChn *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_overlay_param_chn__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_lcam_mc_overlay_param_chn__pack_to_buffer
                     (const TPbNvrLcamMcOverlayParamChn *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_overlay_param_chn__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrLcamMcOverlayParamChn *
       tpb_nvr_lcam_mc_overlay_param_chn__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrLcamMcOverlayParamChn *)
     protobuf_c_message_unpack (&tpb_nvr_lcam_mc_overlay_param_chn__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_lcam_mc_overlay_param_chn__free_unpacked
                     (TPbNvrLcamMcOverlayParamChn *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_overlay_param_chn__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_lcam_mc_stream_compos_info_chn__init
                     (TPbNvrLcamMcStreamComposInfoChn         *message)
{
  static TPbNvrLcamMcStreamComposInfoChn init_value = TPB_NVR_LCAM_MC_STREAM_COMPOS_INFO_CHN__INIT;
  *message = init_value;
}
size_t tpb_nvr_lcam_mc_stream_compos_info_chn__get_packed_size
                     (const TPbNvrLcamMcStreamComposInfoChn *message)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_stream_compos_info_chn__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_lcam_mc_stream_compos_info_chn__pack
                     (const TPbNvrLcamMcStreamComposInfoChn *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_stream_compos_info_chn__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_lcam_mc_stream_compos_info_chn__pack_to_buffer
                     (const TPbNvrLcamMcStreamComposInfoChn *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_stream_compos_info_chn__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrLcamMcStreamComposInfoChn *
       tpb_nvr_lcam_mc_stream_compos_info_chn__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrLcamMcStreamComposInfoChn *)
     protobuf_c_message_unpack (&tpb_nvr_lcam_mc_stream_compos_info_chn__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_lcam_mc_stream_compos_info_chn__free_unpacked
                     (TPbNvrLcamMcStreamComposInfoChn *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_stream_compos_info_chn__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_lcam_mc_ptz_param_chn__init
                     (TPbNvrLcamMcPtzParamChn         *message)
{
  static TPbNvrLcamMcPtzParamChn init_value = TPB_NVR_LCAM_MC_PTZ_PARAM_CHN__INIT;
  *message = init_value;
}
size_t tpb_nvr_lcam_mc_ptz_param_chn__get_packed_size
                     (const TPbNvrLcamMcPtzParamChn *message)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_ptz_param_chn__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_lcam_mc_ptz_param_chn__pack
                     (const TPbNvrLcamMcPtzParamChn *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_ptz_param_chn__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_lcam_mc_ptz_param_chn__pack_to_buffer
                     (const TPbNvrLcamMcPtzParamChn *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_ptz_param_chn__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrLcamMcPtzParamChn *
       tpb_nvr_lcam_mc_ptz_param_chn__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrLcamMcPtzParamChn *)
     protobuf_c_message_unpack (&tpb_nvr_lcam_mc_ptz_param_chn__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_lcam_mc_ptz_param_chn__free_unpacked
                     (TPbNvrLcamMcPtzParamChn *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_ptz_param_chn__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_lcam_mc_roi_info__init
                     (TPbNvrLcamMcRoiInfo         *message)
{
  static TPbNvrLcamMcRoiInfo init_value = TPB_NVR_LCAM_MC_ROI_INFO__INIT;
  *message = init_value;
}
size_t tpb_nvr_lcam_mc_roi_info__get_packed_size
                     (const TPbNvrLcamMcRoiInfo *message)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_roi_info__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_lcam_mc_roi_info__pack
                     (const TPbNvrLcamMcRoiInfo *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_roi_info__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_lcam_mc_roi_info__pack_to_buffer
                     (const TPbNvrLcamMcRoiInfo *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_roi_info__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrLcamMcRoiInfo *
       tpb_nvr_lcam_mc_roi_info__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrLcamMcRoiInfo *)
     protobuf_c_message_unpack (&tpb_nvr_lcam_mc_roi_info__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_lcam_mc_roi_info__free_unpacked
                     (TPbNvrLcamMcRoiInfo *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_roi_info__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_lcam_mc_roi_param__init
                     (TPbNvrLcamMcRoiParam         *message)
{
  static TPbNvrLcamMcRoiParam init_value = TPB_NVR_LCAM_MC_ROI_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_lcam_mc_roi_param__get_packed_size
                     (const TPbNvrLcamMcRoiParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_roi_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_lcam_mc_roi_param__pack
                     (const TPbNvrLcamMcRoiParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_roi_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_lcam_mc_roi_param__pack_to_buffer
                     (const TPbNvrLcamMcRoiParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_roi_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrLcamMcRoiParam *
       tpb_nvr_lcam_mc_roi_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrLcamMcRoiParam *)
     protobuf_c_message_unpack (&tpb_nvr_lcam_mc_roi_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_lcam_mc_roi_param__free_unpacked
                     (TPbNvrLcamMcRoiParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_roi_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_lcam_mc_roi_param_chn__init
                     (TPbNvrLcamMcRoiParamChn         *message)
{
  static TPbNvrLcamMcRoiParamChn init_value = TPB_NVR_LCAM_MC_ROI_PARAM_CHN__INIT;
  *message = init_value;
}
size_t tpb_nvr_lcam_mc_roi_param_chn__get_packed_size
                     (const TPbNvrLcamMcRoiParamChn *message)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_roi_param_chn__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_lcam_mc_roi_param_chn__pack
                     (const TPbNvrLcamMcRoiParamChn *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_roi_param_chn__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_lcam_mc_roi_param_chn__pack_to_buffer
                     (const TPbNvrLcamMcRoiParamChn *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_roi_param_chn__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrLcamMcRoiParamChn *
       tpb_nvr_lcam_mc_roi_param_chn__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrLcamMcRoiParamChn *)
     protobuf_c_message_unpack (&tpb_nvr_lcam_mc_roi_param_chn__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_lcam_mc_roi_param_chn__free_unpacked
                     (TPbNvrLcamMcRoiParamChn *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_roi_param_chn__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_point__init
                     (TPbNvrPoint         *message)
{
  static TPbNvrPoint init_value = TPB_NVR_POINT__INIT;
  *message = init_value;
}
size_t tpb_nvr_point__get_packed_size
                     (const TPbNvrPoint *message)
{
  assert(message->base.descriptor == &tpb_nvr_point__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_point__pack
                     (const TPbNvrPoint *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_point__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_point__pack_to_buffer
                     (const TPbNvrPoint *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_point__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrPoint *
       tpb_nvr_point__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrPoint *)
     protobuf_c_message_unpack (&tpb_nvr_point__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_point__free_unpacked
                     (TPbNvrPoint *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_point__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_polygon__init
                     (TPbNvrPolygon         *message)
{
  static TPbNvrPolygon init_value = TPB_NVR_POLYGON__INIT;
  *message = init_value;
}
size_t tpb_nvr_polygon__get_packed_size
                     (const TPbNvrPolygon *message)
{
  assert(message->base.descriptor == &tpb_nvr_polygon__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_polygon__pack
                     (const TPbNvrPolygon *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_polygon__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_polygon__pack_to_buffer
                     (const TPbNvrPolygon *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_polygon__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrPolygon *
       tpb_nvr_polygon__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrPolygon *)
     protobuf_c_message_unpack (&tpb_nvr_polygon__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_polygon__free_unpacked
                     (TPbNvrPolygon *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_polygon__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_lcam_mc_shade_info__init
                     (TPbNvrLcamMcShadeInfo         *message)
{
  static TPbNvrLcamMcShadeInfo init_value = TPB_NVR_LCAM_MC_SHADE_INFO__INIT;
  *message = init_value;
}
size_t tpb_nvr_lcam_mc_shade_info__get_packed_size
                     (const TPbNvrLcamMcShadeInfo *message)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_shade_info__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_lcam_mc_shade_info__pack
                     (const TPbNvrLcamMcShadeInfo *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_shade_info__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_lcam_mc_shade_info__pack_to_buffer
                     (const TPbNvrLcamMcShadeInfo *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_shade_info__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrLcamMcShadeInfo *
       tpb_nvr_lcam_mc_shade_info__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrLcamMcShadeInfo *)
     protobuf_c_message_unpack (&tpb_nvr_lcam_mc_shade_info__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_lcam_mc_shade_info__free_unpacked
                     (TPbNvrLcamMcShadeInfo *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_shade_info__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_start_end_time__init
                     (TPbNvrStartEndTime         *message)
{
  static TPbNvrStartEndTime init_value = TPB_NVR_START_END_TIME__INIT;
  *message = init_value;
}
size_t tpb_nvr_start_end_time__get_packed_size
                     (const TPbNvrStartEndTime *message)
{
  assert(message->base.descriptor == &tpb_nvr_start_end_time__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_start_end_time__pack
                     (const TPbNvrStartEndTime *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_start_end_time__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_start_end_time__pack_to_buffer
                     (const TPbNvrStartEndTime *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_start_end_time__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrStartEndTime *
       tpb_nvr_start_end_time__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrStartEndTime *)
     protobuf_c_message_unpack (&tpb_nvr_start_end_time__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_start_end_time__free_unpacked
                     (TPbNvrStartEndTime *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_start_end_time__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_time_scale__init
                     (TPbNvrTimeScale         *message)
{
  static TPbNvrTimeScale init_value = TPB_NVR_TIME_SCALE__INIT;
  *message = init_value;
}
size_t tpb_nvr_time_scale__get_packed_size
                     (const TPbNvrTimeScale *message)
{
  assert(message->base.descriptor == &tpb_nvr_time_scale__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_time_scale__pack
                     (const TPbNvrTimeScale *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_time_scale__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_time_scale__pack_to_buffer
                     (const TPbNvrTimeScale *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_time_scale__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrTimeScale *
       tpb_nvr_time_scale__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrTimeScale *)
     protobuf_c_message_unpack (&tpb_nvr_time_scale__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_time_scale__free_unpacked
                     (TPbNvrTimeScale *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_time_scale__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_disarming_time__init
                     (TPbNvrDisarmingTime         *message)
{
  static TPbNvrDisarmingTime init_value = TPB_NVR_DISARMING_TIME__INIT;
  *message = init_value;
}
size_t tpb_nvr_disarming_time__get_packed_size
                     (const TPbNvrDisarmingTime *message)
{
  assert(message->base.descriptor == &tpb_nvr_disarming_time__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_disarming_time__pack
                     (const TPbNvrDisarmingTime *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_disarming_time__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_disarming_time__pack_to_buffer
                     (const TPbNvrDisarmingTime *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_disarming_time__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrDisarmingTime *
       tpb_nvr_disarming_time__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrDisarmingTime *)
     protobuf_c_message_unpack (&tpb_nvr_disarming_time__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_disarming_time__free_unpacked
                     (TPbNvrDisarmingTime *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_disarming_time__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_lcam_mc_shade_param_chn__init
                     (TPbNvrLcamMcShadeParamChn         *message)
{
  static TPbNvrLcamMcShadeParamChn init_value = TPB_NVR_LCAM_MC_SHADE_PARAM_CHN__INIT;
  *message = init_value;
}
size_t tpb_nvr_lcam_mc_shade_param_chn__get_packed_size
                     (const TPbNvrLcamMcShadeParamChn *message)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_shade_param_chn__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_lcam_mc_shade_param_chn__pack
                     (const TPbNvrLcamMcShadeParamChn *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_shade_param_chn__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_lcam_mc_shade_param_chn__pack_to_buffer
                     (const TPbNvrLcamMcShadeParamChn *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_shade_param_chn__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrLcamMcShadeParamChn *
       tpb_nvr_lcam_mc_shade_param_chn__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrLcamMcShadeParamChn *)
     protobuf_c_message_unpack (&tpb_nvr_lcam_mc_shade_param_chn__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_lcam_mc_shade_param_chn__free_unpacked
                     (TPbNvrLcamMcShadeParamChn *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_shade_param_chn__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_lcam_mc_work_mode_chn__init
                     (TPbNvrLcamMcWorkModeChn         *message)
{
  static TPbNvrLcamMcWorkModeChn init_value = TPB_NVR_LCAM_MC_WORK_MODE_CHN__INIT;
  *message = init_value;
}
size_t tpb_nvr_lcam_mc_work_mode_chn__get_packed_size
                     (const TPbNvrLcamMcWorkModeChn *message)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_work_mode_chn__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_lcam_mc_work_mode_chn__pack
                     (const TPbNvrLcamMcWorkModeChn *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_work_mode_chn__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_lcam_mc_work_mode_chn__pack_to_buffer
                     (const TPbNvrLcamMcWorkModeChn *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_work_mode_chn__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrLcamMcWorkModeChn *
       tpb_nvr_lcam_mc_work_mode_chn__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrLcamMcWorkModeChn *)
     protobuf_c_message_unpack (&tpb_nvr_lcam_mc_work_mode_chn__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_lcam_mc_work_mode_chn__free_unpacked
                     (TPbNvrLcamMcWorkModeChn *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_work_mode_chn__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_lcam_mc_auto_wiper_chn__init
                     (TPbNvrLcamMcAutoWiperChn         *message)
{
  static TPbNvrLcamMcAutoWiperChn init_value = TPB_NVR_LCAM_MC_AUTO_WIPER_CHN__INIT;
  *message = init_value;
}
size_t tpb_nvr_lcam_mc_auto_wiper_chn__get_packed_size
                     (const TPbNvrLcamMcAutoWiperChn *message)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_auto_wiper_chn__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_lcam_mc_auto_wiper_chn__pack
                     (const TPbNvrLcamMcAutoWiperChn *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_auto_wiper_chn__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_lcam_mc_auto_wiper_chn__pack_to_buffer
                     (const TPbNvrLcamMcAutoWiperChn *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_auto_wiper_chn__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrLcamMcAutoWiperChn *
       tpb_nvr_lcam_mc_auto_wiper_chn__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrLcamMcAutoWiperChn *)
     protobuf_c_message_unpack (&tpb_nvr_lcam_mc_auto_wiper_chn__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_lcam_mc_auto_wiper_chn__free_unpacked
                     (TPbNvrLcamMcAutoWiperChn *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_auto_wiper_chn__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_lcam_mc_cfg__init
                     (TPbNvrLcamMcCfg         *message)
{
  static TPbNvrLcamMcCfg init_value = TPB_NVR_LCAM_MC_CFG__INIT;
  *message = init_value;
}
size_t tpb_nvr_lcam_mc_cfg__get_packed_size
                     (const TPbNvrLcamMcCfg *message)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_cfg__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_lcam_mc_cfg__pack
                     (const TPbNvrLcamMcCfg *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_cfg__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_lcam_mc_cfg__pack_to_buffer
                     (const TPbNvrLcamMcCfg *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_cfg__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrLcamMcCfg *
       tpb_nvr_lcam_mc_cfg__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrLcamMcCfg *)
     protobuf_c_message_unpack (&tpb_nvr_lcam_mc_cfg__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_lcam_mc_cfg__free_unpacked
                     (TPbNvrLcamMcCfg *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_lcam_mc_cfg__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
static const ProtobufCFieldDescriptor tpb_nvr_lcam_mc_vid_enc_param__field_descriptors[11] =
{
  {
    "enc_type",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLcamMcVidEncParam, has_enc_type),
    offsetof(TPbNvrLcamMcVidEncParam, enc_type),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "frame_rate",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLcamMcVidEncParam, has_frame_rate),
    offsetof(TPbNvrLcamMcVidEncParam, frame_rate),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "res_width",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLcamMcVidEncParam, has_res_width),
    offsetof(TPbNvrLcamMcVidEncParam, res_width),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "res_height",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLcamMcVidEncParam, has_res_height),
    offsetof(TPbNvrLcamMcVidEncParam, res_height),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "max_key_rate",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLcamMcVidEncParam, has_max_key_rate),
    offsetof(TPbNvrLcamMcVidEncParam, max_key_rate),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "bit_rate",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLcamMcVidEncParam, has_bit_rate),
    offsetof(TPbNvrLcamMcVidEncParam, bit_rate),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "rc_mode",
    7,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLcamMcVidEncParam, has_rc_mode),
    offsetof(TPbNvrLcamMcVidEncParam, rc_mode),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "quality",
    8,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLcamMcVidEncParam, has_quality),
    offsetof(TPbNvrLcamMcVidEncParam, quality),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "vid_enc_profile_id",
    9,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLcamMcVidEncParam, has_vid_enc_profile_id),
    offsetof(TPbNvrLcamMcVidEncParam, vid_enc_profile_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "smart_enc",
    10,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLcamMcVidEncParam, has_smart_enc),
    offsetof(TPbNvrLcamMcVidEncParam, smart_enc),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "bit_smooth_level",
    11,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLcamMcVidEncParam, has_bit_smooth_level),
    offsetof(TPbNvrLcamMcVidEncParam, bit_smooth_level),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_lcam_mc_vid_enc_param__field_indices_by_name[] = {
  5,   /* field[5] = bit_rate */
  10,   /* field[10] = bit_smooth_level */
  0,   /* field[0] = enc_type */
  1,   /* field[1] = frame_rate */
  4,   /* field[4] = max_key_rate */
  7,   /* field[7] = quality */
  6,   /* field[6] = rc_mode */
  3,   /* field[3] = res_height */
  2,   /* field[2] = res_width */
  9,   /* field[9] = smart_enc */
  8,   /* field[8] = vid_enc_profile_id */
};
static const ProtobufCIntRange tpb_nvr_lcam_mc_vid_enc_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 11 }
};
const ProtobufCMessageDescriptor tpb_nvr_lcam_mc_vid_enc_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrLcamMcVidEncParam",
  "TPbNvrLcamMcVidEncParam",
  "TPbNvrLcamMcVidEncParam",
  "",
  sizeof(TPbNvrLcamMcVidEncParam),
  11,
  tpb_nvr_lcam_mc_vid_enc_param__field_descriptors,
  tpb_nvr_lcam_mc_vid_enc_param__field_indices_by_name,
  1,  tpb_nvr_lcam_mc_vid_enc_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_lcam_mc_vid_enc_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_lcam_mc_vid_enc_param_chn__field_descriptors[1] =
{
  {
    "vid_enc_param",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrLcamMcVidEncParamChn, n_vid_enc_param),
    offsetof(TPbNvrLcamMcVidEncParamChn, vid_enc_param),
    &tpb_nvr_lcam_mc_vid_enc_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_lcam_mc_vid_enc_param_chn__field_indices_by_name[] = {
  0,   /* field[0] = vid_enc_param */
};
static const ProtobufCIntRange tpb_nvr_lcam_mc_vid_enc_param_chn__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor tpb_nvr_lcam_mc_vid_enc_param_chn__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrLcamMcVidEncParamChn",
  "TPbNvrLcamMcVidEncParamChn",
  "TPbNvrLcamMcVidEncParamChn",
  "",
  sizeof(TPbNvrLcamMcVidEncParamChn),
  1,
  tpb_nvr_lcam_mc_vid_enc_param_chn__field_descriptors,
  tpb_nvr_lcam_mc_vid_enc_param_chn__field_indices_by_name,
  1,  tpb_nvr_lcam_mc_vid_enc_param_chn__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_lcam_mc_vid_enc_param_chn__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_lcam_mc_aud_in_param__field_descriptors[1] =
{
  {
    "aud_in_type",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLcamMcAudInParam, has_aud_in_type),
    offsetof(TPbNvrLcamMcAudInParam, aud_in_type),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_lcam_mc_aud_in_param__field_indices_by_name[] = {
  0,   /* field[0] = aud_in_type */
};
static const ProtobufCIntRange tpb_nvr_lcam_mc_aud_in_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor tpb_nvr_lcam_mc_aud_in_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrLcamMcAudInParam",
  "TPbNvrLcamMcAudInParam",
  "TPbNvrLcamMcAudInParam",
  "",
  sizeof(TPbNvrLcamMcAudInParam),
  1,
  tpb_nvr_lcam_mc_aud_in_param__field_descriptors,
  tpb_nvr_lcam_mc_aud_in_param__field_indices_by_name,
  1,  tpb_nvr_lcam_mc_aud_in_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_lcam_mc_aud_in_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_lcam_mc_aud_enc_param__field_descriptors[4] =
{
  {
    "type",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLcamMcAudEncParam, has_type),
    offsetof(TPbNvrLcamMcAudEncParam, type),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "sample_rate",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLcamMcAudEncParam, has_sample_rate),
    offsetof(TPbNvrLcamMcAudEncParam, sample_rate),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "aud_enc_vol",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLcamMcAudEncParam, has_aud_enc_vol),
    offsetof(TPbNvrLcamMcAudEncParam, aud_enc_vol),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "aec",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLcamMcAudEncParam, has_aec),
    offsetof(TPbNvrLcamMcAudEncParam, aec),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_lcam_mc_aud_enc_param__field_indices_by_name[] = {
  3,   /* field[3] = aec */
  2,   /* field[2] = aud_enc_vol */
  1,   /* field[1] = sample_rate */
  0,   /* field[0] = type */
};
static const ProtobufCIntRange tpb_nvr_lcam_mc_aud_enc_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 4 }
};
const ProtobufCMessageDescriptor tpb_nvr_lcam_mc_aud_enc_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrLcamMcAudEncParam",
  "TPbNvrLcamMcAudEncParam",
  "TPbNvrLcamMcAudEncParam",
  "",
  sizeof(TPbNvrLcamMcAudEncParam),
  4,
  tpb_nvr_lcam_mc_aud_enc_param__field_descriptors,
  tpb_nvr_lcam_mc_aud_enc_param__field_indices_by_name,
  1,  tpb_nvr_lcam_mc_aud_enc_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_lcam_mc_aud_enc_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_lcam_mc_aud_enc_param_chn__field_descriptors[1] =
{
  {
    "aud_enc_param",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrLcamMcAudEncParamChn, n_aud_enc_param),
    offsetof(TPbNvrLcamMcAudEncParamChn, aud_enc_param),
    &tpb_nvr_lcam_mc_aud_enc_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_lcam_mc_aud_enc_param_chn__field_indices_by_name[] = {
  0,   /* field[0] = aud_enc_param */
};
static const ProtobufCIntRange tpb_nvr_lcam_mc_aud_enc_param_chn__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor tpb_nvr_lcam_mc_aud_enc_param_chn__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrLcamMcAudEncParamChn",
  "TPbNvrLcamMcAudEncParamChn",
  "TPbNvrLcamMcAudEncParamChn",
  "",
  sizeof(TPbNvrLcamMcAudEncParamChn),
  1,
  tpb_nvr_lcam_mc_aud_enc_param_chn__field_descriptors,
  tpb_nvr_lcam_mc_aud_enc_param_chn__field_indices_by_name,
  1,  tpb_nvr_lcam_mc_aud_enc_param_chn__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_lcam_mc_aud_enc_param_chn__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_lcam_mc_aud_dec_param__field_descriptors[1] =
{
  {
    "aud_dec_vol",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLcamMcAudDecParam, has_aud_dec_vol),
    offsetof(TPbNvrLcamMcAudDecParam, aud_dec_vol),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_lcam_mc_aud_dec_param__field_indices_by_name[] = {
  0,   /* field[0] = aud_dec_vol */
};
static const ProtobufCIntRange tpb_nvr_lcam_mc_aud_dec_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor tpb_nvr_lcam_mc_aud_dec_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrLcamMcAudDecParam",
  "TPbNvrLcamMcAudDecParam",
  "TPbNvrLcamMcAudDecParam",
  "",
  sizeof(TPbNvrLcamMcAudDecParam),
  1,
  tpb_nvr_lcam_mc_aud_dec_param__field_descriptors,
  tpb_nvr_lcam_mc_aud_dec_param__field_indices_by_name,
  1,  tpb_nvr_lcam_mc_aud_dec_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_lcam_mc_aud_dec_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_lcam_mc_aud_dec_param_chn__field_descriptors[1] =
{
  {
    "aud_dec_param",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrLcamMcAudDecParamChn, n_aud_dec_param),
    offsetof(TPbNvrLcamMcAudDecParamChn, aud_dec_param),
    &tpb_nvr_lcam_mc_aud_dec_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_lcam_mc_aud_dec_param_chn__field_indices_by_name[] = {
  0,   /* field[0] = aud_dec_param */
};
static const ProtobufCIntRange tpb_nvr_lcam_mc_aud_dec_param_chn__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor tpb_nvr_lcam_mc_aud_dec_param_chn__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrLcamMcAudDecParamChn",
  "TPbNvrLcamMcAudDecParamChn",
  "TPbNvrLcamMcAudDecParamChn",
  "",
  sizeof(TPbNvrLcamMcAudDecParamChn),
  1,
  tpb_nvr_lcam_mc_aud_dec_param_chn__field_descriptors,
  tpb_nvr_lcam_mc_aud_dec_param_chn__field_indices_by_name,
  1,  tpb_nvr_lcam_mc_aud_dec_param_chn__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_lcam_mc_aud_dec_param_chn__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_lcam_mc_aud_aec_param__field_descriptors[2] =
{
  {
    "aec_enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLcamMcAudAecParam, has_aec_enable),
    offsetof(TPbNvrLcamMcAudAecParam, aec_enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "aec_type",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLcamMcAudAecParam, has_aec_type),
    offsetof(TPbNvrLcamMcAudAecParam, aec_type),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_lcam_mc_aud_aec_param__field_indices_by_name[] = {
  0,   /* field[0] = aec_enable */
  1,   /* field[1] = aec_type */
};
static const ProtobufCIntRange tpb_nvr_lcam_mc_aud_aec_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_lcam_mc_aud_aec_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrLcamMcAudAecParam",
  "TPbNvrLcamMcAudAecParam",
  "TPbNvrLcamMcAudAecParam",
  "",
  sizeof(TPbNvrLcamMcAudAecParam),
  2,
  tpb_nvr_lcam_mc_aud_aec_param__field_descriptors,
  tpb_nvr_lcam_mc_aud_aec_param__field_indices_by_name,
  1,  tpb_nvr_lcam_mc_aud_aec_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_lcam_mc_aud_aec_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_lcam_mc_aud_aec_param_chn__field_descriptors[1] =
{
  {
    "aud_aec_param",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrLcamMcAudAecParamChn, n_aud_aec_param),
    offsetof(TPbNvrLcamMcAudAecParamChn, aud_aec_param),
    &tpb_nvr_lcam_mc_aud_aec_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_lcam_mc_aud_aec_param_chn__field_indices_by_name[] = {
  0,   /* field[0] = aud_aec_param */
};
static const ProtobufCIntRange tpb_nvr_lcam_mc_aud_aec_param_chn__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor tpb_nvr_lcam_mc_aud_aec_param_chn__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrLcamMcAudAecParamChn",
  "TPbNvrLcamMcAudAecParamChn",
  "TPbNvrLcamMcAudAecParamChn",
  "",
  sizeof(TPbNvrLcamMcAudAecParamChn),
  1,
  tpb_nvr_lcam_mc_aud_aec_param_chn__field_descriptors,
  tpb_nvr_lcam_mc_aud_aec_param_chn__field_indices_by_name,
  1,  tpb_nvr_lcam_mc_aud_aec_param_chn__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_lcam_mc_aud_aec_param_chn__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_lcam_mc_aud_mix_param__field_descriptors[1] =
{
  {
    "mix_enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLcamMcAudMixParam, has_mix_enable),
    offsetof(TPbNvrLcamMcAudMixParam, mix_enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_lcam_mc_aud_mix_param__field_indices_by_name[] = {
  0,   /* field[0] = mix_enable */
};
static const ProtobufCIntRange tpb_nvr_lcam_mc_aud_mix_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor tpb_nvr_lcam_mc_aud_mix_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrLcamMcAudMixParam",
  "TPbNvrLcamMcAudMixParam",
  "TPbNvrLcamMcAudMixParam",
  "",
  sizeof(TPbNvrLcamMcAudMixParam),
  1,
  tpb_nvr_lcam_mc_aud_mix_param__field_descriptors,
  tpb_nvr_lcam_mc_aud_mix_param__field_indices_by_name,
  1,  tpb_nvr_lcam_mc_aud_mix_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_lcam_mc_aud_mix_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_lcam_mc_aud_mix_param_chn__field_descriptors[1] =
{
  {
    "aud_mix_param",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrLcamMcAudMixParamChn, n_aud_mix_param),
    offsetof(TPbNvrLcamMcAudMixParamChn, aud_mix_param),
    &tpb_nvr_lcam_mc_aud_mix_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_lcam_mc_aud_mix_param_chn__field_indices_by_name[] = {
  0,   /* field[0] = aud_mix_param */
};
static const ProtobufCIntRange tpb_nvr_lcam_mc_aud_mix_param_chn__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor tpb_nvr_lcam_mc_aud_mix_param_chn__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrLcamMcAudMixParamChn",
  "TPbNvrLcamMcAudMixParamChn",
  "TPbNvrLcamMcAudMixParamChn",
  "",
  sizeof(TPbNvrLcamMcAudMixParamChn),
  1,
  tpb_nvr_lcam_mc_aud_mix_param_chn__field_descriptors,
  tpb_nvr_lcam_mc_aud_mix_param_chn__field_indices_by_name,
  1,  tpb_nvr_lcam_mc_aud_mix_param_chn__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_lcam_mc_aud_mix_param_chn__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_lcam_mc_rect_num__field_descriptors[4] =
{
  {
    "start_x",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TpbNvrLcamMcRectNum, has_start_x),
    offsetof(TpbNvrLcamMcRectNum, start_x),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "start_y",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TpbNvrLcamMcRectNum, has_start_y),
    offsetof(TpbNvrLcamMcRectNum, start_y),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "width",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TpbNvrLcamMcRectNum, has_width),
    offsetof(TpbNvrLcamMcRectNum, width),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "height",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TpbNvrLcamMcRectNum, has_height),
    offsetof(TpbNvrLcamMcRectNum, height),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_lcam_mc_rect_num__field_indices_by_name[] = {
  3,   /* field[3] = height */
  0,   /* field[0] = start_x */
  1,   /* field[1] = start_y */
  2,   /* field[2] = width */
};
static const ProtobufCIntRange tpb_nvr_lcam_mc_rect_num__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 4 }
};
const ProtobufCMessageDescriptor tpb_nvr_lcam_mc_rect_num__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TpbNvrLcamMcRectNum",
  "TpbNvrLcamMcRectNum",
  "TpbNvrLcamMcRectNum",
  "",
  sizeof(TpbNvrLcamMcRectNum),
  4,
  tpb_nvr_lcam_mc_rect_num__field_descriptors,
  tpb_nvr_lcam_mc_rect_num__field_indices_by_name,
  1,  tpb_nvr_lcam_mc_rect_num__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_lcam_mc_rect_num__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_lcam_mc_mdparam_chn__field_descriptors[4] =
{
  {
    "enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLcamMcMDParamChn, has_enable),
    offsetof(TPbNvrLcamMcMDParamChn, enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "sensitivity",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLcamMcMDParamChn, has_sensitivity),
    offsetof(TPbNvrLcamMcMDParamChn, sensitivity),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "region_num",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLcamMcMDParamChn, has_region_num),
    offsetof(TPbNvrLcamMcMDParamChn, region_num),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "zone_info",
    4,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrLcamMcMDParamChn, n_zone_info),
    offsetof(TPbNvrLcamMcMDParamChn, zone_info),
    &tpb_nvr_lcam_mc_rect_num__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_lcam_mc_mdparam_chn__field_indices_by_name[] = {
  0,   /* field[0] = enable */
  2,   /* field[2] = region_num */
  1,   /* field[1] = sensitivity */
  3,   /* field[3] = zone_info */
};
static const ProtobufCIntRange tpb_nvr_lcam_mc_mdparam_chn__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 4 }
};
const ProtobufCMessageDescriptor tpb_nvr_lcam_mc_mdparam_chn__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrLcamMcMDParamChn",
  "TPbNvrLcamMcMDParamChn",
  "TPbNvrLcamMcMDParamChn",
  "",
  sizeof(TPbNvrLcamMcMDParamChn),
  4,
  tpb_nvr_lcam_mc_mdparam_chn__field_descriptors,
  tpb_nvr_lcam_mc_mdparam_chn__field_indices_by_name,
  1,  tpb_nvr_lcam_mc_mdparam_chn__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_lcam_mc_mdparam_chn__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_lcam_mc_overlay_param_chn__field_descriptors[4] =
{
  {
    "enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLcamMcOverlayParamChn, has_enable),
    offsetof(TPbNvrLcamMcOverlayParamChn, enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "sensitivity",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLcamMcOverlayParamChn, has_sensitivity),
    offsetof(TPbNvrLcamMcOverlayParamChn, sensitivity),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "region_num",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLcamMcOverlayParamChn, has_region_num),
    offsetof(TPbNvrLcamMcOverlayParamChn, region_num),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "zone_info",
    4,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrLcamMcOverlayParamChn, n_zone_info),
    offsetof(TPbNvrLcamMcOverlayParamChn, zone_info),
    &tpb_nvr_lcam_mc_rect_num__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_lcam_mc_overlay_param_chn__field_indices_by_name[] = {
  0,   /* field[0] = enable */
  2,   /* field[2] = region_num */
  1,   /* field[1] = sensitivity */
  3,   /* field[3] = zone_info */
};
static const ProtobufCIntRange tpb_nvr_lcam_mc_overlay_param_chn__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 4 }
};
const ProtobufCMessageDescriptor tpb_nvr_lcam_mc_overlay_param_chn__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrLcamMcOverlayParamChn",
  "TPbNvrLcamMcOverlayParamChn",
  "TPbNvrLcamMcOverlayParamChn",
  "",
  sizeof(TPbNvrLcamMcOverlayParamChn),
  4,
  tpb_nvr_lcam_mc_overlay_param_chn__field_descriptors,
  tpb_nvr_lcam_mc_overlay_param_chn__field_indices_by_name,
  1,  tpb_nvr_lcam_mc_overlay_param_chn__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_lcam_mc_overlay_param_chn__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_lcam_mc_stream_compos_info_chn__field_descriptors[4] =
{
  {
    "gps",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLcamMcStreamComposInfoChn, has_gps),
    offsetof(TPbNvrLcamMcStreamComposInfoChn, gps),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "water_mark",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLcamMcStreamComposInfoChn, has_water_mark),
    offsetof(TPbNvrLcamMcStreamComposInfoChn, water_mark),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "base_ai",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLcamMcStreamComposInfoChn, has_base_ai),
    offsetof(TPbNvrLcamMcStreamComposInfoChn, base_ai),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "azimuth",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLcamMcStreamComposInfoChn, has_azimuth),
    offsetof(TPbNvrLcamMcStreamComposInfoChn, azimuth),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_lcam_mc_stream_compos_info_chn__field_indices_by_name[] = {
  3,   /* field[3] = azimuth */
  2,   /* field[2] = base_ai */
  0,   /* field[0] = gps */
  1,   /* field[1] = water_mark */
};
static const ProtobufCIntRange tpb_nvr_lcam_mc_stream_compos_info_chn__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 4 }
};
const ProtobufCMessageDescriptor tpb_nvr_lcam_mc_stream_compos_info_chn__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrLcamMcStreamComposInfoChn",
  "TPbNvrLcamMcStreamComposInfoChn",
  "TPbNvrLcamMcStreamComposInfoChn",
  "",
  sizeof(TPbNvrLcamMcStreamComposInfoChn),
  4,
  tpb_nvr_lcam_mc_stream_compos_info_chn__field_descriptors,
  tpb_nvr_lcam_mc_stream_compos_info_chn__field_indices_by_name,
  1,  tpb_nvr_lcam_mc_stream_compos_info_chn__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_lcam_mc_stream_compos_info_chn__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_lcam_mc_ptz_param_chn__field_descriptors[2] =
{
  {
    "ptz",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLcamMcPtzParamChn, has_ptz),
    offsetof(TPbNvrLcamMcPtzParamChn, ptz),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "isp",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLcamMcPtzParamChn, has_isp),
    offsetof(TPbNvrLcamMcPtzParamChn, isp),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_lcam_mc_ptz_param_chn__field_indices_by_name[] = {
  1,   /* field[1] = isp */
  0,   /* field[0] = ptz */
};
static const ProtobufCIntRange tpb_nvr_lcam_mc_ptz_param_chn__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_lcam_mc_ptz_param_chn__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrLcamMcPtzParamChn",
  "TPbNvrLcamMcPtzParamChn",
  "TPbNvrLcamMcPtzParamChn",
  "",
  sizeof(TPbNvrLcamMcPtzParamChn),
  2,
  tpb_nvr_lcam_mc_ptz_param_chn__field_descriptors,
  tpb_nvr_lcam_mc_ptz_param_chn__field_indices_by_name,
  1,  tpb_nvr_lcam_mc_ptz_param_chn__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_lcam_mc_ptz_param_chn__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_lcam_mc_roi_info__field_descriptors[2] =
{
  {
    "roi_region",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrLcamMcRoiInfo, roi_region),
    &tpb_nvr_lcam_mc_rect_num__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "roi_profile",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLcamMcRoiInfo, has_roi_profile),
    offsetof(TPbNvrLcamMcRoiInfo, roi_profile),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_lcam_mc_roi_info__field_indices_by_name[] = {
  1,   /* field[1] = roi_profile */
  0,   /* field[0] = roi_region */
};
static const ProtobufCIntRange tpb_nvr_lcam_mc_roi_info__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_lcam_mc_roi_info__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrLcamMcRoiInfo",
  "TPbNvrLcamMcRoiInfo",
  "TPbNvrLcamMcRoiInfo",
  "",
  sizeof(TPbNvrLcamMcRoiInfo),
  2,
  tpb_nvr_lcam_mc_roi_info__field_descriptors,
  tpb_nvr_lcam_mc_roi_info__field_indices_by_name,
  1,  tpb_nvr_lcam_mc_roi_info__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_lcam_mc_roi_info__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_lcam_mc_roi_param__field_descriptors[3] =
{
  {
    "enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLcamMcRoiParam, has_enable),
    offsetof(TPbNvrLcamMcRoiParam, enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "region_num",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLcamMcRoiParam, has_region_num),
    offsetof(TPbNvrLcamMcRoiParam, region_num),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "roi_info",
    3,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrLcamMcRoiParam, n_roi_info),
    offsetof(TPbNvrLcamMcRoiParam, roi_info),
    &tpb_nvr_lcam_mc_roi_info__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_lcam_mc_roi_param__field_indices_by_name[] = {
  0,   /* field[0] = enable */
  1,   /* field[1] = region_num */
  2,   /* field[2] = roi_info */
};
static const ProtobufCIntRange tpb_nvr_lcam_mc_roi_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 3 }
};
const ProtobufCMessageDescriptor tpb_nvr_lcam_mc_roi_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrLcamMcRoiParam",
  "TPbNvrLcamMcRoiParam",
  "TPbNvrLcamMcRoiParam",
  "",
  sizeof(TPbNvrLcamMcRoiParam),
  3,
  tpb_nvr_lcam_mc_roi_param__field_descriptors,
  tpb_nvr_lcam_mc_roi_param__field_indices_by_name,
  1,  tpb_nvr_lcam_mc_roi_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_lcam_mc_roi_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_lcam_mc_roi_param_chn__field_descriptors[1] =
{
  {
    "roi_param",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrLcamMcRoiParamChn, n_roi_param),
    offsetof(TPbNvrLcamMcRoiParamChn, roi_param),
    &tpb_nvr_lcam_mc_roi_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_lcam_mc_roi_param_chn__field_indices_by_name[] = {
  0,   /* field[0] = roi_param */
};
static const ProtobufCIntRange tpb_nvr_lcam_mc_roi_param_chn__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor tpb_nvr_lcam_mc_roi_param_chn__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrLcamMcRoiParamChn",
  "TPbNvrLcamMcRoiParamChn",
  "TPbNvrLcamMcRoiParamChn",
  "",
  sizeof(TPbNvrLcamMcRoiParamChn),
  1,
  tpb_nvr_lcam_mc_roi_param_chn__field_descriptors,
  tpb_nvr_lcam_mc_roi_param_chn__field_indices_by_name,
  1,  tpb_nvr_lcam_mc_roi_param_chn__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_lcam_mc_roi_param_chn__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_point__field_descriptors[2] =
{
  {
    "pos_X",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPoint, has_pos_x),
    offsetof(TPbNvrPoint, pos_x),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "pos_Y",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPoint, has_pos_y),
    offsetof(TPbNvrPoint, pos_y),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_point__field_indices_by_name[] = {
  0,   /* field[0] = pos_X */
  1,   /* field[1] = pos_Y */
};
static const ProtobufCIntRange tpb_nvr_point__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_point__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrPoint",
  "TPbNvrPoint",
  "TPbNvrPoint",
  "",
  sizeof(TPbNvrPoint),
  2,
  tpb_nvr_point__field_descriptors,
  tpb_nvr_point__field_indices_by_name,
  1,  tpb_nvr_point__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_point__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_polygon__field_descriptors[2] =
{
  {
    "point_num",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPolygon, has_point_num),
    offsetof(TPbNvrPolygon, point_num),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "polygon_point",
    2,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrPolygon, n_polygon_point),
    offsetof(TPbNvrPolygon, polygon_point),
    &tpb_nvr_point__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_polygon__field_indices_by_name[] = {
  0,   /* field[0] = point_num */
  1,   /* field[1] = polygon_point */
};
static const ProtobufCIntRange tpb_nvr_polygon__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_polygon__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrPolygon",
  "TPbNvrPolygon",
  "TPbNvrPolygon",
  "",
  sizeof(TPbNvrPolygon),
  2,
  tpb_nvr_polygon__field_descriptors,
  tpb_nvr_polygon__field_indices_by_name,
  1,  tpb_nvr_polygon__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_polygon__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_lcam_mc_shade_info__field_descriptors[3] =
{
  {
    "type",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLcamMcShadeInfo, has_type),
    offsetof(TPbNvrLcamMcShadeInfo, type),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "color",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLcamMcShadeInfo, has_color),
    offsetof(TPbNvrLcamMcShadeInfo, color),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ploygon",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrLcamMcShadeInfo, ploygon),
    &tpb_nvr_polygon__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_lcam_mc_shade_info__field_indices_by_name[] = {
  1,   /* field[1] = color */
  2,   /* field[2] = ploygon */
  0,   /* field[0] = type */
};
static const ProtobufCIntRange tpb_nvr_lcam_mc_shade_info__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 3 }
};
const ProtobufCMessageDescriptor tpb_nvr_lcam_mc_shade_info__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrLcamMcShadeInfo",
  "TPbNvrLcamMcShadeInfo",
  "TPbNvrLcamMcShadeInfo",
  "",
  sizeof(TPbNvrLcamMcShadeInfo),
  3,
  tpb_nvr_lcam_mc_shade_info__field_descriptors,
  tpb_nvr_lcam_mc_shade_info__field_indices_by_name,
  1,  tpb_nvr_lcam_mc_shade_info__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_lcam_mc_shade_info__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_start_end_time__field_descriptors[3] =
{
  {
    "enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrStartEndTime, has_enable),
    offsetof(TPbNvrStartEndTime, enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "start_time",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrStartEndTime, has_start_time),
    offsetof(TPbNvrStartEndTime, start_time),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "end_time",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrStartEndTime, has_end_time),
    offsetof(TPbNvrStartEndTime, end_time),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_start_end_time__field_indices_by_name[] = {
  0,   /* field[0] = enable */
  2,   /* field[2] = end_time */
  1,   /* field[1] = start_time */
};
static const ProtobufCIntRange tpb_nvr_start_end_time__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 3 }
};
const ProtobufCMessageDescriptor tpb_nvr_start_end_time__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrStartEndTime",
  "TPbNvrStartEndTime",
  "TPbNvrStartEndTime",
  "",
  sizeof(TPbNvrStartEndTime),
  3,
  tpb_nvr_start_end_time__field_descriptors,
  tpb_nvr_start_end_time__field_indices_by_name,
  1,  tpb_nvr_start_end_time__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_start_end_time__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_time_scale__field_descriptors[1] =
{
  {
    "time_scale",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrTimeScale, n_time_scale),
    offsetof(TPbNvrTimeScale, time_scale),
    &tpb_nvr_start_end_time__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_time_scale__field_indices_by_name[] = {
  0,   /* field[0] = time_scale */
};
static const ProtobufCIntRange tpb_nvr_time_scale__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor tpb_nvr_time_scale__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrTimeScale",
  "TPbNvrTimeScale",
  "TPbNvrTimeScale",
  "",
  sizeof(TPbNvrTimeScale),
  1,
  tpb_nvr_time_scale__field_descriptors,
  tpb_nvr_time_scale__field_indices_by_name,
  1,  tpb_nvr_time_scale__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_time_scale__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_disarming_time__field_descriptors[1] =
{
  {
    "week_scale",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrDisarmingTime, n_week_scale),
    offsetof(TPbNvrDisarmingTime, week_scale),
    &tpb_nvr_time_scale__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_disarming_time__field_indices_by_name[] = {
  0,   /* field[0] = week_scale */
};
static const ProtobufCIntRange tpb_nvr_disarming_time__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor tpb_nvr_disarming_time__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrDisarmingTime",
  "TPbNvrDisarmingTime",
  "TPbNvrDisarmingTime",
  "",
  sizeof(TPbNvrDisarmingTime),
  1,
  tpb_nvr_disarming_time__field_descriptors,
  tpb_nvr_disarming_time__field_indices_by_name,
  1,  tpb_nvr_disarming_time__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_disarming_time__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_lcam_mc_shade_param_chn__field_descriptors[4] =
{
  {
    "enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLcamMcShadeParamChn, has_enable),
    offsetof(TPbNvrLcamMcShadeParamChn, enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "region_num",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLcamMcShadeParamChn, has_region_num),
    offsetof(TPbNvrLcamMcShadeParamChn, region_num),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "shade_info",
    3,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrLcamMcShadeParamChn, n_shade_info),
    offsetof(TPbNvrLcamMcShadeParamChn, shade_info),
    &tpb_nvr_lcam_mc_shade_info__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "check_time",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrLcamMcShadeParamChn, check_time),
    &tpb_nvr_disarming_time__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_lcam_mc_shade_param_chn__field_indices_by_name[] = {
  3,   /* field[3] = check_time */
  0,   /* field[0] = enable */
  1,   /* field[1] = region_num */
  2,   /* field[2] = shade_info */
};
static const ProtobufCIntRange tpb_nvr_lcam_mc_shade_param_chn__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 4 }
};
const ProtobufCMessageDescriptor tpb_nvr_lcam_mc_shade_param_chn__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrLcamMcShadeParamChn",
  "TPbNvrLcamMcShadeParamChn",
  "TPbNvrLcamMcShadeParamChn",
  "",
  sizeof(TPbNvrLcamMcShadeParamChn),
  4,
  tpb_nvr_lcam_mc_shade_param_chn__field_descriptors,
  tpb_nvr_lcam_mc_shade_param_chn__field_indices_by_name,
  1,  tpb_nvr_lcam_mc_shade_param_chn__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_lcam_mc_shade_param_chn__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_lcam_mc_work_mode_chn__field_descriptors[1] =
{
  {
    "work_mode",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLcamMcWorkModeChn, has_work_mode),
    offsetof(TPbNvrLcamMcWorkModeChn, work_mode),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_lcam_mc_work_mode_chn__field_indices_by_name[] = {
  0,   /* field[0] = work_mode */
};
static const ProtobufCIntRange tpb_nvr_lcam_mc_work_mode_chn__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor tpb_nvr_lcam_mc_work_mode_chn__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrLcamMcWorkModeChn",
  "TPbNvrLcamMcWorkModeChn",
  "TPbNvrLcamMcWorkModeChn",
  "",
  sizeof(TPbNvrLcamMcWorkModeChn),
  1,
  tpb_nvr_lcam_mc_work_mode_chn__field_descriptors,
  tpb_nvr_lcam_mc_work_mode_chn__field_indices_by_name,
  1,  tpb_nvr_lcam_mc_work_mode_chn__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_lcam_mc_work_mode_chn__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_lcam_mc_auto_wiper_chn__field_descriptors[2] =
{
  {
    "sensi_type",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLcamMcAutoWiperChn, has_sensi_type),
    offsetof(TPbNvrLcamMcAutoWiperChn, sensi_type),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ctrl_type",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLcamMcAutoWiperChn, has_ctrl_type),
    offsetof(TPbNvrLcamMcAutoWiperChn, ctrl_type),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_lcam_mc_auto_wiper_chn__field_indices_by_name[] = {
  1,   /* field[1] = ctrl_type */
  0,   /* field[0] = sensi_type */
};
static const ProtobufCIntRange tpb_nvr_lcam_mc_auto_wiper_chn__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_lcam_mc_auto_wiper_chn__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrLcamMcAutoWiperChn",
  "TPbNvrLcamMcAutoWiperChn",
  "TPbNvrLcamMcAutoWiperChn",
  "",
  sizeof(TPbNvrLcamMcAutoWiperChn),
  2,
  tpb_nvr_lcam_mc_auto_wiper_chn__field_descriptors,
  tpb_nvr_lcam_mc_auto_wiper_chn__field_indices_by_name,
  1,  tpb_nvr_lcam_mc_auto_wiper_chn__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_lcam_mc_auto_wiper_chn__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_lcam_mc_cfg__field_descriptors[15] =
{
  {
    "vid_mut_enc_num",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrLcamMcCfg, n_vid_mut_enc_num),
    offsetof(TPbNvrLcamMcCfg, vid_mut_enc_num),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "vid_enc_param_aud",
    2,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrLcamMcCfg, n_vid_enc_param_aud),
    offsetof(TPbNvrLcamMcCfg, vid_enc_param_aud),
    &tpb_nvr_lcam_mc_vid_enc_param_chn__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "aud_in_param",
    3,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrLcamMcCfg, n_aud_in_param),
    offsetof(TPbNvrLcamMcCfg, aud_in_param),
    &tpb_nvr_lcam_mc_aud_in_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "aud_enc_param_aud",
    4,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrLcamMcCfg, n_aud_enc_param_aud),
    offsetof(TPbNvrLcamMcCfg, aud_enc_param_aud),
    &tpb_nvr_lcam_mc_aud_enc_param_chn__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "aud_dec_param_aud",
    5,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrLcamMcCfg, n_aud_dec_param_aud),
    offsetof(TPbNvrLcamMcCfg, aud_dec_param_aud),
    &tpb_nvr_lcam_mc_aud_dec_param_chn__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "aud_aec_param_aud",
    6,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrLcamMcCfg, n_aud_aec_param_aud),
    offsetof(TPbNvrLcamMcCfg, aud_aec_param_aud),
    &tpb_nvr_lcam_mc_aud_aec_param_chn__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "aud_mix_param_aud",
    7,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrLcamMcCfg, n_aud_mix_param_aud),
    offsetof(TPbNvrLcamMcCfg, aud_mix_param_aud),
    &tpb_nvr_lcam_mc_aud_mix_param_chn__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "md_param",
    8,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrLcamMcCfg, n_md_param),
    offsetof(TPbNvrLcamMcCfg, md_param),
    &tpb_nvr_lcam_mc_mdparam_chn__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "overlay_param",
    9,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrLcamMcCfg, n_overlay_param),
    offsetof(TPbNvrLcamMcCfg, overlay_param),
    &tpb_nvr_lcam_mc_overlay_param_chn__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "roi_param",
    10,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrLcamMcCfg, n_roi_param),
    offsetof(TPbNvrLcamMcCfg, roi_param),
    &tpb_nvr_lcam_mc_roi_param_chn__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "stream_compos_info",
    11,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrLcamMcCfg, n_stream_compos_info),
    offsetof(TPbNvrLcamMcCfg, stream_compos_info),
    &tpb_nvr_lcam_mc_stream_compos_info_chn__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ptz_param",
    12,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrLcamMcCfg, n_ptz_param),
    offsetof(TPbNvrLcamMcCfg, ptz_param),
    &tpb_nvr_lcam_mc_ptz_param_chn__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "shade_param",
    13,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrLcamMcCfg, n_shade_param),
    offsetof(TPbNvrLcamMcCfg, shade_param),
    &tpb_nvr_lcam_mc_shade_param_chn__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "work_mode",
    14,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrLcamMcCfg, n_work_mode),
    offsetof(TPbNvrLcamMcCfg, work_mode),
    &tpb_nvr_lcam_mc_work_mode_chn__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "auto_wiper",
    15,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrLcamMcCfg, n_auto_wiper),
    offsetof(TPbNvrLcamMcCfg, auto_wiper),
    &tpb_nvr_lcam_mc_auto_wiper_chn__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_lcam_mc_cfg__field_indices_by_name[] = {
  5,   /* field[5] = aud_aec_param_aud */
  4,   /* field[4] = aud_dec_param_aud */
  3,   /* field[3] = aud_enc_param_aud */
  2,   /* field[2] = aud_in_param */
  6,   /* field[6] = aud_mix_param_aud */
  14,   /* field[14] = auto_wiper */
  7,   /* field[7] = md_param */
  8,   /* field[8] = overlay_param */
  11,   /* field[11] = ptz_param */
  9,   /* field[9] = roi_param */
  12,   /* field[12] = shade_param */
  10,   /* field[10] = stream_compos_info */
  1,   /* field[1] = vid_enc_param_aud */
  0,   /* field[0] = vid_mut_enc_num */
  13,   /* field[13] = work_mode */
};
static const ProtobufCIntRange tpb_nvr_lcam_mc_cfg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 15 }
};
const ProtobufCMessageDescriptor tpb_nvr_lcam_mc_cfg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrLcamMcCfg",
  "TPbNvrLcamMcCfg",
  "TPbNvrLcamMcCfg",
  "",
  sizeof(TPbNvrLcamMcCfg),
  15,
  tpb_nvr_lcam_mc_cfg__field_descriptors,
  tpb_nvr_lcam_mc_cfg__field_indices_by_name,
  1,  tpb_nvr_lcam_mc_cfg__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_lcam_mc_cfg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
