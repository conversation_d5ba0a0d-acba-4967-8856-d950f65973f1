#!/bin/bash
#This script is used to compile whole project.

######################
## Global Variables ##
######################
TARGET_LIB_BASE=../10-common/lib/release/qcom/lib
TARGET_JNI=${TARGET_LIB_BASE}pukdmvs.so
TARGET_JAR=../10-common/version/release/qcom/jar/ipw_ctrl.jar
TARGET_APK=../10-common/version/release/qcom/apk/KDM2412M_signed.apk

######################
## Functions        ##
######################
checkFileExists()
{
	local filename=$1
	if [ -f $filename ]
	then
        echo "==== Generate file ${filename} success ===="
	else
        echo "==== Generate file ${filename} failed ===="
		exit -1
	fi
}

echo "===== compile nvr jni ====="
rm -f $TARGET_JNI
cd jni/jni
./compile_kdmvs.sh
cd -           
checkFileExists $TARGET_JNI

echo "==== compile androidjar ==="
rm -f $TARGET_JAR
cd androidjar
./compile_jar.sh
cd -
checkFileExists $TARGET_JAR

echo "==== compile androidapk ==="
rm -f $TARGET_APK
cd androidapk
./compile_apk.sh
cd -
checkFileExists $TARGET_APK
