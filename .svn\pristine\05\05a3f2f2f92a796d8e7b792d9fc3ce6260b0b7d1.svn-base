path="../../10-common/version/compileinfo/nvrkdvsend_his3536.txt"
date>>$path

cd ./prj_linux

echo ==============================================
echo =      nvr_kdvsend_linux for his3536             =
echo ==============================================

echo "============compile libnvrkdvsend his3536============">>../$path

make -j4 -e DEBUG=1 -f makefile_his3536 clean
make -j4 -e DEBUG=1 -f makefile_his3536 2>>../$path

#makefile install已经安装到指定目录了
#cp -L -r -f libnvrkdvsend.so ../../../10-common/lib/release/his3536/nvrkdvsend

cd ..
