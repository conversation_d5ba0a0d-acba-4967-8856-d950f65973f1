

TOP := ..

COMM_DIR := ../../../common/

SRC_DIR := $(TOP)/source
CURDIR := ./../

## Name and type of the target for this Makefile

SO_TARGET	      := nvrsgwdecpro


## Define debugging symbols
DEBUG = 0
LINUX_COMPILER = _HIS3531DV200_
PWLIB_SUPPORT = 0
CFLAGS += -funwind-tables
## Object files that compose the target(s)

OBJS := $(SRC_DIR)/nvrsgwcore\
		$(SRC_DIR)/nvrsgwencoder\
		$(SRC_DIR)/nvrsgwdecoder\
		$(SRC_DIR)/nvrsgwcfg\
		$(SRC_DIR)/nvrsgwmca\
		$(SRC_DIR)/nvrsgwcfg.pb-c\
		$(SRC_DIR)/nvrsgwupgrade\
		$(SRC_DIR)/nvrsgwcalcres\
		
		
## Libraries to include in shared object file
        
ifneq ($(SLIBS),) 
LDFLAGS += -Wl,-static
LDFLAGS += $(foreach lib,$(SLIBS),-l$(lib))
endif
## Add driver-specific include directory to the search path

INC_PATH += $(CURDIR)/include \
		$(CURDIR)/../common \
	        $(CURDIR)/../../nvrsgwcom/comdef \
		$(CURDIR)/../../../40-service/nvrcfg/include \
		$(CURDIR)/../../../10-common/include/service \
		$(CURDIR)/../../../10-common/include/serviceext2/medt \
		$(CURDIR)/../../../10-common/include/cbb/mediactrl\
		$(CURDIR)/../../../10-common/include/cbb/mediaswitch\
		$(CURDIR)/../../../10-common/include/cbb/debuglog\
		$(CURDIR)/../../../10-common/include/system\
		$(CURDIR)/../../../10-common/include/cbb/protobuf \
		$(CURDIR)/../../../10-common/include/cbb/osp \
		$(CURDIR)/../../../10-common/include/cbb/charconversion\
		$(CURDIR)/../../../10-common/include/cbb/appclt\
		$(CURDIR)/../../../10-common/include/app\
		$(CURDIR)/../../../10-common/include/app/sdscpapp\
		$(CURDIR)/../../../10-common/include/cbb/mxml\
		$(CURDIR)/../../../10-common/include/hal/drvlib_64 \
		$(CURDIR)/../../../10-common/include/hal/drvlib_64/system \
		$(CURDIR)/../../../10-common/include/hal/drvlib_64/hardware \
		$(CURDIR)/../../../10-common/include/cbb/ftpc\
		$(CURDIR)/../../../10-common/include/cbb/kdmfileinterface\
		$(CURDIR)/../../../10-common/include/cbb/rp\
		$(CURDIR)/../../../10-common/include/cbb/mcav2.0\
		$(CURDIR)/../../../40-service/nvrsys/include \
		$(CURDIR)/../../../40-service/nvrcap/include\
		$(CURDIR)/../../../40-service/nvrvtductrl/include \
		$(CURDIR)/../../../41-servicefix/nvrcustcap/include \
		$(CURDIR)/../../../41-servicefix/nvrsgwdec/common \
		$(CURDIR)/../../../41-servicefix/nvrsgwdec/sgw_rc/include \

CFLAGS += -D_SGW_
CFLAGS += -D_HIS3531DV200_


ifeq ($(PWLIB_SUPPORT),1)
   INC_PATH += $(PWLIBDIR)/include/ptlib/unix $(PWLIBDIR)/include
endif


INSTALL_LIB_PATH = ../../../../10-common/lib/release/his3531dv200/speprolib

LDFLAGS += -L../../../../10-common/lib/release/his3531dv200/
LDFLAGS += -L../../../../10-common/lib/release/his3531dv200/appcltlib/

include $(COMM_DIR)/common.mk


