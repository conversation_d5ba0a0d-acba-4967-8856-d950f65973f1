

TOP := ..

COMM_DIR := ../../common/

SRC_DIR := $(TOP)/source
CURDIR := ./

## Name and type of the target for this Makefile

SO_TARGET	      := nvrunifiedlog


## Define debugging symbols
DEBUG = 0
LINUX_COMPILER = _RK3568_
PWLIB_SUPPORT = 0
CFLAGS += -funwind-tables
## Object files that compose the target(s)

OBJS := $(SRC_DIR)/nvrunifiedlog\
			$(SRC_DIR)/../../nvrsimcode/simnvrapmlog\
	
## Libraries to include in shared object file

LIB_PATH += $(TOP)/../../10-common/lib/release/rk3568
LIBS += curl      
ifneq ($(SLIBS),) 
LDFLAGS += -Wl,-static
LDFLAGS += $(foreach lib,$(SLIBS),-l$(lib))
endif
## Add driver-specific include directory to the search path

INC_PATH += $(CURDIR)/../include \
		$(CURDIR)/../../nvrcap/include \
		$(CURDIR)/../../nvrsys/include \
		$(CURDIR)/../../../10-common/include/cbb/osp\
		$(CURDIR)/../../../10-common/include/cbb/protobuf\
		$(CURDIR)/../../../10-common/include/cbb/debuglog \
		$(CURDIR)/../../common \
		$(CURDIR)/../../../10-common/include/service\
		$(CURDIR)/../../../10-common/include/cbb/cjson\
		$(CURDIR)/../../../10-common/include/system\
		$(CURDIR)/../../../10-common/include/app\
		$(CURDIR)/../../../10-common/include/cbb/curl		
		
CFLAGS += -D_RK3568_



ifeq ($(PWLIB_SUPPORT),1)
   INC_PATH += $(PWLIBDIR)/include/ptlib/unix $(PWLIBDIR)/include
endif


INSTALL_LIB_PATH = ../../../10-common/lib/release/rk3568
LDFLAGS += -L$(INSTALL_LIB_PATH)
include $(COMM_DIR)/common.mk
