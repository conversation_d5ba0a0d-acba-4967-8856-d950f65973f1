/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: nvrerrno.proto */

/* Do not generate deprecated warnings for self */
#ifndef PROTOBUF_C__NO_DEPRECATED
#define PROTOBUF_C__NO_DEPRECATED
#endif

#include "nvrerrno.pb-c.h"
static const ProtobufCEnumValue nvr_err__enum_values_by_number[400] =
{
  { "OK", "NVR_ERR__OK", 1000 },
  { "ERROR", "NVR_ERR__ERROR", 1001 },
  { "ASSERT", "NVR_ERR__ASSERT", 1002 },
  { "SEM_TAKE_FAILED", "NVR_ERR__SEM_TAKE_FAILED", 1003 },
  { "SEM_GIVEE_FAILED", "NVR_ERR__SEM_GIVEE_FAILED", 1004 },
  { "PARAM_INVALID", "NVR_ERR__PARAM_INVALID", 1005 },
  { "WAIT_TIMEOUT", "NVR_ERR__WAIT_TIMEOUT", 1006 },
  { "MALLOC_FAILED", "NVR_ERR__MALLOC_FAILED", 1007 },
  { "STRING_ILLEGAL", "NVR_ERR__STRING_ILLEGAL", 1008 },
  { "STRING_TOO_SHORT", "NVR_ERR__STRING_TOO_SHORT", 1009 },
  { "STRING_TOO_LONG", "NVR_ERR__STRING_TOO_LONG", 1010 },
  { "FILE_INEXIST", "NVR_ERR__FILE_INEXIST", 1020 },
  { "WRITE_FILE", "NVR_ERR__WRITE_FILE", 1021 },
  { "EXPORTING_CFG", "NVR_ERR__EXPORTING_CFG", 1022 },
  { "REGISTER_FULL", "NVR_ERR__REGISTER_FULL", 1023 },
  { "ALREADY_REGISTERED", "NVR_ERR__ALREADY_REGISTERED", 1024 },
  { "NO_REGISTERED", "NVR_ERR__NO_REGISTERED", 1025 },
  { "IO_ERROR", "NVR_ERR__IO_ERROR", 1026 },
  { "PARAM_NONEXISTENCE", "NVR_ERR__PARAM_NONEXISTENCE", 1027 },
  { "CAP_NO_SUPPORT", "NVR_ERR__CAP_NO_SUPPORT", 1100 },
  { "USER_EXIST", "NVR_ERR__USER_EXIST", 1200 },
  { "USER_INEXIST", "NVR_ERR__USER_INEXIST", 1201 },
  { "USER_FILE_BROKEN", "NVR_ERR__USER_FILE_BROKEN", 1202 },
  { "USER_NUM_MAX", "NVR_ERR__USER_NUM_MAX", 1203 },
  { "USER_DEL_DISALLOWED", "NVR_ERR__USER_DEL_DISALLOWED", 1204 },
  { "USER_NAME_ILLEGAL", "NVR_ERR__USER_NAME_ILLEGAL", 1205 },
  { "USER_PWD_ILLEGAL", "NVR_ERR__USER_PWD_ILLEGAL", 1206 },
  { "USER_NAME_LEN_TOO_LONG", "NVR_ERR__USER_NAME_LEN_TOO_LONG", 1207 },
  { "USER_NAME_LEN_TOO_SHORT", "NVR_ERR__USER_NAME_LEN_TOO_SHORT", 1208 },
  { "USER_PWD_LEN_TOO_LONG", "NVR_ERR__USER_PWD_LEN_TOO_LONG", 1209 },
  { "USER_PWD_LEN_TOO_SHORT", "NVR_ERR__USER_PWD_LEN_TOO_SHORT", 1210 },
  { "USER_PWD_STRENGTH_WEAK", "NVR_ERR__USER_PWD_STRENGTH_WEAK", 1211 },
  { "USER_MGR_EMAIL_ILLEGAL", "NVR_ERR__USER_MGR_EMAIL_ILLEGAL", 1212 },
  { "USER_REMOTE_IP_INFO_ILLEGAL", "NVR_ERR__USER_REMOTE_IP_INFO_ILLEGAL", 1213 },
  { "USER_NAME_MDY_DISALLOWED", "NVR_ERR__USER_NAME_MDY_DISALLOWED", 1214 },
  { "USER_ADMIN_PERM_MDY_DISALLOWED", "NVR_ERR__USER_ADMIN_PERM_MDY_DISALLOWED", 1215 },
  { "USER_DEV_SYS_NOACTIVE", "NVR_ERR__USER_DEV_SYS_NOACTIVE", 1216 },
  { "USER_PASS_SAME_TO_BEFORE", "NVR_ERR__USER_PASS_SAME_TO_BEFORE", 1217 },
  { "USER_NAME_NULL", "NVR_ERR__USER_NAME_NULL", 1218 },
  { "USER_PASS_NULL", "NVR_ERR__USER_PASS_NULL", 1219 },
  { "USER_PASS_OVERTIME", "NVR_ERR__USER_PASS_OVERTIME", 1220 },
  { "LOG", "NVR_ERR__LOG", 1300 },
  { "USER_LOG_TASK_BUSY", "NVR_ERR__USER_LOG_TASK_BUSY", 1301 },
  { "USER_LOG_TASK_ID_UNAVAILABLE", "NVR_ERR__USER_LOG_TASK_ID_UNAVAILABLE", 1302 },
  { "DEV", "NVR_ERR__DEV", 1400 },
  { "NET", "NVR_ERR__NET", 1500 },
  { "NET_REGISTER_FULL", "NVR_ERR__NET_REGISTER_FULL", 1501 },
  { "NET_ALREADY_REGISTERED", "NVR_ERR__NET_ALREADY_REGISTERED", 1502 },
  { "NET_NO_REGISTERED", "NVR_ERR__NET_NO_REGISTERED", 1503 },
  { "NET_PING_NUM_MAX", "NVR_ERR__NET_PING_NUM_MAX", 1504 },
  { "NET_DOMAIN_ANALY_FAILD", "NVR_ERR__NET_DOMAIN_ANALY_FAILD", 1505 },
  { "NET_PORT_IS_USING", "NVR_ERR__NET_PORT_IS_USING", 1506 },
  { "NET_OTHER_IP_IN_SAME_NET", "NVR_ERR__NET_OTHER_IP_IN_SAME_NET", 1507 },
  { "NET_IP_GW_NOTIN_SAME_NET", "NVR_ERR__NET_IP_GW_NOTIN_SAME_NET", 1508 },
  { "NET_OPERATE_TOO_FREQUENCY", "NVR_ERR__NET_OPERATE_TOO_FREQUENCY", 1509 },
  { "NET_PING_CHN_NO_IP", "NVR_ERR__NET_PING_CHN_NO_IP", 1510 },
  { "NET_INVALID_HTTPS_CRT", "NVR_ERR__NET_INVALID_HTTPS_CRT", 1511 },
  { "NET_SOME_STATIC_ROUTES_INVAILD", "NVR_ERR__NET_SOME_STATIC_ROUTES_INVAILD", 1512 },
  { "NET_AP_DHCPSERVER_MUTEX", "NVR_ERR__NET_AP_DHCPSERVER_MUTEX", 1513 },
  { "MPU", "NVR_ERR__MPU", 1600 },
  { "MPU_DEC_CHN_NUM_OVER_MAX", "NVR_ERR__MPU_DEC_CHN_NUM_OVER_MAX", 1601 },
  { "MPU_DEC_ABILITY_OVER_MAX", "NVR_ERR__MPU_DEC_ABILITY_OVER_MAX", 1602 },
  { "MPU_CHN_ID_IS_DECODING", "NVR_ERR__MPU_CHN_ID_IS_DECODING", 1603 },
  { "MPU_MC_SET_LAYOUT_FAILED", "NVR_ERR__MPU_MC_SET_LAYOUT_FAILED", 1604 },
  { "MPU_MC_SET_DEC_PARAM_FAILED", "NVR_ERR__MPU_MC_SET_DEC_PARAM_FAILED", 1605 },
  { "MPU_MC_SET_OPT_FAILED", "NVR_ERR__MPU_MC_SET_OPT_FAILED", 1606 },
  { "MPU_MC_GET_OPT_FAILED", "NVR_ERR__MPU_MC_GET_OPT_FAILED", 1607 },
  { "MPU_OVER_MC_DEV_ZOOM_CAP", "NVR_ERR__MPU_OVER_MC_DEV_ZOOM_CAP", 1608 },
  { "MPU_BIND_FAILED", "NVR_ERR__MPU_BIND_FAILED", 1609 },
  { "MPU_UNBIND_FAILED", "NVR_ERR__MPU_UNBIND_FAILED", 1610 },
  { "MEDIA", "NVR_ERR__MEDIA", 1700 },
  { "DM", "NVR_ERR__DM", 1800 },
  { "DM_DISK_ID_INVALID", "NVR_ERR__DM_DISK_ID_INVALID", 1801 },
  { "DM_DISK_IN_USE", "NVR_ERR__DM_DISK_IN_USE", 1802 },
  { "DM_NET_DISK_NAME_TOO_LONG", "NVR_ERR__DM_NET_DISK_NAME_TOO_LONG", 1803 },
  { "DM_FUNCTION_NOT_SUPPORT", "NVR_ERR__DM_FUNCTION_NOT_SUPPORT", 1804 },
  { "DM_DISK_USED_BY_RP", "NVR_ERR__DM_DISK_USED_BY_RP", 1805 },
  { "DM_DISK_USED_BY_RP_PLY", "NVR_ERR__DM_DISK_USED_BY_RP_PLY", 1806 },
  { "DM_DISK_USED_BY_RP_DLD", "NVR_ERR__DM_DISK_USED_BY_RP_DLD", 1807 },
  { "DM_DISK_UMOUNT_PART_ERR", "NVR_ERR__DM_DISK_UMOUNT_PART_ERR", 1808 },
  { "DM_DISK_FORMAT_PART_ERR", "NVR_ERR__DM_DISK_FORMAT_PART_ERR", 1809 },
  { "DM_DISK_CHG_CALLBACK_FULL", "NVR_ERR__DM_DISK_CHG_CALLBACK_FULL", 1810 },
  { "DM_DISK_EXPCPTION_FORBID_OPERATION", "NVR_ERR__DM_DISK_EXPCPTION_FORBID_OPERATION", 1811 },
  { "DM_DISK_EXTERNAL_DISK_LIMIT", "NVR_ERR__DM_DISK_EXTERNAL_DISK_LIMIT", 1812 },
  { "DM_DISK_BAD_SECTOR_CHECK_NO_TASKID", "NVR_ERR__DM_DISK_BAD_SECTOR_CHECK_NO_TASKID", 1813 },
  { "DM_DISK_DISK_FS_TYPE_FAILD", "NVR_ERR__DM_DISK_DISK_FS_TYPE_FAILD", 1814 },
  { "DM_DISK_RAID_HOTBACKUP_DISK_SIZE_ERROR", "NVR_ERR__DM_DISK_RAID_HOTBACKUP_DISK_SIZE_ERROR", 1815 },
  { "DM_DISK_SET_QUTOA_SIZE_OVER_ALL_DISK_SIZE", "NVR_ERR__DM_DISK_SET_QUTOA_SIZE_OVER_ALL_DISK_SIZE", 1816 },
  { "DM_DISK_RAID_DELING", "NVR_ERR__DM_DISK_RAID_DELING", 1817 },
  { "DM_DISK_RAID_CREATING", "NVR_ERR__DM_DISK_RAID_CREATING", 1818 },
  { "DM_DISK_RAID_RAIDDISK_ONLINING", "NVR_ERR__DM_DISK_RAID_RAIDDISK_ONLINING", 1819 },
  { "DM_DISK_JUST_SUPPORT_ONE_SMART_DISK", "NVR_ERR__DM_DISK_JUST_SUPPORT_ONE_SMART_DISK", 1820 },
  { "DM_DISK_SEARCH_TASK_FULL", "NVR_ERR__DM_DISK_SEARCH_TASK_FULL", 1821 },
  { "DM_DISK_RAID_SECTOR_ERR", "NVR_ERR__DM_DISK_RAID_SECTOR_ERR", 1822 },
  { "REC", "NVR_ERR__REC", 1900 },
  { "REC_CFG_DATA_NOT_EXIST", "NVR_ERR__REC_CFG_DATA_NOT_EXIST", 1901 },
  { "REC_NO_IDLE_PLY_TASK", "NVR_ERR__REC_NO_IDLE_PLY_TASK", 1902 },
  { "REC_MSIN_STOP_FAILED", "NVR_ERR__REC_MSIN_STOP_FAILED", 1903 },
  { "REC_MSIN_RELEASE_FAILED", "NVR_ERR__REC_MSIN_RELEASE_FAILED", 1904 },
  { "REC_REPEAT_TO_ADD_CHN", "NVR_ERR__REC_REPEAT_TO_ADD_CHN", 1905 },
  { "REC_START_PLY_FAILED", "NVR_ERR__REC_START_PLY_FAILED", 1906 },
  { "REC_BAKUP_TASK_FULL", "NVR_ERR__REC_BAKUP_TASK_FULL", 1907 },
  { "REC_IMG_BAK_TASK_FULL", "NVR_ERR__REC_IMG_BAK_TASK_FULL", 1908 },
  { "REC_CHN_NOT_START", "NVR_ERR__REC_CHN_NOT_START", 1909 },
  { "REC_CMD_DEAL_THREAD_BUSY", "NVR_ERR__REC_CMD_DEAL_THREAD_BUSY", 1910 },
  { "REC_PART_BUSY", "NVR_ERR__REC_PART_BUSY", 1911 },
  { "REC_COMPONENT_LIB_ERR", "NVR_ERR__REC_COMPONENT_LIB_ERR", 1912 },
  { "REC_DISK_STATUS_SLEEP", "NVR_ERR__REC_DISK_STATUS_SLEEP", 1913 },
  { "REC_PLAYER_FULL", "NVR_ERR__REC_PLAYER_FULL", 1914 },
  { "REC_AUD_INVALID", "NVR_ERR__REC_AUD_INVALID", 1915 },
  { "REC_LOCK_PART_RECORDING", "NVR_ERR__REC_LOCK_PART_RECORDING", 1916 },
  { "CFG", "NVR_ERR__CFG", 2000 },
  { "CFG_OPEN_DATABASE_FALID", "NVR_ERR__CFG_OPEN_DATABASE_FALID", 2001 },
  { "CFG_CLOSE_DATABASE_FALID", "NVR_ERR__CFG_CLOSE_DATABASE_FALID", 2002 },
  { "CFG_CREATE_TABLE_FALID", "NVR_ERR__CFG_CREATE_TABLE_FALID", 2003 },
  { "CFG_GET_PARAM_FALID", "NVR_ERR__CFG_GET_PARAM_FALID", 2004 },
  { "CFG_SET_PARAM_FALID", "NVR_ERR__CFG_SET_PARAM_FALID", 2005 },
  { "CFG_NO_THIS_DATA", "NVR_ERR__CFG_NO_THIS_DATA", 2006 },
  { "CFG_NO_TABLE", "NVR_ERR__CFG_NO_TABLE", 2007 },
  { "CFG_INPORT_CFG_DEV_ERR", "NVR_ERR__CFG_INPORT_CFG_DEV_ERR", 2008 },
  { "CFG_INPORT_CFG_CRC_FAILD", "NVR_ERR__CFG_INPORT_CFG_CRC_FAILD", 2009 },
  { "PUI", "NVR_ERR__PUI", 2100 },
  { "PUI_CHNID_ADDED", "NVR_ERR__PUI_CHNID_ADDED", 2101 },
  { "PUI_DEV_REPEAT_ADD", "NVR_ERR__PUI_DEV_REPEAT_ADD", 2102 },
  { "PUI_DEV_ADD_FAILED", "NVR_ERR__PUI_DEV_ADD_FAILED", 2103 },
  { "PUI_CHNID_ADDED_FULL", "NVR_ERR__PUI_CHNID_ADDED_FULL", 2104 },
  { "PUI_APPCLT_ERR", "NVR_ERR__PUI_APPCLT_ERR", 2105 },
  { "PUI_DEV_DEL_FAILED", "NVR_ERR__PUI_DEV_DEL_FAILED", 2106 },
  { "PUI_OVER_MAX_USRNUM", "NVR_ERR__PUI_OVER_MAX_USRNUM", 2107 },
  { "PUI_OVER_MAX_GROUP_NUM", "NVR_ERR__PUI_OVER_MAX_GROUP_NUM", 2108 },
  { "PUI_OVER_MAX_CHN_NUM", "NVR_ERR__PUI_OVER_MAX_CHN_NUM", 2109 },
  { "PUI_LEN_NOT_ENOUGH", "NVR_ERR__PUI_LEN_NOT_ENOUGH", 2110 },
  { "PUI_OVER_MAX_ACPT_BANDWIDTH", "NVR_ERR__PUI_OVER_MAX_ACPT_BANDWIDTH", 2111 },
  { "PUI_PTZ_TASK_RUNING", "NVR_ERR__PUI_PTZ_TASK_RUNING", 2112 },
  { "PUI_VAILD_DEV_UPGRADE_TASK", "NVR_ERR__PUI_VAILD_DEV_UPGRADE_TASK", 2113 },
  { "PUI_NO_DETECT_AREA", "NVR_ERR__PUI_NO_DETECT_AREA", 2114 },
  { "PUI_DEV_FORBIDDEN", "NVR_ERR__PUI_DEV_FORBIDDEN", 2115 },
  { "PUI_AUTH_ID_ERR_FORBIDDEN", "NVR_ERR__PUI_AUTH_ID_ERR_FORBIDDEN", 2116 },
  { "PUI_DEV_NO_EXIST", "NVR_ERR__PUI_DEV_NO_EXIST", 2117 },
  { "PUI_DEV_OFFLINE", "NVR_ERR__PUI_DEV_OFFLINE", 2118 },
  { "VTDUCTRL", "NVR_ERR__VTDUCTRL", 2200 },
  { "VTDU_APPCLT_STREAM_PREPARE_FAILED", "NVR_ERR__VTDU_APPCLT_STREAM_PREPARE_FAILED", 2201 },
  { "VTDU_APPCLT_STREAM_START_FAILED", "NVR_ERR__VTDU_APPCLT_STREAM_START_FAILED", 2202 },
  { "VTDU_SND_IS_FULL", "NVR_ERR__VTDU_SND_IS_FULL", 2203 },
  { "VTDU_SEN_RATE_OVER", "NVR_ERR__VTDU_SEN_RATE_OVER", 2204 },
  { "VTDU_DEV_OFFLINE", "NVR_ERR__VTDU_DEV_OFFLINE", 2205 },
  { "VTDU_MSIN_NO_STREAM", "NVR_ERR__VTDU_MSIN_NO_STREAM", 2206 },
  { "VTDU_MSIN_CREATE_FAILED", "NVR_ERR__VTDU_MSIN_CREATE_FAILED", 2207 },
  { "VTDU_MSIN_SET_OPT_FAILED", "NVR_ERR__VTDU_MSIN_SET_OPT_FAILED", 2208 },
  { "VTDU_MSIN_SET_TRANSPARAM_FAILED", "NVR_ERR__VTDU_MSIN_SET_TRANSPARAM_FAILED", 2209 },
  { "VTDU_MSIN_INPUT_DATA_FAILED", "NVR_ERR__VTDU_MSIN_INPUT_DATA_FAILED", 2210 },
  { "VTDU_MSIN_START_FAILED", "NVR_ERR__VTDU_MSIN_START_FAILED", 2211 },
  { "VTDU_MSIN_STOP_FAILED", "NVR_ERR__VTDU_MSIN_STOP_FAILED", 2212 },
  { "VTDU_MSIN_RELEASE_FAILED", "NVR_ERR__VTDU_MSIN_RELEASE_FAILED", 2213 },
  { "VTDU_ADD_PIPELINE_FAILED", "NVR_ERR__VTDU_ADD_PIPELINE_FAILED", 2215 },
  { "VTDU_REMOVE_PIPELINE_FAILED", "NVR_ERR__VTDU_REMOVE_PIPELINE_FAILED", 2216 },
  { "VTDU_MSOUT_CREATE_FAILED", "NVR_ERR__VTDU_MSOUT_CREATE_FAILED", 2217 },
  { "VTDU_MSOUT_SET_OPT_FAILED", "NVR_ERR__VTDU_MSOUT_SET_OPT_FAILED", 2218 },
  { "VTDU_MSOUT_SET_TRANSPARAM_FAILED", "NVR_ERR__VTDU_MSOUT_SET_TRANSPARAM_FAILED", 2219 },
  { "VTDU_MSOUT_SET_DATA_CB_FAILED", "NVR_ERR__VTDU_MSOUT_SET_DATA_CB_FAILED", 2220 },
  { "VTDU_MSOUT_GET_DATA_POS_FAILED", "NVR_ERR__VTDU_MSOUT_GET_DATA_POS_FAILED", 2221 },
  { "VTDU_MSOUT_GET_DATA_FAILED", "NVR_ERR__VTDU_MSOUT_GET_DATA_FAILED", 2222 },
  { "VTDU_MSOUT_RELEASE_DATA_FAILED", "NVR_ERR__VTDU_MSOUT_RELEASE_DATA_FAILED", 2223 },
  { "VTDU_MSOUT_STRAT_FAILED", "NVR_ERR__VTDU_MSOUT_STRAT_FAILED", 2224 },
  { "VTDU_MSOUT_STOP_FAILED", "NVR_ERR__VTDU_MSOUT_STOP_FAILED", 2225 },
  { "VTDU_MSOUT_RELEASE_FAILED", "NVR_ERR__VTDU_MSOUT_RELEASE_FAILED", 2226 },
  { "VTDU_INPUT_VID_PARAM_INVALID", "NVR_ERR__VTDU_INPUT_VID_PARAM_INVALID", 2227 },
  { "VTDU_INPUT_AUD_PARAM_INVALID", "NVR_ERR__VTDU_INPUT_AUD_PARAM_INVALID", 2228 },
  { "VTDU_IS_AUDCALLING", "NVR_ERR__VTDU_IS_AUDCALLING", 2230 },
  { "VTDU_OVER_MAX_SND_BANDWIDTH", "NVR_ERR__VTDU_OVER_MAX_SND_BANDWIDTH", 2231 },
  { "VTDU_GETDATA_TOO_FAST", "NVR_ERR__VTDU_GETDATA_TOO_FAST", 2232 },
  { "VTDU_GETDATA_TOO_SLOW", "NVR_ERR__VTDU_GETDATA_TOO_SLOW", 2233 },
  { "VTDU_MBNET_OVER_FLOW", "NVR_ERR__VTDU_MBNET_OVER_FLOW", 2234 },
  { "VTDU_BROADCASTING_NO_SUPPORT_CHN", "NVR_ERR__VTDU_BROADCASTING_NO_SUPPORT_CHN", 2235 },
  { "VTDU_RTMP_PARAM_ERROR", "NVR_ERR__VTDU_RTMP_PARAM_ERROR", 2240 },
  { "VTDU_RTMP_ERROR_MAX_SESSION", "NVR_ERR__VTDU_RTMP_ERROR_MAX_SESSION", 2241 },
  { "VTDU_RTMP_ERROR_MEM", "NVR_ERR__VTDU_RTMP_ERROR_MEM", 2242 },
  { "VTDU_RTMP_ERROR_SETUP_URL", "NVR_ERR__VTDU_RTMP_ERROR_SETUP_URL", 2243 },
  { "VTDU_RTMP_ERROR_CONNECT", "NVR_ERR__VTDU_RTMP_ERROR_CONNECT", 2244 },
  { "VTDU_RTMP_ERROR_CONNECT_STREAM", "NVR_ERR__VTDU_RTMP_ERROR_CONNECT_STREAM", 2245 },
  { "VTDU_MEDIA_PUSH_TASK_FULL", "NVR_ERR__VTDU_MEDIA_PUSH_TASK_FULL", 2246 },
  { "VTDU_MEDIA_PUSH_TASK_EXIST", "NVR_ERR__VTDU_MEDIA_PUSH_TASK_EXIST", 2247 },
  { "SMTP_CONNECT_SSL_ERR", "NVR_ERR__SMTP_CONNECT_SSL_ERR", 2300 },
  { "SMTP_ERR", "NVR_ERR__SMTP_ERR", 2301 },
  { "SMTP_FILE_LEN_ERR", "NVR_ERR__SMTP_FILE_LEN_ERR", 2302 },
  { "SMTP_PARAM_INVALID", "NVR_ERR__SMTP_PARAM_INVALID", 2303 },
  { "SMTP_CONNECT_SERVER_ERR", "NVR_ERR__SMTP_CONNECT_SERVER_ERR", 2304 },
  { "SMTP_LOGIN_ERR", "NVR_ERR__SMTP_LOGIN_ERR", 2305 },
  { "SMTP_SEND_ERR", "NVR_ERR__SMTP_SEND_ERR", 2306 },
  { "SMTP_RECV_ERR", "NVR_ERR__SMTP_RECV_ERR", 2307 },
  { "SMTP_CONNECT_TIME_OUT", "NVR_ERR__SMTP_CONNECT_TIME_OUT", 2308 },
  { "SMTP_RESPONSE_ERR", "NVR_ERR__SMTP_RESPONSE_ERR", 2309 },
  { "SMTP_STARTTLS_ERR", "NVR_ERR__SMTP_STARTTLS_ERR", 2311 },
  { "SMTP_ASSERT_ERR", "NVR_ERR__SMTP_ASSERT_ERR", 2312 },
  { "SMTP_DOMAIN_ANALY_ERR", "NVR_ERR__SMTP_DOMAIN_ANALY_ERR", 2313 },
  { "SMTP_NO_TEST_TASK_RUN", "NVR_ERR__SMTP_NO_TEST_TASK_RUN", 2314 },
  { "SMTP_TEST_TASK_RUNING", "NVR_ERR__SMTP_TEST_TASK_RUNING", 2315 },
  { "SMTP_NO_FREE_TASK_ID", "NVR_ERR__SMTP_NO_FREE_TASK_ID", 2316 },
  { "SMTP_TEST_TASK_DONE", "NVR_ERR__SMTP_TEST_TASK_DONE", 2317 },
  { "UPGRADE_VER_ERR", "NVR_ERR__UPGRADE_VER_ERR", 2400 },
  { "RP_ERR", "NVR_ERR__RP_ERR", 2500 },
  { "AIU_ERR", "NVR_ERR__AIU_ERR", 2600 },
  { "AIU_QUEUE_FULL", "NVR_ERR__AIU_QUEUE_FULL", 2601 },
  { "AIU_QUEUE_EMPTY", "NVR_ERR__AIU_QUEUE_EMPTY", 2602 },
  { "AIU_UPDBUF_FULL", "NVR_ERR__AIU_UPDBUF_FULL", 2603 },
  { "LCAM_ERR", "NVR_ERR__LCAM_ERR", 2650 },
  { "LCAM_OSD_SET_PARAM_OUT_AREA", "NVR_ERR__LCAM_OSD_SET_PARAM_OUT_AREA", 2651 },
  { "LCAM_ERR_NONSUPPORT_RECROP", "NVR_ERR__LCAM_ERR_NONSUPPORT_RECROP", 2652 },
  { "AIS__ERR", "NVR_ERR__AIS__ERR", 2700 },
  { "OVER_MAX_SUPPT_CTRLLIB_NUM", "NVR_ERR__OVER_MAX_SUPPT_CTRLLIB_NUM", 2701 },
  { "CTRLLIB_OTHER_OPT_IS_DOING", "NVR_ERR__CTRLLIB_OTHER_OPT_IS_DOING", 2702 },
  { "CTRLLIB_SAME_NAME", "NVR_ERR__CTRLLIB_SAME_NAME", 2703 },
  { "AIS_OVER_MAX_SIZE", "NVR_ERR__AIS_OVER_MAX_SIZE", 2704 },
  { "CTRL_WRITE_DB_FAILED", "NVR_ERR__CTRL_WRITE_DB_FAILED", 2705 },
  { "AIS_OTHER_USR_OPT_ALG_ENGINE", "NVR_ERR__AIS_OTHER_USR_OPT_ALG_ENGINE", 2706 },
  { "AIS_CREATE_FILE_FAILED", "NVR_ERR__AIS_CREATE_FILE_FAILED", 2707 },
  { "CTRL_OPEN_DB_FAILED", "NVR_ERR__CTRL_OPEN_DB_FAILED", 2708 },
  { "CTRL_CREATE_TABLE_FAILED", "NVR_ERR__CTRL_CREATE_TABLE_FAILED", 2709 },
  { "CTRL_EXE_SQL_FAILED", "NVR_ERR__CTRL_EXE_SQL_FAILED", 2710 },
  { "CTRL_PIC_OVER_RAM_SIZE", "NVR_ERR__CTRL_PIC_OVER_RAM_SIZE", 2711 },
  { "CTRL_SYS_CMD_FAILED", "NVR_ERR__CTRL_SYS_CMD_FAILED", 2712 },
  { "CTRL_OTHER_USER_IMPORT", "NVR_ERR__CTRL_OTHER_USER_IMPORT", 2713 },
  { "CTRL_GET_FILE_FAILED", "NVR_ERR__CTRL_GET_FILE_FAILED", 2714 },
  { "CTRL_CREATE_THREAD_FAILED", "NVR_ERR__CTRL_CREATE_THREAD_FAILED", 2715 },
  { "GET_EGI_FAILED", "NVR_ERR__GET_EGI_FAILED", 2716 },
  { "CTRL_COMPARE_USED", "NVR_ERR__CTRL_COMPARE_USED", 2717 },
  { "CTRL_OVER_SUP_NUM", "NVR_ERR__CTRL_OVER_SUP_NUM", 2718 },
  { "AIRP_PIC_QUERY_RESULT_NUM_OVER", "NVR_ERR__AIRP_PIC_QUERY_RESULT_NUM_OVER", 2719 },
  { "AIRP_PIC_NO_FACE_PIC_FEATURE", "NVR_ERR__AIRP_PIC_NO_FACE_PIC_FEATURE", 2720 },
  { "CTRL_ADD_NO_EGI", "NVR_ERR__CTRL_ADD_NO_EGI", 2721 },
  { "AIS_OVER_SUP_DETECT_NUM", "NVR_ERR__AIS_OVER_SUP_DETECT_NUM", 2722 },
  { "AIS_ALG_INVALID", "NVR_ERR__AIS_ALG_INVALID", 2723 },
  { "AIS_CREATE_DETECT_HANDLE_FAILED", "NVR_ERR__AIS_CREATE_DETECT_HANDLE_FAILED", 2724 },
  { "AIS_OVER_SUP_CMP_CHN_NUM", "NVR_ERR__AIS_OVER_SUP_CMP_CHN_NUM", 2725 },
  { "AIS_RULE_NAME_EXIST", "NVR_ERR__AIS_RULE_NAME_EXIST", 2726 },
  { "AIS_CMP_CTRLLIB_EXIST", "NVR_ERR__AIS_CMP_CTRLLIB_EXIST", 2727 },
  { "AIS_OVER_SUP_RULE_NUM", "NVR_ERR__AIS_OVER_SUP_RULE_NUM", 2728 },
  { "AIS_CREATE_CMP_HANDLE_FAILED", "NVR_ERR__AIS_CREATE_CMP_HANDLE_FAILED", 2729 },
  { "CTRL_MEM_ALREADY_DEL", "NVR_ERR__CTRL_MEM_ALREADY_DEL", 2730 },
  { "AIS_ALG_ERR_NEED_REBOOT", "NVR_ERR__AIS_ALG_ERR_NEED_REBOOT", 2731 },
  { "AIS_ALG_ERR_ALG_NOT_MATCH", "NVR_ERR__AIS_ALG_ERR_ALG_NOT_MATCH", 2732 },
  { "AIS_CTRLLIB_AGI_UPDATING", "NVR_ERR__AIS_CTRLLIB_AGI_UPDATING", 2733 },
  { "AIS_CTRLLIB_ADD_LIST_BUSY", "NVR_ERR__AIS_CTRLLIB_ADD_LIST_BUSY", 2734 },
  { "SVR_EDU_ERR", "NVR_ERR__SVR_EDU_ERR", 3000 },
  { "SVR_EDU_PB_UNPACK_ERR", "NVR_ERR__SVR_EDU_PB_UNPACK_ERR", 3001 },
  { "SVR_EDU_H323_CLTAPP_UNINIT", "NVR_ERR__SVR_EDU_H323_CLTAPP_UNINIT", 3101 },
  { "SVR_EDU_H323_SERVICE_DISCONNECT", "NVR_ERR__SVR_EDU_H323_SERVICE_DISCONNECT", 3102 },
  { "SVR_EDU_H323_NO_RECORD", "NVR_ERR__SVR_EDU_H323_NO_RECORD", 3103 },
  { "SVR_EDU_H323_UNREG_GK", "NVR_ERR__SVR_EDU_H323_UNREG_GK", 3104 },
  { "SVR_EDU_H323_NO_CAP", "NVR_ERR__SVR_EDU_H323_NO_CAP", 3105 },
  { "SVR_EDU_H323_OPT_FORBIDDEN", "NVR_ERR__SVR_EDU_H323_OPT_FORBIDDEN", 3106 },
  { "SVR_EDU_PA_CLTAPP_UNINIT", "NVR_ERR__SVR_EDU_PA_CLTAPP_UNINIT", 3151 },
  { "SVR_EDU_PA_SERVICE_DISCONNECT", "NVR_ERR__SVR_EDU_PA_SERVICE_DISCONNECT", 3152 },
  { "SVR_EDU_PA_NO_RECORD", "NVR_ERR__SVR_EDU_PA_NO_RECORD", 3153 },
  { "SVR_EDU_PA_UNREG_GK", "NVR_ERR__SVR_EDU_PA_UNREG_GK", 3154 },
  { "SVR_EDU_PA_NO_CAP", "NVR_ERR__SVR_EDU_PA_NO_CAP", 3155 },
  { "SVR_EDU_PA_OPT_FORBIDDEN", "NVR_ERR__SVR_EDU_PA_OPT_FORBIDDEN", 3156 },
  { "SVR_EDU_PA_NOSUPPORT_INTERACT", "NVR_ERR__SVR_EDU_PA_NOSUPPORT_INTERACT", 3157 },
  { "SVR_EDU_DIRECT_ORDER_FORMAT_INVALID", "NVR_ERR__SVR_EDU_DIRECT_ORDER_FORMAT_INVALID", 3201 },
  { "SVR_EDU_DIRECT_ORDER_NOT_SUPPORT", "NVR_ERR__SVR_EDU_DIRECT_ORDER_NOT_SUPPORT", 3202 },
  { "SVR_EDU_DIRECT_GET_ORDER_FAILED", "NVR_ERR__SVR_EDU_DIRECT_GET_ORDER_FAILED", 3203 },
  { "SVR_EDU_DIRECT_ORDER_LIST_FULL", "NVR_ERR__SVR_EDU_DIRECT_ORDER_LIST_FULL", 3204 },
  { "SVR_EDU_DIRECT_TRACK_HOST_DISCONN", "NVR_ERR__SVR_EDU_DIRECT_TRACK_HOST_DISCONN", 3205 },
  { "SVR_EDU_DIRECT_TRACK_HOST_DISENABLE", "NVR_ERR__SVR_EDU_DIRECT_TRACK_HOST_DISENABLE", 3206 },
  { "SVR_EDU_AMT_UNINIT", "NVR_ERR__SVR_EDU_AMT_UNINIT", 3250 },
  { "SVR_EDU_AMT_SOUND_TEST", "NVR_ERR__SVR_EDU_AMT_SOUND_TEST", 3251 },
  { "SVR_EDU_MPU_OVER_PIC_NUM", "NVR_ERR__SVR_EDU_MPU_OVER_PIC_NUM", 3270 },
  { "SVR_EDU_MPU_ENC_ID_INVALID", "NVR_ERR__SVR_EDU_MPU_ENC_ID_INVALID", 3271 },
  { "SVR_EDU_MPU_ENC_CARD_NOT_EXIST", "NVR_ERR__SVR_EDU_MPU_ENC_CARD_NOT_EXIST", 3272 },
  { "SVR_EDU_MCA_WRITE_CMD_ERR", "NVR_ERR__SVR_EDU_MCA_WRITE_CMD_ERR", 3273 },
  { "SVR_EDU_MPU_WRITE_FRAME_ERR", "NVR_ERR__SVR_EDU_MPU_WRITE_FRAME_ERR", 3274 },
  { "SVR_EDU_MPU_MP4_REC_NOT_SUPPORT", "NVR_ERR__SVR_EDU_MPU_MP4_REC_NOT_SUPPORT", 3275 },
  { "SVR_EDU_MP4_DB_OPRA_STATE_ERR", "NVR_ERR__SVR_EDU_MP4_DB_OPRA_STATE_ERR", 3300 },
  { "SVR_EDU_MP4_DB_REQ_NOT_EXIST", "NVR_ERR__SVR_EDU_MP4_DB_REQ_NOT_EXIST", 3301 },
  { "SVR_EDU_MP4_DISK_SPACE_FULL", "NVR_ERR__SVR_EDU_MP4_DISK_SPACE_FULL", 3302 },
  { "SVR_EDU_MP4_DEL_ALL_REC_ING", "NVR_ERR__SVR_EDU_MP4_DEL_ALL_REC_ING", 3303 },
  { "SVR_EDU_MP4_UMOUNT_DISK", "NVR_ERR__SVR_EDU_MP4_UMOUNT_DISK", 3304 },
  { "SVR_EDU_MP4_DISK_FORMAT_ING", "NVR_ERR__SVR_EDU_MP4_DISK_FORMAT_ING", 3305 },
  { "SVR_EDU_MP4_REC_START_ING", "NVR_ERR__SVR_EDU_MP4_REC_START_ING", 3306 },
  { "SVR_EDU_MP4_PLAYING_NO_DEL_REC", "NVR_ERR__SVR_EDU_MP4_PLAYING_NO_DEL_REC", 3307 },
  { "SVR_EDU_MP4_UPLOAD_ING", "NVR_ERR__SVR_EDU_MP4_UPLOAD_ING", 3308 },
  { "SVR_EDU_CONFCTRL_CONF_ING", "NVR_ERR__SVR_EDU_CONFCTRL_CONF_ING", 3350 },
  { "SVR_EDU_CONFCTRL_SCENE_ERR", "NVR_ERR__SVR_EDU_CONFCTRL_SCENE_ERR", 3351 },
  { "SVR_EDU_CONFCTRL_NO_LISTEN", "NVR_ERR__SVR_EDU_CONFCTRL_NO_LISTEN", 3352 },
  { "SVR_WRTC_UNABLE", "NVR_ERR__SVR_WRTC_UNABLE", 3400 },
  { "SVR_WRTC_IN_CONF", "NVR_ERR__SVR_WRTC_IN_CONF", 3401 },
  { "SVR_FRP_COLLECT_NOW", "NVR_ERR__SVR_FRP_COLLECT_NOW", 3450 },
  { "SVR_FRP_RUN_SYSCMD_FAILED", "NVR_ERR__SVR_FRP_RUN_SYSCMD_FAILED", 3451 },
  { "SVR_RTMP_PUSH_STREAM_ING", "NVR_ERR__SVR_RTMP_PUSH_STREAM_ING", 3500 },
  { "ACKTIVE_EMAIL_ERROR", "NVR_ERR__ACKTIVE_EMAIL_ERROR", 4000 },
  { "ACKTIVE_PASS_TOO_SHORT", "NVR_ERR__ACKTIVE_PASS_TOO_SHORT", 4001 },
  { "ACKTIVE_PASS_TOO_LONG", "NVR_ERR__ACKTIVE_PASS_TOO_LONG", 4002 },
  { "ACKTIVE_PASS_STRENGTH_WEAK", "NVR_ERR__ACKTIVE_PASS_STRENGTH_WEAK", 4003 },
  { "NVR_CHN_GRP_ALIAS_EMPTY", "NVR_ERR__NVR_CHN_GRP_ALIAS_EMPTY", 4004 },
  { "NVR_CHN_GRP_ALIAS_TOO_LONG", "NVR_ERR__NVR_CHN_GRP_ALIAS_TOO_LONG", 4005 },
  { "NVR_CHN_GRP_ID_INVALID", "NVR_ERR__NVR_CHN_GRP_ID_INVALID", 4006 },
  { "NVR_CHN_ALIAS_EMPTY", "NVR_ERR__NVR_CHN_ALIAS_EMPTY", 4007 },
  { "NVR_CHN_ALIAS_TOO_LONG", "NVR_ERR__NVR_CHN_ALIAS_TOO_LONG", 4008 },
  { "NVR_CHN_ID_INVALID", "NVR_ERR__NVR_CHN_ID_INVALID", 4009 },
  { "LOGIN_USER_OVER_MAX_NUM", "NVR_ERR__LOGIN_USER_OVER_MAX_NUM", 4010 },
  { "LOGIN_PWD_ERROR", "NVR_ERR__LOGIN_PWD_ERROR", 4011 },
  { "SVRMPU", "NVR_ERR__SVRMPU", 4200 },
  { "SVRMPU_VID_DISDEV_ID_INVALID", "NVR_ERR__SVRMPU_VID_DISDEV_ID_INVALID", 4201 },
  { "SVRMPU_VID_SET_DISDEV_PARAM_ERR", "NVR_ERR__SVRMPU_VID_SET_DISDEV_PARAM_ERR", 4202 },
  { "SVRMPU_VID_GET_DISDEV_PARAM_ERR", "NVR_ERR__SVRMPU_VID_GET_DISDEV_PARAM_ERR", 4203 },
  { "SVRMPU_VID_LAYOUT_ID_INVALID", "NVR_ERR__SVRMPU_VID_LAYOUT_ID_INVALID", 4204 },
  { "SVRMPU_VID_SET_LAYOUT_PARAM_ERR", "NVR_ERR__SVRMPU_VID_SET_LAYOUT_PARAM_ERR", 4205 },
  { "SVRMPU_VID_GET_LAYOUT_PARAM_ERR", "NVR_ERR__SVRMPU_VID_GET_LAYOUT_PARAM_ERR", 4206 },
  { "SVRMPU_VID_ENC_ID_INVALID", "NVR_ERR__SVRMPU_VID_ENC_ID_INVALID", 4207 },
  { "SVRMPU_VID_SET_ENC_PARAM_ERR", "NVR_ERR__SVRMPU_VID_SET_ENC_PARAM_ERR", 4208 },
  { "SVRMPU_VID_GET_ENC_PARAM_ERR", "NVR_ERR__SVRMPU_VID_GET_ENC_PARAM_ERR", 4209 },
  { "SVRMPU_VID_DEC_ID_INVALID", "NVR_ERR__SVRMPU_VID_DEC_ID_INVALID", 4210 },
  { "SVRMPU_VID_SET_DEC_PARAM_ERR", "NVR_ERR__SVRMPU_VID_SET_DEC_PARAM_ERR", 4211 },
  { "SVRMPU_VID_GET_DEC_PARAM_ERR", "NVR_ERR__SVRMPU_VID_GET_DEC_PARAM_ERR", 4212 },
  { "SVRMPU_VID_DEC_ERR", "NVR_ERR__SVRMPU_VID_DEC_ERR", 4213 },
  { "SVRMPU_VID_OSD_ID_INVALID", "NVR_ERR__SVRMPU_VID_OSD_ID_INVALID", 4214 },
  { "SVRMPU_VID_ADD_OSD_ERR", "NVR_ERR__SVRMPU_VID_ADD_OSD_ERR", 4215 },
  { "SVRMPU_VID_SET_OSD_PARAM_ERR", "NVR_ERR__SVRMPU_VID_SET_OSD_PARAM_ERR", 4216 },
  { "SVRMPU_VID_SET_OSD_PARAM_OUT_AREA", "NVR_ERR__SVRMPU_VID_SET_OSD_PARAM_OUT_AREA", 4217 },
  { "SVRSFCHN", "NVR_ERR__SVRSFCHN", 4300 },
  { "SVRSF_CHN_REM_NAME_INVALID", "NVR_ERR__SVRSF_CHN_REM_NAME_INVALID", 4301 },
  { "SVRSF_CHN_REM_URL_INVALID", "NVR_ERR__SVRSF_CHN_REM_URL_INVALID", 4302 },
  { "SVRSF_CHN_REM_NAME_EXIST", "NVR_ERR__SVRSF_CHN_REM_NAME_EXIST", 4303 },
  { "SVRSF_CHN_REM_DEV_LIST_LIMIT", "NVR_ERR__SVRSF_CHN_REM_DEV_LIST_LIMIT", 4304 },
  { "SVRSF_CHN_REM_DEV_NOT_FOUND", "NVR_ERR__SVRSF_CHN_REM_DEV_NOT_FOUND", 4305 },
  { "SVRSF_CHN_REM_DEV_START", "NVR_ERR__SVRSF_CHN_REM_DEV_START", 4306 },
  { "SVRSF_CHN_REM_DEV_CHN_LIMIT", "NVR_ERR__SVRSF_CHN_REM_DEV_CHN_LIMIT", 4307 },
  { "SVRSF_CHN_REM_DEV_NOT_START", "NVR_ERR__SVRSF_CHN_REM_DEV_NOT_START", 4308 },
  { "SVRSF_CHN_REM_INDEX_INVALID", "NVR_ERR__SVRSF_CHN_REM_INDEX_INVALID", 4309 },
  { "SVRSF_CHN_REM_DEV_NOT_H323", "NVR_ERR__SVRSF_CHN_REM_DEV_NOT_H323", 4310 },
  { "SVRSF_CHN_REM_DEV_H239_START_AGAIN", "NVR_ERR__SVRSF_CHN_REM_DEV_H239_START_AGAIN", 4311 },
  { "SVRSF_CHN_REM_DEV_PEERH239_START", "NVR_ERR__SVRSF_CHN_REM_DEV_PEERH239_START", 4312 },
  { "SVRSF_CHN_REM_DEV_H239_NOT_START", "NVR_ERR__SVRSF_CHN_REM_DEV_H239_NOT_START", 4313 },
  { "SVRSF_CHN_REM_DEV_PEERH239_START_AGAIN", "NVR_ERR__SVRSF_CHN_REM_DEV_PEERH239_START_AGAIN", 4314 },
  { "SVRSF_CHN_REM_DEV_H239_START", "NVR_ERR__SVRSF_CHN_REM_DEV_H239_START", 4315 },
  { "SVRSF_CHN_REM_DEV_PEERH239_NOT_START", "NVR_ERR__SVRSF_CHN_REM_DEV_PEERH239_NOT_START", 4316 },
  { "SVRSF_CHN_REM_DEV_H323_CTRL_ERR", "NVR_ERR__SVRSF_CHN_REM_DEV_H323_CTRL_ERR", 4317 },
  { "SVRSF_CHN_REM_DEV_CHN_LIVING", "NVR_ERR__SVRSF_CHN_REM_DEV_CHN_LIVING", 4318 },
  { "SVRSF_CHN_REM_DEV_IS_STARTING", "NVR_ERR__SVRSF_CHN_REM_DEV_IS_STARTING", 4319 },
  { "SVRSF_CHN_REM_DEV_SIP_START_LIMIT", "NVR_ERR__SVRSF_CHN_REM_DEV_SIP_START_LIMIT", 4320 },
  { "SVRSFSERVER", "NVR_ERR__SVRSFSERVER", 4400 },
  { "SVRSF_SERVER_CMD_NOT_SUPPORT", "NVR_ERR__SVRSF_SERVER_CMD_NOT_SUPPORT", 4401 },
  { "SVRSF_SERVER_USER_NOT_ALLOW", "NVR_ERR__SVRSF_SERVER_USER_NOT_ALLOW", 4402 },
  { "SVRSF_SERVER_DVD_NOT_OK", "NVR_ERR__SVRSF_SERVER_DVD_NOT_OK", 4403 },
  { "SVRSF_SERVER_DISK_NOT_MOUNT", "NVR_ERR__SVRSF_SERVER_DISK_NOT_MOUNT", 4404 },
  { "SVRSF_SERVER_DISK_IS_FULL", "NVR_ERR__SVRSF_SERVER_DISK_IS_FULL", 4405 },
  { "SVRSF_SERVER_GUI_IS_PLAY", "NVR_ERR__SVRSF_SERVER_GUI_IS_PLAY", 4406 },
  { "SVRSF_SERVER_USB_IS_COPY", "NVR_ERR__SVRSF_SERVER_USB_IS_COPY", 4407 },
  { "SVRSF_SERVER_USB_NOT_COPY", "NVR_ERR__SVRSF_SERVER_USB_NOT_COPY", 4408 },
  { "SVRSF_SERVER_USB_IS_REC", "NVR_ERR__SVRSF_SERVER_USB_IS_REC", 4409 },
  { "SVRSF_SERVER_REC_IS_CLEAR", "NVR_ERR__SVRSF_SERVER_REC_IS_CLEAR", 4410 },
  { "SVRSF_SERVER_DB_ERROR", "NVR_ERR__SVRSF_SERVER_DB_ERROR", 4411 },
  { "SVRSF_SERVER_IS_BURN", "NVR_ERR__SVRSF_SERVER_IS_BURN", 4412 },
  { "SVRSF_SERVER_NOT_BURN", "NVR_ERR__SVRSF_SERVER_NOT_BURN", 4413 },
  { "SVRSF_SERVER_USB_NOT_RIGHT", "NVR_ERR__SVRSF_SERVER_USB_NOT_RIGHT", 4414 },
  { "SVRSF_SERVER_USB_NOT_MOUNT", "NVR_ERR__SVRSF_SERVER_USB_NOT_MOUNT", 4415 },
  { "SVRSF_SERVER_USB_HAS_UMOUNT", "NVR_ERR__SVRSF_SERVER_USB_HAS_UMOUNT", 4416 },
  { "SVRSF_SERVER_USB_SPACE_LIMIT", "NVR_ERR__SVRSF_SERVER_USB_SPACE_LIMIT", 4417 },
  { "SVRSF_SERVER_OPER_NOT_1_MIN", "NVR_ERR__SVRSF_SERVER_OPER_NOT_1_MIN", 4418 },
  { "SVRSF_SERVER_OPER_NOT_20_SEC", "NVR_ERR__SVRSF_SERVER_OPER_NOT_20_SEC", 4419 },
  { "SVRSF_SERVER_BURNTASK_ID_ERROR", "NVR_ERR__SVRSF_SERVER_BURNTASK_ID_ERROR", 4420 },
  { "SVRSF_SERVER_IS_APPEND_BURN", "NVR_ERR__SVRSF_SERVER_IS_APPEND_BURN", 4421 },
  { "SVRSF_SERVER_IS_PAUSE_BURN", "NVR_ERR__SVRSF_SERVER_IS_PAUSE_BURN", 4422 },
  { "SVRSF_SERVER_IS_RESUME_BURN", "NVR_ERR__SVRSF_SERVER_IS_RESUME_BURN", 4423 },
  { "SVRSF_SERVER_IS_STOP_BURN", "NVR_ERR__SVRSF_SERVER_IS_STOP_BURN", 4424 },
  { "SVRSF_SERVER_IS_ABORT_BURN", "NVR_ERR__SVRSF_SERVER_IS_ABORT_BURN", 4425 },
  { "SVRSF_SERVER_FILE_NOT_EXIST", "NVR_ERR__SVRSF_SERVER_FILE_NOT_EXIST", 4426 },
  { "SVRSF_SERVER_BURNTASK_NOT_EXIST", "NVR_ERR__SVRSF_SERVER_BURNTASK_NOT_EXIST", 4427 },
  { "SVRSF_SERVER_BURN_UDF_INIT_FAIL", "NVR_ERR__SVRSF_SERVER_BURN_UDF_INIT_FAIL", 4428 },
  { "SVRSF_SERVER_BURN_UDF_FORMAT_FAIL", "NVR_ERR__SVRSF_SERVER_BURN_UDF_FORMAT_FAIL", 4429 },
  { "SVRSF_SERVER_BURN_UDF_CREATE_FOLDER_FAIL", "NVR_ERR__SVRSF_SERVER_BURN_UDF_CREATE_FOLDER_FAIL", 4430 },
  { "SVRSF_SERVER_BURN_OPEN_FILE_FAIL", "NVR_ERR__SVRSF_SERVER_BURN_OPEN_FILE_FAIL", 4431 },
  { "SVRSF_SERVER_BURN_ABORT_FAIL", "NVR_ERR__SVRSF_SERVER_BURN_ABORT_FAIL", 4432 },
  { "SVRSF_SERVER_BURN_UDF_COMPLETE_FAIL", "NVR_ERR__SVRSF_SERVER_BURN_UDF_COMPLETE_FAIL", 4433 },
  { "SVRSF_SERVER_BURN_DISC_NOT_INIT", "NVR_ERR__SVRSF_SERVER_BURN_DISC_NOT_INIT", 4434 },
  { "SVRSF_SERVER_BURN_UDF_ERROR", "NVR_ERR__SVRSF_SERVER_BURN_UDF_ERROR", 4435 },
  { "SVRSF_SERVER_FORMAT_FAIL", "NVR_ERR__SVRSF_SERVER_FORMAT_FAIL", 4436 },
  { "SVRSF_SERVER_CREATE_NEW_TASK_NO_FILE", "NVR_ERR__SVRSF_SERVER_CREATE_NEW_TASK_NO_FILE", 4437 },
  { "SVRSF_SERVER_IS_RECING", "NVR_ERR__SVRSF_SERVER_IS_RECING", 4438 },
  { "SVRSF_SERVER_USB_STATE", "NVR_ERR__SVRSF_SERVER_USB_STATE", 4439 },
  { "SVRSF_SERVER_USB_UMOUNT_FAIL", "NVR_ERR__SVRSF_SERVER_USB_UMOUNT_FAIL", 4440 },
  { "SVRSF_SERVER_DVD_DVD1_NOT_WRITEABLE", "NVR_ERR__SVRSF_SERVER_DVD_DVD1_NOT_WRITEABLE", 4441 },
  { "SVRSF_SERVER_DVD_DVD2_NOT_WRITEABLE", "NVR_ERR__SVRSF_SERVER_DVD_DVD2_NOT_WRITEABLE", 4442 },
  { "SVRSF_SERVER_DVD_BURN_MODE_NOT_SUPPORT", "NVR_ERR__SVRSF_SERVER_DVD_BURN_MODE_NOT_SUPPORT", 4443 },
  { "SVRSF_SERVER_ORDER_DEFAULT_NAME_NOT_CHANGE", "NVR_ERR__SVRSF_SERVER_ORDER_DEFAULT_NAME_NOT_CHANGE", 4444 },
  { "SVRSF_SERVER_ORDER_TEMPLASTE_IS_EXIST", "NVR_ERR__SVRSF_SERVER_ORDER_TEMPLASTE_IS_EXIST", 4445 },
  { "SVRSF_SERVER_ORDER_TEMPLASTE_CTRL_FAIL", "NVR_ERR__SVRSF_SERVER_ORDER_TEMPLASTE_CTRL_FAIL", 4446 },
  { "SVRSF_SERVER_COURT_IS_OPEN", "NVR_ERR__SVRSF_SERVER_COURT_IS_OPEN", 4447 },
  { "SVRSF_SERVER_CREATE_NEW_TASK_TIME_OVER", "NVR_ERR__SVRSF_SERVER_CREATE_NEW_TASK_TIME_OVER", 4448 },
  { "SVRSF_SERVER_DVD_DVD1_WORKING", "NVR_ERR__SVRSF_SERVER_DVD_DVD1_WORKING", 4449 },
  { "SVRSF_SERVER_DVD_DVD2_WORKING", "NVR_ERR__SVRSF_SERVER_DVD_DVD2_WORKING", 4450 },
  { "SVRSF_SERVER_ORDER_TEMPLASTE_IMPORT_MISMATCH", "NVR_ERR__SVRSF_SERVER_ORDER_TEMPLASTE_IMPORT_MISMATCH", 4451 },
  { "SVRSF_SERVER_CHN_NOT_ONLINE", "NVR_ERR__SVRSF_SERVER_CHN_NOT_ONLINE", 4452 },
  { "DECSRV", "NVR_ERR__DECSRV", 4500 },
  { "DECSRV_EXCEPTION_EXCHANGE", "NVR_ERR__DECSRV_EXCEPTION_EXCHANGE", 4501 },
  { "DECSRV_NO_FULL_FRAME_RECV", "NVR_ERR__DECSRV_NO_FULL_FRAME_RECV", 4502 },
  { "DECSRV_NO_STREAM_RECV", "NVR_ERR__DECSRV_NO_STREAM_RECV", 4503 },
  { "DECSRV_GET_RTSP_URL_FAILED", "NVR_ERR__DECSRV_GET_RTSP_URL_FAILED", 4504 },
  { "EXTDEV", "NVR_ERR__EXTDEV", 4550 },
  { "EXTDEV_LOGIN_FAIL", "NVR_ERR__EXTDEV_LOGIN_FAIL", 4551 },
};
static const ProtobufCIntRange nvr_err__value_ranges[] = {
{1000, 0},{1020, 11},{1100, 19},{1200, 20},{1300, 41},{1400, 44},{1500, 45},{1600, 59},{1700, 70},{1800, 71},{1900, 94},{2000, 111},{2100, 121},{2200, 140},{2215, 154},{2230, 168},{2240, 174},{2300, 182},{2311, 192},{2400, 199},{2500, 200},{2600, 201},{2650, 205},{2700, 208},{3000, 243},{3101, 245},{3151, 251},{3201, 258},{3250, 264},{3270, 266},{3300, 272},{3350, 281},{3400, 284},{3450, 286},{3500, 288},{4000, 289},{4200, 301},{4300, 319},{4400, 340},{4500, 393},{4550, 398},{0, 400}
};
static const ProtobufCEnumValueIndex nvr_err__enum_values_by_name[400] =
{
  { "ACKTIVE_EMAIL_ERROR", 289 },
  { "ACKTIVE_PASS_STRENGTH_WEAK", 292 },
  { "ACKTIVE_PASS_TOO_LONG", 291 },
  { "ACKTIVE_PASS_TOO_SHORT", 290 },
  { "AIRP_PIC_NO_FACE_PIC_FEATURE", 228 },
  { "AIRP_PIC_QUERY_RESULT_NUM_OVER", 227 },
  { "AIS_ALG_ERR_ALG_NOT_MATCH", 240 },
  { "AIS_ALG_ERR_NEED_REBOOT", 239 },
  { "AIS_ALG_INVALID", 231 },
  { "AIS_CMP_CTRLLIB_EXIST", 235 },
  { "AIS_CREATE_CMP_HANDLE_FAILED", 237 },
  { "AIS_CREATE_DETECT_HANDLE_FAILED", 232 },
  { "AIS_CREATE_FILE_FAILED", 215 },
  { "AIS_CTRLLIB_ADD_LIST_BUSY", 242 },
  { "AIS_CTRLLIB_AGI_UPDATING", 241 },
  { "AIS_OTHER_USR_OPT_ALG_ENGINE", 214 },
  { "AIS_OVER_MAX_SIZE", 212 },
  { "AIS_OVER_SUP_CMP_CHN_NUM", 233 },
  { "AIS_OVER_SUP_DETECT_NUM", 230 },
  { "AIS_OVER_SUP_RULE_NUM", 236 },
  { "AIS_RULE_NAME_EXIST", 234 },
  { "AIS__ERR", 208 },
  { "AIU_ERR", 201 },
  { "AIU_QUEUE_EMPTY", 203 },
  { "AIU_QUEUE_FULL", 202 },
  { "AIU_UPDBUF_FULL", 204 },
  { "ALREADY_REGISTERED", 15 },
  { "ASSERT", 2 },
  { "CAP_NO_SUPPORT", 19 },
  { "CFG", 111 },
  { "CFG_CLOSE_DATABASE_FALID", 113 },
  { "CFG_CREATE_TABLE_FALID", 114 },
  { "CFG_GET_PARAM_FALID", 115 },
  { "CFG_INPORT_CFG_CRC_FAILD", 120 },
  { "CFG_INPORT_CFG_DEV_ERR", 119 },
  { "CFG_NO_TABLE", 118 },
  { "CFG_NO_THIS_DATA", 117 },
  { "CFG_OPEN_DATABASE_FALID", 112 },
  { "CFG_SET_PARAM_FALID", 116 },
  { "CTRLLIB_OTHER_OPT_IS_DOING", 210 },
  { "CTRLLIB_SAME_NAME", 211 },
  { "CTRL_ADD_NO_EGI", 229 },
  { "CTRL_COMPARE_USED", 225 },
  { "CTRL_CREATE_TABLE_FAILED", 217 },
  { "CTRL_CREATE_THREAD_FAILED", 223 },
  { "CTRL_EXE_SQL_FAILED", 218 },
  { "CTRL_GET_FILE_FAILED", 222 },
  { "CTRL_MEM_ALREADY_DEL", 238 },
  { "CTRL_OPEN_DB_FAILED", 216 },
  { "CTRL_OTHER_USER_IMPORT", 221 },
  { "CTRL_OVER_SUP_NUM", 226 },
  { "CTRL_PIC_OVER_RAM_SIZE", 219 },
  { "CTRL_SYS_CMD_FAILED", 220 },
  { "CTRL_WRITE_DB_FAILED", 213 },
  { "DECSRV", 393 },
  { "DECSRV_EXCEPTION_EXCHANGE", 394 },
  { "DECSRV_GET_RTSP_URL_FAILED", 397 },
  { "DECSRV_NO_FULL_FRAME_RECV", 395 },
  { "DECSRV_NO_STREAM_RECV", 396 },
  { "DEV", 44 },
  { "DM", 71 },
  { "DM_DISK_BAD_SECTOR_CHECK_NO_TASKID", 84 },
  { "DM_DISK_CHG_CALLBACK_FULL", 81 },
  { "DM_DISK_DISK_FS_TYPE_FAILD", 85 },
  { "DM_DISK_EXPCPTION_FORBID_OPERATION", 82 },
  { "DM_DISK_EXTERNAL_DISK_LIMIT", 83 },
  { "DM_DISK_FORMAT_PART_ERR", 80 },
  { "DM_DISK_ID_INVALID", 72 },
  { "DM_DISK_IN_USE", 73 },
  { "DM_DISK_JUST_SUPPORT_ONE_SMART_DISK", 91 },
  { "DM_DISK_RAID_CREATING", 89 },
  { "DM_DISK_RAID_DELING", 88 },
  { "DM_DISK_RAID_HOTBACKUP_DISK_SIZE_ERROR", 86 },
  { "DM_DISK_RAID_RAIDDISK_ONLINING", 90 },
  { "DM_DISK_RAID_SECTOR_ERR", 93 },
  { "DM_DISK_SEARCH_TASK_FULL", 92 },
  { "DM_DISK_SET_QUTOA_SIZE_OVER_ALL_DISK_SIZE", 87 },
  { "DM_DISK_UMOUNT_PART_ERR", 79 },
  { "DM_DISK_USED_BY_RP", 76 },
  { "DM_DISK_USED_BY_RP_DLD", 78 },
  { "DM_DISK_USED_BY_RP_PLY", 77 },
  { "DM_FUNCTION_NOT_SUPPORT", 75 },
  { "DM_NET_DISK_NAME_TOO_LONG", 74 },
  { "ERROR", 1 },
  { "EXPORTING_CFG", 13 },
  { "EXTDEV", 398 },
  { "EXTDEV_LOGIN_FAIL", 399 },
  { "FILE_INEXIST", 11 },
  { "GET_EGI_FAILED", 224 },
  { "IO_ERROR", 17 },
  { "LCAM_ERR", 205 },
  { "LCAM_ERR_NONSUPPORT_RECROP", 207 },
  { "LCAM_OSD_SET_PARAM_OUT_AREA", 206 },
  { "LOG", 41 },
  { "LOGIN_PWD_ERROR", 300 },
  { "LOGIN_USER_OVER_MAX_NUM", 299 },
  { "MALLOC_FAILED", 7 },
  { "MEDIA", 70 },
  { "MPU", 59 },
  { "MPU_BIND_FAILED", 68 },
  { "MPU_CHN_ID_IS_DECODING", 62 },
  { "MPU_DEC_ABILITY_OVER_MAX", 61 },
  { "MPU_DEC_CHN_NUM_OVER_MAX", 60 },
  { "MPU_MC_GET_OPT_FAILED", 66 },
  { "MPU_MC_SET_DEC_PARAM_FAILED", 64 },
  { "MPU_MC_SET_LAYOUT_FAILED", 63 },
  { "MPU_MC_SET_OPT_FAILED", 65 },
  { "MPU_OVER_MC_DEV_ZOOM_CAP", 67 },
  { "MPU_UNBIND_FAILED", 69 },
  { "NET", 45 },
  { "NET_ALREADY_REGISTERED", 47 },
  { "NET_AP_DHCPSERVER_MUTEX", 58 },
  { "NET_DOMAIN_ANALY_FAILD", 50 },
  { "NET_INVALID_HTTPS_CRT", 56 },
  { "NET_IP_GW_NOTIN_SAME_NET", 53 },
  { "NET_NO_REGISTERED", 48 },
  { "NET_OPERATE_TOO_FREQUENCY", 54 },
  { "NET_OTHER_IP_IN_SAME_NET", 52 },
  { "NET_PING_CHN_NO_IP", 55 },
  { "NET_PING_NUM_MAX", 49 },
  { "NET_PORT_IS_USING", 51 },
  { "NET_REGISTER_FULL", 46 },
  { "NET_SOME_STATIC_ROUTES_INVAILD", 57 },
  { "NO_REGISTERED", 16 },
  { "NVR_CHN_ALIAS_EMPTY", 296 },
  { "NVR_CHN_ALIAS_TOO_LONG", 297 },
  { "NVR_CHN_GRP_ALIAS_EMPTY", 293 },
  { "NVR_CHN_GRP_ALIAS_TOO_LONG", 294 },
  { "NVR_CHN_GRP_ID_INVALID", 295 },
  { "NVR_CHN_ID_INVALID", 298 },
  { "OK", 0 },
  { "OVER_MAX_SUPPT_CTRLLIB_NUM", 209 },
  { "PARAM_INVALID", 5 },
  { "PARAM_NONEXISTENCE", 18 },
  { "PUI", 121 },
  { "PUI_APPCLT_ERR", 126 },
  { "PUI_AUTH_ID_ERR_FORBIDDEN", 137 },
  { "PUI_CHNID_ADDED", 122 },
  { "PUI_CHNID_ADDED_FULL", 125 },
  { "PUI_DEV_ADD_FAILED", 124 },
  { "PUI_DEV_DEL_FAILED", 127 },
  { "PUI_DEV_FORBIDDEN", 136 },
  { "PUI_DEV_NO_EXIST", 138 },
  { "PUI_DEV_OFFLINE", 139 },
  { "PUI_DEV_REPEAT_ADD", 123 },
  { "PUI_LEN_NOT_ENOUGH", 131 },
  { "PUI_NO_DETECT_AREA", 135 },
  { "PUI_OVER_MAX_ACPT_BANDWIDTH", 132 },
  { "PUI_OVER_MAX_CHN_NUM", 130 },
  { "PUI_OVER_MAX_GROUP_NUM", 129 },
  { "PUI_OVER_MAX_USRNUM", 128 },
  { "PUI_PTZ_TASK_RUNING", 133 },
  { "PUI_VAILD_DEV_UPGRADE_TASK", 134 },
  { "REC", 94 },
  { "REC_AUD_INVALID", 109 },
  { "REC_BAKUP_TASK_FULL", 101 },
  { "REC_CFG_DATA_NOT_EXIST", 95 },
  { "REC_CHN_NOT_START", 103 },
  { "REC_CMD_DEAL_THREAD_BUSY", 104 },
  { "REC_COMPONENT_LIB_ERR", 106 },
  { "REC_DISK_STATUS_SLEEP", 107 },
  { "REC_IMG_BAK_TASK_FULL", 102 },
  { "REC_LOCK_PART_RECORDING", 110 },
  { "REC_MSIN_RELEASE_FAILED", 98 },
  { "REC_MSIN_STOP_FAILED", 97 },
  { "REC_NO_IDLE_PLY_TASK", 96 },
  { "REC_PART_BUSY", 105 },
  { "REC_PLAYER_FULL", 108 },
  { "REC_REPEAT_TO_ADD_CHN", 99 },
  { "REC_START_PLY_FAILED", 100 },
  { "REGISTER_FULL", 14 },
  { "RP_ERR", 200 },
  { "SEM_GIVEE_FAILED", 4 },
  { "SEM_TAKE_FAILED", 3 },
  { "SMTP_ASSERT_ERR", 193 },
  { "SMTP_CONNECT_SERVER_ERR", 186 },
  { "SMTP_CONNECT_SSL_ERR", 182 },
  { "SMTP_CONNECT_TIME_OUT", 190 },
  { "SMTP_DOMAIN_ANALY_ERR", 194 },
  { "SMTP_ERR", 183 },
  { "SMTP_FILE_LEN_ERR", 184 },
  { "SMTP_LOGIN_ERR", 187 },
  { "SMTP_NO_FREE_TASK_ID", 197 },
  { "SMTP_NO_TEST_TASK_RUN", 195 },
  { "SMTP_PARAM_INVALID", 185 },
  { "SMTP_RECV_ERR", 189 },
  { "SMTP_RESPONSE_ERR", 191 },
  { "SMTP_SEND_ERR", 188 },
  { "SMTP_STARTTLS_ERR", 192 },
  { "SMTP_TEST_TASK_DONE", 198 },
  { "SMTP_TEST_TASK_RUNING", 196 },
  { "STRING_ILLEGAL", 8 },
  { "STRING_TOO_LONG", 10 },
  { "STRING_TOO_SHORT", 9 },
  { "SVRMPU", 301 },
  { "SVRMPU_VID_ADD_OSD_ERR", 316 },
  { "SVRMPU_VID_DEC_ERR", 314 },
  { "SVRMPU_VID_DEC_ID_INVALID", 311 },
  { "SVRMPU_VID_DISDEV_ID_INVALID", 302 },
  { "SVRMPU_VID_ENC_ID_INVALID", 308 },
  { "SVRMPU_VID_GET_DEC_PARAM_ERR", 313 },
  { "SVRMPU_VID_GET_DISDEV_PARAM_ERR", 304 },
  { "SVRMPU_VID_GET_ENC_PARAM_ERR", 310 },
  { "SVRMPU_VID_GET_LAYOUT_PARAM_ERR", 307 },
  { "SVRMPU_VID_LAYOUT_ID_INVALID", 305 },
  { "SVRMPU_VID_OSD_ID_INVALID", 315 },
  { "SVRMPU_VID_SET_DEC_PARAM_ERR", 312 },
  { "SVRMPU_VID_SET_DISDEV_PARAM_ERR", 303 },
  { "SVRMPU_VID_SET_ENC_PARAM_ERR", 309 },
  { "SVRMPU_VID_SET_LAYOUT_PARAM_ERR", 306 },
  { "SVRMPU_VID_SET_OSD_PARAM_ERR", 317 },
  { "SVRMPU_VID_SET_OSD_PARAM_OUT_AREA", 318 },
  { "SVRSFCHN", 319 },
  { "SVRSFSERVER", 340 },
  { "SVRSF_CHN_REM_DEV_CHN_LIMIT", 326 },
  { "SVRSF_CHN_REM_DEV_CHN_LIVING", 337 },
  { "SVRSF_CHN_REM_DEV_H239_NOT_START", 332 },
  { "SVRSF_CHN_REM_DEV_H239_START", 334 },
  { "SVRSF_CHN_REM_DEV_H239_START_AGAIN", 330 },
  { "SVRSF_CHN_REM_DEV_H323_CTRL_ERR", 336 },
  { "SVRSF_CHN_REM_DEV_IS_STARTING", 338 },
  { "SVRSF_CHN_REM_DEV_LIST_LIMIT", 323 },
  { "SVRSF_CHN_REM_DEV_NOT_FOUND", 324 },
  { "SVRSF_CHN_REM_DEV_NOT_H323", 329 },
  { "SVRSF_CHN_REM_DEV_NOT_START", 327 },
  { "SVRSF_CHN_REM_DEV_PEERH239_NOT_START", 335 },
  { "SVRSF_CHN_REM_DEV_PEERH239_START", 331 },
  { "SVRSF_CHN_REM_DEV_PEERH239_START_AGAIN", 333 },
  { "SVRSF_CHN_REM_DEV_SIP_START_LIMIT", 339 },
  { "SVRSF_CHN_REM_DEV_START", 325 },
  { "SVRSF_CHN_REM_INDEX_INVALID", 328 },
  { "SVRSF_CHN_REM_NAME_EXIST", 322 },
  { "SVRSF_CHN_REM_NAME_INVALID", 320 },
  { "SVRSF_CHN_REM_URL_INVALID", 321 },
  { "SVRSF_SERVER_BURNTASK_ID_ERROR", 360 },
  { "SVRSF_SERVER_BURNTASK_NOT_EXIST", 367 },
  { "SVRSF_SERVER_BURN_ABORT_FAIL", 372 },
  { "SVRSF_SERVER_BURN_DISC_NOT_INIT", 374 },
  { "SVRSF_SERVER_BURN_OPEN_FILE_FAIL", 371 },
  { "SVRSF_SERVER_BURN_UDF_COMPLETE_FAIL", 373 },
  { "SVRSF_SERVER_BURN_UDF_CREATE_FOLDER_FAIL", 370 },
  { "SVRSF_SERVER_BURN_UDF_ERROR", 375 },
  { "SVRSF_SERVER_BURN_UDF_FORMAT_FAIL", 369 },
  { "SVRSF_SERVER_BURN_UDF_INIT_FAIL", 368 },
  { "SVRSF_SERVER_CHN_NOT_ONLINE", 392 },
  { "SVRSF_SERVER_CMD_NOT_SUPPORT", 341 },
  { "SVRSF_SERVER_COURT_IS_OPEN", 387 },
  { "SVRSF_SERVER_CREATE_NEW_TASK_NO_FILE", 377 },
  { "SVRSF_SERVER_CREATE_NEW_TASK_TIME_OVER", 388 },
  { "SVRSF_SERVER_DB_ERROR", 351 },
  { "SVRSF_SERVER_DISK_IS_FULL", 345 },
  { "SVRSF_SERVER_DISK_NOT_MOUNT", 344 },
  { "SVRSF_SERVER_DVD_BURN_MODE_NOT_SUPPORT", 383 },
  { "SVRSF_SERVER_DVD_DVD1_NOT_WRITEABLE", 381 },
  { "SVRSF_SERVER_DVD_DVD1_WORKING", 389 },
  { "SVRSF_SERVER_DVD_DVD2_NOT_WRITEABLE", 382 },
  { "SVRSF_SERVER_DVD_DVD2_WORKING", 390 },
  { "SVRSF_SERVER_DVD_NOT_OK", 343 },
  { "SVRSF_SERVER_FILE_NOT_EXIST", 366 },
  { "SVRSF_SERVER_FORMAT_FAIL", 376 },
  { "SVRSF_SERVER_GUI_IS_PLAY", 346 },
  { "SVRSF_SERVER_IS_ABORT_BURN", 365 },
  { "SVRSF_SERVER_IS_APPEND_BURN", 361 },
  { "SVRSF_SERVER_IS_BURN", 352 },
  { "SVRSF_SERVER_IS_PAUSE_BURN", 362 },
  { "SVRSF_SERVER_IS_RECING", 378 },
  { "SVRSF_SERVER_IS_RESUME_BURN", 363 },
  { "SVRSF_SERVER_IS_STOP_BURN", 364 },
  { "SVRSF_SERVER_NOT_BURN", 353 },
  { "SVRSF_SERVER_OPER_NOT_1_MIN", 358 },
  { "SVRSF_SERVER_OPER_NOT_20_SEC", 359 },
  { "SVRSF_SERVER_ORDER_DEFAULT_NAME_NOT_CHANGE", 384 },
  { "SVRSF_SERVER_ORDER_TEMPLASTE_CTRL_FAIL", 386 },
  { "SVRSF_SERVER_ORDER_TEMPLASTE_IMPORT_MISMATCH", 391 },
  { "SVRSF_SERVER_ORDER_TEMPLASTE_IS_EXIST", 385 },
  { "SVRSF_SERVER_REC_IS_CLEAR", 350 },
  { "SVRSF_SERVER_USB_HAS_UMOUNT", 356 },
  { "SVRSF_SERVER_USB_IS_COPY", 347 },
  { "SVRSF_SERVER_USB_IS_REC", 349 },
  { "SVRSF_SERVER_USB_NOT_COPY", 348 },
  { "SVRSF_SERVER_USB_NOT_MOUNT", 355 },
  { "SVRSF_SERVER_USB_NOT_RIGHT", 354 },
  { "SVRSF_SERVER_USB_SPACE_LIMIT", 357 },
  { "SVRSF_SERVER_USB_STATE", 379 },
  { "SVRSF_SERVER_USB_UMOUNT_FAIL", 380 },
  { "SVRSF_SERVER_USER_NOT_ALLOW", 342 },
  { "SVR_EDU_AMT_SOUND_TEST", 265 },
  { "SVR_EDU_AMT_UNINIT", 264 },
  { "SVR_EDU_CONFCTRL_CONF_ING", 281 },
  { "SVR_EDU_CONFCTRL_NO_LISTEN", 283 },
  { "SVR_EDU_CONFCTRL_SCENE_ERR", 282 },
  { "SVR_EDU_DIRECT_GET_ORDER_FAILED", 260 },
  { "SVR_EDU_DIRECT_ORDER_FORMAT_INVALID", 258 },
  { "SVR_EDU_DIRECT_ORDER_LIST_FULL", 261 },
  { "SVR_EDU_DIRECT_ORDER_NOT_SUPPORT", 259 },
  { "SVR_EDU_DIRECT_TRACK_HOST_DISCONN", 262 },
  { "SVR_EDU_DIRECT_TRACK_HOST_DISENABLE", 263 },
  { "SVR_EDU_ERR", 243 },
  { "SVR_EDU_H323_CLTAPP_UNINIT", 245 },
  { "SVR_EDU_H323_NO_CAP", 249 },
  { "SVR_EDU_H323_NO_RECORD", 247 },
  { "SVR_EDU_H323_OPT_FORBIDDEN", 250 },
  { "SVR_EDU_H323_SERVICE_DISCONNECT", 246 },
  { "SVR_EDU_H323_UNREG_GK", 248 },
  { "SVR_EDU_MCA_WRITE_CMD_ERR", 269 },
  { "SVR_EDU_MP4_DB_OPRA_STATE_ERR", 272 },
  { "SVR_EDU_MP4_DB_REQ_NOT_EXIST", 273 },
  { "SVR_EDU_MP4_DEL_ALL_REC_ING", 275 },
  { "SVR_EDU_MP4_DISK_FORMAT_ING", 277 },
  { "SVR_EDU_MP4_DISK_SPACE_FULL", 274 },
  { "SVR_EDU_MP4_PLAYING_NO_DEL_REC", 279 },
  { "SVR_EDU_MP4_REC_START_ING", 278 },
  { "SVR_EDU_MP4_UMOUNT_DISK", 276 },
  { "SVR_EDU_MP4_UPLOAD_ING", 280 },
  { "SVR_EDU_MPU_ENC_CARD_NOT_EXIST", 268 },
  { "SVR_EDU_MPU_ENC_ID_INVALID", 267 },
  { "SVR_EDU_MPU_MP4_REC_NOT_SUPPORT", 271 },
  { "SVR_EDU_MPU_OVER_PIC_NUM", 266 },
  { "SVR_EDU_MPU_WRITE_FRAME_ERR", 270 },
  { "SVR_EDU_PA_CLTAPP_UNINIT", 251 },
  { "SVR_EDU_PA_NOSUPPORT_INTERACT", 257 },
  { "SVR_EDU_PA_NO_CAP", 255 },
  { "SVR_EDU_PA_NO_RECORD", 253 },
  { "SVR_EDU_PA_OPT_FORBIDDEN", 256 },
  { "SVR_EDU_PA_SERVICE_DISCONNECT", 252 },
  { "SVR_EDU_PA_UNREG_GK", 254 },
  { "SVR_EDU_PB_UNPACK_ERR", 244 },
  { "SVR_FRP_COLLECT_NOW", 286 },
  { "SVR_FRP_RUN_SYSCMD_FAILED", 287 },
  { "SVR_RTMP_PUSH_STREAM_ING", 288 },
  { "SVR_WRTC_IN_CONF", 285 },
  { "SVR_WRTC_UNABLE", 284 },
  { "UPGRADE_VER_ERR", 199 },
  { "USER_ADMIN_PERM_MDY_DISALLOWED", 35 },
  { "USER_DEL_DISALLOWED", 24 },
  { "USER_DEV_SYS_NOACTIVE", 36 },
  { "USER_EXIST", 20 },
  { "USER_FILE_BROKEN", 22 },
  { "USER_INEXIST", 21 },
  { "USER_LOG_TASK_BUSY", 42 },
  { "USER_LOG_TASK_ID_UNAVAILABLE", 43 },
  { "USER_MGR_EMAIL_ILLEGAL", 32 },
  { "USER_NAME_ILLEGAL", 25 },
  { "USER_NAME_LEN_TOO_LONG", 27 },
  { "USER_NAME_LEN_TOO_SHORT", 28 },
  { "USER_NAME_MDY_DISALLOWED", 34 },
  { "USER_NAME_NULL", 38 },
  { "USER_NUM_MAX", 23 },
  { "USER_PASS_NULL", 39 },
  { "USER_PASS_OVERTIME", 40 },
  { "USER_PASS_SAME_TO_BEFORE", 37 },
  { "USER_PWD_ILLEGAL", 26 },
  { "USER_PWD_LEN_TOO_LONG", 29 },
  { "USER_PWD_LEN_TOO_SHORT", 30 },
  { "USER_PWD_STRENGTH_WEAK", 31 },
  { "USER_REMOTE_IP_INFO_ILLEGAL", 33 },
  { "VTDUCTRL", 140 },
  { "VTDU_ADD_PIPELINE_FAILED", 154 },
  { "VTDU_APPCLT_STREAM_PREPARE_FAILED", 141 },
  { "VTDU_APPCLT_STREAM_START_FAILED", 142 },
  { "VTDU_BROADCASTING_NO_SUPPORT_CHN", 173 },
  { "VTDU_DEV_OFFLINE", 145 },
  { "VTDU_GETDATA_TOO_FAST", 170 },
  { "VTDU_GETDATA_TOO_SLOW", 171 },
  { "VTDU_INPUT_AUD_PARAM_INVALID", 167 },
  { "VTDU_INPUT_VID_PARAM_INVALID", 166 },
  { "VTDU_IS_AUDCALLING", 168 },
  { "VTDU_MBNET_OVER_FLOW", 172 },
  { "VTDU_MEDIA_PUSH_TASK_EXIST", 181 },
  { "VTDU_MEDIA_PUSH_TASK_FULL", 180 },
  { "VTDU_MSIN_CREATE_FAILED", 147 },
  { "VTDU_MSIN_INPUT_DATA_FAILED", 150 },
  { "VTDU_MSIN_NO_STREAM", 146 },
  { "VTDU_MSIN_RELEASE_FAILED", 153 },
  { "VTDU_MSIN_SET_OPT_FAILED", 148 },
  { "VTDU_MSIN_SET_TRANSPARAM_FAILED", 149 },
  { "VTDU_MSIN_START_FAILED", 151 },
  { "VTDU_MSIN_STOP_FAILED", 152 },
  { "VTDU_MSOUT_CREATE_FAILED", 156 },
  { "VTDU_MSOUT_GET_DATA_FAILED", 161 },
  { "VTDU_MSOUT_GET_DATA_POS_FAILED", 160 },
  { "VTDU_MSOUT_RELEASE_DATA_FAILED", 162 },
  { "VTDU_MSOUT_RELEASE_FAILED", 165 },
  { "VTDU_MSOUT_SET_DATA_CB_FAILED", 159 },
  { "VTDU_MSOUT_SET_OPT_FAILED", 157 },
  { "VTDU_MSOUT_SET_TRANSPARAM_FAILED", 158 },
  { "VTDU_MSOUT_STOP_FAILED", 164 },
  { "VTDU_MSOUT_STRAT_FAILED", 163 },
  { "VTDU_OVER_MAX_SND_BANDWIDTH", 169 },
  { "VTDU_REMOVE_PIPELINE_FAILED", 155 },
  { "VTDU_RTMP_ERROR_CONNECT", 178 },
  { "VTDU_RTMP_ERROR_CONNECT_STREAM", 179 },
  { "VTDU_RTMP_ERROR_MAX_SESSION", 175 },
  { "VTDU_RTMP_ERROR_MEM", 176 },
  { "VTDU_RTMP_ERROR_SETUP_URL", 177 },
  { "VTDU_RTMP_PARAM_ERROR", 174 },
  { "VTDU_SEN_RATE_OVER", 144 },
  { "VTDU_SND_IS_FULL", 143 },
  { "WAIT_TIMEOUT", 6 },
  { "WRITE_FILE", 12 },
};
const ProtobufCEnumDescriptor nvr_err__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "NvrErr",
  "NvrErr",
  "NvrErr",
  "",
  400,
  nvr_err__enum_values_by_number,
  400,
  nvr_err__enum_values_by_name,
  41,
  nvr_err__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
