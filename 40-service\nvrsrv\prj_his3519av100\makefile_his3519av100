

TOP := ..

COMM_DIR := ../../common/

SRC_DIR := $(TOP)/source
CURDIR := ./

## Name and type of the target for this Makefile

APP_TARGET	      := nvrsrv


## Define debugging symbols
DEBUG = 0
LINUX_COMPILER = _HIS3519AV100_
PWLIB_SUPPORT = 0
CFLAGS += -funwind-tables
#CFLAGS += -D__MRTC__
## Object files that compose the target(s)



OBJS := $(SRC_DIR)/nvrsrv\
## Libraries to include in shared object file
LIB_PATH += $(TOP)/../../10-common/lib/release/his3519av100
LIB_PATH += $(TOP)/../../10-common/lib/release/his3519av100/capptz
LIB_PATH += $(TOP)/../../10-common/lib/release/his3519av100/audlib
LIB_PATH += $(TOP)/../../10-common/lib/release/his3519av100/wifim

LIBS +=	nvrcfg nvrlog nvrcap nvrftp nvrcustcap nvrusrmgr nvrnetwork nvrsys nvrpcap nvrproto protobufc sqlite3wrapper malloc debuglog netcbb mbnet ddnsc upnpc drv pthread nvrgeo \
	sysdbg goaheadhelper appbase charconv appclt nvrpui nvrvtductrl nvrmpu nvrprobe httpclient mxml ghttp go rtspclient kdmposa mca osp netpacket kdmtsps kdvencrypt mediaswitch stdc++ nvrqueue ispctrl mediactrl nvralarm nvrdev nvrupgrade nvrcrc ftpc\
	dmsrv nvrrec rpdata rp kdmfileinterface nvrguard nvrsmtp smtp dl ais algctrl airp wifi SDEF pcap kdssl-ext pubsecstack curl nvrcoi msc btctrl nvrunifiedlog\
	encoder hive_ANR hive_common hive_HPF\
	mbm md5lib_hisi3519a_release mpi sac securec \
	smartcodec_armhi3519a_linux \
	kdmfileinterface\
	basicintelligent_his3519A \
	ive\
	aaclcdec_armhi3519a_linux aaclcenc_armhi3519a_linux aaclddec_armhi3519a_linux aacldenc_armhi3519a_linux \
	aec_mulresample_armhi3519a_linux audcodec_armhi3519a_linux audproc_armhi3519a_linux \
	g711_armhi3519a_linux g722_armhi3519a_linux g726_armhi3519a_linux g7221c_armhi3519a_linux stdg722_armhi3519a_linux\
	extexp_armhi3519a_linux videomanage_armhi3519a_linux resamplev2_armhi3519a_linux \
	adpcm_armhi3519a_linux aaclcdec_armhi3519a_linux aaclcenc_armhi3519a_linux \
	spe_armhi3519a_linux asd_armhi3519a_linux mixer_armhi3519a_linux g728_armhi3519a_linux g729_armhi3519a_linux \
	g719_armhi3519a_linux mp3dec_armhi3519a_linux mp3enc_armhi3519a_linux mp2_armhi3519a_linux \
	aaclddec_armhi3519a_linux aacldenc_armhi3519a_linux opus_armhi3519a_linux amr_nb_armhi3519a_linux \
	m\
	kdmssl\
	kdmcrypto\
	cjson\
	udm_dm\
	uuid\
	blkid\
	nvrproduct\
	kdmnatagent\
	rpdownload\
	nvrstitch\
	lwshelper\
	websockets\
	nvrmd5\
    #heapcheck\
    #tcmalloc
ifneq ($(SLIBS),) 
LDFLAGS += -Wl,-static
LDFLAGS += $(foreach lib,$(SLIBS),-l$(lib))
endif
## Add driver-specific include directory to the search path

INC_PATH += $(CURDIR)/../include \
		$(CURDIR)/../../common \
		$(CURDIR)/../../nvrcfg/include \
		$(CURDIR)/../../nvrpui/include \
		$(CURDIR)/../../nvrvtductrl/include \
		$(CURDIR)/../../nvrusrmgr/include \
		$(CURDIR)/../../nvrcap/include \
		$(CURDIR)/../../nvrnetwork/include \
		$(CURDIR)/../../nvrsys/include \
		$(CURDIR)/../../nvrdev/include \
		$(CURDIR)/../../nvralarm/include \
		$(CURDIR)/../../nvrlog/include \
		$(CURDIR)/../../nvrguard/include \
		$(CURDIR)/../../nvrsmtp/include \
		$(CURDIR)/../../nvrmpu/include \
		$(CURDIR)/../../nvrpcap/include \
		$(CURDIR)/../../airp/include \
		$(CURDIR)/../../ais/include \
		$(CURDIR)/../../nvrprobe/include \
		$(CURDIR)/../../nvrextdevmgr/include \
		$(CURDIR)/../../../10-common/include/service \
		$(CURDIR)/../../../10-common/include/tts \
		$(CURDIR)/../../../10-common/include/cbb/debuglog\
		$(CURDIR)/../../../10-common/include/cbb/mbnet\
		$(CURDIR)/../../../10-common/include/system\
		$(CURDIR)/../../../10-common/include/hal/sysdbg\
		$(CURDIR)/../../../10-common/include/cbb/protobuf \
		$(CURDIR)/../../../10-common/include/cbb/osp \
		$(CURDIR)/../../../10-common/include/cbb/sqilte \
		$(CURDIR)/../../../10-common/include/cbb/appclt\
		$(CURDIR)/../../../10-common/include/cbb/mediaswitch\
		$(CURDIR)/../../../10-common/include/cbb/kshield\
		$(CURDIR)/../../../10-common/include/app\
		$(CURDIR)/../../../10-common/include/cbb/kdssl-ext
		
CFLAGS += -D_HIS3519AV100_
#CFLAGS += -D_TCMALLOC_
#CFLAGS += -fno-builtin-malloc -fno-builtin-calloc -fno-builtin-realloc -fno-builtin-free

ifeq ($(PWLIB_SUPPORT),1)
   INC_PATH += $(PWLIBDIR)/include/ptlib/unix $(PWLIBDIR)/include
endif


#INSTALL_NO_UPX_APP_PATH := ../../../10-common/version/release/his3519av100/public
INSTALL_APP_PATH := ../../../10-common/version/release/his3519av100/public
include $(COMM_DIR)/common.mk


