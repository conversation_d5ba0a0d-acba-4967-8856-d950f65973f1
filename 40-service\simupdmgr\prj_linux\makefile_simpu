

TOP := ..

COMM_DIR := ../../common/

SRC_DIR := $(TOP)/source
CURDIR := ./

## Name and type of the target for this Makefile

SO_TARGET	      := simupd


## Define debugging symbols
DEBUG = 0
LINUX_COMPILER = _SIMPU_
PWLIB_SUPPORT = 0
CFLAGS += -funwind-tables
## Object files that compose the target(s)

OBJS := $(SRC_DIR)/simupdmgr \
		
## Libraries to include in shared object file
        
ifneq ($(SLIBS),) 
LDFLAGS += -Wl,-static
LDFLAGS += $(foreach lib,$(SLIBS),-l$(lib))
endif
## Add driver-specific include directory to the search path
	
INC_PATH += $(CURDIR)/../include \
		$(CURDIR)/../../nvrcap/include \
		$(CURDIR)/../../nvrsys/include \
		$(CURDIR)/../../nvrpui/include \
		$(CURDIR)/../../nvralarm/include \
		$(CURDIR)/../../common \
		$(CURDIR)/../../../10-common/include/service\
		$(CURDIR)/../../../10-common/include/cbb/osp\
		$(CURDIR)/../../../10-common/include/cbb/protobuf \
		$(CURDIR)/../../../10-common/include/cbb/debuglog \
		$(CURDIR)/../../../10-common/include/cbb/appclt \
		$(CURDIR)/../../../10-common/include/cbb/sqilte \
		$(CURDIR)/../../../10-common/include/cbb/charconversion \
		$(CURDIR)/../../../10-common/include/cbb/mediaswitch \
		$(CURDIR)/../../../10-common/include/system\
		$(CURDIR)/../../../10-common/include/app \
		$(CURDIR)/../../../10-common/include/cbb/mediactrl \
		$(CURDIR)/../../../10-common/include/cbb/cjson
		
CFLAGS += -D_SIMPU_



ifeq ($(PWLIB_SUPPORT),1)
   INC_PATH += $(PWLIBDIR)/include/ptlib/unix $(PWLIBDIR)/include
endif


INSTALL_LIB_PATH = ../../../10-common/lib/release/simpu

LIB_PATH := ../../../10-common/lib/release/simpu

LIBS :=	 pubsecstack appbase go osp cjson debuglog nvrcore

LDFLAGS += -L$(INSTALL_LIB_PATH)
include $(COMM_DIR)/common.mk
