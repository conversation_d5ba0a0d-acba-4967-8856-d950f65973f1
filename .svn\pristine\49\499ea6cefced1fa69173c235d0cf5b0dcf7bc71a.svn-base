
TOP := ..

COMM_DIR := ../../common/

SRC_DIR := $(TOP)/source
CURDIR := ./

## Name and type of the target for this Makefile

APP_TARGET	      := nvrupdate_cgi


## Define debugging symbols
DEBUG = 0
LINUX_COMPILER = _HIS3516DV300_
PWLIB_SUPPORT = 0
CFLAGS += -funwind-tables

## Object files that compose the target(s)



OBJS := $(SRC_DIR)/nvrupdate\
## Libraries to include in shared object file
LIB_PATH += $(TOP)/../../10-common/lib/release/his3516dv300
LIB_PATH += $(TOP)/../../10-common/lib/release/his3516dv300/capptz
LIB_PATH += $(TOP)/../../10-common/lib/release/his3516dv300/audlib
LIB_PATH += $(TOP)/../../10-common/lib/release/his3516dv300/webrtc
LIB_PATH += $(TOP)/../../10-common/lib/release/his3516dv300/wifim
LIB_PATH += $(TOP)/../../10-common/lib/release/his3516dv300/kdssl-ext
LIB_PATH += $(TOP)/../../10-common/lib/release/his3516dv300/nvrgnss
LIB_PATH += $(TOP)/../../10-common/lib/release/his3516dv300/appcltlib

SLIBS +=pthread dl
LIBS  += nvrcfg nvrlog nvrftp nvrcap nvrcustcap nvrusrmgr nvrnetwork nvrsys nvrproto protobufc sqlite3wrapper malloc debuglog netcbb mbnet wifi ddnsc upnpc drv pthread nvrgeo \
	sysdbg goaheadhelper appbase charconv appclt nvrpui nvrvtductrl nvrmpu nvrprobe nvrpcap httpclient mxml ghttp go osp mrtc mediaswitch kdmtsps kdvencrypt stdc++ nvrqueue mediactrl ispctrl nvralarm nvrdev nvrupgrade nvrcrc ftpc\
	dmsrv nvrrec rpdata rp kdmfileinterface nvrguard nvrsmtp smtp airp gnss dl ais algctrl SDEF pubsecstack curl nvrcoi netpacket\
	ive msc\
	audcodec_armhi3519a_linux\
	smartcodec_armhi3519a_linux\
	basicintelligent_his3516DV300\
	md5lib_hisi3519a_release\
	aec_mulresample_armhi3519a_linux\
	audproc_armhi3519a_linux\
	g711_armhi3519a_linux\
	g719_armhi3519a_linux\
	g722_armhi3519a_linux\
	g726_armhi3519a_linux\
	g728_armhi3519a_linux\
	g729_armhi3519a_linux\
	mp2_armhi3519a_linux\
	mp3dec_armhi3519a_linux\
	mp3enc_armhi3519a_linux\
	opus_armhi3519a_linux\
	stdg722_armhi3519a_linux\
	g7221c_armhi3519a_linux\
	extexp_armhi3519a_linux\
	videomanage_armhi3519a_linux\
	resample_armhi3519a_linux\
	adpcm_armhi3519a_linux\
	aaclcdec_armhi3519a_linux\
	aaclcenc_armhi3519a_linux\
	aaclddec_armhi3519a_linux\
	aacldenc_armhi3519a_linux\
	amr_nb_armhi3519a_linux\
	spe_armhi3519a_linux\
	asd_armhi3519a_linux\
	mixer_armhi3519a_linux\
	aec_3A_mulresample_armhi3519a_linux\
	m \
	pcap\
	kdmssl\
	kdmcrypto\
	cjson\
	uuid\
	blkid\
	nvrproduct\
	kdmnatagent\
	rpdownload\
	nvrdynamicplugin\
	nvrstitch\
	lwshelper\
	websockets\
	nvrmd5\
	lcamclt\
	wmf\
	udm\

ifneq ($(SLIBS),) 
LDFLAGS += -Wl,-static
LDFLAGS += $(foreach lib,$(SLIBS),-l$(lib))
endif
## Add driver-specific include directory to the search path

INC_PATH += $(CURDIR)/../include \
		$(CURDIR)/../../common \
		$(CURDIR)/../../../10-common/include/system\
		$(CURDIR)/../../../10-common/include/hal/drvlib_64\
		$(CURDIR)/../../../10-common/include/hal/drvlib_64/system\
		$(CURDIR)/../../../10-common/include/service \
		$(CURDIR)/../../../10-common/include/cbb/debuglog\
		$(CURDIR)/../../../10-common/include/cbb/protobuf \
		$(CURDIR)/../../../10-common/include/cbb/osp \
		$(CURDIR)/../../../10-common/include/cbb/crc_check
CFLAGS += -D_HIS3516DV300_
CFLAGS += -D_NVRUPDATE_CGI_

ifeq ($(PWLIB_SUPPORT),1)
   INC_PATH += $(PWLIBDIR)/include/ptlib/unix $(PWLIBDIR)/include
endif

#no upx program maybe main Segmentation fault
DO_UPX = 0

INSTALL_APP_PATH := ../../../10-common/version/release/his3516dv300/public

include $(COMM_DIR)/common.mk
