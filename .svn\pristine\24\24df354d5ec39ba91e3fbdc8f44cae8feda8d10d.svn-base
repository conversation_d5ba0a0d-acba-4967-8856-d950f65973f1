

TOP := ..

COMM_DIR := ../../common/

SRC_DIR := $(TOP)/source
CURDIR := ./

## Name and type of the target for this Makefile

SO_TARGET	      := nvrfixcorepro


## Define debugging symbols
DEBUG = 0
LINUX_COMPILER = _QCOM_
PWLIB_SUPPORT = 0
CFLAGS += -funwind-tables
## Object files that compose the target(s)

OBJS := $(SRC_DIR)/nvrfixcore\
		$(SRC_DIR)/nvrfixdev\
		$(SRC_DIR)/nvrfixcfg.pb-c\
		$(SRC_DIR)/nvrfixcfg\
		$(SRC_DIR)/nvrfixisp\
		$(SRC_DIR)/nvrfixmc\
		$(SRC_DIR)/nvrfixplugin\
		$(SRC_DIR)/nvrfixlifestat\

## Libraries to include in shared object file
        
ifneq ($(SLIBS),) 
LDFLAGS += -Wl,-static
LDFLAGS += $(foreach lib,$(SLIBS),-l$(lib))
endif
## Add driver-specific include directory to the search path

INC_PATH += $(CURDIR)/../include \
		$(CURDIR)/../../common \
		$(CURDIR)/../../nvrcustcap/include\
		$(CURDIR)/../../../40-service/nvrcfg/include \
		$(CURDIR)/../../../10-common/include/service \
		$(CURDIR)/../../../10-common/include/cbb/ipcmediactrl\
		$(CURDIR)/../../../10-common/include/cbb/mediaswitch\
		$(CURDIR)/../../../10-common/include/cbb/debuglog\
		$(CURDIR)/../../../10-common/include/system\
		$(CURDIR)/../../../10-common/include/cbb/protobuf \
		$(CURDIR)/../../../10-common/include/cbb/osp \
		$(CURDIR)/../../../10-common/include/cbb/charconversion\
		$(CURDIR)/../../../10-common/include/cbb/appclt\
		$(CURDIR)/../../../10-common/include/app\
		$(CURDIR)/../../../10-common/include/cbb/mxml\
		$(CURDIR)/../../../10-common/include/hal/drvlib_64 \
		$(CURDIR)/../../../10-common/include/cbb/ftpc\
		$(CURDIR)/../../../10-common/include/cbb/kdmfileinterface\
		$(CURDIR)/../../../10-common/include/cbb/rp\
		$(CURDIR)/../../../40-service/nvrsys/include \
		$(CURDIR)/../../../40-service/nvrpui/include\
		$(CURDIR)/../../../40-service/nvrcap/include\
		$(CURDIR)/../../../40-service/lcamclt/include\
		$(CURDIR)/../../../40-service/nvrusrmgr/include\
		$(CURDIR)/../../../10-common/include/cbb/ispctrl\
		$(CURDIR)/../../../40-service/nvrrec/include\
		$(CURDIR)/../../../40-service/nvrvtductrl/include \

CFLAGS += -D_QCOM_



ifeq ($(PWLIB_SUPPORT),1)
   INC_PATH += $(PWLIBDIR)/include/ptlib/unix $(PWLIBDIR)/include
endif


INSTALL_LIB_PATH = ../../../10-common/lib/release/qcom/speprolib

LDFLAGS += -L../../../10-common/lib/release/qcom/
LDFLAGS += -L../../../10-common/lib/release/qcom/appcltlib/
LDFLAGS += -L../../../10-common/lib/release/qcom/ipcmediactrl/

LDFLAGS += -losp
LDFLAGS += -lnvrcore
LDFLAGS += -llcamclt
LDFLAGS += -lispctrl
LDFLAGS += -ladrv

include $(COMM_DIR)/common.mk


