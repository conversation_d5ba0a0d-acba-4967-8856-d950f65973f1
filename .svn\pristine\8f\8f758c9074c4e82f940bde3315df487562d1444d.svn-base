path="../../10-common/version/compileinfo/nvrunifiedlog_ssc339g.txt"
date>>$path

cd ./prj_linux

echo ==============================================
echo =      nvr_unifiedlog_linux for ssc339g           =
echo ==============================================

echo "============compile libnvrunifiedlog ssc339g============">>../$path

make -j4 -e DEBUG=0 -f makefile_ssc339g clean
make -j4 -e DEBUG=0 -f makefile_ssc339g 2>>../$path

cp -L -r -f libnvrunifiedlog.so ../../../10-common/lib/release/ssc339g/

cd ..
