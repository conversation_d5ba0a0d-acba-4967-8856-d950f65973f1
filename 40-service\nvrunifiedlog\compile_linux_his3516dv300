path="../../10-common/version/compileinfo/nvrunifiedlog_his3516dv300.txt"
date>>$path

cd ./prj_linux

echo ==============================================
echo =      nvr_unifiedlog_linux for his3516dv300           =
echo ==============================================

echo "============compile libnvrunifiedlog his3516dv300============">>../$path

make -e DEBUG=0 -f makefile_his3516dv300 clean
make -e DEBUG=0 -f makefile_his3516dv300 2>>../$path

cp -L -r -f libnvrunifiedlog.so ../../../10-common/lib/release/his3516dv300/

cd ..



