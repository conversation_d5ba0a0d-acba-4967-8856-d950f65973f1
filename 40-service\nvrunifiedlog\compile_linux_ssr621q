path="../../10-common/version/compileinfo/nvrunifiedlog_ssr621q.txt"
date>>$path

cd ./prj_linux

echo ==============================================
echo =      nvr_unifiedlog_linux for ssr621q           =
echo ==============================================

echo "============compile libnvrunifiedlog ssr621q============">>../$path

make -j4 -e DEBUG=0 -f makefile_ssr621q clean
make -j4 -e DEBUG=0 -f makefile_ssr621q 2>>../$path

cp -L -r -f libnvrunifiedlog.so ../../../10-common/lib/release/ssr621q/

cd ..
