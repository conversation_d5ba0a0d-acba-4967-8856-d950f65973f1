path="../../10-common/version/compileinfo/nvrcustcap_ax603a.txt"
date>>$path

cd ./prj_linux

echo ==============================================
echo =      nvr_cust_cap_linux for ax603a           =
echo ==============================================

echo "============compile libnvrcustcap ax603a============">>../$path

make -e DEBUG=0 -f makefile_ax603a clean
make -e DEBUG=0 -f makefile_ax603a 2>>../$path

cp -L -r -f libnvrcustcap.so ../../../10-common/lib/release/ax603a/capfix/

cd ..
