
TOP := ..

COMM_DIR := ../../common/

SRC_DIR := $(TOP)/source
CURDIR := ./

## Name and type of the target for this Makefile

APP_TARGET	      := nvrupdate


## Define debugging symbols
DEBUG = 0
LINUX_COMPILER = _SSC339G_
PWLIB_SUPPORT = 0
CFLAGS += -funwind-tables
## Object files that compose the target(s)



OBJS := $(SRC_DIR)/nvrupdate\
## Libraries to include in shared object file
LIB_PATH += $(TOP)/../../10-common/lib/release/ssc339g
SLIBS += nvrupgrade netcbb drv ftpc nvrcrc
LIBS += dl pthread

ifneq ($(SLIBS),) 
LDFLAGS += -Wl,-static
LDFLAGS += $(foreach lib,$(SLIBS),-l$(lib))
endif
## Add driver-specific include directory to the search path

INC_PATH += $(CURDIR)/../include \
		$(CURDIR)/../../common \
		$(CURDIR)/../../../10-common/include/system\
		$(CURDIR)/../../../10-common/include/hal/drvlib_64\
		$(CURDIR)/../../../10-common/include/hal/drvlib_64/system\
		$(CURDIR)/../../../10-common/include/service \
		$(CURDIR)/../../../10-common/include/cbb/debuglog\
		$(CURDIR)/../../../10-common/include/cbb/protobuf \
		$(CURDIR)/../../../10-common/include/cbb/osp \
		$(CURDIR)/../../../10-common/include/cbb/crc_check
CFLAGS += -D_SSC339G_


ifeq ($(PWLIB_SUPPORT),1)
   INC_PATH += $(PWLIBDIR)/include/ptlib/unix $(PWLIBDIR)/include
endif

#no upx program maybe main Segmentation fault
DO_UPX = 0


INSTALL_APP_PATH := ../../../10-common/version/release/ssc339g/public

include $(COMM_DIR)/common.mk
