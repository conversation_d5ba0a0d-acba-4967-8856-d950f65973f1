/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: nvrmpu.proto */

/* Do not generate deprecated warnings for self */
#ifndef PROTOBUF_C__NO_DEPRECATED
#define PROTOBUF_C__NO_DEPRECATED
#endif

#include "nvrmpu.pb-c.h"
void   tpb_nvr_mpu_vid_resolution_param__init
                     (TPbNvrMpuVidResolutionParam         *message)
{
  static TPbNvrMpuVidResolutionParam init_value = TPB_NVR_MPU_VID_RESOLUTION_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_mpu_vid_resolution_param__get_packed_size
                     (const TPbNvrMpuVidResolutionParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_vid_resolution_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_mpu_vid_resolution_param__pack
                     (const TPbNvrMpuVidResolutionParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_vid_resolution_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_mpu_vid_resolution_param__pack_to_buffer
                     (const TPbNvrMpuVidResolutionParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_vid_resolution_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrMpuVidResolutionParam *
       tpb_nvr_mpu_vid_resolution_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrMpuVidResolutionParam *)
     protobuf_c_message_unpack (&tpb_nvr_mpu_vid_resolution_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_mpu_vid_resolution_param__free_unpacked
                     (TPbNvrMpuVidResolutionParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_vid_resolution_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_mpu_vid_dec_window_param__init
                     (TPbNvrMpuVidDecWindowParam         *message)
{
  static TPbNvrMpuVidDecWindowParam init_value = TPB_NVR_MPU_VID_DEC_WINDOW_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_mpu_vid_dec_window_param__get_packed_size
                     (const TPbNvrMpuVidDecWindowParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_vid_dec_window_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_mpu_vid_dec_window_param__pack
                     (const TPbNvrMpuVidDecWindowParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_vid_dec_window_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_mpu_vid_dec_window_param__pack_to_buffer
                     (const TPbNvrMpuVidDecWindowParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_vid_dec_window_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrMpuVidDecWindowParam *
       tpb_nvr_mpu_vid_dec_window_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrMpuVidDecWindowParam *)
     protobuf_c_message_unpack (&tpb_nvr_mpu_vid_dec_window_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_mpu_vid_dec_window_param__free_unpacked
                     (TPbNvrMpuVidDecWindowParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_vid_dec_window_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_mpu_vid_dis_dev_param__init
                     (TPbNvrMpuVidDisDevParam         *message)
{
  static TPbNvrMpuVidDisDevParam init_value = TPB_NVR_MPU_VID_DIS_DEV_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_mpu_vid_dis_dev_param__get_packed_size
                     (const TPbNvrMpuVidDisDevParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_vid_dis_dev_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_mpu_vid_dis_dev_param__pack
                     (const TPbNvrMpuVidDisDevParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_vid_dis_dev_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_mpu_vid_dis_dev_param__pack_to_buffer
                     (const TPbNvrMpuVidDisDevParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_vid_dis_dev_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrMpuVidDisDevParam *
       tpb_nvr_mpu_vid_dis_dev_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrMpuVidDisDevParam *)
     protobuf_c_message_unpack (&tpb_nvr_mpu_vid_dis_dev_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_mpu_vid_dis_dev_param__free_unpacked
                     (TPbNvrMpuVidDisDevParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_vid_dis_dev_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_mpu_vid_dis_layout_info__init
                     (TPbNvrMpuVidDisLayoutInfo         *message)
{
  static TPbNvrMpuVidDisLayoutInfo init_value = TPB_NVR_MPU_VID_DIS_LAYOUT_INFO__INIT;
  *message = init_value;
}
size_t tpb_nvr_mpu_vid_dis_layout_info__get_packed_size
                     (const TPbNvrMpuVidDisLayoutInfo *message)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_vid_dis_layout_info__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_mpu_vid_dis_layout_info__pack
                     (const TPbNvrMpuVidDisLayoutInfo *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_vid_dis_layout_info__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_mpu_vid_dis_layout_info__pack_to_buffer
                     (const TPbNvrMpuVidDisLayoutInfo *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_vid_dis_layout_info__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrMpuVidDisLayoutInfo *
       tpb_nvr_mpu_vid_dis_layout_info__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrMpuVidDisLayoutInfo *)
     protobuf_c_message_unpack (&tpb_nvr_mpu_vid_dis_layout_info__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_mpu_vid_dis_layout_info__free_unpacked
                     (TPbNvrMpuVidDisLayoutInfo *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_vid_dis_layout_info__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_mpu_vid_dis_layout_cfg__init
                     (TPbNvrMpuVidDisLayoutCfg         *message)
{
  static TPbNvrMpuVidDisLayoutCfg init_value = TPB_NVR_MPU_VID_DIS_LAYOUT_CFG__INIT;
  *message = init_value;
}
size_t tpb_nvr_mpu_vid_dis_layout_cfg__get_packed_size
                     (const TPbNvrMpuVidDisLayoutCfg *message)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_vid_dis_layout_cfg__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_mpu_vid_dis_layout_cfg__pack
                     (const TPbNvrMpuVidDisLayoutCfg *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_vid_dis_layout_cfg__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_mpu_vid_dis_layout_cfg__pack_to_buffer
                     (const TPbNvrMpuVidDisLayoutCfg *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_vid_dis_layout_cfg__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrMpuVidDisLayoutCfg *
       tpb_nvr_mpu_vid_dis_layout_cfg__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrMpuVidDisLayoutCfg *)
     protobuf_c_message_unpack (&tpb_nvr_mpu_vid_dis_layout_cfg__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_mpu_vid_dis_layout_cfg__free_unpacked
                     (TPbNvrMpuVidDisLayoutCfg *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_vid_dis_layout_cfg__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_mpu_gui_menu_output_dst_cfg__init
                     (TPbNvrMpuGuiMenuOutputDstCfg         *message)
{
  static TPbNvrMpuGuiMenuOutputDstCfg init_value = TPB_NVR_MPU_GUI_MENU_OUTPUT_DST_CFG__INIT;
  *message = init_value;
}
size_t tpb_nvr_mpu_gui_menu_output_dst_cfg__get_packed_size
                     (const TPbNvrMpuGuiMenuOutputDstCfg *message)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_gui_menu_output_dst_cfg__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_mpu_gui_menu_output_dst_cfg__pack
                     (const TPbNvrMpuGuiMenuOutputDstCfg *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_gui_menu_output_dst_cfg__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_mpu_gui_menu_output_dst_cfg__pack_to_buffer
                     (const TPbNvrMpuGuiMenuOutputDstCfg *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_gui_menu_output_dst_cfg__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrMpuGuiMenuOutputDstCfg *
       tpb_nvr_mpu_gui_menu_output_dst_cfg__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrMpuGuiMenuOutputDstCfg *)
     protobuf_c_message_unpack (&tpb_nvr_mpu_gui_menu_output_dst_cfg__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_mpu_gui_menu_output_dst_cfg__free_unpacked
                     (TPbNvrMpuGuiMenuOutputDstCfg *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_gui_menu_output_dst_cfg__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_mpu_other_cfg__init
                     (TPbNvrMpuOtherCfg         *message)
{
  static TPbNvrMpuOtherCfg init_value = TPB_NVR_MPU_OTHER_CFG__INIT;
  *message = init_value;
}
size_t tpb_nvr_mpu_other_cfg__get_packed_size
                     (const TPbNvrMpuOtherCfg *message)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_other_cfg__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_mpu_other_cfg__pack
                     (const TPbNvrMpuOtherCfg *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_other_cfg__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_mpu_other_cfg__pack_to_buffer
                     (const TPbNvrMpuOtherCfg *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_other_cfg__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrMpuOtherCfg *
       tpb_nvr_mpu_other_cfg__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrMpuOtherCfg *)
     protobuf_c_message_unpack (&tpb_nvr_mpu_other_cfg__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_mpu_other_cfg__free_unpacked
                     (TPbNvrMpuOtherCfg *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_other_cfg__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_mpu_aud_param_cfg__init
                     (TPbNvrMpuAudParamCfg         *message)
{
  static TPbNvrMpuAudParamCfg init_value = TPB_NVR_MPU_AUD_PARAM_CFG__INIT;
  *message = init_value;
}
size_t tpb_nvr_mpu_aud_param_cfg__get_packed_size
                     (const TPbNvrMpuAudParamCfg *message)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_aud_param_cfg__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_mpu_aud_param_cfg__pack
                     (const TPbNvrMpuAudParamCfg *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_aud_param_cfg__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_mpu_aud_param_cfg__pack_to_buffer
                     (const TPbNvrMpuAudParamCfg *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_aud_param_cfg__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrMpuAudParamCfg *
       tpb_nvr_mpu_aud_param_cfg__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrMpuAudParamCfg *)
     protobuf_c_message_unpack (&tpb_nvr_mpu_aud_param_cfg__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_mpu_aud_param_cfg__free_unpacked
                     (TPbNvrMpuAudParamCfg *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_aud_param_cfg__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_mpu_crop_rect_region__init
                     (TPbNvrMpuCropRectRegion         *message)
{
  static TPbNvrMpuCropRectRegion init_value = TPB_NVR_MPU_CROP_RECT_REGION__INIT;
  *message = init_value;
}
size_t tpb_nvr_mpu_crop_rect_region__get_packed_size
                     (const TPbNvrMpuCropRectRegion *message)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_crop_rect_region__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_mpu_crop_rect_region__pack
                     (const TPbNvrMpuCropRectRegion *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_crop_rect_region__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_mpu_crop_rect_region__pack_to_buffer
                     (const TPbNvrMpuCropRectRegion *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_crop_rect_region__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrMpuCropRectRegion *
       tpb_nvr_mpu_crop_rect_region__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrMpuCropRectRegion *)
     protobuf_c_message_unpack (&tpb_nvr_mpu_crop_rect_region__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_mpu_crop_rect_region__free_unpacked
                     (TPbNvrMpuCropRectRegion *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_crop_rect_region__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_mpu_vid_dis_dev__init
                     (TPbNvrMpuVidDisDev         *message)
{
  static TPbNvrMpuVidDisDev init_value = TPB_NVR_MPU_VID_DIS_DEV__INIT;
  *message = init_value;
}
size_t tpb_nvr_mpu_vid_dis_dev__get_packed_size
                     (const TPbNvrMpuVidDisDev *message)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_vid_dis_dev__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_mpu_vid_dis_dev__pack
                     (const TPbNvrMpuVidDisDev *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_vid_dis_dev__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_mpu_vid_dis_dev__pack_to_buffer
                     (const TPbNvrMpuVidDisDev *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_vid_dis_dev__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrMpuVidDisDev *
       tpb_nvr_mpu_vid_dis_dev__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrMpuVidDisDev *)
     protobuf_c_message_unpack (&tpb_nvr_mpu_vid_dis_dev__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_mpu_vid_dis_dev__free_unpacked
                     (TPbNvrMpuVidDisDev *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_vid_dis_dev__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_mpu_win_border__init
                     (TPbNvrMpuWinBorder         *message)
{
  static TPbNvrMpuWinBorder init_value = TPB_NVR_MPU_WIN_BORDER__INIT;
  *message = init_value;
}
size_t tpb_nvr_mpu_win_border__get_packed_size
                     (const TPbNvrMpuWinBorder *message)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_win_border__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_mpu_win_border__pack
                     (const TPbNvrMpuWinBorder *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_win_border__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_mpu_win_border__pack_to_buffer
                     (const TPbNvrMpuWinBorder *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_win_border__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrMpuWinBorder *
       tpb_nvr_mpu_win_border__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrMpuWinBorder *)
     protobuf_c_message_unpack (&tpb_nvr_mpu_win_border__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_mpu_win_border__free_unpacked
                     (TPbNvrMpuWinBorder *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_win_border__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_mpu_vid_dec_window__init
                     (TPbNvrMpuVidDecWindow         *message)
{
  static TPbNvrMpuVidDecWindow init_value = TPB_NVR_MPU_VID_DEC_WINDOW__INIT;
  *message = init_value;
}
size_t tpb_nvr_mpu_vid_dec_window__get_packed_size
                     (const TPbNvrMpuVidDecWindow *message)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_vid_dec_window__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_mpu_vid_dec_window__pack
                     (const TPbNvrMpuVidDecWindow *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_vid_dec_window__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_mpu_vid_dec_window__pack_to_buffer
                     (const TPbNvrMpuVidDecWindow *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_vid_dec_window__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrMpuVidDecWindow *
       tpb_nvr_mpu_vid_dec_window__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrMpuVidDecWindow *)
     protobuf_c_message_unpack (&tpb_nvr_mpu_vid_dec_window__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_mpu_vid_dec_window__free_unpacked
                     (TPbNvrMpuVidDecWindow *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_vid_dec_window__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_mpu_vid_dis_layout_param__init
                     (TPbNvrMpuVidDisLayoutParam         *message)
{
  static TPbNvrMpuVidDisLayoutParam init_value = TPB_NVR_MPU_VID_DIS_LAYOUT_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_mpu_vid_dis_layout_param__get_packed_size
                     (const TPbNvrMpuVidDisLayoutParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_vid_dis_layout_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_mpu_vid_dis_layout_param__pack
                     (const TPbNvrMpuVidDisLayoutParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_vid_dis_layout_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_mpu_vid_dis_layout_param__pack_to_buffer
                     (const TPbNvrMpuVidDisLayoutParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_vid_dis_layout_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrMpuVidDisLayoutParam *
       tpb_nvr_mpu_vid_dis_layout_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrMpuVidDisLayoutParam *)
     protobuf_c_message_unpack (&tpb_nvr_mpu_vid_dis_layout_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_mpu_vid_dis_layout_param__free_unpacked
                     (TPbNvrMpuVidDisLayoutParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_vid_dis_layout_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_mpu_vid_dec_window_info__init
                     (TPbNvrMpuVidDecWindowInfo         *message)
{
  static TPbNvrMpuVidDecWindowInfo init_value = TPB_NVR_MPU_VID_DEC_WINDOW_INFO__INIT;
  *message = init_value;
}
size_t tpb_nvr_mpu_vid_dec_window_info__get_packed_size
                     (const TPbNvrMpuVidDecWindowInfo *message)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_vid_dec_window_info__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_mpu_vid_dec_window_info__pack
                     (const TPbNvrMpuVidDecWindowInfo *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_vid_dec_window_info__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_mpu_vid_dec_window_info__pack_to_buffer
                     (const TPbNvrMpuVidDecWindowInfo *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_vid_dec_window_info__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrMpuVidDecWindowInfo *
       tpb_nvr_mpu_vid_dec_window_info__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrMpuVidDecWindowInfo *)
     protobuf_c_message_unpack (&tpb_nvr_mpu_vid_dec_window_info__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_mpu_vid_dec_window_info__free_unpacked
                     (TPbNvrMpuVidDecWindowInfo *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_vid_dec_window_info__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_mpu_chn_dec_mode__init
                     (TPbNvrMpuChnDecMode         *message)
{
  static TPbNvrMpuChnDecMode init_value = TPB_NVR_MPU_CHN_DEC_MODE__INIT;
  *message = init_value;
}
size_t tpb_nvr_mpu_chn_dec_mode__get_packed_size
                     (const TPbNvrMpuChnDecMode *message)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_chn_dec_mode__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_mpu_chn_dec_mode__pack
                     (const TPbNvrMpuChnDecMode *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_chn_dec_mode__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_mpu_chn_dec_mode__pack_to_buffer
                     (const TPbNvrMpuChnDecMode *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_chn_dec_mode__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrMpuChnDecMode *
       tpb_nvr_mpu_chn_dec_mode__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrMpuChnDecMode *)
     protobuf_c_message_unpack (&tpb_nvr_mpu_chn_dec_mode__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_mpu_chn_dec_mode__free_unpacked
                     (TPbNvrMpuChnDecMode *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_chn_dec_mode__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_mpu_div_dec_mode__init
                     (TPbNvrMpuDivDecMode         *message)
{
  static TPbNvrMpuDivDecMode init_value = TPB_NVR_MPU_DIV_DEC_MODE__INIT;
  *message = init_value;
}
size_t tpb_nvr_mpu_div_dec_mode__get_packed_size
                     (const TPbNvrMpuDivDecMode *message)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_div_dec_mode__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_mpu_div_dec_mode__pack
                     (const TPbNvrMpuDivDecMode *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_div_dec_mode__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_mpu_div_dec_mode__pack_to_buffer
                     (const TPbNvrMpuDivDecMode *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_div_dec_mode__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrMpuDivDecMode *
       tpb_nvr_mpu_div_dec_mode__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrMpuDivDecMode *)
     protobuf_c_message_unpack (&tpb_nvr_mpu_div_dec_mode__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_mpu_div_dec_mode__free_unpacked
                     (TPbNvrMpuDivDecMode *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_div_dec_mode__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_mpu_dis_vid_window_info__init
                     (TPbNvrMpuDisVidWindowInfo         *message)
{
  static TPbNvrMpuDisVidWindowInfo init_value = TPB_NVR_MPU_DIS_VID_WINDOW_INFO__INIT;
  *message = init_value;
}
size_t tpb_nvr_mpu_dis_vid_window_info__get_packed_size
                     (const TPbNvrMpuDisVidWindowInfo *message)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_dis_vid_window_info__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_mpu_dis_vid_window_info__pack
                     (const TPbNvrMpuDisVidWindowInfo *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_dis_vid_window_info__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_mpu_dis_vid_window_info__pack_to_buffer
                     (const TPbNvrMpuDisVidWindowInfo *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_dis_vid_window_info__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrMpuDisVidWindowInfo *
       tpb_nvr_mpu_dis_vid_window_info__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrMpuDisVidWindowInfo *)
     protobuf_c_message_unpack (&tpb_nvr_mpu_dis_vid_window_info__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_mpu_dis_vid_window_info__free_unpacked
                     (TPbNvrMpuDisVidWindowInfo *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_dis_vid_window_info__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_mpu_mgr_param__init
                     (TPbNvrMpuMgrParam         *message)
{
  static TPbNvrMpuMgrParam init_value = TPB_NVR_MPU_MGR_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_mpu_mgr_param__get_packed_size
                     (const TPbNvrMpuMgrParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_mgr_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_mpu_mgr_param__pack
                     (const TPbNvrMpuMgrParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_mgr_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_mpu_mgr_param__pack_to_buffer
                     (const TPbNvrMpuMgrParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_mgr_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrMpuMgrParam *
       tpb_nvr_mpu_mgr_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrMpuMgrParam *)
     protobuf_c_message_unpack (&tpb_nvr_mpu_mgr_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_mpu_mgr_param__free_unpacked
                     (TPbNvrMpuMgrParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_mgr_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_mpu_win_info_param__init
                     (TPbNvrMpuWinInfoParam         *message)
{
  static TPbNvrMpuWinInfoParam init_value = TPB_NVR_MPU_WIN_INFO_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_mpu_win_info_param__get_packed_size
                     (const TPbNvrMpuWinInfoParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_win_info_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_mpu_win_info_param__pack
                     (const TPbNvrMpuWinInfoParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_win_info_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_mpu_win_info_param__pack_to_buffer
                     (const TPbNvrMpuWinInfoParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_win_info_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrMpuWinInfoParam *
       tpb_nvr_mpu_win_info_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrMpuWinInfoParam *)
     protobuf_c_message_unpack (&tpb_nvr_mpu_win_info_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_mpu_win_info_param__free_unpacked
                     (TPbNvrMpuWinInfoParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_win_info_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_mpu_vid_dec_effect__init
                     (TPbNvrMpuVidDecEffect         *message)
{
  static TPbNvrMpuVidDecEffect init_value = TPB_NVR_MPU_VID_DEC_EFFECT__INIT;
  *message = init_value;
}
size_t tpb_nvr_mpu_vid_dec_effect__get_packed_size
                     (const TPbNvrMpuVidDecEffect *message)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_vid_dec_effect__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_mpu_vid_dec_effect__pack
                     (const TPbNvrMpuVidDecEffect *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_vid_dec_effect__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_mpu_vid_dec_effect__pack_to_buffer
                     (const TPbNvrMpuVidDecEffect *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_vid_dec_effect__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrMpuVidDecEffect *
       tpb_nvr_mpu_vid_dec_effect__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrMpuVidDecEffect *)
     protobuf_c_message_unpack (&tpb_nvr_mpu_vid_dec_effect__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_mpu_vid_dec_effect__free_unpacked
                     (TPbNvrMpuVidDecEffect *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_vid_dec_effect__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_mpu_dec_aud_mixer__init
                     (TPbNvrMpuDecAudMixer         *message)
{
  static TPbNvrMpuDecAudMixer init_value = TPB_NVR_MPU_DEC_AUD_MIXER__INIT;
  *message = init_value;
}
size_t tpb_nvr_mpu_dec_aud_mixer__get_packed_size
                     (const TPbNvrMpuDecAudMixer *message)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_dec_aud_mixer__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_mpu_dec_aud_mixer__pack
                     (const TPbNvrMpuDecAudMixer *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_dec_aud_mixer__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_mpu_dec_aud_mixer__pack_to_buffer
                     (const TPbNvrMpuDecAudMixer *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_dec_aud_mixer__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrMpuDecAudMixer *
       tpb_nvr_mpu_dec_aud_mixer__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrMpuDecAudMixer *)
     protobuf_c_message_unpack (&tpb_nvr_mpu_dec_aud_mixer__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_mpu_dec_aud_mixer__free_unpacked
                     (TPbNvrMpuDecAudMixer *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_dec_aud_mixer__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_mpu_aud_ply_param__init
                     (TPbNvrMpuAudPlyParam         *message)
{
  static TPbNvrMpuAudPlyParam init_value = TPB_NVR_MPU_AUD_PLY_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_mpu_aud_ply_param__get_packed_size
                     (const TPbNvrMpuAudPlyParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_aud_ply_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_mpu_aud_ply_param__pack
                     (const TPbNvrMpuAudPlyParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_aud_ply_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_mpu_aud_ply_param__pack_to_buffer
                     (const TPbNvrMpuAudPlyParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_aud_ply_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrMpuAudPlyParam *
       tpb_nvr_mpu_aud_ply_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrMpuAudPlyParam *)
     protobuf_c_message_unpack (&tpb_nvr_mpu_aud_ply_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_mpu_aud_ply_param__free_unpacked
                     (TPbNvrMpuAudPlyParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_aud_ply_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_mpu_aud_ply_cfg__init
                     (TPbNvrMpuAudPlyCfg         *message)
{
  static TPbNvrMpuAudPlyCfg init_value = TPB_NVR_MPU_AUD_PLY_CFG__INIT;
  *message = init_value;
}
size_t tpb_nvr_mpu_aud_ply_cfg__get_packed_size
                     (const TPbNvrMpuAudPlyCfg *message)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_aud_ply_cfg__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_mpu_aud_ply_cfg__pack
                     (const TPbNvrMpuAudPlyCfg *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_aud_ply_cfg__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_mpu_aud_ply_cfg__pack_to_buffer
                     (const TPbNvrMpuAudPlyCfg *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_aud_ply_cfg__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrMpuAudPlyCfg *
       tpb_nvr_mpu_aud_ply_cfg__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrMpuAudPlyCfg *)
     protobuf_c_message_unpack (&tpb_nvr_mpu_aud_ply_cfg__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_mpu_aud_ply_cfg__free_unpacked
                     (TPbNvrMpuAudPlyCfg *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_mpu_aud_ply_cfg__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
static const ProtobufCFieldDescriptor tpb_nvr_mpu_vid_resolution_param__field_descriptors[2] =
{
  {
    "width",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuVidResolutionParam, has_width),
    offsetof(TPbNvrMpuVidResolutionParam, width),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "height",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuVidResolutionParam, has_height),
    offsetof(TPbNvrMpuVidResolutionParam, height),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_mpu_vid_resolution_param__field_indices_by_name[] = {
  1,   /* field[1] = height */
  0,   /* field[0] = width */
};
static const ProtobufCIntRange tpb_nvr_mpu_vid_resolution_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_mpu_vid_resolution_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrMpuVidResolutionParam",
  "TPbNvrMpuVidResolutionParam",
  "TPbNvrMpuVidResolutionParam",
  "",
  sizeof(TPbNvrMpuVidResolutionParam),
  2,
  tpb_nvr_mpu_vid_resolution_param__field_descriptors,
  tpb_nvr_mpu_vid_resolution_param__field_indices_by_name,
  1,  tpb_nvr_mpu_vid_resolution_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_mpu_vid_resolution_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_mpu_vid_dec_window_param__field_descriptors[14] =
{
  {
    "priority",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuVidDecWindowParam, has_priority),
    offsetof(TPbNvrMpuVidDecWindowParam, priority),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "startX",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuVidDecWindowParam, has_startx),
    offsetof(TPbNvrMpuVidDecWindowParam, startx),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "startY",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuVidDecWindowParam, has_starty),
    offsetof(TPbNvrMpuVidDecWindowParam, starty),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "width",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuVidDecWindowParam, has_width),
    offsetof(TPbNvrMpuVidDecWindowParam, width),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "height",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuVidDecWindowParam, has_height),
    offsetof(TPbNvrMpuVidDecWindowParam, height),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "freeze",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrMpuVidDecWindowParam, has_freeze),
    offsetof(TPbNvrMpuVidDecWindowParam, freeze),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "chn_id",
    7,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuVidDecWindowParam, has_chn_id),
    offsetof(TPbNvrMpuVidDecWindowParam, chn_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "enc_id",
    8,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuVidDecWindowParam, has_enc_id),
    offsetof(TPbNvrMpuVidDecWindowParam, enc_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "vid_dec_mode",
    9,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_ENUM,
    offsetof(TPbNvrMpuVidDecWindowParam, has_vid_dec_mode),
    offsetof(TPbNvrMpuVidDecWindowParam, vid_dec_mode),
    &em_nvr_mpu_vid_dec_mode__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "choose_strategy",
    10,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_ENUM,
    offsetof(TPbNvrMpuVidDecWindowParam, has_choose_strategy),
    offsetof(TPbNvrMpuVidDecWindowParam, choose_strategy),
    &em_nvr_view_choose_enc_id_strategy__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "win_id",
    11,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuVidDecWindowParam, has_win_id),
    offsetof(TPbNvrMpuVidDecWindowParam, win_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "bind_enable",
    12,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrMpuVidDecWindowParam, has_bind_enable),
    offsetof(TPbNvrMpuVidDecWindowParam, bind_enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "show_enable",
    13,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrMpuVidDecWindowParam, has_show_enable),
    offsetof(TPbNvrMpuVidDecWindowParam, show_enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "caller_type",
    14,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_ENUM,
    offsetof(TPbNvrMpuVidDecWindowParam, has_caller_type),
    offsetof(TPbNvrMpuVidDecWindowParam, caller_type),
    &em_nvr_mpu_dec_caller_type__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_mpu_vid_dec_window_param__field_indices_by_name[] = {
  11,   /* field[11] = bind_enable */
  13,   /* field[13] = caller_type */
  6,   /* field[6] = chn_id */
  9,   /* field[9] = choose_strategy */
  7,   /* field[7] = enc_id */
  5,   /* field[5] = freeze */
  4,   /* field[4] = height */
  0,   /* field[0] = priority */
  12,   /* field[12] = show_enable */
  1,   /* field[1] = startX */
  2,   /* field[2] = startY */
  8,   /* field[8] = vid_dec_mode */
  3,   /* field[3] = width */
  10,   /* field[10] = win_id */
};
static const ProtobufCIntRange tpb_nvr_mpu_vid_dec_window_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 14 }
};
const ProtobufCMessageDescriptor tpb_nvr_mpu_vid_dec_window_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrMpuVidDecWindowParam",
  "TPbNvrMpuVidDecWindowParam",
  "TPbNvrMpuVidDecWindowParam",
  "",
  sizeof(TPbNvrMpuVidDecWindowParam),
  14,
  tpb_nvr_mpu_vid_dec_window_param__field_descriptors,
  tpb_nvr_mpu_vid_dec_window_param__field_indices_by_name,
  1,  tpb_nvr_mpu_vid_dec_window_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_mpu_vid_dec_window_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_mpu_vid_dis_dev_param__field_descriptors[10] =
{
  {
    "enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrMpuVidDisDevParam, has_enable),
    offsetof(TPbNvrMpuVidDisDevParam, enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "res",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrMpuVidDisDevParam, res),
    &tpb_nvr_mpu_vid_resolution_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "dis_ref_rate",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_ENUM,
    offsetof(TPbNvrMpuVidDisDevParam, has_dis_ref_rate),
    offsetof(TPbNvrMpuVidDisDevParam, dis_ref_rate),
    &em_nvr_display_refresh_rate__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "bg_color",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_ENUM,
    offsetof(TPbNvrMpuVidDisDevParam, has_bg_color),
    offsetof(TPbNvrMpuVidDisDevParam, bg_color),
    &em_nvr_mpu_bg_color__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "brightness",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuVidDisDevParam, has_brightness),
    offsetof(TPbNvrMpuVidDisDevParam, brightness),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "contast",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuVidDisDevParam, has_contast),
    offsetof(TPbNvrMpuVidDisDevParam, contast),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "saturation",
    7,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuVidDisDevParam, has_saturation),
    offsetof(TPbNvrMpuVidDisDevParam, saturation),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "hue",
    8,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuVidDisDevParam, has_hue),
    offsetof(TPbNvrMpuVidDisDevParam, hue),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "dis_mode_type",
    9,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_ENUM,
    offsetof(TPbNvrMpuVidDisDevParam, has_dis_mode_type),
    offsetof(TPbNvrMpuVidDisDevParam, dis_mode_type),
    &em_nvr_mpu_dis_mode_type__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "background_color",
    10,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuVidDisDevParam, has_background_color),
    offsetof(TPbNvrMpuVidDisDevParam, background_color),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_mpu_vid_dis_dev_param__field_indices_by_name[] = {
  9,   /* field[9] = background_color */
  3,   /* field[3] = bg_color */
  4,   /* field[4] = brightness */
  5,   /* field[5] = contast */
  8,   /* field[8] = dis_mode_type */
  2,   /* field[2] = dis_ref_rate */
  0,   /* field[0] = enable */
  7,   /* field[7] = hue */
  1,   /* field[1] = res */
  6,   /* field[6] = saturation */
};
static const ProtobufCIntRange tpb_nvr_mpu_vid_dis_dev_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 10 }
};
const ProtobufCMessageDescriptor tpb_nvr_mpu_vid_dis_dev_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrMpuVidDisDevParam",
  "TPbNvrMpuVidDisDevParam",
  "TPbNvrMpuVidDisDevParam",
  "",
  sizeof(TPbNvrMpuVidDisDevParam),
  10,
  tpb_nvr_mpu_vid_dis_dev_param__field_descriptors,
  tpb_nvr_mpu_vid_dis_dev_param__field_indices_by_name,
  1,  tpb_nvr_mpu_vid_dis_dev_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_mpu_vid_dis_dev_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_mpu_vid_dis_layout_info__field_descriptors[12] =
{
  {
    "dis_dev_type",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_ENUM,
    offsetof(TPbNvrMpuVidDisLayoutInfo, has_dis_dev_type),
    offsetof(TPbNvrMpuVidDisLayoutInfo, dis_dev_type),
    &em_pb_nvr_mpu_vid_dis_dev_type__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "overlay",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrMpuVidDisLayoutInfo, has_overlay),
    offsetof(TPbNvrMpuVidDisLayoutInfo, overlay),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "layout",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_ENUM,
    offsetof(TPbNvrMpuVidDisLayoutInfo, has_layout),
    offsetof(TPbNvrMpuVidDisLayoutInfo, layout),
    &em_pb_nvr_mpu_gui_layout__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "dec_win",
    4,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrMpuVidDisLayoutInfo, n_dec_win),
    offsetof(TPbNvrMpuVidDisLayoutInfo, dec_win),
    &tpb_nvr_mpu_vid_dec_window_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "scale_mode",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_ENUM,
    offsetof(TPbNvrMpuVidDisLayoutInfo, has_scale_mode),
    offsetof(TPbNvrMpuVidDisLayoutInfo, scale_mode),
    &em_nvr_mpu_vid_scale_mode__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "dis_dev_param",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrMpuVidDisLayoutInfo, dis_dev_param),
    &tpb_nvr_mpu_vid_dis_dev_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "bg_color",
    7,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_ENUM,
    offsetof(TPbNvrMpuVidDisLayoutInfo, has_bg_color),
    offsetof(TPbNvrMpuVidDisLayoutInfo, bg_color),
    &em_nvr_mpu_bg_color__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "dec_rect_start_x",
    8,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuVidDisLayoutInfo, has_dec_rect_start_x),
    offsetof(TPbNvrMpuVidDisLayoutInfo, dec_rect_start_x),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "dec_rect_start_y",
    9,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuVidDisLayoutInfo, has_dec_rect_start_y),
    offsetof(TPbNvrMpuVidDisLayoutInfo, dec_rect_start_y),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "dec_win_total_width",
    10,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuVidDisLayoutInfo, has_dec_win_total_width),
    offsetof(TPbNvrMpuVidDisLayoutInfo, dec_win_total_width),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "dec_win_total_heigt",
    11,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuVidDisLayoutInfo, has_dec_win_total_heigt),
    offsetof(TPbNvrMpuVidDisLayoutInfo, dec_win_total_heigt),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "background_color",
    12,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuVidDisLayoutInfo, has_background_color),
    offsetof(TPbNvrMpuVidDisLayoutInfo, background_color),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_mpu_vid_dis_layout_info__field_indices_by_name[] = {
  11,   /* field[11] = background_color */
  6,   /* field[6] = bg_color */
  7,   /* field[7] = dec_rect_start_x */
  8,   /* field[8] = dec_rect_start_y */
  3,   /* field[3] = dec_win */
  10,   /* field[10] = dec_win_total_heigt */
  9,   /* field[9] = dec_win_total_width */
  5,   /* field[5] = dis_dev_param */
  0,   /* field[0] = dis_dev_type */
  2,   /* field[2] = layout */
  1,   /* field[1] = overlay */
  4,   /* field[4] = scale_mode */
};
static const ProtobufCIntRange tpb_nvr_mpu_vid_dis_layout_info__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 12 }
};
const ProtobufCMessageDescriptor tpb_nvr_mpu_vid_dis_layout_info__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrMpuVidDisLayoutInfo",
  "TPbNvrMpuVidDisLayoutInfo",
  "TPbNvrMpuVidDisLayoutInfo",
  "",
  sizeof(TPbNvrMpuVidDisLayoutInfo),
  12,
  tpb_nvr_mpu_vid_dis_layout_info__field_descriptors,
  tpb_nvr_mpu_vid_dis_layout_info__field_indices_by_name,
  1,  tpb_nvr_mpu_vid_dis_layout_info__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_mpu_vid_dis_layout_info__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_mpu_vid_dis_layout_cfg__field_descriptors[1] =
{
  {
    "vid_dis_layout",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrMpuVidDisLayoutCfg, n_vid_dis_layout),
    offsetof(TPbNvrMpuVidDisLayoutCfg, vid_dis_layout),
    &tpb_nvr_mpu_vid_dis_layout_info__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_mpu_vid_dis_layout_cfg__field_indices_by_name[] = {
  0,   /* field[0] = vid_dis_layout */
};
static const ProtobufCIntRange tpb_nvr_mpu_vid_dis_layout_cfg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor tpb_nvr_mpu_vid_dis_layout_cfg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrMpuVidDisLayoutCfg",
  "TPbNvrMpuVidDisLayoutCfg",
  "TPbNvrMpuVidDisLayoutCfg",
  "",
  sizeof(TPbNvrMpuVidDisLayoutCfg),
  1,
  tpb_nvr_mpu_vid_dis_layout_cfg__field_descriptors,
  tpb_nvr_mpu_vid_dis_layout_cfg__field_indices_by_name,
  1,  tpb_nvr_mpu_vid_dis_layout_cfg__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_mpu_vid_dis_layout_cfg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_mpu_gui_menu_output_dst_cfg__field_descriptors[2] =
{
  {
    "dis_dev_type",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_ENUM,
    offsetof(TPbNvrMpuGuiMenuOutputDstCfg, has_dis_dev_type),
    offsetof(TPbNvrMpuGuiMenuOutputDstCfg, dis_dev_type),
    &em_pb_nvr_mpu_vid_dis_dev_type__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "dis_dev_id",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuGuiMenuOutputDstCfg, has_dis_dev_id),
    offsetof(TPbNvrMpuGuiMenuOutputDstCfg, dis_dev_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_mpu_gui_menu_output_dst_cfg__field_indices_by_name[] = {
  1,   /* field[1] = dis_dev_id */
  0,   /* field[0] = dis_dev_type */
};
static const ProtobufCIntRange tpb_nvr_mpu_gui_menu_output_dst_cfg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_mpu_gui_menu_output_dst_cfg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrMpuGuiMenuOutputDstCfg",
  "TPbNvrMpuGuiMenuOutputDstCfg",
  "TPbNvrMpuGuiMenuOutputDstCfg",
  "",
  sizeof(TPbNvrMpuGuiMenuOutputDstCfg),
  2,
  tpb_nvr_mpu_gui_menu_output_dst_cfg__field_descriptors,
  tpb_nvr_mpu_gui_menu_output_dst_cfg__field_indices_by_name,
  1,  tpb_nvr_mpu_gui_menu_output_dst_cfg__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_mpu_gui_menu_output_dst_cfg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_mpu_other_cfg__field_descriptors[3] =
{
  {
    "dec_mode",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_ENUM,
    offsetof(TPbNvrMpuOtherCfg, n_dec_mode),
    offsetof(TPbNvrMpuOtherCfg, dec_mode),
    &em_nvr_mpu_vid_dec_mode__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "gui_output_dst_cfg",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrMpuOtherCfg, gui_output_dst_cfg),
    &tpb_nvr_mpu_gui_menu_output_dst_cfg__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "dis_vid_dec_effect",
    3,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrMpuOtherCfg, n_dis_vid_dec_effect),
    offsetof(TPbNvrMpuOtherCfg, dis_vid_dec_effect),
    &tpb_nvr_mpu_vid_dec_effect__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_mpu_other_cfg__field_indices_by_name[] = {
  0,   /* field[0] = dec_mode */
  2,   /* field[2] = dis_vid_dec_effect */
  1,   /* field[1] = gui_output_dst_cfg */
};
static const ProtobufCIntRange tpb_nvr_mpu_other_cfg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 3 }
};
const ProtobufCMessageDescriptor tpb_nvr_mpu_other_cfg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrMpuOtherCfg",
  "TPbNvrMpuOtherCfg",
  "TPbNvrMpuOtherCfg",
  "",
  sizeof(TPbNvrMpuOtherCfg),
  3,
  tpb_nvr_mpu_other_cfg__field_descriptors,
  tpb_nvr_mpu_other_cfg__field_indices_by_name,
  1,  tpb_nvr_mpu_other_cfg__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_mpu_other_cfg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_mpu_aud_param_cfg__field_descriptors[3] =
{
  {
    "aud_enc_volume",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuAudParamCfg, n_aud_enc_volume),
    offsetof(TPbNvrMpuAudParamCfg, aud_enc_volume),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "aud_dec_volume",
    2,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuAudParamCfg, n_aud_dec_volume),
    offsetof(TPbNvrMpuAudParamCfg, aud_dec_volume),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "aud_mute_status",
    3,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuAudParamCfg, n_aud_mute_status),
    offsetof(TPbNvrMpuAudParamCfg, aud_mute_status),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_mpu_aud_param_cfg__field_indices_by_name[] = {
  1,   /* field[1] = aud_dec_volume */
  0,   /* field[0] = aud_enc_volume */
  2,   /* field[2] = aud_mute_status */
};
static const ProtobufCIntRange tpb_nvr_mpu_aud_param_cfg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 3 }
};
const ProtobufCMessageDescriptor tpb_nvr_mpu_aud_param_cfg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrMpuAudParamCfg",
  "TPbNvrMpuAudParamCfg",
  "TPbNvrMpuAudParamCfg",
  "",
  sizeof(TPbNvrMpuAudParamCfg),
  3,
  tpb_nvr_mpu_aud_param_cfg__field_descriptors,
  tpb_nvr_mpu_aud_param_cfg__field_indices_by_name,
  1,  tpb_nvr_mpu_aud_param_cfg__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_mpu_aud_param_cfg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_mpu_crop_rect_region__field_descriptors[4] =
{
  {
    "startX",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuCropRectRegion, has_startx),
    offsetof(TPbNvrMpuCropRectRegion, startx),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "startY",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuCropRectRegion, has_starty),
    offsetof(TPbNvrMpuCropRectRegion, starty),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "width",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuCropRectRegion, has_width),
    offsetof(TPbNvrMpuCropRectRegion, width),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "height",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuCropRectRegion, has_height),
    offsetof(TPbNvrMpuCropRectRegion, height),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_mpu_crop_rect_region__field_indices_by_name[] = {
  3,   /* field[3] = height */
  0,   /* field[0] = startX */
  1,   /* field[1] = startY */
  2,   /* field[2] = width */
};
static const ProtobufCIntRange tpb_nvr_mpu_crop_rect_region__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 4 }
};
const ProtobufCMessageDescriptor tpb_nvr_mpu_crop_rect_region__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrMpuCropRectRegion",
  "TPbNvrMpuCropRectRegion",
  "TPbNvrMpuCropRectRegion",
  "",
  sizeof(TPbNvrMpuCropRectRegion),
  4,
  tpb_nvr_mpu_crop_rect_region__field_descriptors,
  tpb_nvr_mpu_crop_rect_region__field_indices_by_name,
  1,  tpb_nvr_mpu_crop_rect_region__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_mpu_crop_rect_region__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_mpu_vid_dis_dev__field_descriptors[9] =
{
  {
    "enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrMpuVidDisDev, has_enable),
    offsetof(TPbNvrMpuVidDisDev, enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "res",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrMpuVidDisDev, res),
    &tpb_nvr_mpu_vid_resolution_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "dis_ref_rate",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_ENUM,
    offsetof(TPbNvrMpuVidDisDev, has_dis_ref_rate),
    offsetof(TPbNvrMpuVidDisDev, dis_ref_rate),
    &em_nvr_display_refresh_rate__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "background_color",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuVidDisDev, has_background_color),
    offsetof(TPbNvrMpuVidDisDev, background_color),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "dis_mode_type",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_ENUM,
    offsetof(TPbNvrMpuVidDisDev, has_dis_mode_type),
    offsetof(TPbNvrMpuVidDisDev, dis_mode_type),
    &em_nvr_mpu_dis_mode_type__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "brightness",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuVidDisDev, has_brightness),
    offsetof(TPbNvrMpuVidDisDev, brightness),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "contast",
    7,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuVidDisDev, has_contast),
    offsetof(TPbNvrMpuVidDisDev, contast),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "saturation",
    8,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuVidDisDev, has_saturation),
    offsetof(TPbNvrMpuVidDisDev, saturation),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "hue",
    9,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuVidDisDev, has_hue),
    offsetof(TPbNvrMpuVidDisDev, hue),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_mpu_vid_dis_dev__field_indices_by_name[] = {
  3,   /* field[3] = background_color */
  5,   /* field[5] = brightness */
  6,   /* field[6] = contast */
  4,   /* field[4] = dis_mode_type */
  2,   /* field[2] = dis_ref_rate */
  0,   /* field[0] = enable */
  8,   /* field[8] = hue */
  1,   /* field[1] = res */
  7,   /* field[7] = saturation */
};
static const ProtobufCIntRange tpb_nvr_mpu_vid_dis_dev__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 9 }
};
const ProtobufCMessageDescriptor tpb_nvr_mpu_vid_dis_dev__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrMpuVidDisDev",
  "TPbNvrMpuVidDisDev",
  "TPbNvrMpuVidDisDev",
  "",
  sizeof(TPbNvrMpuVidDisDev),
  9,
  tpb_nvr_mpu_vid_dis_dev__field_descriptors,
  tpb_nvr_mpu_vid_dis_dev__field_indices_by_name,
  1,  tpb_nvr_mpu_vid_dis_dev__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_mpu_vid_dis_dev__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_mpu_win_border__field_descriptors[4] =
{
  {
    "top",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuWinBorder, has_top),
    offsetof(TPbNvrMpuWinBorder, top),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "bottom",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuWinBorder, has_bottom),
    offsetof(TPbNvrMpuWinBorder, bottom),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "left",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuWinBorder, has_left),
    offsetof(TPbNvrMpuWinBorder, left),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "right",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuWinBorder, has_right),
    offsetof(TPbNvrMpuWinBorder, right),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_mpu_win_border__field_indices_by_name[] = {
  1,   /* field[1] = bottom */
  2,   /* field[2] = left */
  3,   /* field[3] = right */
  0,   /* field[0] = top */
};
static const ProtobufCIntRange tpb_nvr_mpu_win_border__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 4 }
};
const ProtobufCMessageDescriptor tpb_nvr_mpu_win_border__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrMpuWinBorder",
  "TPbNvrMpuWinBorder",
  "TPbNvrMpuWinBorder",
  "",
  sizeof(TPbNvrMpuWinBorder),
  4,
  tpb_nvr_mpu_win_border__field_descriptors,
  tpb_nvr_mpu_win_border__field_indices_by_name,
  1,  tpb_nvr_mpu_win_border__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_mpu_win_border__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_mpu_vid_dec_window__field_descriptors[13] =
{
  {
    "win_id",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuVidDecWindow, has_win_id),
    offsetof(TPbNvrMpuVidDecWindow, win_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "priority",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuVidDecWindow, has_priority),
    offsetof(TPbNvrMpuVidDecWindow, priority),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "startX",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuVidDecWindow, has_startx),
    offsetof(TPbNvrMpuVidDecWindow, startx),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "startY",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuVidDecWindow, has_starty),
    offsetof(TPbNvrMpuVidDecWindow, starty),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "width",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuVidDecWindow, has_width),
    offsetof(TPbNvrMpuVidDecWindow, width),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "height",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuVidDecWindow, has_height),
    offsetof(TPbNvrMpuVidDecWindow, height),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "win_border_enable",
    7,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrMpuVidDecWindow, has_win_border_enable),
    offsetof(TPbNvrMpuVidDecWindow, win_border_enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "obsolete_border_width",
    8,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuVidDecWindow, has_obsolete_border_width),
    offsetof(TPbNvrMpuVidDecWindow, obsolete_border_width),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "border_color",
    9,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuVidDecWindow, has_border_color),
    offsetof(TPbNvrMpuVidDecWindow, border_color),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "win_enable",
    10,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrMpuVidDecWindow, has_win_enable),
    offsetof(TPbNvrMpuVidDecWindow, win_enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "crop_enable",
    11,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrMpuVidDecWindow, has_crop_enable),
    offsetof(TPbNvrMpuVidDecWindow, crop_enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "crop_region",
    12,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrMpuVidDecWindow, crop_region),
    &tpb_nvr_mpu_crop_rect_region__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "win_border_width",
    13,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrMpuVidDecWindow, win_border_width),
    &tpb_nvr_mpu_win_border__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_mpu_vid_dec_window__field_indices_by_name[] = {
  8,   /* field[8] = border_color */
  10,   /* field[10] = crop_enable */
  11,   /* field[11] = crop_region */
  5,   /* field[5] = height */
  7,   /* field[7] = obsolete_border_width */
  1,   /* field[1] = priority */
  2,   /* field[2] = startX */
  3,   /* field[3] = startY */
  4,   /* field[4] = width */
  6,   /* field[6] = win_border_enable */
  12,   /* field[12] = win_border_width */
  9,   /* field[9] = win_enable */
  0,   /* field[0] = win_id */
};
static const ProtobufCIntRange tpb_nvr_mpu_vid_dec_window__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 13 }
};
const ProtobufCMessageDescriptor tpb_nvr_mpu_vid_dec_window__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrMpuVidDecWindow",
  "TPbNvrMpuVidDecWindow",
  "TPbNvrMpuVidDecWindow",
  "",
  sizeof(TPbNvrMpuVidDecWindow),
  13,
  tpb_nvr_mpu_vid_dec_window__field_descriptors,
  tpb_nvr_mpu_vid_dec_window__field_indices_by_name,
  1,  tpb_nvr_mpu_vid_dec_window__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_mpu_vid_dec_window__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_mpu_vid_dis_layout_param__field_descriptors[8] =
{
  {
    "window_num",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuVidDisLayoutParam, has_window_num),
    offsetof(TPbNvrMpuVidDisLayoutParam, window_num),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "dec_win_param",
    2,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrMpuVidDisLayoutParam, n_dec_win_param),
    offsetof(TPbNvrMpuVidDisLayoutParam, dec_win_param),
    &tpb_nvr_mpu_vid_dec_window__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "dec_rect_start_x",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuVidDisLayoutParam, has_dec_rect_start_x),
    offsetof(TPbNvrMpuVidDisLayoutParam, dec_rect_start_x),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "dec_rect_start_y",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuVidDisLayoutParam, has_dec_rect_start_y),
    offsetof(TPbNvrMpuVidDisLayoutParam, dec_rect_start_y),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "dec_win_total_width",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuVidDisLayoutParam, has_dec_win_total_width),
    offsetof(TPbNvrMpuVidDisLayoutParam, dec_win_total_width),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "dec_win_total_heigt",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuVidDisLayoutParam, has_dec_win_total_heigt),
    offsetof(TPbNvrMpuVidDisLayoutParam, dec_win_total_heigt),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "layout",
    7,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_ENUM,
    offsetof(TPbNvrMpuVidDisLayoutParam, has_layout),
    offsetof(TPbNvrMpuVidDisLayoutParam, layout),
    &em_pb_nvr_mpu_gui_layout__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "overlay",
    8,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrMpuVidDisLayoutParam, has_overlay),
    offsetof(TPbNvrMpuVidDisLayoutParam, overlay),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_mpu_vid_dis_layout_param__field_indices_by_name[] = {
  2,   /* field[2] = dec_rect_start_x */
  3,   /* field[3] = dec_rect_start_y */
  1,   /* field[1] = dec_win_param */
  5,   /* field[5] = dec_win_total_heigt */
  4,   /* field[4] = dec_win_total_width */
  6,   /* field[6] = layout */
  7,   /* field[7] = overlay */
  0,   /* field[0] = window_num */
};
static const ProtobufCIntRange tpb_nvr_mpu_vid_dis_layout_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 8 }
};
const ProtobufCMessageDescriptor tpb_nvr_mpu_vid_dis_layout_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrMpuVidDisLayoutParam",
  "TPbNvrMpuVidDisLayoutParam",
  "TPbNvrMpuVidDisLayoutParam",
  "",
  sizeof(TPbNvrMpuVidDisLayoutParam),
  8,
  tpb_nvr_mpu_vid_dis_layout_param__field_descriptors,
  tpb_nvr_mpu_vid_dis_layout_param__field_indices_by_name,
  1,  tpb_nvr_mpu_vid_dis_layout_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_mpu_vid_dis_layout_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_mpu_vid_dec_window_info__field_descriptors[5] =
{
  {
    "bind_enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrMpuVidDecWindowInfo, has_bind_enable),
    offsetof(TPbNvrMpuVidDecWindowInfo, bind_enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "not_auto_chn",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrMpuVidDecWindowInfo, has_not_auto_chn),
    offsetof(TPbNvrMpuVidDecWindowInfo, not_auto_chn),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "chn_no",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuVidDecWindowInfo, has_chn_no),
    offsetof(TPbNvrMpuVidDecWindowInfo, chn_no),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "enc_id",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuVidDecWindowInfo, has_enc_id),
    offsetof(TPbNvrMpuVidDecWindowInfo, enc_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "freeze",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrMpuVidDecWindowInfo, has_freeze),
    offsetof(TPbNvrMpuVidDecWindowInfo, freeze),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_mpu_vid_dec_window_info__field_indices_by_name[] = {
  0,   /* field[0] = bind_enable */
  2,   /* field[2] = chn_no */
  3,   /* field[3] = enc_id */
  4,   /* field[4] = freeze */
  1,   /* field[1] = not_auto_chn */
};
static const ProtobufCIntRange tpb_nvr_mpu_vid_dec_window_info__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 5 }
};
const ProtobufCMessageDescriptor tpb_nvr_mpu_vid_dec_window_info__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrMpuVidDecWindowInfo",
  "TPbNvrMpuVidDecWindowInfo",
  "TPbNvrMpuVidDecWindowInfo",
  "",
  sizeof(TPbNvrMpuVidDecWindowInfo),
  5,
  tpb_nvr_mpu_vid_dec_window_info__field_descriptors,
  tpb_nvr_mpu_vid_dec_window_info__field_indices_by_name,
  1,  tpb_nvr_mpu_vid_dec_window_info__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_mpu_vid_dec_window_info__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_mpu_chn_dec_mode__field_descriptors[1] =
{
  {
    "dec_mode",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_ENUM,
    offsetof(TPbNvrMpuChnDecMode, has_dec_mode),
    offsetof(TPbNvrMpuChnDecMode, dec_mode),
    &em_nvr_mpu_vid_dec_mode__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_mpu_chn_dec_mode__field_indices_by_name[] = {
  0,   /* field[0] = dec_mode */
};
static const ProtobufCIntRange tpb_nvr_mpu_chn_dec_mode__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor tpb_nvr_mpu_chn_dec_mode__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrMpuChnDecMode",
  "TPbNvrMpuChnDecMode",
  "TPbNvrMpuChnDecMode",
  "",
  sizeof(TPbNvrMpuChnDecMode),
  1,
  tpb_nvr_mpu_chn_dec_mode__field_descriptors,
  tpb_nvr_mpu_chn_dec_mode__field_indices_by_name,
  1,  tpb_nvr_mpu_chn_dec_mode__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_mpu_chn_dec_mode__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_mpu_div_dec_mode__field_descriptors[1] =
{
  {
    "chn_dec_mode",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrMpuDivDecMode, n_chn_dec_mode),
    offsetof(TPbNvrMpuDivDecMode, chn_dec_mode),
    &tpb_nvr_mpu_chn_dec_mode__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_mpu_div_dec_mode__field_indices_by_name[] = {
  0,   /* field[0] = chn_dec_mode */
};
static const ProtobufCIntRange tpb_nvr_mpu_div_dec_mode__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor tpb_nvr_mpu_div_dec_mode__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrMpuDivDecMode",
  "TPbNvrMpuDivDecMode",
  "TPbNvrMpuDivDecMode",
  "",
  sizeof(TPbNvrMpuDivDecMode),
  1,
  tpb_nvr_mpu_div_dec_mode__field_descriptors,
  tpb_nvr_mpu_div_dec_mode__field_indices_by_name,
  1,  tpb_nvr_mpu_div_dec_mode__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_mpu_div_dec_mode__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_mpu_dis_vid_window_info__field_descriptors[1] =
{
  {
    "win_param",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrMpuDisVidWindowInfo, n_win_param),
    offsetof(TPbNvrMpuDisVidWindowInfo, win_param),
    &tpb_nvr_mpu_vid_dec_window_info__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_mpu_dis_vid_window_info__field_indices_by_name[] = {
  0,   /* field[0] = win_param */
};
static const ProtobufCIntRange tpb_nvr_mpu_dis_vid_window_info__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor tpb_nvr_mpu_dis_vid_window_info__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrMpuDisVidWindowInfo",
  "TPbNvrMpuDisVidWindowInfo",
  "TPbNvrMpuDisVidWindowInfo",
  "",
  sizeof(TPbNvrMpuDisVidWindowInfo),
  1,
  tpb_nvr_mpu_dis_vid_window_info__field_descriptors,
  tpb_nvr_mpu_dis_vid_window_info__field_indices_by_name,
  1,  tpb_nvr_mpu_dis_vid_window_info__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_mpu_dis_vid_window_info__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_mpu_mgr_param__field_descriptors[5] =
{
  {
    "vid_dis_layout_param",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrMpuMgrParam, n_vid_dis_layout_param),
    offsetof(TPbNvrMpuMgrParam, vid_dis_layout_param),
    &tpb_nvr_mpu_vid_dis_layout_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "vid_dis_dev_param",
    2,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrMpuMgrParam, n_vid_dis_dev_param),
    offsetof(TPbNvrMpuMgrParam, vid_dis_dev_param),
    &tpb_nvr_mpu_vid_dis_dev__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "scale_mode",
    3,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_ENUM,
    offsetof(TPbNvrMpuMgrParam, n_scale_mode),
    offsetof(TPbNvrMpuMgrParam, scale_mode),
    &em_nvr_mpu_vid_scale_mode__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "background_color",
    4,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuMgrParam, n_background_color),
    offsetof(TPbNvrMpuMgrParam, background_color),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "dec_mode_param",
    5,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrMpuMgrParam, n_dec_mode_param),
    offsetof(TPbNvrMpuMgrParam, dec_mode_param),
    &tpb_nvr_mpu_div_dec_mode__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_mpu_mgr_param__field_indices_by_name[] = {
  3,   /* field[3] = background_color */
  4,   /* field[4] = dec_mode_param */
  2,   /* field[2] = scale_mode */
  1,   /* field[1] = vid_dis_dev_param */
  0,   /* field[0] = vid_dis_layout_param */
};
static const ProtobufCIntRange tpb_nvr_mpu_mgr_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 5 }
};
const ProtobufCMessageDescriptor tpb_nvr_mpu_mgr_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrMpuMgrParam",
  "TPbNvrMpuMgrParam",
  "TPbNvrMpuMgrParam",
  "",
  sizeof(TPbNvrMpuMgrParam),
  5,
  tpb_nvr_mpu_mgr_param__field_descriptors,
  tpb_nvr_mpu_mgr_param__field_indices_by_name,
  1,  tpb_nvr_mpu_mgr_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_mpu_mgr_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_mpu_win_info_param__field_descriptors[1] =
{
  {
    "win_info",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrMpuWinInfoParam, n_win_info),
    offsetof(TPbNvrMpuWinInfoParam, win_info),
    &tpb_nvr_mpu_dis_vid_window_info__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_mpu_win_info_param__field_indices_by_name[] = {
  0,   /* field[0] = win_info */
};
static const ProtobufCIntRange tpb_nvr_mpu_win_info_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor tpb_nvr_mpu_win_info_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrMpuWinInfoParam",
  "TPbNvrMpuWinInfoParam",
  "TPbNvrMpuWinInfoParam",
  "",
  sizeof(TPbNvrMpuWinInfoParam),
  1,
  tpb_nvr_mpu_win_info_param__field_descriptors,
  tpb_nvr_mpu_win_info_param__field_indices_by_name,
  1,  tpb_nvr_mpu_win_info_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_mpu_win_info_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_mpu_vid_dec_effect__field_descriptors[3] =
{
  {
    "bDCI",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrMpuVidDecEffect, has_bdci),
    offsetof(TPbNvrMpuVidDecEffect, bdci),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "bNR",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrMpuVidDecEffect, has_bnr),
    offsetof(TPbNvrMpuVidDecEffect, bnr),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "bES",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrMpuVidDecEffect, has_bes),
    offsetof(TPbNvrMpuVidDecEffect, bes),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_mpu_vid_dec_effect__field_indices_by_name[] = {
  0,   /* field[0] = bDCI */
  2,   /* field[2] = bES */
  1,   /* field[1] = bNR */
};
static const ProtobufCIntRange tpb_nvr_mpu_vid_dec_effect__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 3 }
};
const ProtobufCMessageDescriptor tpb_nvr_mpu_vid_dec_effect__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrMpuVidDecEffect",
  "TPbNvrMpuVidDecEffect",
  "TPbNvrMpuVidDecEffect",
  "",
  sizeof(TPbNvrMpuVidDecEffect),
  3,
  tpb_nvr_mpu_vid_dec_effect__field_descriptors,
  tpb_nvr_mpu_vid_dec_effect__field_indices_by_name,
  1,  tpb_nvr_mpu_vid_dec_effect__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_mpu_vid_dec_effect__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_mpu_dec_aud_mixer__field_descriptors[8] =
{
  {
    "bEnable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrMpuDecAudMixer, has_benable),
    offsetof(TPbNvrMpuDecAudMixer, benable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "wDelayTime",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuDecAudMixer, has_wdelaytime),
    offsetof(TPbNvrMpuDecAudMixer, wdelaytime),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "wMixChnNum",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuDecAudMixer, has_wmixchnnum),
    offsetof(TPbNvrMpuDecAudMixer, wmixchnnum),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "awMixChnId",
    4,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuDecAudMixer, n_awmixchnid),
    offsetof(TPbNvrMpuDecAudMixer, awmixchnid),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "bCopyToZeroChn",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrMpuDecAudMixer, has_bcopytozerochn),
    offsetof(TPbNvrMpuDecAudMixer, bcopytozerochn),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "eEncType",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuDecAudMixer, has_eenctype),
    offsetof(TPbNvrMpuDecAudMixer, eenctype),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "eSampleRate",
    7,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuDecAudMixer, has_esamplerate),
    offsetof(TPbNvrMpuDecAudMixer, esamplerate),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "wAudEncVolume",
    8,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuDecAudMixer, has_waudencvolume),
    offsetof(TPbNvrMpuDecAudMixer, waudencvolume),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_mpu_dec_aud_mixer__field_indices_by_name[] = {
  3,   /* field[3] = awMixChnId */
  4,   /* field[4] = bCopyToZeroChn */
  0,   /* field[0] = bEnable */
  5,   /* field[5] = eEncType */
  6,   /* field[6] = eSampleRate */
  7,   /* field[7] = wAudEncVolume */
  1,   /* field[1] = wDelayTime */
  2,   /* field[2] = wMixChnNum */
};
static const ProtobufCIntRange tpb_nvr_mpu_dec_aud_mixer__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 8 }
};
const ProtobufCMessageDescriptor tpb_nvr_mpu_dec_aud_mixer__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrMpuDecAudMixer",
  "TPbNvrMpuDecAudMixer",
  "TPbNvrMpuDecAudMixer",
  "",
  sizeof(TPbNvrMpuDecAudMixer),
  8,
  tpb_nvr_mpu_dec_aud_mixer__field_descriptors,
  tpb_nvr_mpu_dec_aud_mixer__field_indices_by_name,
  1,  tpb_nvr_mpu_dec_aud_mixer__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_mpu_dec_aud_mixer__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_mpu_aud_ply_param__field_descriptors[3] =
{
  {
    "eAudPlyInterface",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuAudPlyParam, has_eaudplyinterface),
    offsetof(TPbNvrMpuAudPlyParam, eaudplyinterface),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "byVolume",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrMpuAudPlyParam, has_byvolume),
    offsetof(TPbNvrMpuAudPlyParam, byvolume),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "bMute",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrMpuAudPlyParam, has_bmute),
    offsetof(TPbNvrMpuAudPlyParam, bmute),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_mpu_aud_ply_param__field_indices_by_name[] = {
  2,   /* field[2] = bMute */
  1,   /* field[1] = byVolume */
  0,   /* field[0] = eAudPlyInterface */
};
static const ProtobufCIntRange tpb_nvr_mpu_aud_ply_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 3 }
};
const ProtobufCMessageDescriptor tpb_nvr_mpu_aud_ply_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrMpuAudPlyParam",
  "TPbNvrMpuAudPlyParam",
  "TPbNvrMpuAudPlyParam",
  "",
  sizeof(TPbNvrMpuAudPlyParam),
  3,
  tpb_nvr_mpu_aud_ply_param__field_descriptors,
  tpb_nvr_mpu_aud_ply_param__field_indices_by_name,
  1,  tpb_nvr_mpu_aud_ply_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_mpu_aud_ply_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_mpu_aud_ply_cfg__field_descriptors[1] =
{
  {
    "AudPlyParam",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrMpuAudPlyCfg, n_audplyparam),
    offsetof(TPbNvrMpuAudPlyCfg, audplyparam),
    &tpb_nvr_mpu_aud_ply_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_mpu_aud_ply_cfg__field_indices_by_name[] = {
  0,   /* field[0] = AudPlyParam */
};
static const ProtobufCIntRange tpb_nvr_mpu_aud_ply_cfg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor tpb_nvr_mpu_aud_ply_cfg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrMpuAudPlyCfg",
  "TPbNvrMpuAudPlyCfg",
  "TPbNvrMpuAudPlyCfg",
  "",
  sizeof(TPbNvrMpuAudPlyCfg),
  1,
  tpb_nvr_mpu_aud_ply_cfg__field_descriptors,
  tpb_nvr_mpu_aud_ply_cfg__field_indices_by_name,
  1,  tpb_nvr_mpu_aud_ply_cfg__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_mpu_aud_ply_cfg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCEnumValue em_pb_nvr_mpu_vid_dis_dev_type__enum_values_by_number[5] =
{
  { "HDMI", "EM_PB_NVR_MPU_VID_DIS_DEV_TYPE__HDMI", 0 },
  { "VGA", "EM_PB_NVR_MPU_VID_DIS_DEV_TYPE__VGA", 1 },
  { "CVBS", "EM_PB_NVR_MPU_VID_DIS_DEV_TYPE__CVBS", 2 },
  { "VIRTUAL", "EM_PB_NVR_MPU_VID_DIS_DEV_TYPE__VIRTUAL", 3 },
  { "BT1120", "EM_PB_NVR_MPU_VID_DIS_DEV_TYPE__BT1120", 4 },
};
static const ProtobufCIntRange em_pb_nvr_mpu_vid_dis_dev_type__value_ranges[] = {
{0, 0},{0, 5}
};
static const ProtobufCEnumValueIndex em_pb_nvr_mpu_vid_dis_dev_type__enum_values_by_name[5] =
{
  { "BT1120", 4 },
  { "CVBS", 2 },
  { "HDMI", 0 },
  { "VGA", 1 },
  { "VIRTUAL", 3 },
};
const ProtobufCEnumDescriptor em_pb_nvr_mpu_vid_dis_dev_type__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "EmPbNvrMpuVidDisDevType",
  "EmPbNvrMpuVidDisDevType",
  "EmPbNvrMpuVidDisDevType",
  "",
  5,
  em_pb_nvr_mpu_vid_dis_dev_type__enum_values_by_number,
  5,
  em_pb_nvr_mpu_vid_dis_dev_type__enum_values_by_name,
  1,
  em_pb_nvr_mpu_vid_dis_dev_type__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
static const ProtobufCEnumValue em_pb_nvr_mpu_gui_layout__enum_values_by_number[11] =
{
  { "p1_1x1", "EM_PB_NVR_MPU_GUI_LAYOUT__p1_1x1", 0 },
  { "p2_1x1", "EM_PB_NVR_MPU_GUI_LAYOUT__p2_1x1", 1 },
  { "p4_2x2", "EM_PB_NVR_MPU_GUI_LAYOUT__p4_2x2", 2 },
  { "p9_3x3", "EM_PB_NVR_MPU_GUI_LAYOUT__p9_3x3", 3 },
  { "p16_4x4", "EM_PB_NVR_MPU_GUI_LAYOUT__p16_4x4", 4 },
  { "p25_5x5", "EM_PB_NVR_MPU_GUI_LAYOUT__p25_5x5", 5 },
  { "p36_6x6", "EM_PB_NVR_MPU_GUI_LAYOUT__p36_6x6", 6 },
  { "p49_7x7", "EM_PB_NVR_MPU_GUI_LAYOUT__p49_7x7", 7 },
  { "p64_8x8", "EM_PB_NVR_MPU_GUI_LAYOUT__p64_8x8", 8 },
  { "p6_1xDIV9", "EM_PB_NVR_MPU_GUI_LAYOUT__p6_1xDIV9", 9 },
  { "p8_1xDIV16", "EM_PB_NVR_MPU_GUI_LAYOUT__p8_1xDIV16", 10 },
};
static const ProtobufCIntRange em_pb_nvr_mpu_gui_layout__value_ranges[] = {
{0, 0},{0, 11}
};
static const ProtobufCEnumValueIndex em_pb_nvr_mpu_gui_layout__enum_values_by_name[11] =
{
  { "p16_4x4", 4 },
  { "p1_1x1", 0 },
  { "p25_5x5", 5 },
  { "p2_1x1", 1 },
  { "p36_6x6", 6 },
  { "p49_7x7", 7 },
  { "p4_2x2", 2 },
  { "p64_8x8", 8 },
  { "p6_1xDIV9", 9 },
  { "p8_1xDIV16", 10 },
  { "p9_3x3", 3 },
};
const ProtobufCEnumDescriptor em_pb_nvr_mpu_gui_layout__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "EmPbNvrMpuGuiLayout",
  "EmPbNvrMpuGuiLayout",
  "EmPbNvrMpuGuiLayout",
  "",
  11,
  em_pb_nvr_mpu_gui_layout__enum_values_by_number,
  11,
  em_pb_nvr_mpu_gui_layout__enum_values_by_name,
  1,
  em_pb_nvr_mpu_gui_layout__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
static const ProtobufCEnumValue em_nvr_mpu_vid_scale_mode__enum_values_by_number[2] =
{
  { "FULL", "EM_NVR_MPU_VID_SCALE_MODE__FULL", 0 },
  { "RATIO", "EM_NVR_MPU_VID_SCALE_MODE__RATIO", 1 },
};
static const ProtobufCIntRange em_nvr_mpu_vid_scale_mode__value_ranges[] = {
{0, 0},{0, 2}
};
static const ProtobufCEnumValueIndex em_nvr_mpu_vid_scale_mode__enum_values_by_name[2] =
{
  { "FULL", 0 },
  { "RATIO", 1 },
};
const ProtobufCEnumDescriptor em_nvr_mpu_vid_scale_mode__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "EmNvrMpuVidScaleMode",
  "EmNvrMpuVidScaleMode",
  "EmNvrMpuVidScaleMode",
  "",
  2,
  em_nvr_mpu_vid_scale_mode__enum_values_by_number,
  2,
  em_nvr_mpu_vid_scale_mode__enum_values_by_name,
  1,
  em_nvr_mpu_vid_scale_mode__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
static const ProtobufCEnumValue em_nvr_mpu_vid_dec_mode__enum_values_by_number[3] =
{
  { "REAL", "EM_NVR_MPU_VID_DEC_MODE__REAL", 0 },
  { "BALANCE", "EM_NVR_MPU_VID_DEC_MODE__BALANCE", 1 },
  { "FLUENCY", "EM_NVR_MPU_VID_DEC_MODE__FLUENCY", 2 },
};
static const ProtobufCIntRange em_nvr_mpu_vid_dec_mode__value_ranges[] = {
{0, 0},{0, 3}
};
static const ProtobufCEnumValueIndex em_nvr_mpu_vid_dec_mode__enum_values_by_name[3] =
{
  { "BALANCE", 1 },
  { "FLUENCY", 2 },
  { "REAL", 0 },
};
const ProtobufCEnumDescriptor em_nvr_mpu_vid_dec_mode__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "EmNvrMpuVidDecMode",
  "EmNvrMpuVidDecMode",
  "EmNvrMpuVidDecMode",
  "",
  3,
  em_nvr_mpu_vid_dec_mode__enum_values_by_number,
  3,
  em_nvr_mpu_vid_dec_mode__enum_values_by_name,
  1,
  em_nvr_mpu_vid_dec_mode__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
static const ProtobufCEnumValue em_nvr_display_refresh_rate__enum_values_by_number[3] =
{
  { "_30", "EM_NVR_DISPLAY_REFRESH_RATE___30", 0 },
  { "_50", "EM_NVR_DISPLAY_REFRESH_RATE___50", 1 },
  { "_60", "EM_NVR_DISPLAY_REFRESH_RATE___60", 2 },
};
static const ProtobufCIntRange em_nvr_display_refresh_rate__value_ranges[] = {
{0, 0},{0, 3}
};
static const ProtobufCEnumValueIndex em_nvr_display_refresh_rate__enum_values_by_name[3] =
{
  { "_30", 0 },
  { "_50", 1 },
  { "_60", 2 },
};
const ProtobufCEnumDescriptor em_nvr_display_refresh_rate__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "EmNvrDisplayRefreshRate",
  "EmNvrDisplayRefreshRate",
  "EmNvrDisplayRefreshRate",
  "",
  3,
  em_nvr_display_refresh_rate__enum_values_by_number,
  3,
  em_nvr_display_refresh_rate__enum_values_by_name,
  1,
  em_nvr_display_refresh_rate__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
static const ProtobufCEnumValue em_nvr_mpu_bg_color__enum_values_by_number[4] =
{
  { "RED", "EM_NVR_MPU_BG_COLOR__RED", 0 },
  { "GREEN", "EM_NVR_MPU_BG_COLOR__GREEN", 1 },
  { "BLUE", "EM_NVR_MPU_BG_COLOR__BLUE", 2 },
  { "BLACK", "EM_NVR_MPU_BG_COLOR__BLACK", 3 },
};
static const ProtobufCIntRange em_nvr_mpu_bg_color__value_ranges[] = {
{0, 0},{0, 4}
};
static const ProtobufCEnumValueIndex em_nvr_mpu_bg_color__enum_values_by_name[4] =
{
  { "BLACK", 3 },
  { "BLUE", 2 },
  { "GREEN", 1 },
  { "RED", 0 },
};
const ProtobufCEnumDescriptor em_nvr_mpu_bg_color__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "EmNvrMpuBgColor",
  "EmNvrMpuBgColor",
  "EmNvrMpuBgColor",
  "",
  4,
  em_nvr_mpu_bg_color__enum_values_by_number,
  4,
  em_nvr_mpu_bg_color__enum_values_by_name,
  1,
  em_nvr_mpu_bg_color__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
static const ProtobufCEnumValue em_nvr_view_choose_enc_id_strategy__enum_values_by_number[1] =
{
  { "AUTO", "EM_NVR_VIEW_CHOOSE_ENC_ID_STRATEGY__AUTO", 0 },
};
static const ProtobufCIntRange em_nvr_view_choose_enc_id_strategy__value_ranges[] = {
{0, 0},{0, 1}
};
static const ProtobufCEnumValueIndex em_nvr_view_choose_enc_id_strategy__enum_values_by_name[1] =
{
  { "AUTO", 0 },
};
const ProtobufCEnumDescriptor em_nvr_view_choose_enc_id_strategy__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "EmNvrViewChooseEncIdStrategy",
  "EmNvrViewChooseEncIdStrategy",
  "EmNvrViewChooseEncIdStrategy",
  "",
  1,
  em_nvr_view_choose_enc_id_strategy__enum_values_by_number,
  1,
  em_nvr_view_choose_enc_id_strategy__enum_values_by_name,
  1,
  em_nvr_view_choose_enc_id_strategy__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
static const ProtobufCEnumValue em_nvr_mpu_dec_caller_type__enum_values_by_number[3] =
{
  { "VIWER", "EM_NVR_MPU_DEC_CALLER_TYPE__VIWER", 0 },
  { "PLAYREC", "EM_NVR_MPU_DEC_CALLER_TYPE__PLAYREC", 1 },
  { "VID_CAP", "EM_NVR_MPU_DEC_CALLER_TYPE__VID_CAP", 2 },
};
static const ProtobufCIntRange em_nvr_mpu_dec_caller_type__value_ranges[] = {
{0, 0},{0, 3}
};
static const ProtobufCEnumValueIndex em_nvr_mpu_dec_caller_type__enum_values_by_name[3] =
{
  { "PLAYREC", 1 },
  { "VID_CAP", 2 },
  { "VIWER", 0 },
};
const ProtobufCEnumDescriptor em_nvr_mpu_dec_caller_type__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "EmNvrMpuDecCallerType",
  "EmNvrMpuDecCallerType",
  "EmNvrMpuDecCallerType",
  "",
  3,
  em_nvr_mpu_dec_caller_type__enum_values_by_number,
  3,
  em_nvr_mpu_dec_caller_type__enum_values_by_name,
  1,
  em_nvr_mpu_dec_caller_type__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
static const ProtobufCEnumValue em_nvr_mpu_dis_mode_type__enum_values_by_number[1] =
{
  { "CUSTOM", "EM_NVR_MPU_DIS_MODE_TYPE__CUSTOM", 0 },
};
static const ProtobufCIntRange em_nvr_mpu_dis_mode_type__value_ranges[] = {
{0, 0},{0, 1}
};
static const ProtobufCEnumValueIndex em_nvr_mpu_dis_mode_type__enum_values_by_name[1] =
{
  { "CUSTOM", 0 },
};
const ProtobufCEnumDescriptor em_nvr_mpu_dis_mode_type__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "EmNvrMpuDisModeType",
  "EmNvrMpuDisModeType",
  "EmNvrMpuDisModeType",
  "",
  1,
  em_nvr_mpu_dis_mode_type__enum_values_by_number,
  1,
  em_nvr_mpu_dis_mode_type__enum_values_by_name,
  1,
  em_nvr_mpu_dis_mode_type__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
static const ProtobufCEnumValue em_nvr_mpu_enc_type__enum_values_by_number[5] =
{
  { "H264", "EM_NVR_MPU_ENC_TYPE__H264", 0 },
  { "MJPEG", "EM_NVR_MPU_ENC_TYPE__MJPEG", 1 },
  { "H265", "EM_NVR_MPU_ENC_TYPE__H265", 2 },
  { "SVAC", "EM_NVR_MPU_ENC_TYPE__SVAC", 3 },
  { "MPEG4", "EM_NVR_MPU_ENC_TYPE__MPEG4", 4 },
};
static const ProtobufCIntRange em_nvr_mpu_enc_type__value_ranges[] = {
{0, 0},{0, 5}
};
static const ProtobufCEnumValueIndex em_nvr_mpu_enc_type__enum_values_by_name[5] =
{
  { "H264", 0 },
  { "H265", 2 },
  { "MJPEG", 1 },
  { "MPEG4", 4 },
  { "SVAC", 3 },
};
const ProtobufCEnumDescriptor em_nvr_mpu_enc_type__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "EmNvrMpuEncType",
  "EmNvrMpuEncType",
  "EmNvrMpuEncType",
  "",
  5,
  em_nvr_mpu_enc_type__enum_values_by_number,
  5,
  em_nvr_mpu_enc_type__enum_values_by_name,
  1,
  em_nvr_mpu_enc_type__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
