

TOP := ..

COMM_DIR := ../../common/

SRC_DIR := $(TOP)/source
CURDIR := ./

## Name and type of the target for this Makefile

APP_TARGET	      := nvrsrv


## Define debugging symbols
DEBUG = 0
LINUX_COMPILER = _HIS3531DV200_
PWLIB_SUPPORT = 0
CFLAGS += -funwind-tables
#CFLAGS += -D__MRTC__
## Object files that compose the target(s)



OBJS := $(SRC_DIR)/nvrsrv\
## Libraries to include in shared object file
LIB_PATH += $(TOP)/../../10-common/lib/release/his3531dv200
LIB_PATH += $(TOP)/../../10-common/lib/release/his3531dv200/capsvr
LIB_PATH += $(TOP)/../../10-common/lib/release/his3531dv200/alg
LIB_PATH += $(TOP)/../../10-common/lib/release/his3531dv200/alg/person_cnt
LIB_PATH += $(TOP)/../../10-common/lib/release/his3531dv200/audlib
LIB_PATH += $(TOP)/../../10-common/lib/release/his3531dv200/qt
LIB_PATH += $(TOP)/../../10-common/lib/release/his3531dv200/wifim
LIB_PATH += $(TOP)/../../10-common/lib/release/his3531dv200/libsvr
LIB_PATH += $(TOP)/../../10-common/lib/release/his3531dv200/zeromq

LIBS +=	nvrcfg nvrlog nvrcap nvrcustcap nvrusrmgr nvrnetwork nvrsys nvrproto svrproto nvrftp protobufc sqlite3wrapper malloc debuglog netcbb ddnsc upnpc drv pthread nvrgeo nvrcoi kdvsys\
	sysdbg mca mastermcactrl goaheadhelper appbase charconv appclt nvrpui nvrvtductrl syswrapper svrrnu freetype svrmpu svrvmt ompcu svrfontedit svrfreetypewrapper nvrmpu nvrpcap httpclient mxml ghttp go rtspclient kdmposa osp netpacket kdmtsps kdvencrypt mediaswitch stdc++ nvrqueue nvrprobe mediactrl_nvr fpgactrl nvralarm nvrdev nvrupgrade nvrcrc ftpc\
	dmsrv nvrrec rpdata rp kdmfileinterface nvrguard nvrsmtp smtp dl ais algctrl airp nvrextdev SDEF jpeg json wifi mbnet nvrunifiedlog\
	kdnetsdk_svr\
	lwshelper\
	websockets\
	kdnetsdk_svr\
	lwshelper\
	websockets\
	equalizer_ex_armhi3531dv200_linux \
		mixer_armhi3531dv200_linux \
        spe_armhi3531dv200_linux \
		videomanage_armhi3531dv200_linux \
        audproc_plus_armhi3531dv200_linux \
		speechexcit_armhi3531dv200_linux \
		resamplev2_armhi3531dv200_linux \
		extexp_armhi3531dv200_linux \
        voicechanger_armhi3531dv200_linux \
		multiaec_v202_armhi3531dv200_linux \
        agc_speechsense_armhi3531dv200_linux\
        audcodec_armhi3531dv200_linux \
		g7221c_armhi3531dv200_linux \
        adpcm_armhi3531dv200_linux \
		g711_armhi3531dv200_linux \
        g722_armhi3531dv200_linux \
		aaclcenc_armhi3531dv200_linux \
        aaclcdec_armhi3531dv200_linux \
		g726_armhi3531dv200_linux \
        aaclddec_armhi3531dv200_linux \
		aacldenc_armhi3531dv200_linux \
        amr_nb_armhi3531dv200_linux \
		g719_armhi3531dv200_linux \
        g728_armhi3531dv200_linux \
		g729_armhi3531dv200_linux \
        mp3dec_armhi3531dv200_linux \
		mp3enc_armhi3531dv200_linux \
        opus_armhi3531dv200_linux \
		mp2_armhi3531dv200_linux \
        stdg722_armhi3531dv200_linux \
		dlydct_armhi3531dv200_linux \
        videomanage_armhi3531dv200_linux\
		md5lib_armhisi3531dv200_linux\
	m\
	curl\
	pcap\
	kdmssl\
	kdmcrypto\
	cjson\
	udm\
	uuid\
	blkid\
	nvrproduct\
	kdmnatagent\
	rpdownload\
	nvrstitch\
	Qt5Widgets\
	Qt5Gui\
	Qt5Core\
	qlinuxfb\
	nvrmd5\
	zmq\
    #heapcheck\
    #tcmalloc
ifneq ($(SLIBS),) 
LDFLAGS += -Wl,-static
LDFLAGS += $(foreach lib,$(SLIBS),-l$(lib))
endif
## Add driver-specific include directory to the search path

INC_PATH += $(CURDIR)/../include \
		$(CURDIR)/../../common \
		$(CURDIR)/../../nvrcfg/include \
		$(CURDIR)/../../nvrpui/include \
		$(CURDIR)/../../nvrvtductrl/include \
		$(CURDIR)/../../nvrusrmgr/include \
		$(CURDIR)/../../nvrcap/include \
		$(CURDIR)/../../nvrnetwork/include \
		$(CURDIR)/../../nvrsys/include \
		$(CURDIR)/../../nvrdev/include \
		$(CURDIR)/../../nvralarm/include \
		$(CURDIR)/../../nvrlog/include \
		$(CURDIR)/../../nvrguard/include \
		$(CURDIR)/../../nvrsmtp/include \
		$(CURDIR)/../../nvrmpu/include \
		$(CURDIR)/../../nvrpcap/include \
		$(CURDIR)/../../airp/include \
		$(CURDIR)/../../ais/include \
		$(CURDIR)/../../nvrextdevmgr/include \
		$(CURDIR)/../../../10-common/include/service \
		$(CURDIR)/../../../10-common/include/cbb/debuglog\
		$(CURDIR)/../../../10-common/include/cbb/mbnet\
		$(CURDIR)/../../../10-common/include/system\
		$(CURDIR)/../../../10-common/include/hal/sysdbg\
		$(CURDIR)/../../../10-common/include/cbb/protobuf \
		$(CURDIR)/../../../10-common/include/cbb/osp \
		$(CURDIR)/../../../10-common/include/cbb/sqilte \
		$(CURDIR)/../../../10-common/include/cbb/appclt\
		$(CURDIR)/../../../10-common/include/cbb/mediaswitch\
		$(CURDIR)/../../../10-common/include/cbb/kshield\
		$(CURDIR)/../../../10-common/include/app
		
CFLAGS += -D_HIS3531DV200_
#CFLAGS += -D_TCMALLOC_
#CFLAGS += -fno-builtin-malloc -fno-builtin-calloc -fno-builtin-realloc -fno-builtin-free

ifeq ($(PWLIB_SUPPORT),1)
   INC_PATH += $(PWLIBDIR)/include/ptlib/unix $(PWLIBDIR)/include
endif


INSTALL_NO_UPX_APP_PATH := ../../../10-common/version/release/his3531dv200/bin_noupx
INSTALL_APP_PATH := ../../../10-common/version/release/his3531dv200/bin_svr
include $(COMM_DIR)/common.mk


