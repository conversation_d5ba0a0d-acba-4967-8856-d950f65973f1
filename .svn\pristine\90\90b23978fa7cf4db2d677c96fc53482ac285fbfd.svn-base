/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: nvrsgwcfg.proto */

/* Do not generate deprecated warnings for self */
#ifndef PROTOBUF_C__NO_DEPRECATED
#define PROTOBUF_C__NO_DEPRECATED
#endif

#include "nvrsgwcfg.pb-c.h"
void   tpb_nvr_sgw_vid_enc_param__init
                     (TPbNvrSgwVidEncParam         *message)
{
  static TPbNvrSgwVidEncParam init_value = TPB_NVR_SGW_VID_ENC_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_sgw_vid_enc_param__get_packed_size
                     (const TPbNvrSgwVidEncParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_sgw_vid_enc_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_sgw_vid_enc_param__pack
                     (const TPbNvrSgwVidEncParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_sgw_vid_enc_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_sgw_vid_enc_param__pack_to_buffer
                     (const TPbNvrSgwVidEncParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_sgw_vid_enc_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrSgwVidEncParam *
       tpb_nvr_sgw_vid_enc_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrSgwVidEncParam *)
     protobuf_c_message_unpack (&tpb_nvr_sgw_vid_enc_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_sgw_vid_enc_param__free_unpacked
                     (TPbNvrSgwVidEncParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_sgw_vid_enc_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_sgw_aud_enc_param__init
                     (TPbNvrSgwAudEncParam         *message)
{
  static TPbNvrSgwAudEncParam init_value = TPB_NVR_SGW_AUD_ENC_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_sgw_aud_enc_param__get_packed_size
                     (const TPbNvrSgwAudEncParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_sgw_aud_enc_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_sgw_aud_enc_param__pack
                     (const TPbNvrSgwAudEncParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_sgw_aud_enc_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_sgw_aud_enc_param__pack_to_buffer
                     (const TPbNvrSgwAudEncParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_sgw_aud_enc_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrSgwAudEncParam *
       tpb_nvr_sgw_aud_enc_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrSgwAudEncParam *)
     protobuf_c_message_unpack (&tpb_nvr_sgw_aud_enc_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_sgw_aud_enc_param__free_unpacked
                     (TPbNvrSgwAudEncParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_sgw_aud_enc_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_sgw_vid_enc_param_chn__init
                     (TPbNvrSgwVidEncParamChn         *message)
{
  static TPbNvrSgwVidEncParamChn init_value = TPB_NVR_SGW_VID_ENC_PARAM_CHN__INIT;
  *message = init_value;
}
size_t tpb_nvr_sgw_vid_enc_param_chn__get_packed_size
                     (const TPbNvrSgwVidEncParamChn *message)
{
  assert(message->base.descriptor == &tpb_nvr_sgw_vid_enc_param_chn__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_sgw_vid_enc_param_chn__pack
                     (const TPbNvrSgwVidEncParamChn *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_sgw_vid_enc_param_chn__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_sgw_vid_enc_param_chn__pack_to_buffer
                     (const TPbNvrSgwVidEncParamChn *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_sgw_vid_enc_param_chn__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrSgwVidEncParamChn *
       tpb_nvr_sgw_vid_enc_param_chn__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrSgwVidEncParamChn *)
     protobuf_c_message_unpack (&tpb_nvr_sgw_vid_enc_param_chn__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_sgw_vid_enc_param_chn__free_unpacked
                     (TPbNvrSgwVidEncParamChn *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_sgw_vid_enc_param_chn__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_sgw_aud_enc_param_chn__init
                     (TPbNvrSgwAudEncParamChn         *message)
{
  static TPbNvrSgwAudEncParamChn init_value = TPB_NVR_SGW_AUD_ENC_PARAM_CHN__INIT;
  *message = init_value;
}
size_t tpb_nvr_sgw_aud_enc_param_chn__get_packed_size
                     (const TPbNvrSgwAudEncParamChn *message)
{
  assert(message->base.descriptor == &tpb_nvr_sgw_aud_enc_param_chn__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_sgw_aud_enc_param_chn__pack
                     (const TPbNvrSgwAudEncParamChn *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_sgw_aud_enc_param_chn__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_sgw_aud_enc_param_chn__pack_to_buffer
                     (const TPbNvrSgwAudEncParamChn *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_sgw_aud_enc_param_chn__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrSgwAudEncParamChn *
       tpb_nvr_sgw_aud_enc_param_chn__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrSgwAudEncParamChn *)
     protobuf_c_message_unpack (&tpb_nvr_sgw_aud_enc_param_chn__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_sgw_aud_enc_param_chn__free_unpacked
                     (TPbNvrSgwAudEncParamChn *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_sgw_aud_enc_param_chn__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_sgw_rk3308_ver__init
                     (TPbNvrSgwRk3308Ver         *message)
{
  static TPbNvrSgwRk3308Ver init_value = TPB_NVR_SGW_RK3308_VER__INIT;
  *message = init_value;
}
size_t tpb_nvr_sgw_rk3308_ver__get_packed_size
                     (const TPbNvrSgwRk3308Ver *message)
{
  assert(message->base.descriptor == &tpb_nvr_sgw_rk3308_ver__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_sgw_rk3308_ver__pack
                     (const TPbNvrSgwRk3308Ver *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_sgw_rk3308_ver__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_sgw_rk3308_ver__pack_to_buffer
                     (const TPbNvrSgwRk3308Ver *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_sgw_rk3308_ver__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrSgwRk3308Ver *
       tpb_nvr_sgw_rk3308_ver__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrSgwRk3308Ver *)
     protobuf_c_message_unpack (&tpb_nvr_sgw_rk3308_ver__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_sgw_rk3308_ver__free_unpacked
                     (TPbNvrSgwRk3308Ver *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_sgw_rk3308_ver__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_sgw_cfg__init
                     (TPbNvrSgwCfg         *message)
{
  static TPbNvrSgwCfg init_value = TPB_NVR_SGW_CFG__INIT;
  *message = init_value;
}
size_t tpb_nvr_sgw_cfg__get_packed_size
                     (const TPbNvrSgwCfg *message)
{
  assert(message->base.descriptor == &tpb_nvr_sgw_cfg__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_sgw_cfg__pack
                     (const TPbNvrSgwCfg *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_sgw_cfg__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_sgw_cfg__pack_to_buffer
                     (const TPbNvrSgwCfg *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_sgw_cfg__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrSgwCfg *
       tpb_nvr_sgw_cfg__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrSgwCfg *)
     protobuf_c_message_unpack (&tpb_nvr_sgw_cfg__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_sgw_cfg__free_unpacked
                     (TPbNvrSgwCfg *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_sgw_cfg__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
static const ProtobufCFieldDescriptor tpb_nvr_sgw_vid_enc_param__field_descriptors[11] =
{
  {
    "enc_type",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSgwVidEncParam, has_enc_type),
    offsetof(TPbNvrSgwVidEncParam, enc_type),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "frame_rate",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSgwVidEncParam, has_frame_rate),
    offsetof(TPbNvrSgwVidEncParam, frame_rate),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "res_width",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSgwVidEncParam, has_res_width),
    offsetof(TPbNvrSgwVidEncParam, res_width),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "res_height",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSgwVidEncParam, has_res_height),
    offsetof(TPbNvrSgwVidEncParam, res_height),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "max_key_rate",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSgwVidEncParam, has_max_key_rate),
    offsetof(TPbNvrSgwVidEncParam, max_key_rate),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "bit_rate",
    6,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSgwVidEncParam, has_bit_rate),
    offsetof(TPbNvrSgwVidEncParam, bit_rate),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "rc_mode",
    7,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSgwVidEncParam, has_rc_mode),
    offsetof(TPbNvrSgwVidEncParam, rc_mode),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "quality",
    8,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSgwVidEncParam, has_quality),
    offsetof(TPbNvrSgwVidEncParam, quality),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "vid_enc_profile_id",
    9,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSgwVidEncParam, has_vid_enc_profile_id),
    offsetof(TPbNvrSgwVidEncParam, vid_enc_profile_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "smart_enc",
    10,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSgwVidEncParam, has_smart_enc),
    offsetof(TPbNvrSgwVidEncParam, smart_enc),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "bit_smooth_level",
    11,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSgwVidEncParam, has_bit_smooth_level),
    offsetof(TPbNvrSgwVidEncParam, bit_smooth_level),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_sgw_vid_enc_param__field_indices_by_name[] = {
  5,   /* field[5] = bit_rate */
  10,   /* field[10] = bit_smooth_level */
  0,   /* field[0] = enc_type */
  1,   /* field[1] = frame_rate */
  4,   /* field[4] = max_key_rate */
  7,   /* field[7] = quality */
  6,   /* field[6] = rc_mode */
  3,   /* field[3] = res_height */
  2,   /* field[2] = res_width */
  9,   /* field[9] = smart_enc */
  8,   /* field[8] = vid_enc_profile_id */
};
static const ProtobufCIntRange tpb_nvr_sgw_vid_enc_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 11 }
};
const ProtobufCMessageDescriptor tpb_nvr_sgw_vid_enc_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrSgwVidEncParam",
  "TPbNvrSgwVidEncParam",
  "TPbNvrSgwVidEncParam",
  "",
  sizeof(TPbNvrSgwVidEncParam),
  11,
  tpb_nvr_sgw_vid_enc_param__field_descriptors,
  tpb_nvr_sgw_vid_enc_param__field_indices_by_name,
  1,  tpb_nvr_sgw_vid_enc_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_sgw_vid_enc_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_sgw_aud_enc_param__field_descriptors[4] =
{
  {
    "type",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSgwAudEncParam, has_type),
    offsetof(TPbNvrSgwAudEncParam, type),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "sample_rate",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSgwAudEncParam, has_sample_rate),
    offsetof(TPbNvrSgwAudEncParam, sample_rate),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "aud_enc_vol",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSgwAudEncParam, has_aud_enc_vol),
    offsetof(TPbNvrSgwAudEncParam, aud_enc_vol),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "aec",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSgwAudEncParam, has_aec),
    offsetof(TPbNvrSgwAudEncParam, aec),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_sgw_aud_enc_param__field_indices_by_name[] = {
  3,   /* field[3] = aec */
  2,   /* field[2] = aud_enc_vol */
  1,   /* field[1] = sample_rate */
  0,   /* field[0] = type */
};
static const ProtobufCIntRange tpb_nvr_sgw_aud_enc_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 4 }
};
const ProtobufCMessageDescriptor tpb_nvr_sgw_aud_enc_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrSgwAudEncParam",
  "TPbNvrSgwAudEncParam",
  "TPbNvrSgwAudEncParam",
  "",
  sizeof(TPbNvrSgwAudEncParam),
  4,
  tpb_nvr_sgw_aud_enc_param__field_descriptors,
  tpb_nvr_sgw_aud_enc_param__field_indices_by_name,
  1,  tpb_nvr_sgw_aud_enc_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_sgw_aud_enc_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_sgw_vid_enc_param_chn__field_descriptors[1] =
{
  {
    "vid_enc_param",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrSgwVidEncParamChn, n_vid_enc_param),
    offsetof(TPbNvrSgwVidEncParamChn, vid_enc_param),
    &tpb_nvr_sgw_vid_enc_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_sgw_vid_enc_param_chn__field_indices_by_name[] = {
  0,   /* field[0] = vid_enc_param */
};
static const ProtobufCIntRange tpb_nvr_sgw_vid_enc_param_chn__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor tpb_nvr_sgw_vid_enc_param_chn__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrSgwVidEncParamChn",
  "TPbNvrSgwVidEncParamChn",
  "TPbNvrSgwVidEncParamChn",
  "",
  sizeof(TPbNvrSgwVidEncParamChn),
  1,
  tpb_nvr_sgw_vid_enc_param_chn__field_descriptors,
  tpb_nvr_sgw_vid_enc_param_chn__field_indices_by_name,
  1,  tpb_nvr_sgw_vid_enc_param_chn__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_sgw_vid_enc_param_chn__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_sgw_aud_enc_param_chn__field_descriptors[1] =
{
  {
    "aud_enc_param",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrSgwAudEncParamChn, n_aud_enc_param),
    offsetof(TPbNvrSgwAudEncParamChn, aud_enc_param),
    &tpb_nvr_sgw_aud_enc_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_sgw_aud_enc_param_chn__field_indices_by_name[] = {
  0,   /* field[0] = aud_enc_param */
};
static const ProtobufCIntRange tpb_nvr_sgw_aud_enc_param_chn__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor tpb_nvr_sgw_aud_enc_param_chn__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrSgwAudEncParamChn",
  "TPbNvrSgwAudEncParamChn",
  "TPbNvrSgwAudEncParamChn",
  "",
  sizeof(TPbNvrSgwAudEncParamChn),
  1,
  tpb_nvr_sgw_aud_enc_param_chn__field_descriptors,
  tpb_nvr_sgw_aud_enc_param_chn__field_indices_by_name,
  1,  tpb_nvr_sgw_aud_enc_param_chn__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_sgw_aud_enc_param_chn__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_sgw_rk3308_ver__field_descriptors[1] =
{
  {
    "rk3308_ver",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BYTES,
    offsetof(TPbNvrSgwRk3308Ver, has_rk3308_ver),
    offsetof(TPbNvrSgwRk3308Ver, rk3308_ver),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_sgw_rk3308_ver__field_indices_by_name[] = {
  0,   /* field[0] = rk3308_ver */
};
static const ProtobufCIntRange tpb_nvr_sgw_rk3308_ver__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor tpb_nvr_sgw_rk3308_ver__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrSgwRk3308Ver",
  "TPbNvrSgwRk3308Ver",
  "TPbNvrSgwRk3308Ver",
  "",
  sizeof(TPbNvrSgwRk3308Ver),
  1,
  tpb_nvr_sgw_rk3308_ver__field_descriptors,
  tpb_nvr_sgw_rk3308_ver__field_indices_by_name,
  1,  tpb_nvr_sgw_rk3308_ver__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_sgw_rk3308_ver__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_sgw_cfg__field_descriptors[4] =
{
  {
    "chn_style",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrSgwCfg, n_chn_style),
    offsetof(TPbNvrSgwCfg, chn_style),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "vid_enc_chn_param",
    2,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrSgwCfg, n_vid_enc_chn_param),
    offsetof(TPbNvrSgwCfg, vid_enc_chn_param),
    &tpb_nvr_sgw_vid_enc_param_chn__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "aud_enc_chn_param",
    3,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbNvrSgwCfg, n_aud_enc_chn_param),
    offsetof(TPbNvrSgwCfg, aud_enc_chn_param),
    &tpb_nvr_sgw_aud_enc_param_chn__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "rk3308_ver",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BYTES,
    offsetof(TPbNvrSgwCfg, has_rk3308_ver),
    offsetof(TPbNvrSgwCfg, rk3308_ver),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_sgw_cfg__field_indices_by_name[] = {
  2,   /* field[2] = aud_enc_chn_param */
  0,   /* field[0] = chn_style */
  3,   /* field[3] = rk3308_ver */
  1,   /* field[1] = vid_enc_chn_param */
};
static const ProtobufCIntRange tpb_nvr_sgw_cfg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 4 }
};
const ProtobufCMessageDescriptor tpb_nvr_sgw_cfg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrSgwCfg",
  "TPbNvrSgwCfg",
  "TPbNvrSgwCfg",
  "",
  sizeof(TPbNvrSgwCfg),
  4,
  tpb_nvr_sgw_cfg__field_descriptors,
  tpb_nvr_sgw_cfg__field_indices_by_name,
  1,  tpb_nvr_sgw_cfg__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_sgw_cfg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
