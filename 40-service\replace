#!/bin/bash

echo "================ replace ver ================"
file=./../10-common/include/service/nvrsys.h
file1=nvrsys.h.bak
str=`grep -n '#define NVR_SYS_SOFT_VER' $file`
ver=`echo $str|sed 's/.*"\(.*\)"\(.*\)/\1/'`
a1=`echo $ver|awk -F. '{print $2}'`
a2=`echo $ver|awk -F. '{print $3}'`
a3=`echo $ver|awk -F. '{print $4}'`
newver=8.$a1.$a2.$[$a3+1]
sed "s/$ver/$newver/g" $file > $file1
mv $file1 $file
svn commit -F /home/<USER>/workspace/svn-commit.tmp $file
echo "================ replace qcomver ================"
file=./../40-service/androidapk/app/src/main/AndroidManifest.xml
file1=AndroidManifest.xml.bak
str=`grep -n 'android:versionName' $file`
ver=`echo $str|sed 's/.*"\(.*\)"\(.*\)/\1/'`
sed "s/$ver/$newver/g" $file > $file1
mv $file1 $file
svn commit -F /home/<USER>/workspace/svn-commit.tmp $file
