path="../../10-common/version/compileinfo/nvrkdvsend_cv2x.txt"
date>>$path

cd ./prj_linux

echo ==============================================
echo =      nvr_kdvsend_linux for cv2x    =
echo ==============================================

echo "============compile libnvrkdvsend cv2x============">>../$path

make -j4 -e DEBUG=1 -f makefile_cv2x clean
make -j4 -e DEBUG=1 -f makefile_cv2x 2>>../$path

#makefile install已经安装到指定目录了
#cp -L -r -f libnvrkdvsend.so ../../../10-common/lib/release/cv2x/nvrkdvsend

cd ..
