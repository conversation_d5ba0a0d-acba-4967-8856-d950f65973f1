#ifndef _MEDIA_CTRL_H_
#define _MEDIA_CTRL_H_
#include "netpacket.h"

#ifdef __cplusplus
extern "C" {
#endif

#define MEDIACTRL_MAX_AUD_CAP_NUM (2)  /* 最大音频采集通道数 */
#define MEDIACTRL_MAX_AUD_ENC_NUM (1)  /* 最大音频编码通道数 */
#define MEDIACTRL_MAX_AUD_PLY_NUM (1)  /* 最大音频播放通道数 */
#define MEDIACTRL_MAX_AUD_DEC_NUM (1)  /* 最大音频接收通道数 */

#define MEDIACTRL_MAX_VID_CAP_NUM (10)  /* 最大视频采集通道数 */
#define MEDIACTRL_MAX_VID_ENC_NUM (8)   /* 最大视频编码通道数 */
#define MEDIACTRL_MAX_VID_DEC_NUM (128) /* 最大视频解码通道数 */

#define MEDIACTRL_MAX_OSD_AREA_NUM (20)  /* 每个编码通道最大OSD块数 */
#define MEDIACTRL_MAX_MOVING_TARGET_NUM 40      //场景中运动目标最大个数
#define MEDIACTRL_MAX_ABNORM_OBJECT_NUM    10        //场景中最大异常目标数目

#define MEDIACTRL_MAX_ENC_FISHEYE_REGION    (4)    /* Max Region Per Enc Stream, should follow HISI definition  FISHEYE_MAX_REGION_NUM */

typedef enum
{
    /* cap chn define */
    MEDIACTRL_VID_CHN_VIN_BASE            = 0x00200000,
    MEDIACTRL_VID_CHN_VIN_SENSOR_0        = MEDIACTRL_VID_CHN_VIN_BASE, /* first sensor or back sensor */
    MEDIACTRL_VID_CHN_VIN_SENSOR_1,                                   /* second sensor or front sensor */
    MEDIACTRL_VID_CHN_VIN_SENSOR_2,
    MEDIACTRL_VID_CHN_VIN_SENSOR_3,
    MEDIACTRL_VID_CHN_VIN_SENSOR_4,
    MEDIACTRL_VID_CHN_VIN_SENSOR_5,
    MEDIACTRL_VID_CHN_VIN_SENSOR_6,
    MEDIACTRL_VID_CHN_VIN_SENSOR_7,
    MEDIACTRL_VID_CHN_VIN_BT1120_0,
    MEDIACTRL_VID_CHN_VIN_BT1120_1,
    MEDIACTRL_VID_CHN_VIN_BT1120_2,
    MEDIACTRL_VID_CHN_VIN_BT1120_3,
    MEDIACTRL_VID_CHN_VIN_CMOS_0,
    MEDIACTRL_VID_CHN_VIN_CMOS_1,
    MEDIACTRL_VID_CHN_VIN_USB_CAMERA_0,
    MEDIACTRL_VID_CHN_VIN_USB_HDMI_0,
    /* Dec chn define Dec chn 0: MEDIACTRL_VID_CHN_DEC_BASE, Dec chn 1: (MEDIACTRL_VID_CHN_DEC_BASE+ 1) ... */
    MEDIACTRL_VID_CHN_DEC_BASE          = 0x00400000,
    /* Stitch chn define */
    MEDIACTRL_VID_CHN_STITCH_BASE       = 0x00800000,
    MEDIACTRL_VID_CHN_STITCH_GPU        = MEDIACTRL_VID_CHN_STITCH_BASE, /* 合成通道 */
    MEDIACTRL_VID_CHN_STITCH_CPU,
    MEDIACTRL_VID_CHN_EXTERN_INPUT    = 0x01000000,
}EMediaCtrlVidChnType;                   /* 视频通道类型 */

typedef enum
{
    /* *< YUV 422 Interleaved format - UYVY. */
    MEDIACTRL_DF_YUV422I_UYVY = 0x0000,
    /* *< YUV 422 Interleaved format - YUYV. */
    MEDIACTRL_DF_YUV422I_YUYV,
    /* *< YUV 422 Interleaved format - YVYU. */
    MEDIACTRL_DF_YUV422I_YVYU,
    /* *< YUV 422 Interleaved format - VYUY. */
    MEDIACTRL_DF_YUV422I_VYUY,
    /* *< YUV 422 Semi-Planar - Y separate, UV interleaved. */
    MEDIACTRL_DF_YUV422SP_UV,
    /* *< YUV 422 Semi-Planar - Y separate, VU interleaved. */
    MEDIACTRL_DF_YUV422SP_VU,
    /* *< YUV 422 Planar - Y, U and V separate. */
    MEDIACTRL_DF_YUV422P,
    /* *< YUV 420 Semi-Planar - Y separate, UV interleaved. */
    MEDIACTRL_DF_YUV420SP_UV,
    /* *< YUV 420 Semi-Planar - Y separate, VU interleaved. */
    MEDIACTRL_DF_YUV420SP_VU,
    /* *< YUV 420 Planar - Y, U and V separate. */
    MEDIACTRL_DF_YUV420P,
    /* *< YUV 444 Planar - Y, U and V separate. */
    MEDIACTRL_DF_YUV444P,
    /* *< YUV 444 interleaved - YUVYUV... */
    MEDIACTRL_DF_YUV444I,
    /* *< RGBA 8888 interleaved - RGBARGBA... big-Endian ABGRABGR Little-Endian */
    MEDIACTRL_DF_RGBA8888,
    /* *< RGB 888 interleaved - RGBRGB... big-Endian BGRBGR Little-Endian */
    MEDIACTRL_DF_RGB888,
    MEDIACTRL_DF_BGR888,
    MEDIACTRL_DF_ARGB8888,
    MEDIACTRL_DF_ARGB1555,
    MEDIACTRL_DF_ARGB4444,
    /* *< RAW DATA */
    MEDIACTRL_DF_RAW,
}EMediaCtrlVidDateFormat;

typedef enum
{
    MEDIACTRL_CAMERA_MODE_NORMAL    = 0x00,
    MEDIACTRL_CAMERA_MODE_LOW_DELAY = 0x01,   /* 低延时模式 */
    MEDIACTRL_CAMERA_MODE_HDR       = 0x02,   /* HDR模式 */
    MEDIACTRL_CAMERA_MODE_HDSDI     = 0x04,   /* HDSDI模式 */
    MEDIACTRL_CAMERA_MODE_FISH_EYE    = 0x08,   /* muti-region warping模式 */
    MEDIACTRL_CAMERA_MODE_4K_DIV4   = 0x10,   /* 4K 分成4路1080P编码 */
    MEDIACTRL_CAMERA_MODE_INTELLI   = 0x20,   /* 智能ipc模式 */
    MEDIACTRL_CAMERA_MODE_VEHICLE   = 0x40,    /* 卡口模式 */
    MEDIACTRL_CAMERA_MODE_DYNAMIC_INIT = 0x80, /* This mode cap and encode start by MediaCtrlStartEncode(Cap) */
    MEDIACTRL_CAMERA_MODE_EPTZ      = 0x100,
    MEDIACTRL_CAMERA_MODE_DOUBLE_FRAME = 0X200, /* This is long-short frame mode for one sensor */
    MEDIACTRL_CAMERA_MODE_KDV       = 0x400,    /*used for keda kdv product*/
    MEDIACTRL_CAMERA_MODE_LDC       = 0x800,    /*used for keda kdv product*/
    MEDIACTRL_CAMERA_MODE_EIS       = 0x1000,   /* used for ipc or hdc eis */
    MEDIACTRL_CAMERA_MODE_BINNING   = 0x2000,   /* used for binning mode */
}EMediaCtrlCameraModeType;


typedef enum
{
    MEDIACTRL_SNAP_QUALITY_LOW,                          /* 低 */
    MEDIACTRL_SNAP_QUALITY_MID,                          /* 中 */
    MEDIACTRL_SNAP_QUALITY_HIGH                          /* 高 */
}EMediaCtrlSnapQuality;                                  /* 抓拍质量 */

typedef enum
{
    MEDIACTRL_IMAGE_QUALITY_LOWEST,                       /* 图像质量最低 */
    MEDIACTRL_IMAGE_QUALITY_LOWER,                        /* 较低 */
    MEDIACTRL_IMAGE_QUALITY_LOW,                          /* 低 */
    MEDIACTRL_IMAGE_QUALITY_MID,                          /* 中 */
    MEDIACTRL_IMAGE_QUALITY_BETTER,                       /* 较好 */
    MEDIACTRL_IMAGE_QUALITY_BEST                         /* 最高 */
}EMediaCtrlImageQuality;

typedef enum
{
    MEDIACTRL_ROI_LEVEL_LOW,
    MEDIACTRL_ROI_LEVEL_MID,
    MEDIACTRL_ROI_LEVEL_HIGH
}EMediaCtrlRoiLevel;

typedef enum
{
    MEDIACTRL_DIS_LEVEL_NONE,
    MEDIACTRL_DIS_LEVEL_LOWEST,
    MEDIACTRL_DIS_LEVEL_LOW,
    MEDIACTRL_DIS_LEVEL_MID,
    MEDIACTRL_DIS_LEVEL_HIGH,
    MEDIACTRL_DIS_LEVEL_HIGHEST,
}EMediaCtrlDisLevel;

typedef enum
{
    MEDIACTRL_VID_ENC_H264,
    MEDIACTRL_VID_ENC_MJPEG,
    MEDIACTRL_VID_ENC_MPEG4,
    MEDIACTRL_VID_ENC_H265,
    MEDIACTRL_VID_ENC_SVAC,
    MEDIACTRL_VID_ENC_SVAC2,
    MEDIACTRL_VID_ENC_SOFT_SVAC,
    MEDIACTRL_VID_ENC_SOFT_SVAC2
}EMediaCtrlVidEncType;

typedef enum
{
    MEDIACTRL_VID_DEC_H264,
    MEDIACTRL_VID_DEC_H265,
    MEDIACTRL_VID_DEC_MJPEG,
    MEDIACTRL_VID_DEC_MPEG4,
    MEDIACTRL_VID_DEC_SVAC,
    MEDIACTRL_VID_DEC_SVAC2,
    MEDIACTRL_VID_DEC_MAX
}EMediaCtrlVidDecType;

typedef struct
{
    BOOL32 bDCI;       /* 动态对比度增强，默认关闭 */
    BOOL32 bNR;        /* 降噪，默认关闭 */
    BOOL32 bES;        /* 边缘平滑，默认开启 */
}TMediaCtrlVidEffect;

typedef enum
{
    MEDIACTRL_VDEC_FRAME_IP,       /* IP帧 */
    MEDIACTRL_VDEC_FRAME_IPB,      /* IPB帧 */
    MEDIACTRL_VDEC_FRAME_I,        /* I帧 */
}EMediaCtrlVidDecFrameMode;

typedef enum
{
    MEDIACTRL_SMART_ENC_DROI,        /* 开启动态ROI功能 */
    MEDIACTRL_SMART_ENC_DKFI,        /* 开启动态I帧间隔功能 */
    MEDIACTRL_SMART_ENC_TYPE_MAX
}EMediaCtrlSmartEncType;

typedef enum
{
    MEDIACTRL_VID_RC_CBR,
    MEDIACTRL_VID_RC_VBR,
    MEDIACTRL_VID_RC_AVBR
}EMediaCtrlVidRcMode;

typedef enum
{
    MEDIACTRL_VOUT_NO,              /* 不支持回显 */
    MEDIACTRL_VOUT_CVBS_PAL,        /* 回显CVBS P制 */
    MEDIACTRL_VOUT_CVBS_NTSC,       /* 回显CVBS N制 */
    MEDIACTRL_VOUT_HDSDI,           /* 回显SDI */
    MEDIACTRL_VOUT_LCD,             /* 数字接口 */
    MEDIACTRL_VOUT_HDMI,            /* 回显HDMI */
    MEDIACTRL_VOUT_DSI,             /* DSI MIPI-TX */
    MEDIACTRL_VOUT_BT1120,          /* BT1120与其他芯片对接 */
}EMediaCtrlVoutType;

typedef enum
{
    MEDIACTRL_VOUT_COLOR_RGB444,
    MEDIACTRL_VOUT_COLOR_YCBCR422,
    MEDIACTRL_VOUT_COLOR_YCBCR444,
    MEDIACTRL_VOUT_COLOR_YCBCR420,
}EMediaCtrlVoutColorType;

typedef enum
{
    MEDIACTRL_HDMI_EVENT_HOTPLUG,            /* hot-plug event */
    MEDIACTRL_HDMI_EVENT_NO_PLUG,            /* disconnection event */
}EMediaCtrlVoutHpType;

typedef enum
{
    MEDIACTRL_PIC_JPEG,
    MEDIACTRL_PIC_YUV420,
    MEDIACTRL_PIC_BMP,
    MEDIACTRL_PIC_PNG,
    MEDIACTRL_PIC_GIF
}EMediaCtrlPicType;                 /* 图片格式 */

typedef enum
{
    MEDIACTRL_ROTATE_NON,          /* 不旋转 */
    MEDIACTRL_ROTATE_RIGHT,        /* 右转 */
    MEDIACTRL_ROTATE_LEFT,         /* 左转 */
    MEDIACTRL_ROTATE_180           /* 180 degree only used for mobile devices */
}EMediaCtrlRotateType;

typedef enum
{
    MEDIACTRL_FLIP_NON,         /* 不翻转 */
    MEDIACTRL_FLIP_H,           /* 水平翻转 */
    MEDIACTRL_FLIP_V,           /* 垂直翻转 */
    MEDIACTRL_FLIP_C            /* 中心翻转 */
}EMediaCtrlFlipType;

typedef enum
{
    MEDIACTRL_OVERLAY_SINGLE_COLOR,  /* 单色遮蔽 */
    MEDIACTRL_OVERLAY_MOSAIC         /* 马赛克遮蔽 */
}EMediaCtrlOverlayType;              /* 图像遮蔽方式 */

typedef enum
{
    MEDIACTRL_COLOR_BLACK,
    MEDIACTRL_COLOR_RED,
    MEDIACTRL_COLOR_ORANGE,
    MEDIACTRL_COLOR_GREEN,
    MEDIACTRL_COLOR_YELLOW,
    MEDIACTRL_COLOR_BLUE,
    MEDIACTRL_COLOR_WHITE
}EMediaCtrlColor;

typedef enum
{
    MEDIACTRL_MOSAIC_STATIC,    /* 固定宽高和坐标的马赛克区域，默认这种方式 */
    MEDIACTRL_MOSAIC_DYNAMIC    /* 实时变动的马赛克区域 */
}EMediaCtrlMosaicType;

typedef enum
{
    MEDIACTRL_H264_PROFILE_BASELINE,
    MEDIACTRL_H264_PROFILE_MAIN,
    MEDIACTRL_H264_PROFILE_HIGH
}EMediaCtrlH264ProfileId;

typedef enum
{
    MEDIACTRL_AUD_ENC_PCMA,
    MEDIACTRL_AUD_ENC_PCMU,
    MEDIACTRL_AUD_ENC_G728,
    MEDIACTRL_AUD_ENC_G7221C,
    MEDIACTRL_AUD_ENC_ADPCM,
    MEDIACTRL_AUD_ENC_G722,
    MEDIACTRL_AUD_ENC_AACLC,
    MEDIACTRL_AUD_ENC_G726,
    MEDIACTRL_AUD_ENC_OPUS,
    MEDIACTRL_AUD_ENC_AMR
}EMediaCtrlAudEncType;

typedef enum
{
    MEDIACTRL_AUD_AACLC_16K_MONO = 20,
    MEDIACTRL_AUD_AACLC_16K_STEREO = 36,
    MEDIACTRL_AUD_AACLC_32K_MONO = 19,
    MEDIACTRL_AUD_AACLC_32K_STEREO = 35,
    MEDIACTRL_AUD_AACLC_48K_MONO = 17,
    MEDIACTRL_AUD_AACLC_48K_STEREO = 33,
    MEDIACTRL_AUD_AACLC_MAX
}EMediaCtrlAudAACLCMode;

typedef enum
{
    MEDIACTRL_AUD_SAMPLE_RATE_8K  = 8000,
    MEDIACTRL_AUD_SAMPLE_RATE_16K = 16000,
    MEDIACTRL_AUD_SAMPLE_RATE_32K = 32000,
    MEDIACTRL_AUD_SAMPLE_RATE_48K = 48000
}EMediaCtrlAudSampleRate;

typedef enum
{
    MEDIACTRL_AUD_SAMPLE_BITS_8  = 8,
    MEDIACTRL_AUD_SAMPLE_BITS_16 = 16,
    MEDIACTRL_AUD_SAMPLE_BITS_32 = 32
}EMediaCtrlAudSampleBITS;

typedef enum
{
    MEDIACTRL_AUD_CHANNEL_MONO = 1,
    MEDIACTRL_AUD_CHANNEL_STEREO = 2
}EMediaCtrlAudChannelMode;

typedef enum
{
    MEDIACTRL_AUD_INPUT_LINE_IN,  //默认是line in输入模式
    MEDIACTRL_AUD_INPUT_MIC_IN,
    MEDIACTRL_AUD_INPUT_ENC_EXT,  //数据源自外部编码后的数据
    MEDIACTRL_AUD_INPUT_BLE_IN,   //蓝牙输入模式
    MEDIACTRL_AUD_INPUT_HDMI_IN   //HDMI输入模式
}EMediaCtrlAudInputMode;

typedef enum
{
    MEDIACTRL_AUD_OUTPUT_LINE_OUT,   //默认是line in输入模式
    MEDIACTRL_AUD_OUTPUT_SPEAKER_OUT,
    MEDIACTRL_AUD_OUTPUT_BLE_OUT,    //蓝牙输出模式
    MEDIACTRL_AUD_OUTPUT_HDMI_OUT    //HDMI输出模式
}EMediaCtrlAudOutputMode;

typedef enum
{
    MEDIACTRL_AUD_MOBILE_CHN_MEDIA,  /* 手机设备的多媒体通道 */
    MEDIACTRL_AUD_MOBILE_CHN_VOICE   /* 手机设备的通话通道 */
}EMediaCtrlAudMobileChnType;        /* 手机设备音频通道类型 */

typedef enum
{
    MEDIACTRL_AUD_AEC_KEDACOM,   /* 科达自有aec */
    MEDIACTRL_AUD_AEC_WEBRTC     /* webrtc的aec 目前只Android平台支持 */
}EMediaCtrlAudAecType;

typedef enum
{
    MEDIACTRL_AUD_MICIN,
    MEDIACTRL_AUD_LINEIN1 = MEDIACTRL_AUD_MICIN,    //mic-in
    MEDIACTRL_AUD_LINEIN2,                            //line-in
    MEDIACTRL_AUD_LINEOUT,                            //line-out
    MEDIACTRL_AUD_MIX0,
    MEDIACTRL_AUD_MIX1,
    MEDIACTRL_AUD_DEC_BASE,     /* 解码通道开始 0:MEDIACTRL_AUD_DEC_BASE 1:MEDIACTRL_AUD_DEC_BASE +1 */

    MEDIACTRL_AUD_CHAN_MAX
}EMediaCtrlAudChannelType;

typedef enum
{
    MEDIACTRL_AUD_METER_TYPE_PEAK = 0,    //音频峰值db
    MEDIACTRL_AUD_METER_TYPE_RMS,    //平均能量db
}EMediaCtrlAudMeterType;

typedef enum
{
    MEDIACTRL_RUNNING_SUCCESS = 0,     /* 状态正常 */
    MEDIACTRL_RUNNING_AUD_CAP_ERROR,   /* 音频采集异常 */
    MEDIACTRL_RUNNING_AUD_ENC_ERROR,   /* 音频编码异常 */
    MEDIACTRL_RUNNING_AUD_DEC_ERROR,   /* 音频解码异常 */
    MEDIACTRL_RUNNING_AUD_PLY_ERROR,   /* 音频播放异常 */
    MEDIACTRL_RUNNING_VID_CAP_ERROR,   /* 视频采集异常 */
    MEDIACTRL_RUNNING_VID_ENC_ERROR,    /* 视频编码异常 */
    MEDIACTRL_RUNNING_VID_CAMERA_PREEMPT    /* android camera preempt by another apk */
}EMediaCtrlRunningStatus;

typedef enum
{
    MEDIACTRL_VIN_INPUT_TYPE_SENSOR,
    MEDIACTRL_VIN_INPUT_TYPE_YUV,
}EMediaCtrlVinInputType;

typedef enum
{
    MEDIACTRL_VIN_PNP_EVENT_NONE,
    MEDIACTRL_VIN_PNP_EVENT_SIGNAL_LOST,
    MEDIACTRL_VIN_PNP_EVENT_SIGNAL_RECOVERY,
    MEDIACTRL_VIN_PNP_EVENT_SIGNAL_CHANGE,
    MEDIACTRL_VIN_PNP_EVENT_LAST = MEDIACTRL_VIN_PNP_EVENT_SIGNAL_CHANGE,
}EMediaCtrlVinPnpEvent;

typedef enum
{
    MEDIACTRL_FISHEYE_MOUNT_DESKTOP,
    MEDIACTRL_FISHEYE_MOUNT_CEILING,
    MEDIACTRL_FISHEYE_MOUNT_WALL,
    MEDIACTRL_FISHEYE_MOUNT_LAST
}EMediaCtrlFisheyeMountMode;

typedef enum
{
    MEDIACTRL_FISHEYE_VIEW_360_PANORAMA,
    MEDIACTRL_FISHEYE_VIEW_180_PANORAMA,
    MEDIACTRL_FISHEYE_VIEW_NORMAL,
    MEDIACTRL_FISHEYE_VIEW_NO_TRANSFORMATION,
    MEDIACTRL_FISHEYE_VIEW_LAST
}EMediaCtrlFisheyeViewMode;

typedef enum
{
    MEDIACTRL_HISI_STITCH = 0,
    MEDIACTRL_PTGUI_STITCH,
}EMediaCtrlStitchScheme;

typedef enum
{
    MEDIACTRL_STITCH_CROP_NONE = 0,
    MEDIACTRL_STITCH_CROP_ENABLE,
}EMediaCtrlStitchCrop;

typedef enum
{
    MEDIACTRL_STITCH_OUTRES_CAPWH = 0,
    MEDIACTRL_STITCH_OUTRES_5640X1920,
}EMediaCtrlStitchOutResolution;

typedef enum
{
    ESTITCH_6SENSOR_180DEGREE_SLANT,
    ESTITCH_6SENSOR_180DEGREE_HORIZONTAL,
    ESTITCH_6SENSOR_360DEGREE_SLANT,
    ESTITCH_6SENSOR_360DEGREE_HORIZONTAL,
    ESTITCH_3SENSOR_180DEGREE_SLANT,
    ESTITCH_3SENSOR_180DEGREE_HORIZONTAL,
    ESTITCH_3SENSOR_360DEGREE_SLANT,
    ESTITCH_3SENSOR_360DEGREE_HORIZONTAL,
}EMediaCtrlStitchMechanism;

typedef struct
{
    s32 nCenterX;
    s32 nCenterY;
    u32 dwFovX;
    u32 dwFovY;
    s32 nYaw;
    s32 nPitch;
    s32 nRoll;
}TMediaCtrlStitchFinetuneParam;

typedef struct
{
    s32    s32X;
    s32    s32Y;
    u32    u32Width;
    u32    u32Height;
}TMediaCtrlFisheyeRegionRect;

typedef struct
{
    EMediaCtrlFisheyeViewMode ViewMode;  /* fisheye view mode */
    u32 u32Pan;
    u32 u32Radius;
    u32 u32ViewAngle;            //horizontal view angle,  20 ~ 60 degree for zoom function via EPTZ
    u32 u32TrapezoidCoef;
    u32 u32Tile;
    u32 u32HorZoom;
    u32 u32VerZoom;
}TMediaCtrlFisheyeRegionParam;

typedef struct
{
    BOOL32 bEnable;
    EMediaCtrlFisheyeMountMode mount_mode;
    u32 u32RegionNum;  /* [0, 4] */
    TMediaCtrlFisheyeRegionParam fisheye_region_param[MEDIACTRL_MAX_ENC_FISHEYE_REGION];
}TMediaCtrlEncFisheyeParam;

typedef enum
{
    //多个类型时，用 0|EMediaCtrlAlgObjectType 得到值
    MEDIACTRL_OBJTYPE_UNKNOWN  = 0x00000000,    //目标类型未知
    MEDIACTRL_OBJTYPE_PED      = 0x00000001,    //行人
    MEDIACTRL_OBJTYPE_VEH      = 0x00000002,    //车辆
    MEDIACTRL_OBJTYPE_NONMOTOR = 0x00000004,    //非机动车
    MEDIACTRL_OBJTYPE_SHIP     = 0x00000008     //船
}EMediaCtrlAlgObjectType;

//运动目标位置信息
typedef struct
{
    u16 wLeft;                     //目标的左边界
    u16 wRight;                    //目标的右边界
    u16 wTop;                      //目标的上边界
    u16 wBottom;                   //目标的下边界
    u32 dwPixelNum;                     //目标前景像素数
    u32 dwTargetArea;                 //目标的面积
    u8 EffectFlag;                 //异常目标有效性标志（INVALID：无效，EFFECTIVE：有效） 是否需要？
    u16 wAreaIndex;                //区域编号
    u16 wTargetIndex;             //目标编号
    EMediaCtrlAlgObjectType eObjType;  // 通过 与EMediaCtrlAlgObjectType 得到目标类型
}TMediaCtrlMovingObject;

typedef struct
{
    u16 wWidth;
    u16 wHeight;
}TMediaCtrlVidReslution;

typedef struct
{
    u16 wWidth;
    u16 wHeight;
}TMediaCtrlRectSize;

typedef struct
{
    u16 wStartX;
    u16 wStartY;
    u16 wWidth;
    u16 wHeight;
}TMediaCtrlRectRegion;

typedef enum
{
    MEDIACTRL_STITCH_MODE_NONE,
    MEDIACTRL_STITCH_MODE_HOR,
    MEDIACTRL_STITCH_MODE_VER,
    MEDIACTRL_STITCH_MODE_OP,
    MEDIACTRL_STITCH_MODE_BUTT,
}EMediaCtrlStitchMode;

typedef struct
{
    EMediaCtrlStitchMode stitch_mode;
    s32 cap_chn0;
    s32 cap_chn1;
    TMediaCtrlRectRegion cap_src_rect0;
    TMediaCtrlRectRegion cap_dst_rect0;
    TMediaCtrlRectRegion cap_src_rect1;
    TMediaCtrlRectRegion cap_dst_rect1;
    TMediaCtrlVidReslution out_res;
}TMediaCtrlStitchParam;

typedef struct
{
    s32 vout_dev;
    EMediaCtrlVoutType eVoutType;          /* 回显方式 */
    EMediaCtrlVoutHpType eVoutHpType;     /* 回显热插拔类型 */
}TMediaCtrlVoutHpParam;

typedef void (*TMediaCtrlVoHpCallBack)(TMediaCtrlVoutHpParam *ptVoHpParam);

typedef struct
{
    s32 vout_dev;
    BOOL32 bOpen;                    /* 回显开关 */
    EMediaCtrlVoutType eVoutType;    /* 回显方式 */
    EMediaCtrlVidChnType eVoutSrc;   /* 回显数据源 */
    TMediaCtrlVidReslution tVoutRes; /* 回显分辨率 */
    EMediaCtrlVoutColorType eVoutColor;
    BOOL32 bInterence;               /* 是否隔行 */
    u32 dwFramerate;                 /* 帧率 */
    BOOL32 bForce_DVI;
    TMediaCtrlStitchParam stitch_param;
    TMediaCtrlVoHpCallBack tVoutHpCB;
}TMediaCtrlVoutParam;

typedef struct
{
    EMediaCtrlRotateType eVidRotate;  /* 旋转角度 */
    EMediaCtrlFlipType eVidFlip;      /* 翻转方式 */
}TMediaCtrlRotateFlipParam;

typedef struct
{
    BOOL32 bEnable;                    /* 畸变矫正使能 */
    BOOL32 bZoomEnable;                /* 远端放大使能 */
    u32 dwRatio;                       /* 畸变矫正参数 范围在不同平台有差异，hisi 0-100 */
    u32 dwRatioEx;                     /* 辅助畸变校正参数 Android平台使用，hisi未使用 */
    BOOL32 bCrop;                      /* 是否需要进行裁剪 */
    TMediaCtrlRectRegion tCropRect;    /* 裁剪区域，校正后裁剪后放大到原始比例，裁剪区域宽高与原始宽高比例需要一致 */
    u32 dwSharpThre;                   /* 锐化等级，0~100，用于Android畸变矫正后的效果提升 */
    s32 nTeleZoomInRatio;              /* 以下参数是远端放大参数 */
    s32 nTeleCurvLevel;
    s32 nTeleMultVal;
    s32 nWideRatio;
    s32 nMidWidth;
    s32 nMidHeight;
    s32 nAdjHeight;
    float fWidthScale;
}TMediaCtrlLdcParam;

typedef struct
{
    u16 wPosX;
    u16 wPosY;
}TMediaCtrlPoint;

typedef struct
{
    u16 wPosX;
    u16 wPosY;
}TMediaCtrlPosition;

typedef struct
{
    u32 dwWidthMin;            /* 运动目标尺寸过滤信息 */
    u32 dwHeightMin;
    u32 dwWidthMax;
    u32 dwHeightMax;
}TMediaCtrlTargetFilterInfo;

typedef enum
{
    MEDIACTRL_VOUT_ZOOM_IN_TYPE_RECT,
    MEDIACTRL_VOUT_ZOOM_IN_TYPE_RATIO,
}EMediaCtrlVoutZoomInType;

typedef struct
{
    u32 u32XRatio;
    u32 u32YRatio;
    u32 u32WRatio;
    u32 u32HRatio;
}TMediaCtrlVoutZoonInRatio;

typedef struct
{
    EMediaCtrlVoutZoomInType eVoutZoomInType;
    TMediaCtrlRectRegion tVoutZoomInRect;
    TMediaCtrlVoutZoonInRatio tVoutZoomInRatio;
}TMediaCtrlVoutZoomInParam;

#define MEDIACTRL_MAX_OVERLAY_POLYGON_POINT (8)         /* 遮蔽多变形最大顶点数 */
typedef struct
{
    TMediaCtrlPoint atPloygonPoint[MEDIACTRL_MAX_OVERLAY_POLYGON_POINT];
    u32 dwPointNum;
}TMediaCtrlPloygon;

#define MEDIACTRL_MAX_NET_PACKET_NUM (8)              /* 每条流最大打包种类数量 */

typedef struct
{
    u32 atNetPacketId[MEDIACTRL_MAX_NET_PACKET_NUM];  /* 打包通道号数组，按照实际支持类型设置 */
    u32 dwNetPacketNum;                               /* 支持打包种类 */
}TMediaCtrlNetPacketInfo;

typedef struct
{
    EMediaCtrlAudSampleRate eSampleRate;
}TMediaCtrlAudCapCapacity;                              /* 音频采集能力级 */

typedef struct
{
    u8 byEncChnNum;                                          /* 当前采集通道的编码通道数 */
}TMediaCtrlAudEncCapcity;                                 /* 音频编码能力级 */

typedef struct
{
    u8 byDecChnNum;                                          /* 当前播放通道的解码通道数 */
}TMediaCtrlAudDecCapcity;                                 /* 音频编码能力级 */

typedef struct
{
    u8 byEncChnNum;                                                /* 设备实际编码通道数 */
    TMediaCtrlVidReslution atMaxRes[MEDIACTRL_MAX_VID_ENC_NUM];    /* 每条流编码最大分辨率 */
}TMediaCtrlVidEncCapcity;                                          /* 视频编码能力级 */

typedef struct
{
    u32 dwVidDecTotalPixel;               /* 视频解码总的最大像素数 */
    TMediaCtrlVidReslution tMaxDecRes;    /* 解码通道解码最大分辨率 */
}TMediaCtrlVidDecCapability;

typedef struct
{
    u32 dwTotalSize;                                     /* 当前编码通道所有OSD总面积大小 */
    u32 dwMaxAreaNum;                                    /* 当前编码通道最大OSD块数 */
    u32 dwMaxAraeSize[MEDIACTRL_MAX_OSD_AREA_NUM];       /* 每块OSD区域最大面积大小,只对初始化时须设置最大面积平台有效 */
}TMediaCtrlVidiChnOsdCapcity;

typedef struct
{
    EMediaCtrlVinInputType eVinInputType;    /* 视频输入源类型 */
    EMediaCtrlVidChnType eVinChnType;       /* 视频采集通道名字 */
    TMediaCtrlVidReslution tVidInRes;   /* 视频源输入分辨率 */
    u32 dwVinCapRate;  /* 视频输入帧率 */
    BOOL32 bInterlace;  /* 是否隔行 */
    BOOL32 bAutoScreenPos;  /* 是否采用自动屏幕调整,用于编码卡VGA偏屏自动调整 */
}TMediaCtrlVinCapcity;

typedef struct
{
    u8 byOsdChnNum;                                    /* OSD通道数, 与编码通道数一致 */
    TMediaCtrlVidiChnOsdCapcity atChnOsdCapcity[MEDIACTRL_MAX_VID_ENC_NUM]; /* 每个通道OSD能力 */
}TMediaCtrlVidOsdCapcity;

typedef struct
{
    TMediaCtrlVidReslution tCanvasRes; /* 画布分辨率 遮蔽、简单智能区域设置以此为归一化宽高 */
}TMediaCtrlRegionCanvasCapcity;

typedef struct
{
    s32 nSupportCameraMode;    /* 支持的模式，多模式支持可以相与 */
}TMediaCtrlCameraModeCapcity;

typedef struct
{
    BOOL32 bOpen;              /* 是否开启低延时模式 */
}TMediaCtrlLowDelayModeParam;  /* 低延时模式参数 */

typedef struct
{
    BOOL32 bOpen;         /* 是否开启HDR模式 */
}TMediaCtrlHdrModeParam;  /* HDR模式参数 */

typedef struct
{
    BOOL32 bOpen;         /* 是否开启fisheye模式 */
}TMediaCtrlFisheyeModeParam;  /* fisheye模式参数 */

#define MEDIACTRL_MAX_VID_SUB_CAP_NUM (2)         /* 采集子通道最大数量，包含主流大图，副流小图等 */
#define MEDIACTRL_MAX_VID_EXTERN_RES_NUM (5)         /* 智能额外输入的最大通道数 */

typedef struct
{
    TMediaCtrlVidReslution atIntelligentCapRes[MEDIACTRL_MAX_VID_SUB_CAP_NUM]; /* 智能流分辨率 */
    TMediaCtrlVidReslution atIntelligentExternRes[MEDIACTRL_MAX_VID_EXTERN_RES_NUM]; /* 智能额外输入的分辨率 */
    u32 adwIntelligentCapRate[MEDIACTRL_MAX_VID_SUB_CAP_NUM];  /* 智能流帧率 */
    u8 byIntelligentRawBufNum[MEDIACTRL_MAX_VID_SUB_CAP_NUM];  /* raw buf num */
    u8 byIntelligentYuvBufNum[MEDIACTRL_MAX_VID_SUB_CAP_NUM];  /* yuv buf num */
    TMediaCtrlVidReslution tIntelligentJpegRes;                /* big jpeg snap res */
    u8 byIsRawPackingMode;                                     /* raw packing mode */
    u8 byEncDelayFrameNum;                                     /* enc delay frame for yuv edit [0~5] */
}TMediaCtrlIntelligentModeParam;

typedef struct
{
    TMediaCtrlLowDelayModeParam tLowDelayParam;     /* 低延时模式参数 */
    TMediaCtrlHdrModeParam tHdrParam;               /* HDR模式参数 */
    TMediaCtrlIntelligentModeParam tIntelliParam;   /* 智能模式参数 */
}TMediaCtrlCameraModeParam;                       /* 摄像机模式参数 */

typedef struct
{
    u8 byAudCapChnNum;                                                         /* 音频采集通道数 */
    TMediaCtrlAudCapCapacity tAudCapCapacity;
    TMediaCtrlAudEncCapcity atAudEncCapcity[MEDIACTRL_MAX_AUD_CAP_NUM];           /* 音频编码相关能力级 */
    u8 byAudPlyChnNum;                                                         /* 音频播放通道数 */
    TMediaCtrlAudDecCapcity atAudDecCapcity[MEDIACTRL_MAX_AUD_PLY_NUM];           /* 音频解码相关能力级 */
    u8 byAudPcmMixChnNum;                                                      /* 音频混音通道数量 */
    u8 byVidCapChnNum;                                                         /* 视频采集通道数 */
    TMediaCtrlVinCapcity atVinCapcity[MEDIACTRL_MAX_VID_CAP_NUM];              /* 视频输入源能力级别 */
    TMediaCtrlVidEncCapcity atVidEncCapcity[MEDIACTRL_MAX_VID_CAP_NUM];        /* 视频编码相关能力级 */
    TMediaCtrlVidOsdCapcity atVidOsdCapcity[MEDIACTRL_MAX_VID_CAP_NUM];        /* OSD 能力级 */
    TMediaCtrlCameraModeCapcity tCameraModeCapcity;                            /* 摄像机模式能力级 */
    TMediaCtrlRegionCanvasCapcity tRegionCanvasCapcity; /* 区域画布大小能力级，遮蔽，简单智能坐标以它为相对单位 */
    u8 byVidDecChnNum;                                                         /* 视频解码通道数 */
    TMediaCtrlVidDecCapability tVidDecCapability;                              /* 视频解码能力集 */
}TMediaCtrlCapcity;                                                            /* 媒体控制能力级 */

typedef struct
{
    EMediaCtrlAudSampleBITS eSampleBits;
    u32 dwFrameLen;
    EMediaCtrlAudChannelMode eChannelMode; /* 以上参数可以不填，采用底层默认值，为以后特殊平台预留 */
    EMediaCtrlAudInputMode eInputMode;
    EMediaCtrlAudMobileChnType eMobileChnType; /* 手机音频通道模式 */
    u8 byCapVolume;                 /* 采集音量 0-99 0为静音，99最大,根据设备实际音量调节, 推荐值1 */
    BOOL32 bSpeEnable;//采集端spe是否使能，0:不使能；1:使能。
    u32 dwSpeLevel;//采集端spe级别，取值0-3，噪声抑制程度由低到高。
    BOOL32 bEncMixEnable;    //是否开启编码混音，0不开启，1开启
}TMediaCtrlAudCapParam;

typedef struct
{
    EMediaCtrlAudSampleBITS eSampleBits;
    u32 dwFrameLen;
    EMediaCtrlAudChannelMode eChannelMode; /* 以上参数可以不填，采用底层默认值，为以后特殊平台预留 */
    u8 byPlyVolume;                 /* 播放音量 0-99 0为静音，99最大,根据设备实际音量调节,推荐值 90 */
    BOOL32 bSpeEnable;//播放端spe是否使能，0:不使能；1:使能。
    u32 dwSpeLevel;//播放端spe级别，取值0-3，噪声抑制程度由低到高。
    EMediaCtrlAudOutputMode eOutputMode;
}TMediaCtrlAudPlyParam;

typedef struct
{
    EMediaCtrlAudEncType eEncType;         /* 音频编码类型 */
    EMediaCtrlAudAACLCMode eAaclcMode;       /* 音频编码类型为aaclc时的mode，填0时对aaclc默认为16K采样率、单声道模式 */
    u32 dwEncBitrate;                       /* 音频编码码率，目前仅G726使用， 填0时对G726默认采用32Kbps，可取值16000,24000,32000,40000 */
    u8 byEncVolume;                        /* 编码音量 0-100 100最大, 0静音，50为保持不变 */
}TMediaCtrlAudEncParam;

typedef struct
{
    u8 byDecVolume;                        /* 解码音量, 0-100 100最大, 0静音，50为保持不变 */
}TMediaCtrlAudDecParam;

typedef struct
{
    BOOL32 bAecEnable;
    EMediaCtrlAudAecType eAecType;
}TMediaCtrlAudAecParam;

typedef struct
{
    BOOL32 bMixEnable;
}TMediaCtrlAudMixParam;

typedef struct
{
    u32 dwReserved;
}TMediaCtrlAudEncMixParam;

typedef struct
{
	BOOL32 bResEnable;                     /* 重采样使能 */
	EMediaCtrlAudSampleRate eInSampleRate; /* 输入采样率,不大于最大采集采样率,输出采样率为编码的采样率 */
}TMediaCtrlAudResParam;                    /* 重采样参数设置,当前蓝牙模式下生效 */

typedef struct
{
    BOOL32 bMeterEnable;            //该通道的meter功能是否开启
    EMediaCtrlAudMeterType eType;    //输出的数据类型，是均值还是峰值
    BOOL32 bHoldEnable;                //开关Hold Time功能
    BOOL32 bIndefiniteHold;            //配合Hold功能使用，只在数值超过目前数值时，对输出进行更新
    u32 dwHoldTime;                    //决定多久时间变化一次数据值 最小单位是ms
}TMediaCtrlAudMeterParam;

typedef struct
{
    TMediaCtrlAudCapParam tCapParam;
    TMediaCtrlAudPlyParam tPlyParam;
    TMediaCtrlAudEncParam atEncParam[MEDIACTRL_MAX_AUD_ENC_NUM];       /* 音频编码类型 */
    TMediaCtrlAudDecParam atDecParam[MEDIACTRL_MAX_AUD_DEC_NUM];
    TMediaCtrlAudAecParam tAecParam;
    TMediaCtrlAudMixParam tMixParam;
    TMediaCtrlAudResParam tResParam;
}TMediaCtrlAudParam;

typedef struct
{
    TMediaCtrlRectRegion tRoiRegion;                           /* ROI区域 */
    EMediaCtrlRoiLevel eRoiLevel;                              /* ROI等级 */
}TMediaCtrlRoiInfo;

typedef struct
{
    EMediaCtrlVidChnType   eVinChnType;     /* 视频输入源类型 */
    TMediaCtrlVidReslution tVidInRes;         /* 视频源输入分辨率 */
    u32 dwVinCapRate;                         /* 视频输入帧率 */
    BOOL32 bInterlace;                         /* 是否隔行 */
    void *pvIspParam;                        /* isp 参数 编码卡设置NULL */
}TMediaCtrlVinParam;

typedef enum
{
    MEDIACTRL_ENC_SCENE_MODE_NORMAL,                        /* 正常模式 */
    MEDIACTRL_ENC_SCENE_MODE_VEHICLE,                       /* 车载模式 */
    MEDIACTRL_ENC_SCENE_MODE_MOBILE,                        /* Mobile encoder mode */
}EMediaCtrlEncSceneMode;                                    /* 编码场景模式 */

#define MEDIACTRL_MAX_ROI_REGION (4)
typedef struct
{
    EMediaCtrlVidEncType eEncType;                          /* 视频编码类型 */
    EMediaCtrlVidRcMode  eRcMode;                           /* 码流控制方式 CBR VBR */
    u16   wMaxKeyFrameInterval;                             /* 最大I帧间隔 */
    u16   wBitRate;                                         /* 码率 */
    u8    byFrameRate;                                        /* 帧率 */
    u8    bySmartH264Flag;                                  /* 是否开启smartH264编码,现在只有0 0通道可以开启 */
    EMediaCtrlImageQuality    eImageQuality;                /* 图像质量 */
    EMediaCtrlH264ProfileId    eH264ProfileId;              /* 编码复杂度 */
    TMediaCtrlVidReslution tVidCapRes;                      /* 采集分辨率，未裁剪时即为编码分辨率 */
    TMediaCtrlVidReslution tVidEncRes;                      /* 编码分辨率 */
    BOOL32 bCrop;                                            /* 是否采集 */
    TMediaCtrlPosition tCropStart;                            /* 裁剪起点 */
    TMediaCtrlRectSize tCropSize;                                                                     /*裁剪相对宽高*/
    s32 nRoiNum;                                            /* ROI区域数0，代表关闭 */
    TMediaCtrlRoiInfo atRoiInfo[MEDIACTRL_MAX_ROI_REGION];  /* 每个区域ROI信息数组 */
    u32    bySrcFrameRate;                                    /* 初始输入帧率 */
    EMediaCtrlEncSceneMode eEncSceneMode;                   /* 编码场景模式 */
    EMediaCtrlVidChnType aeEncSource;
    u8 byBitrateSmoothLevel;    /* 0~100 */
}TMediaCtrlVidEncParam;                                     /* 视频编码参数 */

typedef struct
{
    u32 dwFrameRate;                          /* 帧率 */
    BOOL32 bGetFrame;                         /* 是否获取解码后数据 */
    EMediaCtrlVidDecType      eDecType;       /* 解码类型 */
    TMediaCtrlVidReslution    tVidDecRes;     /* 解码分辨率 */
    EMediaCtrlVidDateFormat   eDateFormat;    /* 需要获取的解码数据类型 */
    TMediaCtrlVidEffect       tVidDecEffect;  /* 解码效果 */
    EMediaCtrlVidDecFrameMode eFrameMode;     /* 视频解码帧模式 */
}TMediaCtrlVidDecParam;

typedef enum
{
    SOFT_DIS,                                           /* 软件防抖 */
    HARD_DIS,                                               /* 硬件防抖 */
    HYBRID_DIS                                                /* 混合防抖 */
}EMediaCtrldis;                                              /* 防抖模式 */
typedef enum
{
    MEDIACTRL_DIS_GYRO_MODE_IPC,
    MEDIACTRL_DIS_GYRO_MODE_DV,
}EMediaCtrlGyroMode;

typedef struct
{
    BOOL32             bDisEnable;      /* 是否开启防抖 */
    u32                dwDisLevel;      /* 防抖等级 */
    EMediaCtrldis      ctrldis;         /* 防抖模式 */
    float              fHorFov;         /* 水平视场角(硬件防抖需要设置) */
    BOOL32             bDisStillEnable; /* 暂停防抖(变倍或转云台需要将设置) */
    EMediaCtrlGyroMode eGyroMode;       /* 硬件防抖有效,EMediaCtrlGyroMode */
}TMediaCtrlDisParam;


typedef struct
{
    BOOL32 bCrop;                                    /* 是否采集 */
    TMediaCtrlRectRegion tCropRect;                            /* 裁剪起点 */
}TMediaCtrlCropParam;

typedef enum
{
    MEDIACTRL_ENC_FREEZE_SUCCESS = 0,     /* 状态正常 */
    MEDIACTRL_ENC_FREEZE_FAIL
}EMediaCtrlVidEncFreezeStatus;

typedef enum
{
    MEDIACTRL_STITCH_CALIBRATION_GPU = 0,    /*  2019.11.12 SOC Calibration Type , Currently only Hisi 3559AV100 support */
    MEDIACTRL_STITCH_CALIBRATION_SOFT    /*  2019.11.12 Software Calibration , Currently only use PTGUI */
}EMediaCtrlStitchCalibrateType;

/* =================================================================================
  简单智能相关接口
*================================================================================== */
typedef enum
{
    MEDIACTRL_ALARM_MOTION,            /* 移动侦测 */
    MEDIACTRL_ALARM_OVERLAY,           /* 遮蔽侦测 */
    MEDIACTRL_ALARM_DEFOCUS,           /* 虚焦检测 */
    MEDIACTRL_ALARM_SCENE_CHG,         /* 场景变化检测 */
    MEDIACTRL_ALARM_REGION_INVASION,   /* 区域入侵 */
    MEDIACTRL_ALARM_REGION_ENTER,    /* 区域进入 */
    MEDIACTRL_ALARM_REGION_LEAVE,    /* 区域离开 */
    MEDIACTRL_ALARM_PTZ_TRACK,         /* 单球跟踪 */
    MEDIACTRL_ALARM_TRIP_LINE,         /* 拌线检测 */
    MEDIACTRL_ALARM_FACE,              /* 人脸检测 */
    MEDIACTRL_ALARM_AUDIO_ABNORMAL,    /* 声音异常检测 */
    MEDIACTRL_ALARM_REMOVEDOBJECT_DET,        /* 物体移除检测 */
    MEDIACTRL_ALARM_ABANDONOBJECT_DET,       /* 物体丢弃检测 */
    MEDIACTRL_ALARM_GATHER_DET,        /* 人员聚集检测 */
    MEDIACTRL_ALARM_MOTION_TARGET_DET, /* 移动目标侦测 */
    MEDIACTRL_ALARM_LUMA_CHG,         /* 亮度变化检测 */
    MEDIACTRL_ALARM_GAUSS_INFO,        /* Feedback the gauss info(the moving target's info) to the web */
    MEDIACTRL_ALARM_TEACHER_TRACK,        /* eptz camera use not in sac */
    MEDIACTRL_ALARM_CAR_PLATE_DET,
    MEDIACTRL_ALARM_FACE_MOSAIC,      /* 人脸马赛克 */
    MEDIACTRL_ALARM_MAN_PLATE_DET,
    /* 高级智能，检测大图，得到人、车信息，
      除了新增EMediaCtrlAlgObjectType，配置参数与基础智能一致，
      对于相同功能，基础和高级只能开一种 */
    MEDIACTRL_ADVANCED_MOTION,            /* 移动侦测 */
    MEDIACTRL_ADVANCED_TRIP_LINE,         /* 拌线检测 */
    MEDIACTRL_ADVANCED_REGION_ENTER,      /* 区域进入 */
    MEDIACTRL_ADVANCED_REGION_LEAVE,      /* 区域离开 */
    MEDIACTRL_ADVANCED_REGION_INVASION,   /* 区域入侵 */
    MEDIACTRL_ADVANCED_GATHER_DET,          /* 人员聚集 */
    MEDIACTRL_ADVANCED_DOME_TRACK,         /* dome track */
    MEDIACTRL_ADVANCED_FIREALARM,         /* fire datect */
}EMediaCtrlAlarmType;

/* ================================================================================================
TMediaCtrlAlarmDetectCB :告警事件回调函数
nCapChn：    告警事件对应的通道号
eAlarmType:  告警事件类型
pCBParam:    告警事件回调函数参数,对于不同的告警类型有不同定义
             移动侦测: TMediaCtrlMdCBParam,               遮蔽告警: TMediaCtrlOdCBParam
             虚焦检测：TMediaCtrlDeFocusCBParam           场景变换：TMediaCtrlSceneChgCBParam
             区域入侵：TMediaCtrlRegionInvasionCBParam    单球跟踪：TMediaCtrlPtzTrackCBParam
             拌线检测：TMediaCtrlTripLineCBParam          人脸检测：TMediaCtrlFaceDetectCBParam
             移动目标检测: TMediaCtrlMtdCBParam         亮度变化: TMediaCtrlLumaChgCBParam
================================================================================================ */
typedef s32 (*TMediaCtrlAlarmDetectCB)(s32 nCapChn, EMediaCtrlAlarmType eAlarmType, void *pCBParam);

/* ============================================================================================
函数名：MediaCtrlSetAlarmDetectCallBack
用途:   设置告警事件回调
============================================================================================== */
s32 MediaCtrlSetAlarmDetectCallBack(TMediaCtrlAlarmDetectCB tAlarmDetectCB);

/* ============================================================================================
函数名：MediaCtrlSetAlarmDetectParam
用途:   设置告警事件的参数
参数：
cap_chn:    视频采集通道号
eAlarmType: 视频告警类型
pParam:    告警事件对应的参数，不同的告警类型有不同参数，
           移动侦测: TMediaCtrlMdParam,               遮蔽告警: TMediaCtrlOdParam
           虚焦检测：TMediaCtrlDeFocusParam           场景变换：TMediaCtrlSceneChgParam
           区域入侵：TMediaCtrlRegionInvasionParam    单球跟踪：TMediaCtrlPtzTrackParam
           拌线检测：TMediaCtrlTripLineParam          人脸检测：TMediaCtrlFaceDetectParam
           移动目标检测: TMediaCtrlMtdParam         亮度变化: TMediaCtrlLumaChgParam
============================================================================================== */
s32 MediaCtrlSetAlarmDetectParam(s32 cap_chn, EMediaCtrlAlarmType eAlarmType, void *pParam);

typedef enum
{
    MEDIACTRL_SAC_GROUNF = 10,
    MEDIACTRL_SAC_VER
}EMediaCtrlSacSceneType;
/* =================================音频异常检测========================================= */
typedef struct
{
    u32 dwSensitivity;            //灵敏度调节[1-99]
    u32 dwThreshdBValue;        //告警阈值范围(0 - 100)
    BOOL32 bEnable;         //异常音检测标志位   1：开启 0：关闭
}TMediaCtrlAudioDetectParam;

typedef struct
{
    BOOL32 bAlarm;                            /* 告警状态 */
    s32 s32PowerValue;         // 一段时间内的语音峰值功率(dB)
}TMediaCtrlAudioDetectCBParam;                              /* 声音异常检测回调参数结构体 */

/* =================================移动侦测========================================= */
#define MEDIACTRL_MAX_MD_REGION (16)
typedef struct
{
    BOOL32 bAlarm;                             /* 告警或恢复 */
    u32    dwAreaIndex;                        /* 告警或恢复的区域编号 */
}TMediaCtrlRegionAlarmState;                    /* 区域告警状态结构体 */

typedef struct
{
    TMediaCtrlRectRegion tMdRegion;                         /* 移动侦测区域 */
    u32 dwSensitivity;                                      /* 移动侦测灵敏度, 0-100 */
    EMediaCtrlAlgObjectType eObjType;
}TMediaCtrlMdInfo;

typedef struct
{
    TMediaCtrlMdInfo atMdInfo[MEDIACTRL_MAX_MD_REGION];     /* 每个区域移动侦测参数数组 */
    u32 dwRegionNum;                                        /* 移动侦测区域数 */
}TMediaCtrlMdParam;                                         /* 移动侦测参数 */

typedef struct
{
    TMediaCtrlRegionAlarmState atMdState[MEDIACTRL_MAX_MD_REGION]; /* 状态数组 */
    u32 dwRegionNum;                            /* 告警，恢复状态改变的区域数 */
}TMediaCtrlMdCBParam;                              /* 移动侦测回调参数结构体 */


/* =================================遮挡告警========================================= */
#define MEDIACTRL_MAX_OD_REGION (4)

typedef struct
{
    TMediaCtrlRectRegion tOdRegion;                 /* 遮蔽侦测区域 */
    u32 dwSensitivity;                           /* 灵敏度1-10 */
}TMediaCtrlOdInfo;                                 /* Overlay detect info 遮蔽侦测参数信息 */

typedef struct
{
    TMediaCtrlOdInfo atOdInfo[MEDIACTRL_MAX_OD_REGION];
    u32 dwRegionNum;                                  /* 遮蔽区域数 */
}TMediaCtrlOdParam;                                    /* 遮蔽侦测参数 */

typedef struct
{
    TMediaCtrlRegionAlarmState atOdState[MEDIACTRL_MAX_OD_REGION]; /* 状态数组 */
    u32 dwRegionNum;                            /* 告警，恢复状态改变的区域数 */
}TMediaCtrlOdCBParam;                              /* 遮蔽侦测回调参数结构体 */

/* =================================虚焦检测========================================= */
typedef struct
{
    u32 dwSensitivity;                           //灵敏度   [1-3]
    BOOL32 bEnable;
}TMediaCtrlDeFocusParam;

typedef struct
{
    BOOL32 bAlarm;
}TMediaCtrlDeFocusCBParam;

/* =================================场景变换========================================= */
typedef struct
{
    u32 dwSensitivity;                              //灵敏度    [1-3]
    BOOL32 bEnable;
}TMediaCtrlSceneChgParam;

typedef struct
{
    BOOL32 bAlarm;
}TMediaCtrlSceneChgCBParam;

/* =================================区域入侵========================================= */
#define MEDIACTRL_MAX_INVASION_POINT_NUM (10)
#define MEDIACTRL_MAX_INVASION_REGION_NUM (4)


typedef struct
{
    u32 dwPointNum;
    TMediaCtrlPoint atPoint[MEDIACTRL_MAX_INVASION_POINT_NUM];
    EMediaCtrlAlgObjectType eObjType;
}TMediaCtrlRegionInvasionInfo;

typedef struct
{
    u32 dwRegionNum;
    u32 dwSensitivity;        /* 灵敏度[1-100] */
    u8 TrigTimeThr;            //触发时间阈值1-10
    TMediaCtrlRegionInvasionInfo atInvasionInfo[MEDIACTRL_MAX_INVASION_REGION_NUM];
    TMediaCtrlTargetFilterInfo tFilterInfo;
}TMediaCtrlRegionInvasionParam;

typedef struct
{
    u32 dwRegionNum;                            /* 告警，恢复状态改变的区域数 */
    TMediaCtrlRegionAlarmState atInvasionStat[MEDIACTRL_MAX_INVASION_REGION_NUM];
    u16 wTargetNum;                                                    //运动目标数
    TMediaCtrlMovingObject atAbnormTargets[MEDIACTRL_MAX_ABNORM_OBJECT_NUM];             //异常运动目标
}TMediaCtrlRegionInvasionCBParam;

/* ==================================区域进入======================================= */
#define MEDIACTRL_MAX_ENTER_POINT_NUM (10)
#define MEDIACTRL_MAX_ENTER_REGION_NUM (4)

typedef struct
{
    u32 dwPointNum;
    TMediaCtrlPoint atPoint[MEDIACTRL_MAX_ENTER_POINT_NUM];
    EMediaCtrlAlgObjectType eObjType;
}TMediaCtrlRegionEnterInfo;

typedef struct
{
    u32 dwRegionNum;
    u32 dwSensitivity;        /* 灵敏度[1-100] */
    TMediaCtrlRegionEnterInfo atEnterInfo[MEDIACTRL_MAX_ENTER_REGION_NUM];
    TMediaCtrlTargetFilterInfo tFilterInfo;
}TMediaCtrlRegionEnterParam;

typedef struct
{
    u32 dwRegionNum;                            /* 告警的区域数 */
    TMediaCtrlRegionAlarmState atEnterStat[MEDIACTRL_MAX_ENTER_REGION_NUM];
    u16 wTargetNum;                                                    //运动目标数
    TMediaCtrlMovingObject atAbnormTargets[MEDIACTRL_MAX_ABNORM_OBJECT_NUM];             //异常运动目标
}TMediaCtrlRegionEnterCBParam;

/* ==================================区域离开======================================== */
#define MEDIACTRL_MAX_LEAVE_POINT_NUM (9)
#define MEDIACTRL_MAX_LEAVE_REGION_NUM (4)

typedef struct
{
    TMediaCtrlPoint atPoint[MEDIACTRL_MAX_LEAVE_POINT_NUM];
    u32 dwPointNum;
    EMediaCtrlAlgObjectType eObjType;
}TMediaCtrlRegionLeaveInfo;

typedef struct
{
    TMediaCtrlRegionLeaveInfo atLeaveInfo[MEDIACTRL_MAX_LEAVE_REGION_NUM];
    u32 dwRegionNum;
    u32 dwSensitivity;        /* 灵敏度[1-100] */
    TMediaCtrlTargetFilterInfo tFilterInfo;
}TMediaCtrlRegionLeaveParam;

typedef struct
{
    TMediaCtrlRegionAlarmState atLeaveStat[MEDIACTRL_MAX_LEAVE_REGION_NUM];
    u32 dwRegionNum;                            /* 告警的区域数 */
    u16 wTargetNum;                                                    //运动目标数
    TMediaCtrlMovingObject atAbnormTargets[MEDIACTRL_MAX_ABNORM_OBJECT_NUM];             //异常运动目标
}TMediaCtrlRegionLeaveCBParam;

/* ==================================人员聚集检测=================================== */
#define MEDIACTRL_MAX_GATHER_POINT_NUM (10)
#define MEDIACTRL_MAX_GATHER_REGION_NUM (1)

typedef struct
{
    TMediaCtrlPoint atPoint[MEDIACTRL_MAX_GATHER_POINT_NUM];
    u32 dwPointNum;
    u32 dwProp;
    EMediaCtrlAlgObjectType eObjType;
}TMediaCtrlGatherInfo;

typedef struct
{
    TMediaCtrlGatherInfo atGatherInfo[MEDIACTRL_MAX_GATHER_REGION_NUM];
    u32 dwRegionNum;
    TMediaCtrlTargetFilterInfo tFilterInfo;
}TMediaCtrlGatherParam;

typedef struct
{
    TMediaCtrlRegionAlarmState atGatherStat[MEDIACTRL_MAX_GATHER_REGION_NUM];
    u32 dwRegionNum;                            /* 告警的区域数 */
}TMediaCtrlGatherCBParam;

/* =================================物体取走================================== */
#define MEDIACTRL_MAX_REMOVED_POINT_NUM (10)
#define MEDIACTRL_MAX_REMOVED_REGION_NUM (4)

typedef enum
{
    MEDIACTRL_REMOVED_TYPE_REMOVE,
    MEDIACTRL_REMOVED_TYPE_ABANDON
}EMediaCtrlRemovedType;

typedef struct
{
    TMediaCtrlPoint atPoint[MEDIACTRL_MAX_REMOVED_POINT_NUM];
    u32 dwPointNum;
}TMediaCtrlRemovedInfo;

typedef struct
{
    TMediaCtrlRemovedInfo atObjectInfo[MEDIACTRL_MAX_REMOVED_REGION_NUM];
    u32 dwRegionNum;
    u32 dwSensitivity;                    //灵敏度[1-3]
    u8 u8TrigTimeThr;                    //触发时间阈值5-100 海康
    TMediaCtrlTargetFilterInfo tFilterInfo;
}TMediaCtrlRemovedParam;

typedef struct
{
    TMediaCtrlRegionAlarmState atObjectStat[MEDIACTRL_MAX_REMOVED_REGION_NUM];
    u32 dwRegionNum;             /* 告警，恢复状态改变的区域数 */
    u16 wTargetNum;                                                    //运动目标数
    TMediaCtrlMovingObject atAbnormTargets[MEDIACTRL_MAX_ABNORM_OBJECT_NUM];             //异常运动目标
}TMediaCtrlRemovedCBParam;

/* =================================物体遗留================================== */
#define MEDIACTRL_MAX_ABANDON_POINT_NUM (10)
#define MEDIACTRL_MAX_ABANDON_REGION_NUM (4)

typedef enum
{
    MEDIACTRL_ABANDON_TYPE_REMOVE,
    MEDIACTRL_ABANDON_TYPE_ABANDON
}EMediaCtrlAbandonType;

typedef struct
{
    TMediaCtrlPoint atPoint[MEDIACTRL_MAX_ABANDON_POINT_NUM];
    u32 dwPointNum;
}TMediaCtrlAbandonInfo;

typedef struct
{
    TMediaCtrlAbandonInfo atObjectInfo[MEDIACTRL_MAX_ABANDON_REGION_NUM];
    u32 dwRegionNum;
    u32 dwSensitivity;                    //灵敏度[1-3]
    u8 u8TrigTimeThr;                    //触发时间阈值5-100 海康
    TMediaCtrlTargetFilterInfo tFilterInfo;
}TMediaCtrlAbandonParam;

typedef struct
{
    TMediaCtrlRegionAlarmState atObjectStat[MEDIACTRL_MAX_ABANDON_REGION_NUM];
    u32 dwRegionNum;             /* 告警，恢复状态改变的区域数 */
    u16 wTargetNum;                                                    //运动目标数
    TMediaCtrlMovingObject atAbnormTargets[MEDIACTRL_MAX_ABNORM_OBJECT_NUM];             //异常运动目标
}TMediaCtrlAbandonCBParam;

/* =================================单球跟踪========================================= */
#define MEDIACTRL_MAX_INTELLI_TRACK_POINT_NUM (10)

typedef struct
{
    TMediaCtrlPoint atPoint[MEDIACTRL_MAX_INTELLI_TRACK_POINT_NUM];
    u32 dwPointNum;
}TMediaCtrlIntelliTrackInfo;

typedef struct
{
    BOOL32 bEnable;
    TMediaCtrlIntelliTrackInfo atIntelliTrackInfo; //单球跟踪的区域信息
    float fZoomTime;
}TMediaCtrlPtzTrackParam;

typedef struct
{
    s32 s32FirstFrameFlag;       //第一帧的标志0:不跟踪，1：点对点，2：V，3：delatV, 4.回到初始跟踪位置
    double dPixelX;               //s32FirstFrameFlag=1时，点对点方式（160ms内）把目标拉到画面中心
    double dPixelY;
    u32 dwZoomValue;
    float fZoomFlag;           //变倍标志(0:不变倍，非零:实际zoom值)
    u32 dwHorDrection;      //水平方向  （s32FirstFrameFlag=0）
    u32 dwVerDrection;      //垂直方向  （s32FirstFrameFlag=0）
    double dPixelDiffHor;   //速度模式 水平像素差  （s32FirstFrameFlag=0）
    double dPixelDiffVer;   //速度模式 垂直像素差   （s32FirstFrameFlag=0）
    double dTime;           //时间延时

    u32 dwReserved;
}TMediaCtrlPtzTrackCBParam;

/* =================================拌线检测========================================= */
typedef enum
{
    MEDIACTRL_TRIP_DIR_A_TO_B,
    MEDIACTRL_TRIP_DIR_B_TO_A,
    MEDIACTRL_TRIP_DIR_A_AND_B
}EMediaCtrlTripLineType;

typedef struct
{
    TMediaCtrlPoint tStartPoint;
    TMediaCtrlPoint tEndPoint;
    TMediaCtrlPoint tRefAPoint;
    TMediaCtrlPoint tRefBPoint;
    EMediaCtrlTripLineType eTripLineType;
    EMediaCtrlAlgObjectType eObjType;
}TMediaCtrlTripLineInfo;

#define MEDIACTRL_MAX_TRIP_LINE_NUM (4)

typedef struct
{
    TMediaCtrlTripLineInfo atTripLineInfo[MEDIACTRL_MAX_TRIP_LINE_NUM];
    u32 dwSensitivity;        /* 灵敏度[1-100] */
    u32 dwLineNum;
    TMediaCtrlTargetFilterInfo tFilterInfo;
}TMediaCtrlTripLineParam;

typedef struct
{
    BOOL32 bAlarm;                             /* 是否告警 */
    u32    dwLineIndex;                        /* 告警的线编号 */
    EMediaCtrlTripLineType eTripLineType;      /* 越线的方向 */
}TMediaCtrlTripLineAlarmState;

typedef struct
{
    TMediaCtrlTripLineAlarmState atTripLineStat[MEDIACTRL_MAX_TRIP_LINE_NUM];
    u32 dwRegionNum;                            /* 告警的区域数 */
    u16 wTargetNum;                                                    //运动目标数
    TMediaCtrlMovingObject atAbnormTargets[MEDIACTRL_MAX_ABNORM_OBJECT_NUM];             //异常运动目标
}TMediaCtrlTripLineCBParam;                    /* 拌线检测回调参数，只上报报警状态 */

/* =================================人脸检测========================================= */
#define MEDIACTRL_MAX_FACE_DETECT_NUM (50)

typedef struct
{
    BOOL32 bEnable;
}TMediaCtrlFaceDetectParam;

typedef struct
{
    BOOL32 bAlarm;
    u32 dwFaceNum; //人脸数目
    TMediaCtrlRectRegion atFaces[MEDIACTRL_MAX_FACE_DETECT_NUM]; //输出检测到的目标
}TMediaCtrlFaceDetectCBParam;

/* =================================人脸马赛克========================================= */
typedef struct
{
    BOOL32 bEnable;
    u32 dwDetectLeft;       //测量区域的左边界
    u32 dwDetectRight;      //测量区域的右边界
    u32 dwDetectTop;       //测量区域的上边界
    u32 dwDetectBottom;   //测量区域的下边界
    double dScale;            //放大或者缩小人脸，小于1缩小，大于1放大.[0, 2]
}TMediaCtrlFaceMosaicParam;

/* =================================运动目标检测========================================= */
#define MEDIACTRL_MAX_MTD_REGION (1)//最大运动目标检测的区域数
#define MEDIACTRL_MAX_MTD_POINT_NUM (10)//每个区域最多点数
#define MEDIACTRL_MAX_MTD_TARGET_NUM (20)//每个区域最多徘徊目标数

typedef struct
{
    u16 wProp;     //占比参数
    u16 wPointNum;
    TMediaCtrlPoint atPoint[MEDIACTRL_MAX_MTD_POINT_NUM];
}TMediaCtrlMtdInfo;

typedef struct
{
    u32 dwRegionNum;                                        /* 移动目标侦测区域数 */
    TMediaCtrlMtdInfo atMtdInfo[MEDIACTRL_MAX_MTD_REGION];     /* 每个区域移动目标侦测参数数组 */
}TMediaCtrlMtdParam;                                         /* 移动目标侦测参数 */

typedef struct
{
    u16 wHoverNum;          //场景中的徘徊目标数
    TMediaCtrlRectRegion atHoverTarget[MEDIACTRL_MAX_MTD_TARGET_NUM]; //场景中的徘徊目标矩形框
    TMediaCtrlPoint atHoverPoint[MEDIACTRL_MAX_MTD_TARGET_NUM];//场景中的徘徊目标质心
}TMediaCtrlMtdTarget;

typedef struct
{
    u32 dwRegionNum;                            /* 告警，恢复状态改变的区域数 */
    TMediaCtrlMtdTarget atMtdTarget[MEDIACTRL_MAX_MTD_REGION];
}TMediaCtrlMtdCBParam;                              /* 移动目标侦测回调参数结构体 */

/* =================================单球跟踪================================== */
#define MEDIACTRL_MAX_DOMETRACK_POINT_NUM  (11) /* 跟踪坐标 */
#define MEDIACTRL_MAX_DOMETRACK_REGION_NUM  (4) /* 检测区域数 */

typedef struct
{
    u32 dwPointNum;
    TMediaCtrlPoint atPoint[MEDIACTRL_MAX_DOMETRACK_POINT_NUM];
    EMediaCtrlAlgObjectType eObjType;
}TMediaCtrlDomeTrackInfo;

typedef struct
{
    u8  bySensitivity;                      /* 灵敏度：0-100 */
    u32 dwRegionNum;                        /* 检测区域数 */
    TMediaCtrlDomeTrackInfo atDomeTrackInfo[MEDIACTRL_MAX_DOMETRACK_REGION_NUM]; /* 单球跟踪信息 */
}TMediaCtrlDomeTrackParam;

typedef struct
{
    u16 wTargetNum;                        /* 目标数 */
    TMediaCtrlMovingObject atDomeTrackTargets[MEDIACTRL_MAX_MOVING_TARGET_NUM]; /* 场景中所有运动目标信息 */
}TMediaCtrlDomeTrackCBParam;

/*=================================fire alarm=========================================*/
#define MEDIACTRL_MAX_FIREALARM_POINT_NUM (11)  /* point num */
#define MEDIACTRL_MAX_FIREALARM_REGION_NUM (8)  /* region num */
#define MEDIACTRL_MAX_FIRE_ABNORM_NUM      (10) /* abnorm num */

typedef struct
{
    u32 dwPointNum;
    TMediaCtrlPoint atPoint[MEDIACTRL_MAX_FIREALARM_POINT_NUM];
}TMediaCtrlFireAlarmInfo;

typedef struct
{
    u8  bySensitivity;                      /* 0-100 */
    u32 dwRegionNum;                        /* detect area */
    u32 dwRoERegionNum;                     /* detect roe area */
    u32 dwMinObjectsize;                    /* min target size */
    TMediaCtrlFireAlarmInfo atFireAlarmInfo[MEDIACTRL_MAX_FIREALARM_REGION_NUM];
    TMediaCtrlFireAlarmInfo atRoERegionInfo[MEDIACTRL_MAX_FIREALARM_REGION_NUM];
}TMediaCtrlFireAlarmParam;

typedef enum
{
    MEDIACTRL_OBJTYPE_NONE  = 0,
    MEDIACTRL_OBJTYPE_FIRE  = 1,
    MEDIACTRL_OBJTYPE_SMOKE = 2,
}EMediaCtrlFireObjType;

typedef struct
{
    u16 wLeft;
    u16 wRight;
    u16 wTop;
    u16 wBottom;
    u16 wAreaIndex;
    EMediaCtrlFireObjType eObjType;
}TMediaCtrlFireAlarmObject;

typedef struct
{
    s32 nFireNum;
    TMediaCtrlFireAlarmObject atFire[MEDIACTRL_MAX_FIRE_ABNORM_NUM];
    TMediaCtrlRegionAlarmState atAlarmState[MEDIACTRL_MAX_FIREALARM_REGION_NUM];
}TMediaCtrlFireAlarmCBParam;
/* =================================运动目标检测========================================= */

/* =================================移动目标坐标========================================= */

typedef struct
{
    u32 dwTargetNum;
    TMediaCtrlMovingObject atMovingTarget[MEDIACTRL_MAX_MOVING_TARGET_NUM]; //场景中所有运动目标信息
}TMediaCtrlMovingTargetInfo;
/* =================================移动目标坐标========================================= */

/* =================================亮度变换========================================= */
typedef struct
{
    u32 dwSensitivity; //灵敏度    [1-3]，分别对应低,中,高
    u32 dwLumaMin;     //最小亮度值[0,255]   SVR使用时，将dwSensitivity参数置0之后传给算法
    u32 dwLumaMax;     //最大亮度值
    BOOL32 bEnable;
}TMediaCtrlLumaChgParam;

typedef struct
{
    BOOL32 bAlarm;
    u32 dwAlarmType;  //[no alarm:0  high brightness:1  low brightness: 2]
}TMediaCtrlLumaChgCBParam;
/* =======================================eptz teacher track=============================================== */
#define MEDIACTRL_EPTZ_MAX_ROSTRUM_NUM  (3)
#define MEDIACTRL_EPTZ_MAX_ROI_NUM      (1)
#define MEDIACTRL_EPTZ_MAX_MASK_ROI_NUM (5)

typedef enum
{
    MEDIACTRL_EPTZ_SINGLE_MODE = 1,
    MEDIACTRL_EPTZ_MULTI_MODE
}EMediaCtrlEptzModeType;

typedef struct
{
    s32 nMultiToSingle;
    s32 nSingleToMulti;
    s32 nDownRostrum;
    s32 nUpRostrum;
    s32 nDetectObj;
    s32 nNoObj;
    s32 nInPPT;
    s32 nOutPPT;
}TMediaCtrlEptzSwitchTime;

typedef struct
{
    BOOL32 bOpen;
    s32 atnLeftBord[MEDIACTRL_EPTZ_MAX_ROSTRUM_NUM];
    s32 atnRightBord[MEDIACTRL_EPTZ_MAX_ROSTRUM_NUM];
    s32 atnDownLineY[MEDIACTRL_EPTZ_MAX_ROSTRUM_NUM];
    s32 nPodiumNum;
    s32 nRoiNum;
    TMediaCtrlRectRegion attROI[MEDIACTRL_EPTZ_MAX_ROI_NUM];
    s32 nMaskNum;
    TMediaCtrlRectRegion attMaskROI[MEDIACTRL_EPTZ_MAX_MASK_ROI_NUM];
    TMediaCtrlRectRegion tRostrum;
    TMediaCtrlRectRegion tObjSrcRect;
    TMediaCtrlRectRegion tPPTRect;
    TMediaCtrlRectRegion tTexieRect;
    TMediaCtrlEptzSwitchTime tEptzSwitchTime;
    s32 nSensitive;
    s32 nIsVerticalTrack;
    EMediaCtrlEptzModeType eEptzModeType;
}TMediaCtrlTeacherTrackParam;

#define MEDIACTRL_EPTZ_MAX_OBJ_NUM  (15)
typedef enum
{
    MEDIACTRL_EPTZ_FULLVIEW = 1,
    MEDIACTRL_EPTZ_ZOOMVIEW
}EMediaCtrlEptzPicType;

typedef enum
{
    MEDIACTRL_EPTZ_IN_PPT = 0,
    MEDIACTRL_EPTZ_NOT_IN_PPT
}EMediaCtrlEptzPPT;

typedef enum
{
    MEDIACTRL_EPTZ_TRACK_NORMAL = 0,
    MEDIACTRL_EPTZ_TRACK_LR,
    MEDIACTRL_EPTZ_TRACK_DOWN,
    MEDIACTRL_EPTZ_TRACK_MULTI,
    MEDIACTRL_EPTZ_TRACK_LOST
}EMediaCtrlEptzTrackStatus;

typedef struct
{
    s32 nRectNum;
    TMediaCtrlRectRegion attRectPos[MEDIACTRL_EPTZ_MAX_OBJ_NUM];
    TMediaCtrlRectRegion tRect;
    EMediaCtrlEptzPicType eEptzPicType;
    EMediaCtrlEptzPPT eEptzPPT;
    EMediaCtrlEptzTrackStatus eEptzTrackStatus;
}TMediaCtrlTeacherTrackCBParam;

/* =================================亮度变换========================================= */
typedef struct
{
    EMediaCtrlPicType ePicType;         /* 图片格式 无效格式表示抓拍失败 */
    TMediaCtrlVidReslution tPicRes;     /* 图片分辨率 */
    u32 dwPicLen;                       /* 图片长度，对于压缩图片有用 */
    u8 *pPicAddr;                       /* 图片所在内存 */
}TMediaCtrlPicInfo;                     /* 图片信息 */

typedef enum
{
    MEDIACTRL_SNAP_STATUS_OK,           /* 抓拍成功 */
    MEDIACTRL_SNAP_STATUS_NON,           /* 此次回调不携带没有此类型图片，分两次回调传递 */
    MEDIACTRL_SNAP_STATUS_ERROR        /* 抓拍失败 */
}EMediaCtrlSnapStatus;
typedef struct
{
    TMediaCtrlPicInfo tOrgPicInfo;       /* 抓拍原始图片信息 */
    EMediaCtrlSnapStatus eOrgStatus;   /* 抓拍原始图片状态 */
    TMediaCtrlPicInfo tThumbnailsInfo;  /* 缩略图图片信息 */
    EMediaCtrlSnapStatus eThumbnailsStatus; /* 缩略图状态 */
    u32 dwContext;                      /* 回调上下文 */
}TMediaCtrlSnapCallbackInfo;            /* 抓拍回调信息 */

/* =================================车牌检测========================================= */
#define MEDIACTRL_MAX_CAR_PLATE_NUM (16)
typedef struct
{
    BOOL32 bOpen;
    BOOL32 bMannulMode;
}TMediaCtrlCarDetectParam;

typedef struct
{
    u8 atbyPlateInfo[MEDIACTRL_MAX_CAR_PLATE_NUM];
    TMediaCtrlPicInfo tPicInfo;
    float fScore;
    BOOL32 bMannul;
}TMediaCtrlCarDetectCBParam;

#define MEDIACTRL_MAX_MAN_DETECT_NUM (16)

typedef struct
{
    BOOL32 bOpen;
    BOOL32 bMannulMode;
}TMediaCtrlManDetectParam;

typedef struct
{
    s32 nRectNum;
    TMediaCtrlRectRegion attRectPos[MEDIACTRL_MAX_MAN_DETECT_NUM];
    TMediaCtrlPicInfo tPicInfo;
    BOOL32 bOrig;
    float fScore;
    BOOL32 bMannul;
}TMediaCtrlManDetectCBParam;
/* ================================================================================== */


/* **********************************************************************************
函数名：TMediaCtrlPicSnapCB   图片抓拍回调
dwCapChn: 抓拍视频采集通道号
ptSnapCBInfo：抓拍回调信息
*********************************************************************************** */
typedef void (*TMediaCtrlPicSnapCB)(s32 dwCapChn, TMediaCtrlSnapCallbackInfo *ptSnapCBInfo);

typedef enum
{
    MEDIACTRL_SNAP_FROM_ENCODE_STREAM, /* 抓拍来源于编码 */
    MEDIACTRL_SNAP_FROM_INPUT_FRAME,    /* 抓拍来源于输入的帧结构 */
    MEDIACTRL_SNAP_FROM_CAPTURE
}EMediaCtrlSnapSource;

typedef struct
{
    EMediaCtrlPicType ePicType;                 /* 图片格式 */
    EMediaCtrlSnapQuality ePicQuality;          /* 图片质量 */
    TMediaCtrlVidReslution tOrgPicRes;          /* 抓拍原始图片分辨率 */
    TMediaCtrlVidReslution tThumbnailsRes;      /* 缩略图分辨率, 0x0表示不需要缩列图 */
    /* 若缩略图使用YUV转换，填写此值，空间需要预分配，否则为NULL, 抓拍请求可能会丢,注意内存回收 */
    u8 *pThumbnailsYuvAddr;
    u32 dwContext;                              /* 回调函数上下文 */
    TMediaCtrlPicSnapCB tPicSnapCB;             /* 图片抓拍回调函数 */
    BOOL32 bNeedStop;
    EMediaCtrlSnapSource eSnapSource;           /* 抓拍图片的来源 */
    NEWFRAMEHDR *pFhr;                          /* 抓拍源为输入的帧时，填入真实的帧，否则为NULL */
    u32 dwSnapBindStream;                       /* 抓拍第几路码流 */
}TMediaCtrlPicSnapParam;

typedef struct
{
    EMediaCtrlOverlayType eType;                     /* 图像遮蔽方式 */
    EMediaCtrlColor eColor;                          /* 单色遮蔽颜色 */
    EMediaCtrlMosaicType eMosaic;                    /* 马赛克遮挡类型，静态或动态 */
    TMediaCtrlPloygon tPloygon;                      /* 多边行, 点数2代表矩形,左上角坐标与右下角坐标 */
}TMediaCtrlOverlayInfo;

#define MEDIACTRL_MAX_OVERLAY_REGION (8)
typedef struct
{
    TMediaCtrlOverlayInfo atOverlayInfo[MEDIACTRL_MAX_OVERLAY_REGION];
    u32 dwRegionNum;                                /* 遮蔽区域数 */
}TMediaCtrlOverlayParam;                            /* 图像遮蔽参数结构体 */

//动态ROI设置，开或者关
typedef struct
{
    u32 dwOpen;            /* 0-disable, 1-enable */
}TMediaCtrlDRoiParam;

//动态I帧间隔设置，开或者关
typedef struct
{
    u32 dwOpen;            /* 0-disable, 1-enable */
}TMediaCtrlDkfiParam;

/* ============================================================================================
函数名：MediaCtrlSetSmartEnc
用途:   设置智能编码功能开启的参数
参数：
cap_chn:    视频采集通道号
enc_chn:    编码通道号，目前仅支持主流0
eSmartType: 智能编码功能类型
pParam:    智能编码各个功能对应的参数
           动态roi: TMediaCtrlDRoiParam,              动态I帧间隔: TMediaCtrlDkfiParam
============================================================================================== */
s32 MediaCtrlSetSmartEnc(s32 nCapChn, s32 nEncChn, EMediaCtrlSmartEncType eSmartType, void *pParam);


typedef struct
{
    TMediaCtrlCameraModeParam *ptCameraModeParam;                       /* 摄像机模式参数 */
    TMediaCtrlVoutParam *ptVoutParam;                                   /* 本地回显初始化参数 */
    TMediaCtrlVoutParam *ptVoutParam1;                                   /* 本地回显初始化参数1 */
    TMediaCtrlVidEncParam *atpVidEncParam[MEDIACTRL_MAX_VID_ENC_NUM];   /* 指向编码参数的指针数组 */
    TMediaCtrlVidDecParam atDecChnParam[MEDIACTRL_MAX_VID_DEC_NUM];     /* 视频解码通道参数 */
    EMediaCtrlVidChnType aeEncSource[MEDIACTRL_MAX_VID_ENC_NUM];        /* 编码通道的数据源 */
    TMediaCtrlRotateFlipParam *ptRotateFlipParam;                        /* 视频翻转旋转参数 */
    TMediaCtrlNetPacketInfo *atpVidNetPacket[MEDIACTRL_MAX_VID_ENC_NUM]; /* 视频打包编号指针数组 */
    TMediaCtrlEncFisheyeParam *atpVidEncFisheyeParam[MEDIACTRL_MAX_VID_ENC_NUM];
    TMediaCtrlEncFisheyeParam *atpVidEncFisheyeParam1[MEDIACTRL_MAX_VID_ENC_NUM];
    EMediaCtrlStitchScheme eStitchScheme;
    EMediaCtrlStitchCrop eStitchCrop;
    EMediaCtrlStitchOutResolution eStitchOutRes;
}TMediaCtrlVidInitInfo;                                                  /* 视频初始化参数结构体 */

typedef struct
{
    TMediaCtrlAudParam *ptAudParam;                                     /* 指向音频参数结构体 */
    TMediaCtrlNetPacketInfo *atpAudNetPacket[MEDIACTRL_MAX_AUD_ENC_NUM]; /* 音频打包编号指针数组 */
}TMediaCtrlAudInitInfo;                                                  /* 音频初始化结构体 */

typedef struct
{
    u8 *pBmpFile;                 /* 指向bmp图片文件内容的指针 */
    u32 dwBmpLen;                 /* bmp 文件长度 */
}TMediaCtrlBmpFileInfo;           /* bmp 图片信息 */

typedef struct
{
    TMediaCtrlBmpFileInfo tBmpInfo; /* bmp 图片信息 */
    TMediaCtrlPosition tStartPos;   /* OSD显示的起点，相对视频左上角坐标 */
    s32 nAreaId;                    /* 暂时保留，为以后扩展单一刷新区域用 */
    u8 byClarity;                   /* 透明度 0全透明 255不透明 */
}TMediaCtrlAreaOsdInfo;             /* OSD 区域信息 */

typedef enum
{
    MEDIACTRL_STREAM_COMPOS_ALARM,      /* 简单智能类信息叠加 */
    MEDIACTRL_STREAM_COMPOS_WATERMARK,  /* 数字水印叠加 */
    MEDIACTRL_STREAM_COMPOS_GPS,        /* GPS 信息叠加 */
    MEDIACTRL_STREAM_COMPOS_PTZ        /* 镜头方位 信息叠加 */
}EMediaCtrlStreamComposType;            /* 码流信息叠加类型 */

typedef struct
{
    u8 *dat_addr;
    u16 width;
    u16 height;
}TMediaCtrlVoFbOsdInfo;

typedef struct
{
    u32 bEnabalColorkey;
    u32 dwColorkey;
    EMediaCtrlVidDateFormat pixelFormat;
}TMediaCtrlFbOsdParam;

typedef struct
{
    u8 *pbyAddr;
    u32 dwLen;
}TMediaCtrlGpsComposInfo;

typedef struct
{
    u8 *pbyAddr;
    u32 dwLen;
}TMediaCtrlPtzComposInfo;

typedef struct
{
    EMediaCtrlStreamComposType eComposType;   /* 叠加类型 */
    BOOL32 bComposEnable;                     /* 开启关闭叠加 */
    union                                     /* 联合体，根据不同类型赋值，数字水印，基础智能没有参数 */
    {
        TMediaCtrlGpsComposInfo tGpsInfo;
        TMediaCtrlPtzComposInfo tPtzInfo;
    };
}TMediaCtrlStreamComposInfo;

typedef struct
{
    void *jvm;
    void *java_context;
    BOOL32 bCommonDev; /* 1 Common android device 0 Kedacom android device */
}TMediaCtrlAndroidPreInitInfo;

typedef struct
{
    u16 wPosX;
    u16 wPosY;
}TMediaCtrlRegionParam;

typedef struct
{
    BOOL32 bEnable;        /* 叠加使能 */
    u32 dwSrcEncChn;    /* 叠加编码通道 */
    u32 dwDstEncChn;    /* 被叠加编码通道 */
    TMediaCtrlRegionParam tRegionParam;    /* 叠加区域信息 */
}TMediaCtrlDualChnOverlay;

/* ******************************************************************
  MediaCtrlPlatformPreInit
  功能：部分平台需要调用此接口进行预初始化
pvPlatformPreInitInfo: 平台特有的初始化结构体 android 平台 TMediaCtrlAndroidPreInitInfo
****************************************************************** */
s32 MediaCtrlPlatformPreInit(void *pvPlatformPreInitInfo);

/* ************************************************************************
函数名：        MediaCtrlInit
功能：          初始化媒体控制库
ptMidiaCapcity: 媒体能力级
nVidCapChn :    视频采集通道号（每次初始化一路通道，多通道分多次初始化）
ptVidInitInfo:  视频初始化参数
ptISPParam      ISP初始化参数  (若对通通道无需ISP，传递NULL)
nAudCapChn      音频采集通道号
ptAudInitInfo:  音频初始化参数  （若数量与视频不匹配，可设置为NULL)
返回值：        0代表成功，非0代表失败
************************************************************************* */
s32 MediaCtrlInit(const TMediaCtrlCapcity *ptMidiaCapcity, s32 nVidCapChn, \
        const TMediaCtrlVidInitInfo *ptVidInitInfo, void *ptISPParam,\
        s32 nAudCapChn, const TMediaCtrlAudInitInfo *ptAudInitInfo);

/* ************************************************************************
函数名：        MediaCtrlDeInit
功能：          去初始化媒体控制库
nVidCapChn :    视频采集通道号（每次初始化一路通道，多通道分多次初始化）
nAudCapChn      音频采集通道号
返回值：        0代表成功，非0代表失败
************************************************************************* */
s32 MediaCtrlDeInit(s32 nVidCapChn, s32 nAudCapChn);

/* *******************************************************************************************
函数名：        MediaCtrlSetAudParam
功能：            设置音频参数
nCapChn:        音频采集通道号, 或者音频播放通道号
nEncChn            音频编码通道号，或者音频解码通道号
ptAudParam:     指向音频参数结构体
返回值：        0代表成功，非0代表失败
注意：          为了将音频参数接口统一，此接口对采集、播放、编码、解码同时设置有效,底层根据差异
                决定修改哪部分的内容, AEC与混音的对应nCapChn ,nEncChn 都为0
****************************************************************************************** */
s32 MediaCtrlSetAudParam(s32 nCapChn, const TMediaCtrlAudParam *ptAudParam);

/* *****************************************************************************************
函数名：       MediaCtrlSetCameraMode
eModeType:     需要设置摄像机模式
param          摄像机模式对应的参数 如 低延时模式为: TMediaCtrlLowDelayModeParam
                                       HDR模式为:    TMediaCtrlHdrModeParam
****************************************************************************************** */
s32 MediaCtrlSetCameraModeParam(s32 cap_chn, EMediaCtrlCameraModeType eModeType, void *pParam);

/* *****************************************************************************************
函数名：        MediaCtrlStartVidCap
功能：            启动视频通道采集
eVinChnType:    视频采集通道类型
ptVinParam:      采集参数
****************************************************************************************** */
s32 MediaCtrlStartVidCap(EMediaCtrlVidChnType eVinChnType, TMediaCtrlVinParam *ptVinParam);

typedef enum
{
    MEDIACTRL_VID_VI_SD,        //标清输入
    MEDIACTRL_VID_VI_HD,        //高清输入
    MEDIACTRL_VID_VI_USER,      //用户自定义
}EMediaCtrlViCscType;

typedef struct
{
    EMediaCtrlViCscType eVinChnType;     /* 视频输入源是高清或标清 */
    u32 dwLuma;
    u32 dwContract;
    u32 dwHue;
    u32 dwSatu;
}TMediaCtrlViCSCAttr;

typedef struct
{
    TMediaCtrlRectRegion tCropRect;
}TMediaCtrlEncEptzInfo;

/* *****************************************************************************************
函数名：        MediaCtrlSetViCSCAttr
功能：            设置输入的亮度色度饱和度对比度参数
eVinChn:        视频输入通道类型，用以判断是采集设备0或1
ptViCscParam:   输入亮度色度饱和度对比度参数
****************************************************************************************** */
s32 MediaCtrlSetViCSCAttr(EMediaCtrlVidChnType eVinChn, TMediaCtrlViCSCAttr *ptViCscParam);

/* *****************************************************************************************
函数名：        MediaCtrlSetVidEncParam
功能：            停止视频通道采集
eVinChnType:    视频采集通道类型
****************************************************************************************** */
s32 MediaCtrlStopVidCap(EMediaCtrlVidChnType eVinChnType);


/* *****************************************************************************************
函数名：        MediaCtrlSetVidEncParam
功能：          设置视频编码参数
nCapChn:        视频采集通道号
nEncChn:        视频编码通道号
ptVidEncParam:  指向视频编码参数结构体指针
****************************************************************************************** */
s32 MediaCtrlSetVidEncParam(s32 nCapChn, s32 nEncChn, const TMediaCtrlVidEncParam *ptVidEncParam);


/* *****************************************************************************************
函数名：        MediaCtrlSetVidEncCropParam
功能：          设置视频编码通道裁剪参数
nEncChn:        视频编码通道号
ptVidEncParam:  指向视频编码通道裁剪参数的结构体指针
                裁剪坐标以tCanvasRes的宽高作为基准，即10000x10000为基准
****************************************************************************************** */
s32 MediaCtrlSetVidEncCropParam(s32 nEncChn, const TMediaCtrlCropParam *ptVidCropParam);


/* *****************************************************************************************
函数名：        MediaCtrlSetDisParam
功能：      设置防抖参数
nCapChn:        视频采集通道号
ptDisParam:  指向防抖参数结构体指针
****************************************************************************************** */
s32 MediaCtrlSetDisParam(s32 nCapChn, const TMediaCtrlDisParam *ptDisParam);

/* *****************************************************************************************
函数名：        MediaCtrlSetCropParam
功能：      设置裁剪参数
nCapChn:        视频采集通道号
ptCropParam:  指向裁剪参数结构体指针
****************************************************************************************** */
s32 MediaCtrlSetCropParam(s32 nCapChn, const TMediaCtrlCropParam *ptCropParam);

/* Set encode region from vin */
s32 MediaCtrlSetEncEptz(s32 nCapChn, s32 nEncChn, const TMediaCtrlEncEptzInfo *ptEptzInfo);

s32 MediaCtrlSetVidEncFreeze(s32 nCapChn, s32 nEncChn, u32 enable);
typedef s32 (*TMediaCtrlEncFreezeStatusCB)(EMediaCtrlVidEncFreezeStatus eRunningStatus);

s32 MediaCtrlSetEncFreezeStatusCallBack(TMediaCtrlEncFreezeStatusCB tRunningStatusCB);
/* 设置视频遮蔽参数 */
s32 MediaCtrlSetVidOverlayParam(s32 nCapChn, const TMediaCtrlOverlayParam *ptOverlayParam);

/* 设置编码通道的遮挡参数, 主辅流同等生效 */
s32 MediaCtrlSetVidEncOverlayParam(s32 nEncChn, const TMediaCtrlOverlayParam *ptOverlayParam);

/* ***************************************************************************************************
FuncName:    MediaCtrlStartVidEncode
nEncChn:     enocode channel for start encode
ptEncParam:  encode param for channel
eEncSource:  which channle bind to encode
limit:       This func now only use in MEDIACTRL_CAMERA_MODE_DYNAMIC_INIT
**************************************************************************************************** */
s32 MediaCtrlStartVidEncode(s32 nEncChn, TMediaCtrlVidEncParam *ptEncParam, EMediaCtrlVidChnType eEncSource);

/* ***************************************************************************************************
FuncName:    MediaCtrlStopVidEncode
nEncChn:     enocode channel for stop encode
limit:       This func now only use in MEDIACTRL_CAMERA_MODE_DYNAMIC_INIT
**************************************************************************************************** */
s32 MediaCtrlStopVidEncode(s32 nEncChn);

/* =================================================================================
函数名：MediaCtrlStartPicSnap
作用：  开始抓拍，由于有缩略图的需求，但不同平台存在性能差异主要有以下3种方式实现
        1.JPEG + YUV 对于不能提供第二路jpeg，但能提供小yuv的方案
        2.JPEG + JPEG 对于能提供大小2路JPEG编码的平台
        3.JPEG        对于CPU性能极好，可以软解码yuv压缩JPEG缩略图的平台
        在回掉的实现上，不同平台有差异性，对于1,2两种方案，可能同时回调，也可能非同时
        回调，从回调参数中的status可以反映上述差异
=================================================================================== */

s32 MediaCtrlStartPicSnap(s32 nCapChn, const TMediaCtrlPicSnapParam *ptPicSnapParam);



/* 设置图像回显格式 */
s32 MediaCtrlSetVoutParam(s32 nCapChn, const TMediaCtrlVoutParam *ptVoutParam);

s32 MediaCtrlGetVoutParam(s32 nCapChn, TMediaCtrlVoutParam *ptVoutParam);

/* =================================================================================
函数名：MediaCtrlSetVoHpCallBack
作用：设置HDMI热插拔回调
=================================================================================== */
s32 MediaCtrlSetVoutHpCallBack(TMediaCtrlVoHpCallBack pfCallBack);

s32 MediaCtrlSetVoutZoomInParam(s32 nCapChn, const TMediaCtrlVoutZoomInParam *ptVoutZoomInParam);

s32 MediaCtrlGetVoutZoomInParam(s32 nCapChn, const TMediaCtrlVoutZoomInParam *ptVoutZoomInParam);

/* 设置图像旋转，翻转 */
s32 MediaCtrlSetVidRotateFlipParam(s32 nCapChn, const TMediaCtrlRotateFlipParam *ptVidRotateFlipParam);

/* 设置畸变矫正参数 */
s32 MediaCtrlSetLdcParam(s32 nCapChn, const TMediaCtrlLdcParam *ptVidLdcParam);

/* 强制关键帧 */
s32 MeidiaCtrlForceKeyFrame(s32 nCapChn, s32 nEncChn);

/* 推送音频帧数据 */
s32 MediaCtrlPutNetRecAudioFrame(s32 nRecChn, const NEWFRAMEHDR *pFhr);

/* 推送外部编码后的数据 */
s32 MediaCtrlPutEncAudioFrame(s32 nCapChn, NEWFRAMEHDR *pFhr);
/* 推送外部PCM原始数据，传入整个数据 */
u32 MediaCtrlPutAudRecPcmFrame(s32 nPlayChn, char *pchData, u32 dwDataLen);

//获取encmix元件的参数
s32 MediaCtrlAudEncMixGetParam(TMediaCtrlAudEncMixParam *pAudEncMixParam);

//设置encmix元件的参数
s32 MediaCtrlAudEncMixSetParam(TMediaCtrlAudEncMixParam *pAudEncMixParam);

//获取aud meter元件的统计数据
s32 MediaCtrlAudMeterGetStatus(EMediaCtrlAudChannelType nMeterChn, s32 *rmsDb, s32 *peakDb);

//设置aud meter元件的参数
s32 MediaCtrlAudMeterSetParam(EMediaCtrlAudChannelType nMeterChn, TMediaCtrlAudMeterParam *pMeterParam);

/* ================================================================================================
  MediaCtrlStartAudCap
  Start audio capture
  =============================================================================================== */
s32 MediaCtrlStartAudCap(EMediaCtrlAudChannelType eChnType, TMediaCtrlAudCapParam *ptCapParam);

/* ================================================================================================
  MediaCtrlStopAudCap
  Stop audio capture
  =============================================================================================== */
s32 MediaCtrlStopAudCap(EMediaCtrlAudChannelType eChnType);

/* ================================================================================================
func:        MediaCtrlStartAudEncode
            Start audio encode
nEncChn:    which channel to encode
ptEncParam: audio encode param
eEncSource: which channel bind to encoder
  =============================================================================================== */
s32 MediaCtrlStartAudEncode(s32 nEncChn, TMediaCtrlAudEncParam *ptEncParam, EMediaCtrlAudChannelType eEncSource);

/* ================================================================================================
func:    MediaCtrlStopAudEncode
desc:    Stop audio encode
nEncChn: which chn to stop encode
  =============================================================================================== */
s32 MediaCtrlStopAudEncode(s32 nEncChn);

/* ================================================================================================
func:    MediaCtrlStartAudDecode
desc:    start audio decode
nDecChn: which chn to start decode
  =============================================================================================== */
s32 MediaCtrlStartAudDecode(s32 nDecChn);

/* ================================================================================================
func:    MediaCtrlStopAudDecode
desc:    stop audio decode
nDecChn: which chn to stop decode
  =============================================================================================== */
s32 MediaCtrlStopAudDecode(s32 nDecChn);

/* ================================================================================================
func:    MediaCtrlSetAudDecParam
desc:    set dec param
nDecChn: which chn to set
ptDecParam: dec param
  =============================================================================================== */
s32 MediaCtrlSetAudDecParam(s32 nDecChnm, TMediaCtrlAudDecParam *ptDecParam);

/* =================================================================================================
func:       MediaCtrlStartAudPlay
desc:       start audio play
ePlyType:   audio play type current only MEDIACTRL_AUD_LINEOUT
ptPlyParam: audio play param
ePlySorce:  audio play pcm source which from LINE1 LINE2 MIX0 MIX1 or DEC
=============================================================================================== */
s32 MediaCtrlStartAudPlay(EMediaCtrlAudChannelType ePlyType, TMediaCtrlAudPlyParam *ptPlyParam, EMediaCtrlAudChannelType ePlySorce);

/* =================================================================================================
func:       MediaCtrlStartAudPlay
desc:       stop audio play
ePlyType:   audio play type current only MEDIACTRL_AUD_LINEOUT
=============================================================================================== */
s32 MediaCtrlStopAudPlay(EMediaCtrlAudChannelType ePlyType);

/* ===============================================================================================
  func:       MediaCtrlSetAudEncParam
  desc:       Set audio encode param
  nEncChn:    which encode chn to set param
  ptEncParam: audio encode param
  =============================================================================================== */
s32 MediaCtrlSetAudEncParam(s32 nEncChn, TMediaCtrlAudEncParam *ptEncParam);

/* ===============================================================================================
  func:       MediaCtrlSetAudAecParam
  desc:       Set audio aec param
  eChnType:   audio chn type, only input channel
  ptAecParam: audio aec param
  =============================================================================================== */
s32 MediaCtrlSetAudAecParam(EMediaCtrlAudChannelType eChnType, TMediaCtrlAudAecParam *ptAecParam);

typedef struct
{
    u16 dwTargetLeveldBOv;
    u16 dwDigitalCompressionGaindB;
    BOOL32    bLimiterEnable;
}TMediaCtrlAudAgcConfig;

/* ===============================================================================================
  MediaCtrlStartAgc
  start aud agc input and output can all use agc
eChnType: input use MEDIACTRL_AUD_MICIN or MEDIACTRL_AUD_LINEIN1 out agc use MEDIACTRL_AUD_LINEOUT
ptAgcConfig: agc cofig param
  ============================================================================================== */
s32 MediaCtrlStartAudAgc(EMediaCtrlAudChannelType eChnType, TMediaCtrlAudAgcConfig *ptAgcConfig);

/* ===============================================================================================
  MediaCtrlStartAgc
  stop aud agc input and output can all use agc
eChnType: input use MEDIACTRL_AUD_MICIN or MEDIACTRL_AUD_LINEIN1 out agc use MEDIACTRL_AUD_LINEOUT
  ============================================================================================== */
s32 MediaCtrlStopAudAgc(EMediaCtrlAudChannelType eChnType);

#define MEDIACTRL_MAX_MIX_SOURCE (4)

typedef struct
{
    u32 dwSourceNum;
    EMediaCtrlAudChannelType eMixSource[MEDIACTRL_MAX_MIX_SOURCE];
}TMediaCtrlAudPcmMixParam;

/* ===============================================================================================
func:       MediaCtrlStartAudPcmMix
desc:       stop audio mix
nMixChn:    nMixChn mix chn
ptMixParam: mix param
  ============================================================================================== */
s32 MediaCtrlStartAudPcmMix(s32 nMixChn, TMediaCtrlAudPcmMixParam *ptMixParam);

/* ===============================================================================================
func:     MediaCtrlStopAudPcmMix
desc:     stop audio mix
nMixChn:  nMixChn mix chn
  ============================================================================================== */
s32 MediaCtrlStopAudPcmMix(s32 nMixChn);

typedef struct
{
    BOOL32 bMixEnable;
    u8 byMixDecChn;
}TMediaCtrlAudAdpcmMixParam;

/* ===============================================================================================
func:       MediaCtrlSetAudResampleParam
desc:       set audio resample
nCapChn:    audio chn
ptResParam: resample param
  ============================================================================================== */
s32 MediaCtrlSetAudResampleParam(s32 nCapChn, TMediaCtrlAudResParam *ptResParam);

/* ===============================================================================================
func:       MediaCtrlSetAudAdpcmMixParam
desc:       set audio mixer
nMixChn:    nMixChn mix chn
ptMixParam: mix param
  ============================================================================================== */
s32 MediaCtrlSetAudAdpcmMixParam(u32 nMixChn, TMediaCtrlAudAdpcmMixParam *ptMixParam);

typedef struct
{
    EMediaCtrlAudSampleBITS eSampleBits;
    EMediaCtrlAudChannelMode eChannelMode;
    EMediaCtrlAudSampleRate eSampleRate;
    u32 dwFrameSamples;                    /* sample points */
}TMediaCtrlAudPcmParam;

typedef struct
{
    u32 dwPcmLen;
    u8  *pbyPcmAddr;
}TMediaCtrlAudPcmBuf;

typedef s32 (*TMediaCtrlAudPcmCB)(s32 nCapChn, TMediaCtrlAudPcmParam *ptParam, TMediaCtrlAudPcmBuf *ptBuf);
/* ===========================================================================================
func: MediaCtrlRegisterAudPcmCallback
desc: register pcm stram callback
  =========================================================================================== */
s32 MediaCtrlRegisterAudPcmCallback(TMediaCtrlAudPcmCB pfCallback, TMediaCtrlAudPcmParam *ptReqParam);

/* ===================================================================================
函数名：   MediaCtrlSetChnOsdParam
功能：     设置对应通道的OSD
nCapChn:   采集通道号
nEncChn:   编码通道号
nAeraNum:  设置通道的OSD区域数量
atpAreaOsdInfo: 区域的OSD信息数组
返回值     0：正常， 其余值异常
注意点：  1.考虑到部分平台OSD内存不同区域在内存上时连续分布的，更改其中的某一区域大小
            会对其他区域造成影响，因此这种平台OSD的设置是以编码通道为单位的，在更改此
            编码通道中某一区域时，必须重置此编码通道所有的OSD, 对应无此限制的平台，考
            到兼容性，暂定也此方案，若发现性能不足则使用TMediaCtrlAreaOsdInfo中的
            nAreaId实现单独区域更新。
           2.bmp的宽高及显示起点上层必须按照要求对齐，若不能满足，将图片填充黑色像素
===================================================================================== */
s32 MediaCtrlSetChnOsd(s32 nCapChn, s32 nEncChn, s32 nAeraNum, const TMediaCtrlAreaOsdInfo atpAreaOsdInfo[]);

/* =============================================================================================
函数名：MediaCtrlUpdateOsdArea
功能：  局部更新对应OSD区域上内容
nCapChn: 采集通道号
nEncChn: 编码通道号
nAreaId: 局部刷新OSD区域编号
ptAreaOsdInfo: 刷新区域OSD信息
ptUpdateRect:  需要刷新OSD的区域，相对于视频左上角坐标
返回值： 0：正常， 其他值异常
注意点：刷新的区域必须在先前设置的区域之内，若此区域刚开始无需现在部分内容，需要预先用黑色像素
        填充，为后续局部刷新预留好区域
============================================================================================= */
s32 MediaCtrlUpdateOsdArea(s32 nCapChn, s32 nEncChn, s32 nAeraId, \
        const TMediaCtrlAreaOsdInfo *ptAreaOsdInfo, TMediaCtrlRectRegion *ptUpdateRect);


/* ===================================================================================
函数名：   MediaCtrlSetVoutOsdParam
功能：     设置对应回显通道的OSD, 使用同MediaCtrlSetChnOsdParam
===================================================================================== */
s32 MediaCtrlSetVoutOsd(s32 nCapChn, s32 nAeraNum, const TMediaCtrlAreaOsdInfo atpAreaOsdInfo[]);

/* ===================================================================================
 函数名：MediaCtrlUpdateVoutOsdArea
 功能：  局部更新对应回显OSD区域上内容，使用同MediaCtrlUpdateOsdArea
===================================================================================== */
s32 MediaCtrlUpdateVoutOsdArea(s32 nCapChn, s32 nAeraId, \
        const TMediaCtrlAreaOsdInfo *ptAreaOsdInfo, TMediaCtrlRectRegion *ptUpdateRect);


/* ===================================================================================
函数名：   MediaCtrlSetViOsd
功能：     设置对应VI输入通道的OSD, 使用同MediaCtrlSetChnOsdParam
===================================================================================== */
s32 MediaCtrlSetViOsd(EMediaCtrlVidChnType eVinType, s32 nAeraNum, const TMediaCtrlAreaOsdInfo atpAreaOsdInfo[]);


/* ===================================================================================
 函数名：MediaCtrlUpdateViOsdArea
 功能：  局部更新对应OSD区域上内容
 nCapChn: 采集通道号
 nEncChn: 编码通道号
 nAreaId: 刷新OSD区域编号
 ptAreaOsdInfo: 刷新区域OSD信息
 ptUpdateRect:    需要刷新OSD的区域，相对于视频左上角坐标
 返回值： 0：正常， 其他值异常
 注意点：刷新的区域必须在先前设置的区域之内，若此区域刚开始无需现在部分内容，需要预先用黑色像素
         填充，为后续局部刷新预留好区域
         所给OSD信息将会被整体更新到OSD区域，上层需要给出对应的OSD信息和区域坐标
===================================================================================== */
s32 MediaCtrlUpdateViOsdArea(EMediaCtrlVidChnType eVinType, s32 nAeraId, \
        const TMediaCtrlAreaOsdInfo *ptAreaOsdInfo, TMediaCtrlRectRegion *ptUpdateRect);

s32 MediaCtrlSetVoutFbOsd(s32 nCapChn, s32 nAeraNum, const TMediaCtrlAreaOsdInfo atpAreaOsdInfo[]);

s32 MediaCtrlSetVoutFbParam(u32 nGraphChn, TMediaCtrlFbOsdParam *ptFbOsdParam);

s32 MediaCtrlUpdateVoutOsdArea(s32 nCapChn, s32 nAeraId, \
        const TMediaCtrlAreaOsdInfo *ptAreaOsdInfo, TMediaCtrlRectRegion *ptUpdateRect);

s32 MediaCtrlUpdateVoutFbOsdArea(s32 nCapChn, s32 nAeraId, \
        const TMediaCtrlAreaOsdInfo *ptAreaOsdInfo, TMediaCtrlRectRegion *ptUpdateRect);

/* ===================================================================================
函数名：MediaCtrlGetVoutFbOsd
功能：获取FB OSD区域信息
osdinfo：FB OSD区域信息，包括需要粘贴OSD的内存、当前fb的分辨率宽高
返回值：0正常，其他值异常
注意点：此接口将fb开放给使用者，可以根据获取的地址，自由粘贴OSD
===================================================================================== */
s32 MediaCtrlGetVoutFbOsd(s32 nGraphChn, TMediaCtrlVoFbOsdInfo *osdinfo);

/* ===================================================================================
函数名：MediaCtrlShowVoutFb
功能：打开或者关闭framebuf
返回值：0正常，其他值异常
注意点：可以和MediaCtrlGetVoutFbOsd配合使用
===================================================================================== */
s32 MediaCtrlShowVoutFb(s32 nGraphChn, BOOL32 bShow);

typedef void (*TMediaCtrlVoFbResFrameCB)(const TMediaCtrlVidReslution *pFbRes);
/* ===================================================================================
函数名：MediaCtrlSetVoFbResCallBack
功能：设置fb分辨率改变回调
===================================================================================== */
s32 MediaCtrlSetVoFbResCallBack(TMediaCtrlVoFbResFrameCB pfCallBack);

/* ===================================================================================
 函数名：MediaCtrlGetVoutOsdRefSize
 功能：  获取回显osd的参考的大小，部分平台回显OSD是相对于采集的，业务需要感知OSD的参考大小
         暂时只对3516A有效，其他的默认主流分辨率大小
tMainEncRes：主流分辨率大小，输入参数，需要业务指定（异步命令处理，保证同步)
tRefRes:     回显OSD的参考大小，业务根据此值缩放回显OSD图片
===================================================================================== */
s32 MediaCtrlGetVoutOsdRefSize(s32 nCapChn, TMediaCtrlVidReslution *tMainEncRes, TMediaCtrlVidReslution *tRefRes);

/* =============================================================================================
  函数名：MediaCtrlEncodeProcess
  功能：  对图片进行编码处理
  tInPic: 未编码图片信息，图片格式只支持YUV420,只要填宽高，大小可不填,图片空间预分配
  tOutPic: 编码后的图片信息，图片只支持JPEG, 输出结构中带有长度，图片空间预分配
ePicQuality: 图片质量
============================================================================================== */
s32 MediaCtrlPicEncodeProcess(TMediaCtrlPicInfo *tInPic, TMediaCtrlPicInfo *tOutPic, EMediaCtrlImageQuality ePicQuality);

/* =============================================================================================
  函数名：MediaCtrlGetH264HeaderInfo
  功能：  获取H264 SPS PPS　头部信息
  pHeaderInfo: H264头部内存指针，需要由调用者预先分配
  dwHeaderLen: 需要拷贝的头部长度，由调用者传入，现在最大值为128字节
  返回值：<0 代表获取失败，否则为实际拷贝的头部长度
============================================================================================== */
s32 MediaCtrlGetH264HeaderInfo(s32 nCapChn, s32 nEncChn, u8 *pHeaderInfo, u32 dwHeaderLen);


typedef s32 (*TMediaCtrlVinPnpEventCB)(s32 nCapChn, EMediaCtrlVinPnpEvent eVinPnpType);
/* =============================================================================================
  函数名：MediaCtrlSetVinPnpEventCallBack
  功能：  设置vin输入源改变回调函数, 如视频源丢失,新视频源接入
============================================================================================== */
s32 MediaCtrlSetVinPnpEventCallBack(TMediaCtrlVinPnpEventCB tVinPnpEventCB);

typedef s32 (*TMediaCtrlRunningStatusCB)(EMediaCtrlRunningStatus eRunningStatus);
/* =============================================================================================
  函数名：MediaCtrlSetRunningStatusCallBack
  功能：  设置媒体控制运行状态回调,每隔5s中上报运行状态,正确上报MEDIACTRL_RUNNING_SUCCESS
============================================================================================== */
s32 MediaCtrlSetRunningStatusCallBack(TMediaCtrlRunningStatusCB tRunningStatusCB);

typedef struct
{
    EMediaCtrlRunningStatus eRunningStatus;
    EMediaCtrlVidChnType eVinChnType;       /* for cap error use this */
    s32 nEncChn;                            /* for enc error usd this */
}TMediaCtrlRunningStatusEx;
/* ========================================================================================
nChnNo: for cap error mean cap chn for enc error mean enc chn
======================================================================================---= */
typedef s32 (*TMediaCtrlRunningStatusCBEx)(TMediaCtrlRunningStatusEx *ptStatusEx);

s32 MediaCtrlSetRunningStatusCallBackEx(TMediaCtrlRunningStatusCBEx tRunningStatusCBEx);

/* *****************************************************************************************
函数名：        MediaCtrlGetVidEncParam
功能：          获取视频编码参数 暂时此接口主要用于协议同步分辨率切换的过程
                因为媒体控制采用异步机制，协议需要知道确切的分辨率切换时机
nCapChn:        视频采集通道号
nEncChn:        视频编码通道号
ptVidEncParam:  指向视频编码参数结构体指针
注意点：        由于暂时只获取分辨率，未对整个结构体加锁，获取所有参数可能存在不一致性
****************************************************************************************** */
s32 MediaCtrlGetVidEncParam(s32 nCapChn, s32 nEncChn, TMediaCtrlVidEncParam *ptVidEncParam);

/*
   设置码流叠加信息
 */
s32 MediaCtrlSetStreamComposInfo(s32 nCapChn, s32 nEncChn, TMediaCtrlStreamComposInfo *compos_info);

/* *****************************************************************************
  函 数 名：MediaCtrlTLVinfoCallbakRegister
  功  能： 回调TLV信息给业务
***************************************************************************** */
typedef void (*TLVinfoCallback)(s32 enc_chn, void *TLVinfo, u32 length);//业务实现该函数，尽快处理TLVinfo
u32 MediaCtrlTLVinfoCallbakRegister(TLVinfoCallback pCallbak);

/* *****************************************************************************
  函 数 名：MediaCtrlSetEncFrameCallBack
  bVideo:  TURE 代表视频， FALSE代表音频
  enc_chn: 视频： 0 主流 1副流, 音频：0 第一路音频，1第二路音频
  pFhr:    音视频帧数据
  pFhrEx:  视频扩展sei信息
  功  能：设置编码帧数据回调函数，用于获取编码后的帧数据
          编码格式定义m_byMediaType  见"kdvdef.h" MEDIA_TYPE_H264 MEDIA_TYPE_H265等
***************************************************************************** */
typedef void (*TMediaCtrlEncFrameCB)(BOOL32 bVideo, s32 enc_chn, const NEWFRAMEHDR *pFhr, const TFrameExInfo *pFhrEx);
u32 MediaCtrlSetEncFrameCallBack(TMediaCtrlEncFrameCB pfCallBack);
/* ============================================================================================
  This 4 function is used only in android, for apk local show
  ============================================================================================ */

typedef struct
{
    EMediaCtrlRotateType eVidRotate;
    EMediaCtrlFlipType eVidFlip;
}TMediaCtrlRenderCfg;

typedef struct
{
    TMediaCtrlRectRegion CropRect;
}TMediaCtrlRenderParam;

/* ***************************************************************************
Func:        MediaCtrlCreateRenderChanne
            Create a local show channle with surface
pvWindow:   In android is surface where to show
pnRenderIdx:return value for next func use
ptRenderCfg: render cfg
**************************************************************************** */
s32 MediaCtrlCreateRenderChannel(void *pvWindow, s32 *pnRenderIdx, TMediaCtrlRenderCfg *ptRenderCfg);

/* ***************************************************************************
Func:        MediaCtrlDestroyRenderChannel
            Destroy nRenderIdx render channle
nRenderIdx: Which render channle to destroy
**************************************************************************** */
s32 MediaCtrlDestroyRenderChannel(s32 nRenderIdx);

/* ***************************************************************************
Func:        MediaCtrlStartRender
            Start Render show
nRenderIdx: Which render channle to start
**************************************************************************** */
s32 MediaCtrlStartRender(s32 nRenderIdx, EMediaCtrlVidChnType eRenderSource);

/* ***************************************************************************
Func:        MediaCtrlSetRender
            Set Render param
nRenderIdx: Which render channle to set
ptRenderParam: render param point
**************************************************************************** */
s32 MediaCtrlSetRender(s32 nRenderIdx, TMediaCtrlRenderParam *ptRenderParam);

/* ***************************************************************************
Func:        MediaCtrlDrawRender
            Sraw Render
nRenderIdx: Which render channle to start
**************************************************************************** */
s32 MediaCtrlDrawRender(s32 nRenderIdx, EMediaCtrlVidChnType eRenderSource);

/* ***************************************************************************
Func:        MediaCtrlStartRender
            Stop Render show
nRenderIdx: Which render channle to stop
**************************************************************************** */
s32 MediaCtrlStopRender(s32 nRenderIdx);

#define MEDIACTRL_MAX_FOCUS_METER_AREA (4)

typedef struct
{
    s32 nLeft;
    s32 nTop;
    s32 nRight;
    s32 nBottom;
    s32 nWeight;
}TMediactrlAreaWeight;

typedef struct
{
    u32 dwAreaNum;
    TMediactrlAreaWeight attArea[MEDIACTRL_MAX_FOCUS_METER_AREA];
}TMediaCtrlVidFocusArea;

typedef struct
{
    u32 dwAreaNum;
    TMediactrlAreaWeight attArea[MEDIACTRL_MAX_FOCUS_METER_AREA];
}TMediaCtrlVidMeterArea;

/* ***************************************************************************
Func:        MediaCtrlAreaFocusAndMeter
            Area focus and metering only use common android device other use ispctrl
eVinChnType: which SENSOR0 or SENSOR1
ptFocusArea: focus area which region from [-1000, 1000]
ptMeterArea: meter area which region from [-1000, 1000]
**************************************************************************** */
s32 MediaCtrlAreaFocusAndMeter(EMediaCtrlVidChnType eVinChnType,
        TMediaCtrlVidFocusArea *ptFocusArea,
        TMediaCtrlVidMeterArea *ptMeterArea);

typedef enum
{
    MEDIACTRL_FOCUS_MODE_AUTO,               //Stop AutoFocus
    MEDIACTRL_FOCUS_MODE_CONTINUOUS_PICTURE, //Continous Focus PictureMode
    MEDIACTRL_FOCUS_MODE_CONTINUOUS_VIDEO,   //Continous Focus VideoMode
}EMediaCtrlCameraFocusMode;

/* ************************************************************************************
func:     MediaCtrlSetCameraFocusMode
desc:     set the focus mode for camera
eVinType: SENSOR0
eMode:    focus mode
************************************************************************************ */
s32 MediaCtrlSetCameraFocusMode( EMediaCtrlVidChnType eVinChnType,EMediaCtrlCameraFocusMode fMode);

typedef enum
{
    MEDIACTRL_FLASH_MODE_OFF,
    MEDIACTRL_FLASH_MODE_AUTO,
    MEDIACTRL_FLASH_MODE_ON,
    MEDIACTRL_FLASH_MODE_RED_EYE,
    MEDIACTRL_FLASH_MODE_TORCH
}EMediaCtrlCameraFlashMode;


/* ************************************************************************************
func:     MediaCtrlSetCameraFlashMode
desc:     set the light mode for camera
eVinType: which SENSOR0 or SENSOR1
eMode:    flash mode
************************************************************************************ */
s32 MediaCtrlSetCameraFlashMode(EMediaCtrlVidChnType eVinChnType, EMediaCtrlCameraFlashMode eMode);

typedef enum
{
	MEDIACTRL_VIDEO_STABLIZATION_MODE_OFF,
	MEDIACTRL_VIDEO_STABLIZATION_MODE_EIS,
	MEDIACTRL_VIDEO_STABLIZATION_MODE_OIS,
}EMediaCtrlVideoStablizationMode;

/*************************************************************************************
func:     MediaCtrlSetCameraStablizationMode
desc:     set the video stablization mode for camera
eVinType: SENSOR0
sMode:    VideoStablization mode
*************************************************************************************/
s32 MediaCtrlSetVideoStablizationMode(EMediaCtrlVidChnType eVinChnType, EMediaCtrlVideoStablizationMode sMode);


typedef enum
{
	MEDIACTRL_BITSTREAM_MODE_PREVIEW,
	MEDIACTRL_BITSTREAM_MODE_VIDEO,
	MEDIACTRL_BITSTREAM_MODE_BOTH,
}EMediaCtrlBitStreamMode;

/*************************************************************************************
func:     MediaCtrlSetCameraBitStreamMode
desc:     set the camera bitstream for camera
eVinType: SENSOR0
bMode:    BitStreamMode mode
*************************************************************************************/
s32 MediaCtrlSetCameraBitStreamMode(EMediaCtrlVidChnType eVinChnType, EMediaCtrlBitStreamMode bMode);

/* ==========================================================================================
  Video dec
  ========================================================================================== */
typedef s32 (*TMediaCtrlDecReleaseCB) (s32 nDecChn, NEWFRAMEHDR *ptFrame);

/* ==========================================================================================
  Put encoded video frame to decode
  *======================================================================================== */
s32 MediaCtrlVidDecPutFrame(s32 nDecChn, NEWFRAMEHDR *ptFrame);


/* ==========================================================================================
  register release frame callback when frame is decoded call this callback to release frame
  *======================================================================================== */
s32 MediaCtrlVidDecSetReleaseCallback(TMediaCtrlDecReleaseCB tDecReleaseCB);

/* ==========================================================================================
  start video decode
  *======================================================================================== */
s32 MediaCtrlStartVidDecode(s32 nDecChn);

/* ==========================================================================================
  stop video decode
  *======================================================================================== */
s32 MediaCtrlStopVidDecode(s32 nDecChn);

/* ==========================================================================================
 Set Fisheye Parameter
  *======================================================================================== */
s32 MediaCtrlSetFisheyeParam(s32 nCapChn, s32 nEncChn, const TMediaCtrlEncFisheyeParam *ptFisheyeParam);


/* ==========================================================================================
  以下接口提供给智能ipc使用
  ========================================================================================= */

#define MEDIACTRL_MAX_VID_DAT_PLANES (3)          /* 数据最大平面数 */

#define MEDIACTRL_MAX_INTELLI_ALG_BUF_NUM (50)    /* 算法最大缓存个数 */

typedef enum
{
    MEDIACTRL_ALG_PROCESSOR_LOCAL_DSP,      /* 内部DSP */
    MEDIACTRL_ALG_PROCESSOR_REMOTE_DSP,     /* 外部部DSP */
    MEDIACTRL_ALG_PROCESSOR_LOCAL_X86,       /* baytrail */
    MEDIACTRL_ALG_PROCESSOR_ARM,            /* arm */
    MEDIACTRL_ALG_PROCESSOR_GPU,             /* gpu */
    MEDIACTRL_ALG_PROCESSOR_HORIZON         /* horizon module */
}EMediaCtrlAlgProcesserType;                /* 算法处理器类型 */

typedef enum
{
    MEDIACTRL_IMG_SEI = 1,      /* SEI贴图 */
}EMediaCtrlImgType;             /* 图片类型 */

typedef enum
{
    MEDIACTRL_VID_ENC_FRAME_TYPE_I,
    MEDIACTRL_VID_ENC_FRAME_TYPE_P,
    MEDIACTRL_VID_ENC_FRAME_TYPE_B
}EMediaCtrlVidEncFrameType;

typedef struct
{
    s32 nSubCapChn;                                 /* 采集子通道号，0 主流大图，1副流小图 */
    u32 dwOffsetX;                                  /* 有效数据的X偏移 */
    u32 dwOffsetY;                                  /* 有效数据的Y偏移 */
    u32 dwWidth;                                    /* 有效数据宽度 */
    u32 dwPitch[MEDIACTRL_MAX_VID_DAT_PLANES];      /* 整个数据区域宽度 */
    u32 dwHeight;                                   /* 有效数据高度 */
    EMediaCtrlVidDateFormat eDateFormat;            /* 采集数据类型 */
    u8 *pbyPhyAddr[MEDIACTRL_MAX_VID_DAT_PLANES];   /* 数据存储物理地址 */
    u8 *pbyVirAddr[MEDIACTRL_MAX_VID_DAT_PLANES];   /* 数据存储虚拟地址 */
    void *pvExternSei;                              /* 扩展sei信息指针 */
    u32 dwExternSeiLen;                             /* 扩展Sei信息长度 */
    u64 qwTimeStamp;                                /* 时间戳 */
    u32 dwPoolId;                                   /* 从hisi获取的buff，它的poolid */
}TMediaCtrlVidCapFrameInfo;                         /* 采集帧数据信息 */

typedef struct
{
    TMediaCtrlVidCapFrameInfo tFrameInfo[MEDIACTRL_MAX_VID_SUB_CAP_NUM];
    u32 dwFrameNum;                           /* 实际有效帧数 */
}TMediaCtrlVidCapFrameInfoEx;


typedef enum {
    MEDIACTRL_SEI_ADD_MODE_TIMESTAMP_MATCH  = 0, /* 当前 sei 数据只添加到时间戳匹配的帧上,
                                                    每一帧数据的sei数据由上层提供,
                                                    不提供导致超时等待从而帧率降低 */
    MEDIACTRL_SEI_ADD_MODE_FOLLOW_EVERY_FRM = 1, /* 当前 sei 数据添加到当前调用后的帧上,
                                                    时间戳无效,最新设置的sei数据覆盖之前 */
    MEDIACTRL_SEI_ADD_MODE_FOLLOW_I_FRM     = 2, /* 当前 sei 数据添加到当前调用后的I帧上,
                                                 时间戳无效,最新设置的sei数据覆盖之前 */
    MEDIACTRL_SEI_ADD_MODE_MAX
} EMediaCtrlSeiAddMode;

typedef enum {
    MEDIACTRL_SEI_TLV_TYPE_EXT           = 0,          /* 扩展sei数据TLV类型，该类型是为了新的接口兼容原来自定义SEI的叠加 */
    MEDIACTRL_SEI_TLV_TYPE_UPPER_DEFINED = 0x20040000, /* 业务自定义类型，从该数据开始使用 */
} EMediaCtrlSeiTlvType;

typedef struct {
    u32                dwType;        /* sei信息TLV类型 */
    void              *pvData;        /* sei信息指针 */
    u32                dwLen;         /* sei信息长度 */
    u64                qwTimeStamp;   /* 时间戳 */
} TMediaCtrlSeiInfo;


/* Soft Encoder Inpurt Structure*/
typedef struct
{
    unsigned char *yuv_addr;
    u32 dwWidth;
    u32 dwHeight;
    u64 llTimeStamp;
}TMediaCtrlVidCapInfo;

typedef struct
{
    u32 nEncChn;                              /* 编码通道号 */
    u8* pbyVirAddr;                           /* 编码帧虚拟地址 */
    u8* pbyPhyAddr;                           /* 编码帧物理地址 */
    u32 dwFrameWidth;                         /* 帧宽度 */
    u32 dwFrameHeight;                        /* 帧高度 */
    u32 dwFrameSize;                          /* 帧大小 */
    u64 qwTimeStamp;                          /* 时间戳 */
    EMediaCtrlVidEncType eEncType;            /* 编码类型 */
    EMediaCtrlVidEncFrameType eEncFrameType;  /* 编码帧类型 */
}TMediaCtrlVidEncFrameInfo;                   /* 编码帧信息 */

typedef struct
{
    TMediaCtrlVidEncParam jpeg_enc_param;
    TMediaCtrlVidEncFrameInfo jpeg_frame;
    BOOL32 bScale;                     /* 是否需要缩小 */
    TMediaCtrlVidReslution tScaleRes;  /* 缩小输出的分辨率 */
}TMediaCtrlSnapShotInfo;

typedef struct
{
    BOOL32 bCorp;                      /* 是否需要编码裁剪编码 TURE时下面的区域有效 */
    TMediaCtrlRectRegion tEncRegion;   /* 编码区域 */
    u32 dwJpegQuality;                 /* 图片质量0-99 */
    u64 qwTimeStamp;                   /* 时间戳 智能技术部，用此时间戳标记要抓拍的buffer，智能ipc产品部可以不填 */
    u32 dwSnapIndex;                   /* 抓拍索引号，用于区分同一数据的不同次抓拍 */
    BOOL32 bBufCanFree;                   /* 标记buffer是否已经完成所有编码，可以释放 */
    TMediaCtrlVidCapFrameInfoEx *ptFrameInfoEx; /* 抓拍yuv帧数据，智能ipc产品部含抓拍yuv数据，智能技术部可以不填 */
    BOOL32 bAddOsd;                      /* 是否叠加字幕 TURE时下面的区域有效 */
    TMediaCtrlAreaOsdInfo *ptAreaOsdInfo;
    BOOL32 bScale;                     /* 是否需要缩小 */
    TMediaCtrlVidReslution tScaleRes;  /* 缩小输出的分辨率 */
    u8 *pJpegFile;                     /* 输出的jpeg文件路径 */
}TMediaCtrlIntelliSnapParam;

typedef struct
{
    EMediaCtrlAlgProcesserType  eAlgProcesserType;  /* DSP类型，内部DSP还外部DSP */
    void **pvAlgHandle;                             /* 算法Handle */
    void *pvParams;                                    /* 算法初始化输入参数指针 */
    u32 dwParamsLen;                                /* 算法初始化输入参数长度 */
}TMediaCtrlIntelliAlgInitInfo;

typedef struct
{
    EMediaCtrlImgType eImgType;      /* 贴图类型 */
    BOOL32 bOpenSnapFrame;           /* 交通卡口：获取抓拍帧 */
    BOOL32 bGetVoutSrc;              /* 鑾峰彇鍥炴樉瀵瑰簲YUV */
}TMediaCtrlIntelliImgInitInfo;

typedef struct
{
    void *pvAlgHandle;        /* 算法Handle */
    void *pvParams;           /* 设置算法参数指针 */
    u32 dwParamsLen;          /* 设置算法参数长度 */
}TMediaCtrlIntelliAlgParams;

typedef struct
{
    void *pvAlgHandle;                         /* 算法Handle */
    TMediaCtrlVidCapFrameInfoEx tFrameInfoEx;  /* 采集buffer信息 */
    void *pvAlgAttachInfo;                     /* 算法附加信息 */
    u32  dwAlgAttachInfoLen;                   /* 算法附加信息长度 */
}TMediaCtrlIntelliAlgInputInfo;                /* 算法输入信息 */

typedef struct
{
    u64 qwBufTimeStamp[MEDIACTRL_MAX_INTELLI_ALG_BUF_NUM]; /* 缓存buf的链表，用时间戳做标示 */
    u32 dwBufNum;                                          /* buf数 */
}TMediaCtrlIntelliAlgBufList;

typedef struct
{
    TMediaCtrlIntelliAlgBufList ptSnapBufList;   /* 本次算法输出后需要抓拍的buffer */
    void *pvAlgOutput;                             /* 算法输出的结果 */
    u32 dwAlgOutputLength;                         /* 算法输出长度 */
}TMediaCtrlIntelliAlgOutputInfo;

typedef struct
{
    void *pvAlgHandle;           /* 算法Handle */
    void *pvAlgStatus;           /* 获取算法状态指针 */
    u32 dwAlgStatusLen;          /* 状态信息长度 */
}TMediaCtrlIntelliAlgStatusInfo;

typedef struct
{
    void *pvAlgHandle;           /* 算法Handle */
    void *pvAlgStatis;           /* 获取算法状态指针 */
    u32 dwAlgStatisLen;          /* 状态信息长度 */
}TMediaCtrlIntelliAlgStatisInfo;

typedef struct
{
    void *pvAlgHandle;           /* 算法Handle */
    void *pvAlgVersion;          /* 获取算法版本指针 */
    u32  l32AlgVersionLen;       /* 版本信息长度 */
}TMediaCtrlIntelliAlgVersionInfo;

typedef struct
{
    u8 *pbyExBuf;               /* 扩展叠加信息 */
    u32 dwExBufLen;             /* 扩展叠加信息长度 */
}TMediaCtrlFrameExInfo;

typedef s32 (*PFMediaCtrlIntelliAlgOutputCB)(TMediaCtrlIntelliAlgOutputInfo *ptOutput);
/* ============================================================================================================
定义：       PFMediaCtrlIntelliSnapCB
功能：       智能抓拍回调函数定义
nCapChn      采集通道号，用于多目摄像头区别采集通道
dwSnapIndex: 抓拍索引，回传启动抓拍时传入的索引值,用于区分同一buffer的不同次抓拍
============================================================================================================ */
typedef s32 (*PFMediaCtrlIntelliSnapCB)(s32 nCapChn,u32 dwSnapIndex, TMediaCtrlVidEncFrameInfo *ptEncFrameInfo);

/* ============================================================================================================
定义：       PFMediaCtrlIntelliVidCapCB
功能：       智能抓拍采集回调
nCapChn      采集通道号，用于多目摄像头区别采集通道
ptFrameInfoEx：集数据buf信息
============================================================================================================ */
typedef s32 (*PFMediaCtrlIntelliVidCapCB)(s32 nCapChn, TMediaCtrlVidCapFrameInfoEx *ptFrameInfoEx);

/* *****************************************************************************
  函 数 名：MediaCtrlIntelliSetVidCapCallback
  功  ??：  设置采集回调
***************************************************************************** */
s32 MediaCtrlIntelliSetVidCapCallback(PFMediaCtrlIntelliVidCapCB pfCallback);

/* *****************************************************************************
  函 数 名：MediaCtrlIntelliGetCapFrame
  功  能：  获取采集帧数据
***************************************************************************** */
s32 MediaCtrlIntelliGetCapFrame(s32 nCapChn, TMediaCtrlVidCapFrameInfoEx *ptFrameInfoEx);

/* *****************************************************************************
  函 数 名：MediaCtrlIntelliPutCapFrame
  功  能：  释放采集帧数据，同时填充sei信息到媒体控制进行编??
***************************************************************************** */
s32 MediaCtrlIntelliPutCapFrame(s32 nCapChn, TMediaCtrlVidCapFrameInfoEx *ptFrameInfoEx);

/* *************************************************************************************
func:          MediaCtrlGetEncYuv
desc:          get yuv frame of the encChn, it's from vpss in hisi.
nEncChn:       the encChn id
ptFrameInfoEx: yuv frame of the encChn
*************************************************************************************** */
s32 MediaCtrlGetEncYuv(s32 nEncChn, TMediaCtrlVidCapFrameInfoEx *ptEncYuvFrm);

/* *************************************************************************************
func:          MediaCtrlPutEncYuv
desc:          put yuv frame of the encChn, be used with MediaCtrlGetEncYuv.
nEncChn:       the encChn id
ptFrameInfoEx: yuv frame from MediaCtrlGetEncYuv
*************************************************************************************** */
s32 MediaCtrlPutEncYuv(s32 nEncChn, TMediaCtrlVidCapFrameInfoEx *ptEncYuvFrm);

/* *************************************************************************************
func:          MediaCtrlIntelliGetDecFrame
desc:          get yuv frame which from nDechn channle decoder
nDechn:        which decoder yuv to get
ptFrameInfoEx: yuv frame
*************************************************************************************** */
s32 MediaCtrlIntelliGetDecFrame(s32 nDecChn, TMediaCtrlVidCapFrameInfoEx *ptFrameInfoEx);

/* =====================================================================================
func:          MediaCtrlIntelliPutDecFrame
desc:          release dec yuv frame
nDechn:        which decoder yuv to release
ptFrameInfoEx: yuv frame
  ====================================================================================== */

s32 MediaCtrlIntelliPutDecFrame(s32 nDecChn, TMediaCtrlVidCapFrameInfoEx *ptFrameInfoEx);

/* ************************************************************************************** */
s32 MediaCtrlVidGetEmptyFrame(TMediaCtrlVidCapFrameInfo *frame_info);

/* =====================================================================================
func:          MediaCtrlVidColorConvert
desc:          支持hisi buf数据的颜色空间转换，目前有
                1. NV21转rgb，此时ptSrcRect，ptDstRect默认整个图像
                2. NV21和NV12互转，此时ptSrcRect，ptDstRect无需设置，置为NULL
               ptSrcFrame是源图buf，ptDstFrame是从MediaCtrlVidGetEmptyFrame获得的buf
  ====================================================================================== */
s32 MediaCtrlVidColorConvert(TMediaCtrlVidCapFrameInfo  *ptSrcFrame, TMediaCtrlRectRegion *ptSrcRect,
                             TMediaCtrlVidCapFrameInfo  *ptDstFrame, TMediaCtrlRectRegion *ptDstRect);

s32 MediaCtrlVidReturnEmptyFrame(TMediaCtrlVidCapFrameInfo *frame_info);

/* *****************************************************************************
  函 数 名：MediaCtrlIntelliAlgInit
  功  能：智能算法初始化
***************************************************************************** */
s32 MediaCtrlIntelliAlgInit(TMediaCtrlIntelliAlgInitInfo *ptInitInfo);

/* *****************************************************************************
  函 数 名：MediaCtrlIntelliAlgProcess
  功  能：智能算法处理
***************************************************************************** */
s32 MediaCtrlIntelliAlgProcess(TMediaCtrlIntelliAlgInputInfo *ptInputInfo);

/* *****************************************************************************
  函 数 名：MediaCtrlIntelliSetAlgOutputCallback
  功  能：设置算法输出回调
***************************************************************************** */
s32 MediaCtrlIntelliSetAlgOutputCallback(PFMediaCtrlIntelliAlgOutputCB pfAlgOutputCB);

/* *****************************************************************************
  函 数 名：MediaCtrlIntelliSetAlgParams
  功  能： 设置智能算法参数
***************************************************************************** */
s32 MediaCtrlIntelliSetAlgParams(TMediaCtrlIntelliAlgParams *ptParams);

/* *****************************************************************************
  函 数 名：MediaCtrlIntelliGetAlgParams
  功 能： 获取智能算法参数
***************************************************************************** */
s32 MediaCtrlIntelliGetAlgParams(TMediaCtrlIntelliAlgParams *ptParams);

/* *****************************************************************************
  函 数 名：MediaCtrlIntelliStartSnap
  功  能： 启动智能抓拍
***************************************************************************** */
s32 MediaCtrlIntelliStartSnap(s32 cap_chn, TMediaCtrlIntelliSnapParam *ptParam);

/* *****************************************************************************
  函 数 名：MediaCtrlIntelliCacheClean
  功  能： 将Cache中的数据同步到内存
***************************************************************************** */
s32 MediaCtrlIntelliCacheClean(u8* pAddr, u8 *pPhyAddr, u32 dwSize);

/* *****************************************************************************
  函 数 名：MediaCtrlIntelliCacheInvalid
  功  能： 将内存中的数据同步到Cache
***************************************************************************** */
s32 MediaCtrlIntelliCacheInvalid(u8 *pAddr, u8 *pPhyAddr, u32 dwSize);

/* *****************************************************************************
  函 数 名：MediaCtrlIntelliSetSnapCallback
  功  能： 设置智能抓拍回调
***************************************************************************** */
s32 MediaCtrlIntelliSetSnapCallback(PFMediaCtrlIntelliSnapCB pfSnapCB);

/* *****************************************************************************
  函 数 名：MediaCtrlIntelliGetAlgStatus
  功  能： 获取智能算法状态，直接封装智能算法接口
***************************************************************************** */
s32 MediaCtrlIntelliGetAlgStatus(TMediaCtrlIntelliAlgStatusInfo *ptStatusInfo);

/* *****************************************************************************
  函 数 名：MediaCtrlIntelliGetAlgStatis
  功  能： 获取智能算法统计信息，直接封装智能算法接口
***************************************************************************** */
s32 MediaCtrlIntelliGetAlgStatis(TMediaCtrlIntelliAlgStatisInfo *ptStatisInfo);

/* *****************************************************************************
  函 数 名：MediaCtrlIntelliGetAlgVersion
  功  能： 获取智能算法版本号，直接封装智能算法接口
***************************************************************************** */
s32 MediaCtrlIntelliGetAlgVersion(TMediaCtrlIntelliAlgVersionInfo *ptVersioInfo);

/* *****************************************************************************
  函 数 名：MediaCtrlIntelliGetEmptyFrame
  功  能： 获取空的yuv帧缓冲，主要用于填充完数据后进行jpeg编码
***************************************************************************** */
s32 MediaCtrlIntelliGetEmptyFrame(TMediaCtrlVidCapFrameInfoEx *ptFrameInfoEx);

/* *****************************************************************************
  函 数 名：MediaCtrlIntelliPutEmptyFrame
  功  能： 释放之前获取的空的yuv帧缓冲，用于jpeg编码后对buffer的释放
***************************************************************************** */
s32 MediaCtrlIntelliPutEmptyFrame(TMediaCtrlVidCapFrameInfoEx *ptFrameInfoEx);

/* *****************************************************************************
  函 数 名：MediaCtrlIntelliPutSeiInfo
  功  能： 算法增加sei信息(扩展sei方式),本接口不建议使用,建议改用
          MediaCtrlSetSeiAddMode, MediaCtrlEnableSei,MediaCtrlPutSeiInfo
          MediaCtrlDisableSei实现
***************************************************************************** */
s32 MediaCtrlIntelliPutSeiInfo(s32 nEncChn, TMediaCtrlVidCapFrameInfoEx *ptFrameInfoEx);



/* *****************************************************************************
  函 数 名：MediaCtrlRegisterSeiWriter
  功  能： 注册sei写入用户，在所有sei操作之前调用
  说 明：成功返回user id，失败返回NULL
***************************************************************************** */
void *MediaCtrlRegisterSeiWriter(void);

/* *****************************************************************************
  函 数 名：MediaCtrlUnRegisterSeiWriter
  功  能： 销毁sei写入用户，销毁后用户无法再操作sei
  说 明：成功返回0，出错返回负值
***************************************************************************** */
s32 MediaCtrlUnRegisterSeiWriter(void *pvWriterId);

/* *****************************************************************************
  函 数 名：MediaCtrlSetSeiAddMode
  功  能： 设置sei添加的模式，建议在开启之前调用
  说 明：成功返回0，出错返回负值
***************************************************************************** */
s32 MediaCtrlSetSeiAddMode(void *pvWriterId, s32 nEncChn, u32 dwTlvType, EMediaCtrlSeiAddMode eMode);

/* *****************************************************************************
  函 数 名：MediaCtrlEnableSei
  功  能： 开启sei信息
  说 明：开启sei后，如果是接口使用者提供sei数据来逐帧匹配，
         那么请尽快压数据，不然会造成内部队列阻塞，后续压入sei数据失败。
        本函数成功返回0，出错返回负值
***************************************************************************** */
s32 MediaCtrlEnableSei(void *pvWriterId, s32 nEncChn, u32 dwTlvType);

/* *****************************************************************************
  函 数 名：MediaCtrlPutSeiInfo
  功  能： 增加sei信息
  说 明：dwCnt是数组ptSeiInf成员个数。设计 dwCnt 参数是出于考虑同时配置多个编码帧，
         或者配置同一编码帧不同类型sei数据考虑。
         注意，一帧编码帧只能配置一次特定类型sei数据，需要将相同类型sei数据合并成一个来配置。
         本函数成功返回0，出错返回负值
***************************************************************************** */
s32 MediaCtrlPutSeiInfo(void *pvWriterId, s32 nEncChn, TMediaCtrlSeiInfo *ptSeiInf, u32 dwCnt);

/* *****************************************************************************
  函 数 名：MediaCtrlDisableSei
  功  能： 关闭sei信息
  说 明：成功返回0，出错返回负值
***************************************************************************** */
s32 MediaCtrlDisableSei(void *pvWriterId, s32 nEncChn, u32 dwTlvType);



/* *****************************************************************************
  函 数 名：MediaCtrlIntelliImgInit
  功  能： 智能算法叠加h264/h265额外信息初始化
***************************************************************************** */
s32 MediaCtrlIntelliImgInit(TMediaCtrlIntelliImgInitInfo *mc_img_init);

/* *****************************************************************************
func:           MediaCtrlSendYUVFrameToEncoder
desc:           send out yuv frame to instead cap channel nCapChn yuv
nCapChn:        instead cap channel number
ptFrameInfoEx:  yuv frame
pvContext       context used to buffer release callback
 ***************************************************************************** */
s32 MediaCtrlSendYUVFrameToEncoder(s32 nCapChn, TMediaCtrlVidCapFrameInfoEx *ptFrameInfoEx, void *pvContext);

typedef s32 (*TMediaCtrlEncYUVReleaseCB) (s32 nCapChn, TMediaCtrlVidCapFrameInfoEx *ptFrameInfoEx, void *pvContext);
/* *****************************************************************************
func:             SetEncYUVReleaseCallBack
desc:             set callback when encoder use yuv complete can free
tEncYuvReleaseCB: yuv release callback
***************************************************************************** */
s32 MediaCtrlSetEncYUVReleaseCallBack(TMediaCtrlEncYUVReleaseCB tEncYuvReleaseCB);

/* ========================================================================================================= */
typedef struct
{
    s32 ViExtwidth;
    s32 ViExtHeight;
}TMediaCtrlIntellViExtInfo;

s32 MediaCtrlChangeIntelliCapFrame(s32 nCapChn, TMediaCtrlIntellViExtInfo *ptViExtInfo);

typedef void* PImgAlgHandle;

typedef enum
{
    MEDIACTRL_ALG_DETECT,           /* 算法检测 */
    MEDIACTRL_ALG_EXTRACT_FEATURE,  /* 提取特征 */
    MEDIACTRL_ALG_COMPARE_FEATURE,  /* 特征比对 */
}EMediaCtrlAlgSwitchType;

typedef struct
{
    void *pAlgResult;    /* 算法结果 */
    u32 dwReultLen;         /* 结果大小 */
    u64 qwTimeStamp;     /* 时间戳 */
}TMediaCtrlImgAlgInfo;

typedef s32 (*TMediaCtrlImgAlgCB) (TMediaCtrlImgAlgInfo *pAlgResultInfo);

typedef struct
{
    s32 nAlgType;     /* alg type is from alg header file */
    EMediaCtrlAlgProcesserType eProcerType;
    void *pvAlgParam;
    u32 dwAlgParamLen;
    TMediaCtrlImgAlgCB tImgAlgProcessCB;  /* 算法回调函数，用于外厂算法模块 */
}TMediaCtrlImgOpenInfo;

#define MEDIACTRL_MAX_IMG_ALG_USE_BUFFERS (50)

typedef struct
{
    TMediaCtrlVidCapFrameInfoEx *ptAlgFrame;
    void *pvAlgParam;   /* in algprocess input mean alg input param, algprocess out mean alg out param */
    u32 dwAlgParamLen;  /* alg param length */
    void *pvCurBufTag;  /* used in algprocess input mean which buffer input */
    u32 dwFreeBufNum;   /* used in algprocess output mean free buf num */
    void *pvFreeBufTags[MEDIACTRL_MAX_IMG_ALG_USE_BUFFERS]; /* used in algprocess output mean which input buf tag can free */
}TMediaCtrlImgProcInfo;

s32 MediaCtrlImgAlgOpen(PImgAlgHandle *pvAlgHandle, TMediaCtrlImgOpenInfo *ptOpenInfo);

s32 MediaCtrlImgAlgProcess(PImgAlgHandle pAlgHadle, TMediaCtrlImgProcInfo *img_in, TMediaCtrlImgProcInfo *img_out);

s32 MediaCtrlImgAlgClose(PImgAlgHandle pAlgHadle);

/* pvVersionInfo is TVersionInfo in VideoAlg.h */
s32 MediaCtrlImgGetVersion(s32 nAlgType, EMediaCtrlAlgProcesserType eProcerType, void *pvVersionInfo);

typedef enum
{
    MEDIACTRL_DATE_CONVERT_CROP  = 0x01,
    MEDIACTRL_DATE_CONVERT_SCAL  = 0x02,
    MEDIACTRL_DATE_CONVERT_COLOR = 0x04,
}EMediactrlDateConvertType;

typedef struct
{
    TMediaCtrlVidCapFrameInfoEx *ptFrame;
    TMediaCtrlRectRegion tCropRect;
}TMediactrlDateConvertParam;

/* *****************************************************************************
func MediaCtrlFrameDateConvert
use this func do crop scal or color convert
eType: some platform both support: use MEDIACTRL_DATE_CONVERT_CROP|MEDIACTRL_DATE_CONVERT_SCAL
describe: 在hisi平台上，ptInParam中的ptFrame从媒控获取，如MediaCtrlIntelliGetCapFrame，
          ptOutParam中的ptFrame从MediaCtrlIntelliGetEmptyFrame获取，
          需要指定宽高和图像格式，使用完毕后需要MediaCtrlIntelliPutEmptyFrame
          目前只支持yuv缩放
****************************************************************************** */
s32 MediaCtrlFrameDateConvert(EMediactrlDateConvertType eType, TMediactrlDateConvertParam *ptInParam, TMediactrlDateConvertParam *ptOutParam);

/* *****************************************************************************
  函 数 MediaCtrlSetDualChnOverlay
  功  能： 适用于双路采集的情况下将第二路采集叠加到第一路采集通道中
***************************************************************************** */
s32 MediaCtrlSetDualChnOverlay(s32 nCapChn, TMediaCtrlDualChnOverlay *ptDualChnOverlayParam);

s32 MediaCtrlQueryPointDst2Src(s32 *nCapChn,TMediaCtrlPoint * dst_point,TMediaCtrlPoint * src_point);

s32 MediaCtrlQueryPointSrc2Dst(s32 nCapChn, TMediaCtrlPoint *ptSrcPoint, TMediaCtrlPoint *ptDstPoint);

s32 MediaCtrlSetStitchScheme(EMediaCtrlStitchScheme eScheme);

s32 MediaCtrlSetStitchOutputResolution(EMediaCtrlStitchOutResolution eResolution);

s32 MediaCtrlFinetuneStitchParam(TMediaCtrlStitchFinetuneParam *ptFinetuneParam);

/* *****************************************************************************
  函 数 MediaCtrlGetStitchParam
  功  能： 获取拼接参数，bIsDefaultParam为1时，获取默认值；为0时，获取当前已调节过的值。
***************************************************************************** */
s32 MediaCtrlGetStitchParam(BOOL32 bIsDefaultParam, TMediaCtrlStitchFinetuneParam *ptDefaultParam);

/* *****************************************************************************
  函 数 MediaCtrlGetFisheyeCalibration
  功  能： 适用于鱼眼设备实时获取镜头矫正中心点
***************************************************************************** */
s32 MediaCtrlGetFisheyeCalibration(s32 nCapChn, s32 *s32OffsetH, s32 *s32OffsetV);

/* *****************************************************************************
  函 数 MediaCtrlSetFisheyeCalibration
  功  能： 适用于鱼眼设备实时调整镜头矫正中心点
***************************************************************************** */
s32 MediaCtrlSetFisheyeCalibration(s32 nCapChn, s32 s32OffsetH);

/* *****************************************************************************
  函 数 MediaCtrlGetVoutEdidInfo
  功  能：
***************************************************************************** */
s32 MediaCtrlGetVoutEdidInfo(u32 chip_id, void *pArgs);

/* *****************************************************************************
  函 数 MediaCtrlSetVidVoutFreeze
  功  能：
***************************************************************************** */
s32 MediaCtrlSetVidVoutFreeze(s32 nCapChn, u32 enable);

/* *****************************************************************************
  函 数 名：MediaCtrlPutExternYUVFrame
  功  能： 外部放入YUV数据
***************************************************************************** */
s32 MediaCtrlPutExternYUVFrame(TMediaCtrlVidCapFrameInfo *ptFrameInfo);

/* *****************************************************************************
  函 数 名：MediaCtrlPutExternYUVFrameEnd
  功  能： 通知下层，外部停止放入YUV数据
***************************************************************************** */
s32 MediaCtrlPutExternYUVFrameEnd();


/* *****************************************************************************
func:             MediaCtrlPutExternYUVToEnc
desc:             put extern yuv frame to enc, pvContext for releaseCB.
***************************************************************************** */
s32 MediaCtrlPutExternYUVToEnc(s32 nCapChn, s32 nEncChn, TMediaCtrlVidCapFrameInfo *ptFrameInfo, void *pvContext);

typedef s32 (*TMediaCtrlExternYUVReleaseCB) (s32 nCapChn,  s32 nEncChn, TMediaCtrlVidCapFrameInfo *ptFrameInfo, void *pvContext);
/* *****************************************************************************
func:             MediaCtrlSetExternYUVReleaseCallBack
desc:             set callback when encoder use yuv frame ok and can free
***************************************************************************** */
s32 MediaCtrlSetExternYUVReleaseCallBack(TMediaCtrlExternYUVReleaseCB tExternYuvReleaseCB);

/* *****************************************************************************
func:             MediaCtrlGetVinRawData
desc:             get raw data
***************************************************************************** */
s32 MediaCtrlGetVinRawData(TMediaCtrlVidCapFrameInfo *pstvideoframe);

/* *****************************************************************************
func:             MediaCtrlPutVinRawData
desc:             put raw data
***************************************************************************** */
s32 MediaCtrlPutVinRawData(TMediaCtrlVidCapFrameInfo *pstvideoframe);

/* *****************************************************************************
func:             MediaCtrlGetInstantCapFrame
desc:             get instant cap frame
***************************************************************************** */
s32 MediaCtrlGetInstantCapFrame(s32 nCapChn, TMediaCtrlVidCapFrameInfoEx *ptFrameInfoEx);

/* *****************************************************************************
func:             MediaCtrlPutInstantCapFrame
desc:             put instant cap frame
***************************************************************************** */
s32 MediaCtrlPutInstantCapFrame(s32 nCapChn, TMediaCtrlVidCapFrameInfoEx *ptFrameInfoEx);

/* *****************************************************************************
func:             MediaCtrlGetAVSFrm
desc:             get avs chn frame
***************************************************************************** */
s32 MediaCtrlGetAVSFrm(s32 nAVSChn, TMediaCtrlVidCapFrameInfo *ptVideoFrame);

/* *****************************************************************************
func:             MediaCtrlPutAVSFrm
desc:             release avs chn frame
***************************************************************************** */
s32 MediaCtrlPutAVSFrm(s32 nAVSChn, TMediaCtrlVidCapFrameInfo *ptVideoFrame);


/* *****************************************************************************
func:             MediaCtrlGetVidDecFrame
desc:             get the decoded frame
                  bGetLatest: 是否获取当前解码缓存帧中最新的buf，
                                 0 不是，此时按序获取buf，
                                 1  是， 获取当前最新的一个解码帧。
                                 buf队列满了时丢帧。
***************************************************************************** */
s32 MediaCtrlGetVidDecFrame(s32 nVidDecChn, BOOL32 bGetLatest, TMediaCtrlVidCapFrameInfo *ptDecFrame, s32 nMilliSec);

/* *****************************************************************************
func:             MediaCtrlReturnVidDecFrame
desc:             release the decoded frame
***************************************************************************** */
s32 MediaCtrlReturnVidDecFrame(s32 nVidDecChn, TMediaCtrlVidCapFrameInfo *ptDecFrame);

/* *****************************************************************************
func:             MediaCtrlVidDecSetParam
desc:             set the dec param
***************************************************************************** */
s32 MediaCtrlVidDecSetParam(s32 nVidDecChn, TMediaCtrlVidDecParam *ptVidDecParam);

/* *****************************************************************************
func:             MediaCtrlStitchFinetuneLut
desc:             fineture stitch lut according to Calibration Type
***************************************************************************** */
s32 MediaCtrlStitchFinetuneLut(EMediaCtrlStitchCalibrateType type);

/* *****************************************************************************
func:             MediaCtrlIntelliGetYuvFromJpeg
desc:             get the yuv from jpeg dec frame
***************************************************************************** */
s32 MediaCtrlIntelliGetYuvFromJpeg(TMediaCtrlVidEncFrameInfo *pstJpegParam, TMediaCtrlVidCapFrameInfo *ptFrameInfoEx);

/* *****************************************************************************
func:             MediaCtrlIntelliPutYuvFromJpeg
desc:             put the yuv from jpeg dec frame
***************************************************************************** */
s32 MediaCtrlIntelliPutYuvFromJpeg(TMediaCtrlVidCapFrameInfo *ptFrameInfoEx);

/* *****************************************************************************
func:             MediaCtrlImgAlgChange
desc:             change img alg change
***************************************************************************** */
s32 MediaCtrlImgAlgChange(PImgAlgHandle pAlgHandle, TMediaCtrlImgOpenInfo *ptOpenInfo);

/* *****************************************************************************
func:             MediaCtrlColorConvert
desc:             color convert
***************************************************************************** */
s32 MediaCtrlColorConvert(TMediaCtrlVidCapFrameInfo *ptSrcFrameInfo, TMediaCtrlVidCapFrameInfo *ptDstFrameInfo);

/* ***************************************************************************
func:        MediaCtrlSetVidSmartEncFrameCallBack
desc:        set vid enc frame to smart
**************************************************************************** */
typedef void (*TMediaCtrlVidSmartEncFrameCB)(s32 enc_chn, TMediaCtrlVidEncFrameInfo *ptEncFrameInfo, TMediaCtrlFrameExInfo *ptFrameExInfo);
u32 MediaCtrlSetVidSmartEncFrameCallBack(TMediaCtrlVidSmartEncFrameCB pfCallBack);

/* ***************************************************************************
func:        MediaCtrlSetFlashNoticeLogCallBack
desc:        set printf cb
**************************************************************************** */
typedef void (*TMediaCtrlDebugLogPrintfCB)(const char *szFmt, ...);
s32 MediaCtrlSetFlashNoticeLogCallBack(TMediaCtrlDebugLogPrintfCB pfCallBack);

/* ***************************************************************************
func:        MediaCtrlSetFlashErrLogCallBack
desc:        set printf cb
**************************************************************************** */
s32 MediaCtrlSetFlashErrLogCallBack(TMediaCtrlDebugLogPrintfCB pfCallBack);

/* ***************************************************************************
func:        MediaCtrlSetMemNoticeLogCallBack
desc:        set printf cb
**************************************************************************** */
s32 MediaCtrlSetMemNoticeLogCallBack(TMediaCtrlDebugLogPrintfCB pfCallBack);

/* ***************************************************************************
func:        MediaCtrlSendFrameToVout
desc:        set vid frame(yuv) to vout(bt1120/hdmi/sdi...)
**************************************************************************** */
s32 MediaCtrlSendFrameToVout(s32 nVoutChn, TMediaCtrlVidCapFrameInfoEx *ptFrameInfoEx);

/* ***************************************************************************
func:        MediaCtrlVidViLinkVout
desc:        link/unlink vi vout mod
param:       ture:link false:unlink
**************************************************************************** */
s32 MediaCtrlVidViLinkVout(s32 nCapChn, BOOL32 bLink);

#ifdef __cplusplus
}
#endif

#endif
