path="../../10-common/version/compileinfo/nvrsys_ubuntu_64.txt"
date>>$path

cd ./prj_linux

echo ==============================================
echo =      nvr_stitch_linux for ubuntu_64           =
echo ==============================================

echo "============compile libnvrstitch ubuntu_64============">>../$path

make -e DEBUG=0 -f makefile_ubuntu_64 clean
make -e DEBUG=0 -f makefile_ubuntu_64 2>>../$path

make -e FSANITIZE=1 -f makefile_ubuntu_64_sanitizer clean
make -e FSANITIZE=1 -f makefile_ubuntu_64_sanitizer 2>>../$path

cd ..
