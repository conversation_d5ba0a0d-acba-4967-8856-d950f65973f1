/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: nvrpui.proto */

/* Do not generate deprecated warnings for self */
#ifndef PROTOBUF_C__NO_DEPRECATED
#define PROTOBUF_C__NO_DEPRECATED
#endif

#include "nvrpui.pb-c.h"
void   tpb_pui_chn_info_param__init
                     (TPbPuiChnInfoParam         *message)
{
  static TPbPuiChnInfoParam init_value = TPB_PUI_CHN_INFO_PARAM__INIT;
  *message = init_value;
}
size_t tpb_pui_chn_info_param__get_packed_size
                     (const TPbPuiChnInfoParam *message)
{
  assert(message->base.descriptor == &tpb_pui_chn_info_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_pui_chn_info_param__pack
                     (const TPbPuiChnInfoParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_pui_chn_info_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_pui_chn_info_param__pack_to_buffer
                     (const TPbPuiChnInfoParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_pui_chn_info_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbPuiChnInfoParam *
       tpb_pui_chn_info_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbPuiChnInfoParam *)
     protobuf_c_message_unpack (&tpb_pui_chn_info_param__descriptor,
                                allocator, len, data);
}
void   tpb_pui_chn_info_param__free_unpacked
                     (TPbPuiChnInfoParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_pui_chn_info_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_pui_chn_dev_cfg__init
                     (TPbPuiChnDevCfg         *message)
{
  static TPbPuiChnDevCfg init_value = TPB_PUI_CHN_DEV_CFG__INIT;
  *message = init_value;
}
size_t tpb_pui_chn_dev_cfg__get_packed_size
                     (const TPbPuiChnDevCfg *message)
{
  assert(message->base.descriptor == &tpb_pui_chn_dev_cfg__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_pui_chn_dev_cfg__pack
                     (const TPbPuiChnDevCfg *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_pui_chn_dev_cfg__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_pui_chn_dev_cfg__pack_to_buffer
                     (const TPbPuiChnDevCfg *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_pui_chn_dev_cfg__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbPuiChnDevCfg *
       tpb_pui_chn_dev_cfg__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbPuiChnDevCfg *)
     protobuf_c_message_unpack (&tpb_pui_chn_dev_cfg__descriptor,
                                allocator, len, data);
}
void   tpb_pui_chn_dev_cfg__free_unpacked
                     (TPbPuiChnDevCfg *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_pui_chn_dev_cfg__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_pui_dev_param__init
                     (TPbNvrPuiDevParam         *message)
{
  static TPbNvrPuiDevParam init_value = TPB_NVR_PUI_DEV_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_pui_dev_param__get_packed_size
                     (const TPbNvrPuiDevParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_pui_dev_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_pui_dev_param__pack
                     (const TPbNvrPuiDevParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_pui_dev_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_pui_dev_param__pack_to_buffer
                     (const TPbNvrPuiDevParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_pui_dev_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrPuiDevParam *
       tpb_nvr_pui_dev_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrPuiDevParam *)
     protobuf_c_message_unpack (&tpb_nvr_pui_dev_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_pui_dev_param__free_unpacked
                     (TPbNvrPuiDevParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_pui_dev_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_app_clt_device_id__init
                     (TPbAppCltDeviceID         *message)
{
  static TPbAppCltDeviceID init_value = TPB_APP_CLT_DEVICE_ID__INIT;
  *message = init_value;
}
size_t tpb_app_clt_device_id__get_packed_size
                     (const TPbAppCltDeviceID *message)
{
  assert(message->base.descriptor == &tpb_app_clt_device_id__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_app_clt_device_id__pack
                     (const TPbAppCltDeviceID *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_app_clt_device_id__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_app_clt_device_id__pack_to_buffer
                     (const TPbAppCltDeviceID *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_app_clt_device_id__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbAppCltDeviceID *
       tpb_app_clt_device_id__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbAppCltDeviceID *)
     protobuf_c_message_unpack (&tpb_app_clt_device_id__descriptor,
                                allocator, len, data);
}
void   tpb_app_clt_device_id__free_unpacked
                     (TPbAppCltDeviceID *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_app_clt_device_id__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_pui_dev_cfg__init
                     (TPbNvrPuiDevCfg         *message)
{
  static TPbNvrPuiDevCfg init_value = TPB_NVR_PUI_DEV_CFG__INIT;
  *message = init_value;
}
size_t tpb_nvr_pui_dev_cfg__get_packed_size
                     (const TPbNvrPuiDevCfg *message)
{
  assert(message->base.descriptor == &tpb_nvr_pui_dev_cfg__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_pui_dev_cfg__pack
                     (const TPbNvrPuiDevCfg *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_pui_dev_cfg__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_pui_dev_cfg__pack_to_buffer
                     (const TPbNvrPuiDevCfg *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_pui_dev_cfg__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrPuiDevCfg *
       tpb_nvr_pui_dev_cfg__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrPuiDevCfg *)
     protobuf_c_message_unpack (&tpb_nvr_pui_dev_cfg__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_pui_dev_cfg__free_unpacked
                     (TPbNvrPuiDevCfg *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_pui_dev_cfg__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nv_pui_group_info__init
                     (TPbNvPuiGroupInfo         *message)
{
  static TPbNvPuiGroupInfo init_value = TPB_NV_PUI_GROUP_INFO__INIT;
  *message = init_value;
}
size_t tpb_nv_pui_group_info__get_packed_size
                     (const TPbNvPuiGroupInfo *message)
{
  assert(message->base.descriptor == &tpb_nv_pui_group_info__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nv_pui_group_info__pack
                     (const TPbNvPuiGroupInfo *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nv_pui_group_info__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nv_pui_group_info__pack_to_buffer
                     (const TPbNvPuiGroupInfo *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nv_pui_group_info__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvPuiGroupInfo *
       tpb_nv_pui_group_info__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvPuiGroupInfo *)
     protobuf_c_message_unpack (&tpb_nv_pui_group_info__descriptor,
                                allocator, len, data);
}
void   tpb_nv_pui_group_info__free_unpacked
                     (TPbNvPuiGroupInfo *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nv_pui_group_info__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_pui_time_info__init
                     (TPbPuiTimeInfo         *message)
{
  static TPbPuiTimeInfo init_value = TPB_PUI_TIME_INFO__INIT;
  *message = init_value;
}
size_t tpb_pui_time_info__get_packed_size
                     (const TPbPuiTimeInfo *message)
{
  assert(message->base.descriptor == &tpb_pui_time_info__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_pui_time_info__pack
                     (const TPbPuiTimeInfo *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_pui_time_info__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_pui_time_info__pack_to_buffer
                     (const TPbPuiTimeInfo *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_pui_time_info__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbPuiTimeInfo *
       tpb_pui_time_info__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbPuiTimeInfo *)
     protobuf_c_message_unpack (&tpb_pui_time_info__descriptor,
                                allocator, len, data);
}
void   tpb_pui_time_info__free_unpacked
                     (TPbPuiTimeInfo *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_pui_time_info__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_nvr_pui_time_param__init
                     (TPbNvrPuiTimeParam         *message)
{
  static TPbNvrPuiTimeParam init_value = TPB_NVR_PUI_TIME_PARAM__INIT;
  *message = init_value;
}
size_t tpb_nvr_pui_time_param__get_packed_size
                     (const TPbNvrPuiTimeParam *message)
{
  assert(message->base.descriptor == &tpb_nvr_pui_time_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_nvr_pui_time_param__pack
                     (const TPbNvrPuiTimeParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_nvr_pui_time_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_nvr_pui_time_param__pack_to_buffer
                     (const TPbNvrPuiTimeParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_nvr_pui_time_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbNvrPuiTimeParam *
       tpb_nvr_pui_time_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbNvrPuiTimeParam *)
     protobuf_c_message_unpack (&tpb_nvr_pui_time_param__descriptor,
                                allocator, len, data);
}
void   tpb_nvr_pui_time_param__free_unpacked
                     (TPbNvrPuiTimeParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_nvr_pui_time_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_pui_ptz_ctrl_param__init
                     (TPbPuiPtzCtrlParam         *message)
{
  static TPbPuiPtzCtrlParam init_value = TPB_PUI_PTZ_CTRL_PARAM__INIT;
  *message = init_value;
}
size_t tpb_pui_ptz_ctrl_param__get_packed_size
                     (const TPbPuiPtzCtrlParam *message)
{
  assert(message->base.descriptor == &tpb_pui_ptz_ctrl_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_pui_ptz_ctrl_param__pack
                     (const TPbPuiPtzCtrlParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_pui_ptz_ctrl_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_pui_ptz_ctrl_param__pack_to_buffer
                     (const TPbPuiPtzCtrlParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_pui_ptz_ctrl_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbPuiPtzCtrlParam *
       tpb_pui_ptz_ctrl_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbPuiPtzCtrlParam *)
     protobuf_c_message_unpack (&tpb_pui_ptz_ctrl_param__descriptor,
                                allocator, len, data);
}
void   tpb_pui_ptz_ctrl_param__free_unpacked
                     (TPbPuiPtzCtrlParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_pui_ptz_ctrl_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_pui_dev_list_manger__init
                     (TPbPuiDevListManger         *message)
{
  static TPbPuiDevListManger init_value = TPB_PUI_DEV_LIST_MANGER__INIT;
  *message = init_value;
}
size_t tpb_pui_dev_list_manger__get_packed_size
                     (const TPbPuiDevListManger *message)
{
  assert(message->base.descriptor == &tpb_pui_dev_list_manger__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_pui_dev_list_manger__pack
                     (const TPbPuiDevListManger *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_pui_dev_list_manger__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_pui_dev_list_manger__pack_to_buffer
                     (const TPbPuiDevListManger *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_pui_dev_list_manger__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbPuiDevListManger *
       tpb_pui_dev_list_manger__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbPuiDevListManger *)
     protobuf_c_message_unpack (&tpb_pui_dev_list_manger__descriptor,
                                allocator, len, data);
}
void   tpb_pui_dev_list_manger__free_unpacked
                     (TPbPuiDevListManger *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_pui_dev_list_manger__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_pui_dev_off_line_cfg__init
                     (TPbPuiDevOffLineCfg         *message)
{
  static TPbPuiDevOffLineCfg init_value = TPB_PUI_DEV_OFF_LINE_CFG__INIT;
  *message = init_value;
}
size_t tpb_pui_dev_off_line_cfg__get_packed_size
                     (const TPbPuiDevOffLineCfg *message)
{
  assert(message->base.descriptor == &tpb_pui_dev_off_line_cfg__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_pui_dev_off_line_cfg__pack
                     (const TPbPuiDevOffLineCfg *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_pui_dev_off_line_cfg__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_pui_dev_off_line_cfg__pack_to_buffer
                     (const TPbPuiDevOffLineCfg *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_pui_dev_off_line_cfg__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbPuiDevOffLineCfg *
       tpb_pui_dev_off_line_cfg__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbPuiDevOffLineCfg *)
     protobuf_c_message_unpack (&tpb_pui_dev_off_line_cfg__descriptor,
                                allocator, len, data);
}
void   tpb_pui_dev_off_line_cfg__free_unpacked
                     (TPbPuiDevOffLineCfg *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_pui_dev_off_line_cfg__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_pui_off_line_cfg_list__init
                     (TPbPuiOffLineCfgList         *message)
{
  static TPbPuiOffLineCfgList init_value = TPB_PUI_OFF_LINE_CFG_LIST__INIT;
  *message = init_value;
}
size_t tpb_pui_off_line_cfg_list__get_packed_size
                     (const TPbPuiOffLineCfgList *message)
{
  assert(message->base.descriptor == &tpb_pui_off_line_cfg_list__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_pui_off_line_cfg_list__pack
                     (const TPbPuiOffLineCfgList *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_pui_off_line_cfg_list__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_pui_off_line_cfg_list__pack_to_buffer
                     (const TPbPuiOffLineCfgList *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_pui_off_line_cfg_list__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbPuiOffLineCfgList *
       tpb_pui_off_line_cfg_list__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbPuiOffLineCfgList *)
     protobuf_c_message_unpack (&tpb_pui_off_line_cfg_list__descriptor,
                                allocator, len, data);
}
void   tpb_pui_off_line_cfg_list__free_unpacked
                     (TPbPuiOffLineCfgList *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_pui_off_line_cfg_list__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_pui_intell_dev_param__init
                     (TPbPuiIntellDevParam         *message)
{
  static TPbPuiIntellDevParam init_value = TPB_PUI_INTELL_DEV_PARAM__INIT;
  *message = init_value;
}
size_t tpb_pui_intell_dev_param__get_packed_size
                     (const TPbPuiIntellDevParam *message)
{
  assert(message->base.descriptor == &tpb_pui_intell_dev_param__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_pui_intell_dev_param__pack
                     (const TPbPuiIntellDevParam *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_pui_intell_dev_param__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_pui_intell_dev_param__pack_to_buffer
                     (const TPbPuiIntellDevParam *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_pui_intell_dev_param__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbPuiIntellDevParam *
       tpb_pui_intell_dev_param__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbPuiIntellDevParam *)
     protobuf_c_message_unpack (&tpb_pui_intell_dev_param__descriptor,
                                allocator, len, data);
}
void   tpb_pui_intell_dev_param__free_unpacked
                     (TPbPuiIntellDevParam *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_pui_intell_dev_param__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   tpb_pui_intell_dev_list__init
                     (TPbPuiIntellDevList         *message)
{
  static TPbPuiIntellDevList init_value = TPB_PUI_INTELL_DEV_LIST__INIT;
  *message = init_value;
}
size_t tpb_pui_intell_dev_list__get_packed_size
                     (const TPbPuiIntellDevList *message)
{
  assert(message->base.descriptor == &tpb_pui_intell_dev_list__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t tpb_pui_intell_dev_list__pack
                     (const TPbPuiIntellDevList *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &tpb_pui_intell_dev_list__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t tpb_pui_intell_dev_list__pack_to_buffer
                     (const TPbPuiIntellDevList *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &tpb_pui_intell_dev_list__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
TPbPuiIntellDevList *
       tpb_pui_intell_dev_list__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (TPbPuiIntellDevList *)
     protobuf_c_message_unpack (&tpb_pui_intell_dev_list__descriptor,
                                allocator, len, data);
}
void   tpb_pui_intell_dev_list__free_unpacked
                     (TPbPuiIntellDevList *message,
                      ProtobufCAllocator *allocator)
{
  assert(message->base.descriptor == &tpb_pui_intell_dev_list__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
static const ProtobufCFieldDescriptor tpb_pui_chn_info_param__field_descriptors[5] =
{
  {
    "chn_id",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbPuiChnInfoParam, has_chn_id),
    offsetof(TPbPuiChnInfoParam, chn_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "dev_id",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbPuiChnInfoParam, has_dev_id),
    offsetof(TPbPuiChnInfoParam, dev_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "dev_src_id",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbPuiChnInfoParam, has_dev_src_id),
    offsetof(TPbPuiChnInfoParam, dev_src_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "chn_name_len",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbPuiChnInfoParam, has_chn_name_len),
    offsetof(TPbPuiChnInfoParam, chn_name_len),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "chn_name",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BYTES,
    offsetof(TPbPuiChnInfoParam, has_chn_name),
    offsetof(TPbPuiChnInfoParam, chn_name),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_pui_chn_info_param__field_indices_by_name[] = {
  0,   /* field[0] = chn_id */
  4,   /* field[4] = chn_name */
  3,   /* field[3] = chn_name_len */
  1,   /* field[1] = dev_id */
  2,   /* field[2] = dev_src_id */
};
static const ProtobufCIntRange tpb_pui_chn_info_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 5 }
};
const ProtobufCMessageDescriptor tpb_pui_chn_info_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbPuiChnInfoParam",
  "TPbPuiChnInfoParam",
  "TPbPuiChnInfoParam",
  "",
  sizeof(TPbPuiChnInfoParam),
  5,
  tpb_pui_chn_info_param__field_descriptors,
  tpb_pui_chn_info_param__field_indices_by_name,
  1,  tpb_pui_chn_info_param__number_ranges,
  (ProtobufCMessageInit) tpb_pui_chn_info_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_pui_chn_dev_cfg__field_descriptors[2] =
{
  {
    "set_dev_osd_enable",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbPuiChnDevCfg, has_set_dev_osd_enable),
    offsetof(TPbPuiChnDevCfg, set_dev_osd_enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "set_dev_name_enable",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbPuiChnDevCfg, has_set_dev_name_enable),
    offsetof(TPbPuiChnDevCfg, set_dev_name_enable),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_pui_chn_dev_cfg__field_indices_by_name[] = {
  1,   /* field[1] = set_dev_name_enable */
  0,   /* field[0] = set_dev_osd_enable */
};
static const ProtobufCIntRange tpb_pui_chn_dev_cfg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_pui_chn_dev_cfg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbPuiChnDevCfg",
  "TPbPuiChnDevCfg",
  "TPbPuiChnDevCfg",
  "",
  sizeof(TPbPuiChnDevCfg),
  2,
  tpb_pui_chn_dev_cfg__field_descriptors,
  tpb_pui_chn_dev_cfg__field_indices_by_name,
  1,  tpb_pui_chn_dev_cfg__number_ranges,
  (ProtobufCMessageInit) tpb_pui_chn_dev_cfg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_pui_dev_param__field_descriptors[2] =
{
  {
    "dev_param",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrPuiDevParam, dev_param),
    &tpb_pui_chn_info_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "chn_dev_cfg",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrPuiDevParam, chn_dev_cfg),
    &tpb_pui_chn_dev_cfg__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_pui_dev_param__field_indices_by_name[] = {
  1,   /* field[1] = chn_dev_cfg */
  0,   /* field[0] = dev_param */
};
static const ProtobufCIntRange tpb_nvr_pui_dev_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_pui_dev_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrPuiDevParam",
  "TPbNvrPuiDevParam",
  "TPbNvrPuiDevParam",
  "",
  sizeof(TPbNvrPuiDevParam),
  2,
  tpb_nvr_pui_dev_param__field_descriptors,
  tpb_nvr_pui_dev_param__field_indices_by_name,
  1,  tpb_nvr_pui_dev_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_pui_dev_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_app_clt_device_id__field_descriptors[1] =
{
  {
    "dev_handle",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbAppCltDeviceID, has_dev_handle),
    offsetof(TPbAppCltDeviceID, dev_handle),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_app_clt_device_id__field_indices_by_name[] = {
  0,   /* field[0] = dev_handle */
};
static const ProtobufCIntRange tpb_app_clt_device_id__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor tpb_app_clt_device_id__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbAppCltDeviceID",
  "TPbAppCltDeviceID",
  "TPbAppCltDeviceID",
  "",
  sizeof(TPbAppCltDeviceID),
  1,
  tpb_app_clt_device_id__field_descriptors,
  tpb_app_clt_device_id__field_indices_by_name,
  1,  tpb_app_clt_device_id__number_ranges,
  (ProtobufCMessageInit) tpb_app_clt_device_id__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_pui_dev_cfg__field_descriptors[4] =
{
  {
    "dev_cfg_src_num",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvrPuiDevCfg, has_dev_cfg_src_num),
    offsetof(TPbNvrPuiDevCfg, dev_cfg_src_num),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "dev_cfg_dev_handle",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrPuiDevCfg, dev_cfg_dev_handle),
    &tpb_app_clt_device_id__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "dev_cfg_vid_src_chn_id",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BYTES,
    offsetof(TPbNvrPuiDevCfg, has_dev_cfg_vid_src_chn_id),
    offsetof(TPbNvrPuiDevCfg, dev_cfg_vid_src_chn_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "dev_cfg_xml",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbNvrPuiDevCfg, dev_cfg_xml),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_pui_dev_cfg__field_indices_by_name[] = {
  1,   /* field[1] = dev_cfg_dev_handle */
  0,   /* field[0] = dev_cfg_src_num */
  2,   /* field[2] = dev_cfg_vid_src_chn_id */
  3,   /* field[3] = dev_cfg_xml */
};
static const ProtobufCIntRange tpb_nvr_pui_dev_cfg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 4 }
};
const ProtobufCMessageDescriptor tpb_nvr_pui_dev_cfg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrPuiDevCfg",
  "TPbNvrPuiDevCfg",
  "TPbNvrPuiDevCfg",
  "",
  sizeof(TPbNvrPuiDevCfg),
  4,
  tpb_nvr_pui_dev_cfg__field_descriptors,
  tpb_nvr_pui_dev_cfg__field_indices_by_name,
  1,  tpb_nvr_pui_dev_cfg__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_pui_dev_cfg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nv_pui_group_info__field_descriptors[5] =
{
  {
    "group_id",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvPuiGroupInfo, has_group_id),
    offsetof(TPbNvPuiGroupInfo, group_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "chn_count",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvPuiGroupInfo, has_chn_count),
    offsetof(TPbNvPuiGroupInfo, chn_count),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "group_name_len",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbNvPuiGroupInfo, has_group_name_len),
    offsetof(TPbNvPuiGroupInfo, group_name_len),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "chn_list",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BYTES,
    offsetof(TPbNvPuiGroupInfo, has_chn_list),
    offsetof(TPbNvPuiGroupInfo, chn_list),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "group_name",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BYTES,
    offsetof(TPbNvPuiGroupInfo, has_group_name),
    offsetof(TPbNvPuiGroupInfo, group_name),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nv_pui_group_info__field_indices_by_name[] = {
  1,   /* field[1] = chn_count */
  3,   /* field[3] = chn_list */
  0,   /* field[0] = group_id */
  4,   /* field[4] = group_name */
  2,   /* field[2] = group_name_len */
};
static const ProtobufCIntRange tpb_nv_pui_group_info__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 5 }
};
const ProtobufCMessageDescriptor tpb_nv_pui_group_info__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvPuiGroupInfo",
  "TPbNvPuiGroupInfo",
  "TPbNvPuiGroupInfo",
  "",
  sizeof(TPbNvPuiGroupInfo),
  5,
  tpb_nv_pui_group_info__field_descriptors,
  tpb_nv_pui_group_info__field_indices_by_name,
  1,  tpb_nv_pui_group_info__number_ranges,
  (ProtobufCMessageInit) tpb_nv_pui_group_info__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_pui_time_info__field_descriptors[2] =
{
  {
    "sync_type",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_ENUM,
    offsetof(TPbPuiTimeInfo, has_sync_type),
    offsetof(TPbPuiTimeInfo, sync_type),
    &em_nvr_pui_sync_time_type__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "sync_time_zone",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbPuiTimeInfo, has_sync_time_zone),
    offsetof(TPbPuiTimeInfo, sync_time_zone),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_pui_time_info__field_indices_by_name[] = {
  1,   /* field[1] = sync_time_zone */
  0,   /* field[0] = sync_type */
};
static const ProtobufCIntRange tpb_pui_time_info__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_pui_time_info__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbPuiTimeInfo",
  "TPbPuiTimeInfo",
  "TPbPuiTimeInfo",
  "",
  sizeof(TPbPuiTimeInfo),
  2,
  tpb_pui_time_info__field_descriptors,
  tpb_pui_time_info__field_indices_by_name,
  1,  tpb_pui_time_info__number_ranges,
  (ProtobufCMessageInit) tpb_pui_time_info__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_nvr_pui_time_param__field_descriptors[2] =
{
  {
    "time_info",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbNvrPuiTimeParam, time_info),
    &tpb_pui_time_info__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "time_sync_support",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbNvrPuiTimeParam, has_time_sync_support),
    offsetof(TPbNvrPuiTimeParam, time_sync_support),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_nvr_pui_time_param__field_indices_by_name[] = {
  0,   /* field[0] = time_info */
  1,   /* field[1] = time_sync_support */
};
static const ProtobufCIntRange tpb_nvr_pui_time_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_nvr_pui_time_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbNvrPuiTimeParam",
  "TPbNvrPuiTimeParam",
  "TPbNvrPuiTimeParam",
  "",
  sizeof(TPbNvrPuiTimeParam),
  2,
  tpb_nvr_pui_time_param__field_descriptors,
  tpb_nvr_pui_time_param__field_indices_by_name,
  1,  tpb_nvr_pui_time_param__number_ranges,
  (ProtobufCMessageInit) tpb_nvr_pui_time_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_pui_ptz_ctrl_param__field_descriptors[2] =
{
  {
    "addr_num",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbPuiPtzCtrlParam, has_addr_num),
    offsetof(TPbPuiPtzCtrlParam, addr_num),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "proto_type",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_ENUM,
    offsetof(TPbPuiPtzCtrlParam, has_proto_type),
    offsetof(TPbPuiPtzCtrlParam, proto_type),
    &em_nvr_pui_ptz_proto_type__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_pui_ptz_ctrl_param__field_indices_by_name[] = {
  0,   /* field[0] = addr_num */
  1,   /* field[1] = proto_type */
};
static const ProtobufCIntRange tpb_pui_ptz_ctrl_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor tpb_pui_ptz_ctrl_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbPuiPtzCtrlParam",
  "TPbPuiPtzCtrlParam",
  "TPbPuiPtzCtrlParam",
  "",
  sizeof(TPbPuiPtzCtrlParam),
  2,
  tpb_pui_ptz_ctrl_param__field_descriptors,
  tpb_pui_ptz_ctrl_param__field_indices_by_name,
  1,  tpb_pui_ptz_ctrl_param__number_ranges,
  (ProtobufCMessageInit) tpb_pui_ptz_ctrl_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_pui_dev_list_manger__field_descriptors[5] =
{
  {
    "dev_list",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbPuiDevListManger, n_dev_list),
    offsetof(TPbPuiDevListManger, dev_list),
    &tpb_nvr_pui_dev_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "dev_cfg",
    2,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbPuiDevListManger, n_dev_cfg),
    offsetof(TPbPuiDevListManger, dev_cfg),
    &tpb_nvr_pui_dev_cfg__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "dev_group_list",
    3,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbPuiDevListManger, n_dev_group_list),
    offsetof(TPbPuiDevListManger, dev_group_list),
    &tpb_nv_pui_group_info__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "time_param",
    4,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbPuiDevListManger, n_time_param),
    offsetof(TPbPuiDevListManger, time_param),
    &tpb_nvr_pui_time_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ptz_ctrl_param",
    5,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbPuiDevListManger, n_ptz_ctrl_param),
    offsetof(TPbPuiDevListManger, ptz_ctrl_param),
    &tpb_pui_ptz_ctrl_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_pui_dev_list_manger__field_indices_by_name[] = {
  1,   /* field[1] = dev_cfg */
  2,   /* field[2] = dev_group_list */
  0,   /* field[0] = dev_list */
  4,   /* field[4] = ptz_ctrl_param */
  3,   /* field[3] = time_param */
};
static const ProtobufCIntRange tpb_pui_dev_list_manger__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 5 }
};
const ProtobufCMessageDescriptor tpb_pui_dev_list_manger__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbPuiDevListManger",
  "TPbPuiDevListManger",
  "TPbPuiDevListManger",
  "",
  sizeof(TPbPuiDevListManger),
  5,
  tpb_pui_dev_list_manger__field_descriptors,
  tpb_pui_dev_list_manger__field_indices_by_name,
  1,  tpb_pui_dev_list_manger__number_ranges,
  (ProtobufCMessageInit) tpb_pui_dev_list_manger__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_pui_dev_off_line_cfg__field_descriptors[5] =
{
  {
    "alias",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BYTES,
    offsetof(TPbPuiDevOffLineCfg, has_alias),
    offsetof(TPbPuiDevOffLineCfg, alias),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "alias_len",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbPuiDevOffLineCfg, has_alias_len),
    offsetof(TPbPuiDevOffLineCfg, alias_len),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "set_dev_osd",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbPuiDevOffLineCfg, has_set_dev_osd),
    offsetof(TPbPuiDevOffLineCfg, set_dev_osd),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "set_dev_name",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbPuiDevOffLineCfg, has_set_dev_name),
    offsetof(TPbPuiDevOffLineCfg, set_dev_name),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "need_set",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbPuiDevOffLineCfg, has_need_set),
    offsetof(TPbPuiDevOffLineCfg, need_set),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_pui_dev_off_line_cfg__field_indices_by_name[] = {
  0,   /* field[0] = alias */
  1,   /* field[1] = alias_len */
  4,   /* field[4] = need_set */
  3,   /* field[3] = set_dev_name */
  2,   /* field[2] = set_dev_osd */
};
static const ProtobufCIntRange tpb_pui_dev_off_line_cfg__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 5 }
};
const ProtobufCMessageDescriptor tpb_pui_dev_off_line_cfg__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbPuiDevOffLineCfg",
  "TPbPuiDevOffLineCfg",
  "TPbPuiDevOffLineCfg",
  "",
  sizeof(TPbPuiDevOffLineCfg),
  5,
  tpb_pui_dev_off_line_cfg__field_descriptors,
  tpb_pui_dev_off_line_cfg__field_indices_by_name,
  1,  tpb_pui_dev_off_line_cfg__number_ranges,
  (ProtobufCMessageInit) tpb_pui_dev_off_line_cfg__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_pui_off_line_cfg_list__field_descriptors[1] =
{
  {
    "dev_offline_cfg_list",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbPuiOffLineCfgList, n_dev_offline_cfg_list),
    offsetof(TPbPuiOffLineCfgList, dev_offline_cfg_list),
    &tpb_pui_dev_off_line_cfg__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_pui_off_line_cfg_list__field_indices_by_name[] = {
  0,   /* field[0] = dev_offline_cfg_list */
};
static const ProtobufCIntRange tpb_pui_off_line_cfg_list__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor tpb_pui_off_line_cfg_list__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbPuiOffLineCfgList",
  "TPbPuiOffLineCfgList",
  "TPbPuiOffLineCfgList",
  "",
  sizeof(TPbPuiOffLineCfgList),
  1,
  tpb_pui_off_line_cfg_list__field_descriptors,
  tpb_pui_off_line_cfg_list__field_indices_by_name,
  1,  tpb_pui_off_line_cfg_list__number_ranges,
  (ProtobufCMessageInit) tpb_pui_off_line_cfg_list__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_pui_intell_dev_param__field_descriptors[5] =
{
  {
    "normal_add",
    1,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_BOOL,
    offsetof(TPbPuiIntellDevParam, has_normal_add),
    offsetof(TPbPuiIntellDevParam, normal_add),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "chn_info",
    2,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(TPbPuiIntellDevParam, chn_info),
    &tpb_pui_chn_info_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ip_type",
    3,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(TPbPuiIntellDevParam, has_ip_type),
    offsetof(TPbPuiIntellDevParam, ip_type),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "chn_ip",
    4,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbPuiIntellDevParam, chn_ip),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "device_id",
    5,
    PROTOBUF_C_LABEL_OPTIONAL,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(TPbPuiIntellDevParam, device_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_pui_intell_dev_param__field_indices_by_name[] = {
  1,   /* field[1] = chn_info */
  3,   /* field[3] = chn_ip */
  4,   /* field[4] = device_id */
  2,   /* field[2] = ip_type */
  0,   /* field[0] = normal_add */
};
static const ProtobufCIntRange tpb_pui_intell_dev_param__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 5 }
};
const ProtobufCMessageDescriptor tpb_pui_intell_dev_param__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbPuiIntellDevParam",
  "TPbPuiIntellDevParam",
  "TPbPuiIntellDevParam",
  "",
  sizeof(TPbPuiIntellDevParam),
  5,
  tpb_pui_intell_dev_param__field_descriptors,
  tpb_pui_intell_dev_param__field_indices_by_name,
  1,  tpb_pui_intell_dev_param__number_ranges,
  (ProtobufCMessageInit) tpb_pui_intell_dev_param__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor tpb_pui_intell_dev_list__field_descriptors[1] =
{
  {
    "dev_list",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(TPbPuiIntellDevList, n_dev_list),
    offsetof(TPbPuiIntellDevList, dev_list),
    &tpb_pui_intell_dev_param__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned tpb_pui_intell_dev_list__field_indices_by_name[] = {
  0,   /* field[0] = dev_list */
};
static const ProtobufCIntRange tpb_pui_intell_dev_list__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor tpb_pui_intell_dev_list__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "TPbPuiIntellDevList",
  "TPbPuiIntellDevList",
  "TPbPuiIntellDevList",
  "",
  sizeof(TPbPuiIntellDevList),
  1,
  tpb_pui_intell_dev_list__field_descriptors,
  tpb_pui_intell_dev_list__field_indices_by_name,
  1,  tpb_pui_intell_dev_list__number_ranges,
  (ProtobufCMessageInit) tpb_pui_intell_dev_list__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCEnumValue em_nvr_pui_sync_time_type__enum_values_by_number[4] =
{
  { "SYNC_TIME_CLOSE", "EM_NVR_PUI_SYNC_TIME_TYPE__SYNC_TIME_CLOSE", 0 },
  { "SYNC_TIME_AND_ZONE", "EM_NVR_PUI_SYNC_TIME_TYPE__SYNC_TIME_AND_ZONE", 1 },
  { "SYNC_TIME_LOCALTIME", "EM_NVR_PUI_SYNC_TIME_TYPE__SYNC_TIME_LOCALTIME", 2 },
  { "SYNC_TIME_UTCTIME", "EM_NVR_PUI_SYNC_TIME_TYPE__SYNC_TIME_UTCTIME", 3 },
};
static const ProtobufCIntRange em_nvr_pui_sync_time_type__value_ranges[] = {
{0, 0},{0, 4}
};
static const ProtobufCEnumValueIndex em_nvr_pui_sync_time_type__enum_values_by_name[4] =
{
  { "SYNC_TIME_AND_ZONE", 1 },
  { "SYNC_TIME_CLOSE", 0 },
  { "SYNC_TIME_LOCALTIME", 2 },
  { "SYNC_TIME_UTCTIME", 3 },
};
const ProtobufCEnumDescriptor em_nvr_pui_sync_time_type__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "EmNvrPuiSyncTimeType",
  "EmNvrPuiSyncTimeType",
  "EmNvrPuiSyncTimeType",
  "",
  4,
  em_nvr_pui_sync_time_type__enum_values_by_number,
  4,
  em_nvr_pui_sync_time_type__enum_values_by_name,
  1,
  em_nvr_pui_sync_time_type__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
static const ProtobufCEnumValue em_nvr_pui_ptz_proto_type__enum_values_by_number[1] =
{
  { "PTZPROTO_TYPE_PELCO_D_KEDA", "EM_NVR_PUI_PTZ_PROTO_TYPE__PTZPROTO_TYPE_PELCO_D_KEDA", 1 },
};
static const ProtobufCIntRange em_nvr_pui_ptz_proto_type__value_ranges[] = {
{1, 0},{0, 1}
};
static const ProtobufCEnumValueIndex em_nvr_pui_ptz_proto_type__enum_values_by_name[1] =
{
  { "PTZPROTO_TYPE_PELCO_D_KEDA", 0 },
};
const ProtobufCEnumDescriptor em_nvr_pui_ptz_proto_type__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "EmNvrPuiPtzProtoType",
  "EmNvrPuiPtzProtoType",
  "EmNvrPuiPtzProtoType",
  "",
  1,
  em_nvr_pui_ptz_proto_type__enum_values_by_number,
  1,
  em_nvr_pui_ptz_proto_type__enum_values_by_name,
  1,
  em_nvr_pui_ptz_proto_type__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
