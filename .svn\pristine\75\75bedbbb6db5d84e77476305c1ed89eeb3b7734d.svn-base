#ifndef _PCLINT_COMMON_H_
#define _PCLINT_COMMON_H_

#ifndef MEMCPY_CAST
    #define MEMCPY_CAST(dst, src, len) memcpy(static_cast<void*>(dst), static_cast<const void*>(src), len)
#endif

#ifndef MEMSET_CAST
   #define MEMSET_CAST(dst, val, len)  memset(static_cast<void*>(dst), val, len)
#endif

#ifndef STRCMP_CAST
    #define STRCMP_CAST(val1, val2)  strcmp(static_cast<const char*>(val1), static_cast<const char*>(val2))
#endif

#ifndef UNUSED
	#define UNUSED(x) x
	//#define UNUSED(expr) do { } while (0)
#endif

#endif