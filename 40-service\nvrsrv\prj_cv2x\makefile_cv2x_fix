

TOP := ..

COMM_DIR := ../../common/

SRC_DIR := $(TOP)/source
CURDIR := ./

## Name and type of the target for this Makefile

APP_TARGET	      := nvrsrv


## Define debugging symbols
DEBUG = 0
DO_UPX = 0
LINUX_COMPILER = _CV2X_
PWLIB_SUPPORT = 0
CFLAGS += -funwind-tables
#CFLAGS += -D__MRTC__
CFLAGS += -D_CV2X_
## Object files that compose the target(s)



OBJS := $(SRC_DIR)/nvrsrv\
## Libraries to include in shared object file
LIB_PATH += $(TOP)/../../10-common/lib/release/cv2x
LIB_PATH += $(TOP)/../../10-common/lib/release/cv2x/capfix
LIB_PATH += $(TOP)/../../10-common/lib/release/cv2x/audlib
LIB_PATH += $(TOP)/../../10-common/lib/release/cv2x/wifim
LIB_PATH += $(TOP)/../../10-common/lib/release/cv2x/kdssl-ext
LIB_PATH += $(TOP)/../../10-common/lib/release/cv2x/nvrgnss
LIB_PATH += $(TOP)/../../10-common/lib/release/cv2x/apm
LIBS +=	nvrcfg nvrlog nvrcap nvrftp nvrcustcap nvrusrmgr nvredgeosunitif nvrnetwork nvrsys nvrpcap nvrproto protobufc sqlite3wrapper malloc debuglog netcbb mbnet ddnsc upnpc drv pthread nvrgeo \
	sysdbg goaheadhelper appbase charconv appclt nvrpui nvrvtductrl nvrmpu nvrprobe httpclient mxml ghttp go rtspclient kdmposa mca osp netpacket kdmtsps mediaswitch stdc++ nvrqueue ispctrl mediactrl nvralarm nvrdev nvrupgrade nvrcrc ftpc\
	dmsrv nvrrec rpdata rp kdmfileinterface nvrguard nvrsmtp smtp dl ais algctrl airp gnss wifi kdvsys SDEF pcap pubsecstack curl nvrcoi msc btctrl  apmsdk nvrunifiedlog\
    mbm md5lib_armamba_linux\
	kdmfileinterface\
    basicintelligent_amba\
    aaclcdec_armamba_cv2x_linux aaclcenc_armamba_cv2x_linux aaclddec_armamba_cv2x_linux aacldenc_armamba_cv2x_linux \
	aec_mulresample_armamba_cv2x_linux audcodec_armamba_cv2x_linux audproc_armamba_cv2x_linux \
	g711_armamba_cv2x_linux g722_armamba_cv2x_linux g726_armamba_cv2x_linux g7221c_armamba_cv2x_linux stdg722_armamba_cv2x_linux\
	extexp_armamba_cv2x_linux videomanage_armamba_cv2x_linux resample_armamba_cv2x_linux \
	adpcm_armamba_cv2x_linux aaclcdec_armamba_cv2x_linux aaclcenc_armamba_cv2x_linux \
	spe_armamba_cv2x_linux asd_armamba_cv2x_linux mixer_armamba_cv2x_linux g728_armamba_cv2x_linux g729_armamba_cv2x_linux \
	g719_armamba_cv2x_linux mp3dec_armamba_cv2x_linux mp3enc_armamba_cv2x_linux mp2_armamba_cv2x_linux \
	aaclddec_armamba_cv2x_linux aacldenc_armamba_cv2x_linux opus_armamba_cv2x_linux amr_nb_armamba_cv2x_linux \
    asound\
    img_aaa_flow_v5\
    lua-5.3\
    utils\
    eis\
    dewarp\
    encoder\
	m\
	kdmssl\
	kdmcrypto\
	cjson\
    udm\
	uuid\
	blkid\
	nvrproduct\
	kdmnatagent\
	rpdownload\
	nvrdynamicplugin\
	nvrstitch\
	lwshelper\
	websockets\
	skf_szgx_3310uk\
	skf_szgx_3310st\
	kdssl-ext\
	smartcodec_amba_linux\
	nvrmd5\
    #heapcheck\
    #tcmalloc
ifneq ($(SLIBS),) 
LDFLAGS += -Wl,-static
LDFLAGS += $(foreach lib,$(SLIBS),-l$(lib))
endif
## Add driver-specific include directory to the search path

INC_PATH += $(CURDIR)/../include \
		$(CURDIR)/../../common \
		$(CURDIR)/../../nvrcfg/include \
		$(CURDIR)/../../nvrpui/include \
		$(CURDIR)/../../nvrvtductrl/include \
		$(CURDIR)/../../nvrusrmgr/include \
		$(CURDIR)/../../nvrcap/include \
		$(CURDIR)/../../nvrnetwork/include \
		$(CURDIR)/../../nvrsys/include \
		$(CURDIR)/../../nvrdev/include \
		$(CURDIR)/../../nvralarm/include \
		$(CURDIR)/../../nvrlog/include \
		$(CURDIR)/../../nvrguard/include \
		$(CURDIR)/../../nvrsmtp/include \
		$(CURDIR)/../../nvrmpu/include \
		$(CURDIR)/../../nvrpcap/include \
		$(CURDIR)/../../airp/include \
		$(CURDIR)/../../ais/include \
		$(CURDIR)/../../nvrextdevmgr/include \
		$(CURDIR)/../../../10-common/include/service \
		$(CURDIR)/../../../10-common/include/tts \
		$(CURDIR)/../../../10-common/include/cbb/debuglog\
		$(CURDIR)/../../../10-common/include/cbb/mbnet\
		$(CURDIR)/../../../10-common/include/system\
		$(CURDIR)/../../../10-common/include/hal/sysdbg\
		$(CURDIR)/../../../10-common/include/cbb/protobuf \
		$(CURDIR)/../../../10-common/include/cbb/osp \
		$(CURDIR)/../../../10-common/include/cbb/sqilte \
		$(CURDIR)/../../../10-common/include/cbb/appclt\
		$(CURDIR)/../../../10-common/include/cbb/mediaswitch\
		$(CURDIR)/../../../10-common/include/cbb/kshield\
		$(CURDIR)/../../../10-common/include/cbb/kdssl-ext\
		$(CURDIR)/../../../10-common/include/cbb/kdvsys\
		$(CURDIR)/../../../10-common/include/app\
		$(CURDIR)/../../../10-common/include/apm\
		$(CURDIR)/../../../10-common/include/hal/drvlib_64\
		$(CURDIR)/../../../10-common/include/hal/drvlib_64/system
		
#CFLAGS += -D_TCMALLOC_
#CFLAGS += -fno-builtin-malloc -fno-builtin-calloc -fno-builtin-realloc -fno-builtin-free

ifeq ($(PWLIB_SUPPORT),1)
   INC_PATH += $(PWLIBDIR)/include/ptlib/unix $(PWLIBDIR)/include
endif


INSTALL_APP_PATH := ../../../10-common/version/release/cv2x/bin_fixipc
include $(COMM_DIR)/common.mk


