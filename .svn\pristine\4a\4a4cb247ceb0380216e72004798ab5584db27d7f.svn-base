#ifndef AVTRM_H
#define AVTRM_H

struct NormBlockArray {
	int x1, y1, x2, y2;
	int recchar;
	int NNscore;
	int orgcolor;
	int bthresh;
	int	IsChinChar;
	unsigned int	ColorFlag;
	unsigned char   binblk[16][16];
	unsigned char	FGcolor[4], BGcolor[4];
};

void AVT_Init_RM(const char *model_absolute_path);

void AVT_Rec_Image(unsigned char *buf, int width, int height, int row_sample_method, struct NormBlockArray *nbar, int *LPNcount, int *n);

#endif
