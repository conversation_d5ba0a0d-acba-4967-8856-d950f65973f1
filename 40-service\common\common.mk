###
### Copyright (c) 2004 Keda Telecom, Inc.
###

#########################################################################
###
###  DESCRIPTION:
###    Common definitions for all Makefiles.
###
###  MAKEFILE OPTIONS:
###    These are options that should be specified in the Makefile.
###    At a minimum, one or more of ARC_TARGET, SO_TARGET,
###    APP_TARGET, and/or DIRS must be defined.
###
###    ARC_TARGET - target name for archive file, excluding file
###      extension.
###    SO_TARGET - target name for shared object file, excluding file
###      extension.
###    APP_TARGET - target name of application.
###    DIRS - list of subdirectories to execute "make" in.
###    OBJS - list of object files (without .o extension) that compose
###      the target.
###    LIBS - optional list of libraries that the target uses--
###      don't include the suffix (.a). Libraries that other libraries
###      depend on (e.g. romcon) should be put later in the list.
###    INSTALL_INC - list of header files to install. (Libs will always
###      be installed.)
###    INSTALL_INC_LOC - Use "os" to specify that headers should be
###      installed into the os-specific directory. Otherwise the
###      headers will be installed to "common" (default=common).
###    INSTALL_LIB_LOC - Use "os" to specify that libraries should be
###      installed into the os-specific directory. Otherwise the
###      libraries will be installed to "common" (default=common).
###
###  TOOLKIT ENVIRONMENT VARIABLES:
###    These environment variables should be set before compiling.
###
###    ETI_TOOLKIT - must point to the base directory of the
###      Equator toolkit.
###    ETI_TOOLKIT_INSTALL - must point to a location to put
###      the built driver binaries.
###    ETI_TOOLKIT_LOCAL - optional variable that can point to a
###      location to pick up headers and libraries before the
###      Equator toolkit. It should point to the same location
###      as ETI_TOOLKIT_INSTALL.
###
###  OPTIONAL ENVIRONMENT VARIABLES:
###    These are options that are normally set in environment varibles.
###    They can also be set in the Makefile or on the make command line
###    (e.g. "make DEBUG=1"). Settings in the Makefile takes precendence
###    over the command line, which takes precendence over environment
###    variables. Unless otherwise stated, options should be set to 1
###    or 0 or left unset to use the default. Settings on the make
###    command line will propagate down to subdirectories when building
###    a tree.
###
###    DEBUG - include symbols and define "DEBUG" symbol during
###      compile (default=0).
###    INC_PATH - Additional directories to be searched for headers,
###      separated by spaces. Default is to use the compiler's default
###      path, adding RTOS_INCLUDES for VxWorks builds.
###    LIB_PATH - Additional directories to be searched for libraries,
###      separated by spaces. Default is to use the linker's default
###      path.
###    CFLAGS - Additional compile options.
###    LDFLAGS - Additional link options.
###
#########################################################################

## Add appropriate suffixes and extensions


ifneq ($(ARC_TARGET),)
  ARC_TARGET := lib$(ARC_TARGET)$(LIB_SUFFIX).a
endif

ifneq ($(SO_TARGET),)
  SO_TARGET := lib$(SO_TARGET)$(LIB_SUFFIX).so
endif

## Special variables to help with clean targets

DIRSC := $(foreach dir,$(DIRS),$(dir)(clean))
ASMS := $(foreach obj,$(OBJS),$(basename $(obj)).s)

## Put the extension on all objs

OBJS := $(foreach obj,$(OBJS),$(obj).o)


## Turn on debug flag and define DEBUG symbol for debug builds


ifeq ($(DEBUG),1)
  CFLAGS += -g
  ifeq ($(LINUX_COMPILER),_EQUATOR_)
    CFLAGS += -O2
  else
    CFLAGS += -O0
  endif
  CFLAGS += -DDEBUG=$(DEBUG)
endif

ifeq ($(DEBUG),0)
  CFLAGS += -O2
  CFLAGS += -DNDEBUG
endif

CFLAGS += -Wall -Wshadow -Wpointer-arith 

ifneq ($(SO_TARGET),)
  CFLAGS += -fpic
endif

ifeq ($(LINUX_COMPILER),_EQUATOR_)
   CFLAGS += -D_EQUATOR_
endif

ifeq ($(LINUX_COMPILER),_ARM_)   
   CFLAGS += -DARCH=ARM
   CFLAGS += -D_ARM_
endif


ifeq ($(LINUX_COMPILER),_HIS3536_)  
   CFLAGS += -DARCH=ARM
   CFLAGS += -D_ARM_
   CFLAGS += -D_HIS3536_
endif

ifeq ($(LINUX_COMPILER),_AX603A_)   
   CFLAGS += -DARCH=ARM
   CFLAGS += -D_ARM_
   CFLAGS += -D_AX603A_
endif

ifeq ($(LINUX_COMPILER),_RK3568_)   
   CFLAGS += -DARCH=ARM
   CFLAGS += -D_ARM_
   CFLAGS += -D_RK3568_
endif

ifeq ($(PWLIB_SUPPORT),1)
   CFLAGS += -DPWLIB_SUPPORT -DPTRACING=1 -D_REENTRANT -DPHAS_TEMPLATES -DPMEMORY_CHECK=1 -DPASN_LEANANDMEAN -pipe -fPIC
endif

CFLAGS += -D_LINUX_ -fno-omit-frame-pointer -rdynamic 

#gcc sanitizer test with -g
ifdef FSANITIZE
ifeq ($(FSANITIZE), 1)
   CFLAGS += -fsanitize=address -g
endif
endif
## Add include path and constant definitions to
## compile options

CFLAGS += $(foreach dir,$(INC_PATH),-I$(dir))


## Add library path and libraries to link options
ifeq ($(filter _QCOM_ _HIS3559A_,$(LINUX_COMPILER)),)
LDFLAGS += -Wl,-call_shared
endif
LDFLAGS += $(foreach lib,$(LIB_PATH),-L$(lib))


ifneq ($(SO_TARGET),)
  LDFLAGS += -shared
endif

## When using a shared object library and not building
## the shared object library itself, don't link with the
## libraries. Don't know how to do "or" in make, so use
## an intermediate variable.

LDFLAGS += $(foreach lib,$(LIBS),-l$(lib)$(LIB_SUFFIX))

## Set up library install location
ifndef INSTALL_LIB_PATH
  ifeq ($(INSTALL_LIB_LOC),os)
    INSTALL_LIB_PATH = $(ETI_TOOLKIT_INSTALL)/$(RTOS_DIR)/$(MAP_ARCH)_lib
  else
    INSTALL_LIB_PATH = $(ETI_TOOLKIT_INSTALL)/common/$(MAP_ARCH)_lib
  endif
endif


## Set up application install location
ifndef INSTALL_APP_PATH
  APP_DIR ?= unknown
  INSTALL_APP_PATH = $(ETI_TOOLKIT_INSTALL)/app/$(APP_DIR)
endif


## Set up the tools to use
STRIP   = ls
UPX = ls

ifeq ($(LINUX_COMPILER),_ARM_)
CROSS = arm-linux-
endif


ifeq ($(LINUX_COMPILER),_HIS3536_)
CROSS = /opt/arm-hisiv300-linux/bin/arm-hisiv300-linux-uclibcgnueabi-
STRIP      = $(CROSS)strip
UPX = ../../../10-common/version/tools/upx
endif

ifeq ($(LINUX_COMPILER),_HIS3536C_)
CROSS = /opt/hisi-linux/x86-arm/arm-hisiv500-linux/bin/arm-hisiv500-linux-uclibcgnueabi-
STRIP      = $(CROSS)strip
UPX = ../../../10-common/version/tools/upx
endif

ifeq ($(LINUX_COMPILER),_HIS3516AV200_)
CROSS = /opt/hisi-linux/x86-arm/arm-hisiv500-linux/bin/arm-hisiv500-linux-uclibcgnueabi-
STRIP      = $(CROSS)strip
#UPX = ../../../10-common/version/tools/upx
endif

ifeq ($(LINUX_COMPILER),_HIS3516DV300_)
CROSS = /opt/hisi-linux/x86-arm/arm-himix200-linux/bin/arm-himix200-linux-
STRIP      = $(CROSS)strip
#UPX = ../../../10-common/version/tools/upx
endif

ifeq ($(LINUX_COMPILER),_NETRA81XX_)
CROSS = /opt/ti/ti81xx/ipncrdk-3.8/linux_devkit/bin/arm-arago-linux-gnueabi-
STRIP      = $(CROSS)strip
UPX = ../../../10-common/version/tools/upx
endif

ifeq ($(LINUX_COMPILER),_HIS3559A_)
CROSS = /opt/hisi-linux/x86-arm/aarch64-himix100-linux/bin/aarch64-himix100-linux-
STRIP      = $(CROSS)strip
UPX = ../../../10-common/version/tools/upx
endif

ifeq ($(LINUX_COMPILER),_QCOM_)
CROSS = /opt/arm-linux-androideabi-4.8/bin/arm-linux-androideabi-
STRIP      = $(CROSS)strip
UPX = ../../../10-common/version/tools/upx
endif

ifeq ($(LINUX_COMPILER),_HIS3531D_)
CROSS = /opt/hisi-linux/x86-arm/arm-hisiv500-linux/bin/arm-hisiv500-linux-uclibcgnueabi-
STRIP      = $(CROSS)strip
UPX = ../../../10-common/version/tools/upx
endif

ifeq ($(LINUX_COMPILER),_HIS3519AV100_)
CROSS = /opt/hisi-linux/x86-arm/arm-himix200-linux/bin/arm-himix200-linux-
STRIP      = $(CROSS)strip
UPX = ../../../10-common/version/tools/upx
endif
ifeq ($(LINUX_COMPILER),_HIS3531DV200_)
CROSS = /opt/hisi-linux/x86-arm/aarch64-himix200-linux/bin/aarch64-himix200-linux-
STRIP      = $(CROSS)strip
UPX = ../../../10-common/version/tools/upx
endif

ifeq ($(LINUX_COMPILER),_CV2X_)
CROSS = /opt/ambarella/linaro-aarch64-2020.09-gcc10.2-linux5.4/bin/aarch64-linux-gnu-
STRIP      = $(CROSS)strip
UPX = ../../../10-common/version/tools/upx
endif

ifeq ($(LINUX_COMPILER),_MLU220_)
CROSS = /opt/cambricon/gcc-linaro-6.2.1-2016.11-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-
STRIP      = $(CROSS)strip
#UPX = ../../../10-common/version/tools/upx
endif

ifeq ($(LINUX_COMPILER),_SSR621Q_)
CROSS = /opt/sigmastar/gcc-arm-8.2-2018.08-x86_64-arm-linux-gnueabihf/bin/arm-linux-gnueabihf-
STRIP      = $(CROSS)strip
UPX = ../../../10-common/version/tools/upx
endif

ifeq ($(LINUX_COMPILER), _SSC339G_)
CROSS = /opt/sigmastar/gcc-sigmastar-9.1.0-2019.11-x86_64_arm-linux-gnueabihf/bin/arm-linux-gnueabihf-9.1.0-
STRIP      = $(CROSS)strip
UPX = ../../../10-common/version/tools/upx
endif

ifeq ($(LINUX_COMPILER),_AX603A_)
CROSS = /opt/axera-linux/x86-arm/gcc-linaro-7.5.0-2019.12-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-
STRIP      = $(CROSS)strip
UPX = ../../../10-common/version/tools/upx
endif

ifeq ($(LINUX_COMPILER),_RK3568_)
CROSS = /opt/rockchip/aarch64/gcc-buildroot-9.3.0-2020.03-x86_64_aarch64-rockchip-linux-gnu/bin/aarch64-linux-
STRIP      = $(CROSS)strip
UPX = ../../../10-common/version/tools/upx
endif

ifeq ($(LINUX_COMPILER),_CE3226_)
CROSS = /opt/cambricon/gcc-linaro-6.2.1-2016.11-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-
STRIP      = $(CROSS)strip
UPX = ../../../10-common/version/tools/upx
endif

CC      = $(CROSS)gcc
CPP     = $(CROSS)g++
LD      = $(CROSS)gcc
AR      = $(CROSS)ar
INSTALL = install -D -m 644
OBJDUMP = objdump
RM      = -@rm -f

ifeq ($(LINUX_COMPILER),_EQUATOR_)
CC      = e++
CPP     = e++
LD      = e++
endif

##----------------------------------------
## Do upx or not
ifdef DO_UPX
ifeq ($(DO_UPX), 0)
UPX = ls
endif
endif
##------------------------------------------------------------------------
## Rules

## Suffix rules

$(SRC_DIR)/%.o: $(SRC_DIR)/%.s
	$(CC) -c -o $@ $(CFLAGS) $<
$(SRC_DIR)/%.o: $(SRC_DIR)/%.cpp
	$(CPP) -c -o $@ $(CFLAGS) $<


## Rules for making archives

ifneq ($(strip $(ARC_TARGET)),)
  
  ifneq ($(LINUX_COMPILER),_HHPPC_82XX_)
      CFLAGS += -DFD_SETSIZE=512
  endif

  ifneq ($(LINUX_COMPILER),_HHPPC_85XX_)
      CFLAGS += -DFD_SETSIZE=512
  endif

  ifneq ($(LINUX_COMPILER),_ARM_)
      CFLAGS += -DFD_SETSIZE=512
  endif
    
  all: install
  
  install: install_inc install_arc
  
  install_arc: $(ARC_TARGET)
	$(INSTALL) $(ARC_TARGET) $(INSTALL_LIB_PATH)/$(ARC_TARGET)
  
  $(ARC_TARGET) : $(OBJS)
	$(AR) crus $(ARC_TARGET) $(OBJS)

  uninstall: uninstallarc
  
  uninstallarc:
	$(foreach file, $(INSTALL_INC), $(RM) $(INSTALL_INC_PATH)/$(file) ;)
	$(RM) $(INSTALL_LIB_PATH)/$(ARC_TARGET)

  clearall:
	$(RM) $(ARC_TARGET) $(INSTALL_LIB_PATH)/$(ARC_TARGET) $(OBJS)

  clean: cleanarc
  
  cleanarc:
	$(RM) $(ARC_TARGET)

endif


## Rules for making shared object

ifneq ($(strip $(SO_TARGET)),)
  
  all: install
  
  install: install_inc install_so
  
  install_so: $(SO_TARGET)
	$(INSTALL) $(SO_TARGET) $(INSTALL_LIB_PATH)/$(SO_TARGET)
   
  $(SO_TARGET) : $(OBJS)
	$(LD) $(OBJS) -o $(SO_TARGET) $(LDFLAGS)
  uninstall: uninstallso
  
  uninstallso:
	$(foreach file, $(INSTALL_INC), $(RM) $(INSTALL_INC_PATH)/$(file) ;)
	$(RM) $(INSTALL_LIB_PATH)/$(SO_TARGET)



  clean: cleanso
  
  cleanso:
	$(RM) $(SO_TARGET)
  cleanall:
	$(RM) $(SO_TARGET) $(INSTALL_LIB_PATH)/$(SO_TARGET) $(OBJS)
endif


## Rules for making applications

ifneq ($(strip $(APP_TARGET)),)

  all: install
  
  install: install_inc install_app
  
  install_app: $(APP_TARGET)
	$(INSTALL) $(APP_TARGET) $(INSTALL_APP_PATH)/$(APP_TARGET)
  
  $(APP_TARGET): $(OBJS)
	$(LD) $(OBJS) -o $(APP_TARGET) $(LDFLAGS)
#	$(OBJDUMP) --syms $(APP_TARGET) | sort | grep " g" > $(APP_TARGET).map

##If do upx, install APP_TARGET to no-upx dir first
ifneq ($(UPX), ls)
ifdef INSTALL_NO_UPX_APP_PATH
	$(INSTALL) $(APP_TARGET) $(INSTALL_NO_UPX_APP_PATH)/$(APP_TARGET)
endif
endif
##
	$(UPX) $(APP_TARGET)

  clean: cleanapp
  
  cleanapp:
	$(RM) $(APP_TARGET)

  cleanall:
	$(RM) $(APP_TARGET) $(INSTALL_APP_PATH)/$(APP_TARGET) $(OBJS)

endif


## Rules for making subdirectories

ifneq ($(strip $(DIRS)),)

  all: $(DIRS)
  $(DIRS): FORCE
	$(MAKE) -C $@
  $(DIRSC): FORCE
	$(MAKE) -C $@ clean
  FORCE:
  clean: $(DIRSC)

endif


## Shared rules

install: install_inc

install_inc:
	$(foreach file, $(INSTALL_INC), $(INSTALL) $(file) $(INSTALL_INC_PATH)/$(notdir $(file)) ;)

clean: cleanobjs

cleanobjs:
	$(RM) $(ASMS) $(OBJS) *.pdb *.map


## Rule to pre-install all headers

setup:
	(cd $(TOP);    \
	make install_inc;   \
	echo )
