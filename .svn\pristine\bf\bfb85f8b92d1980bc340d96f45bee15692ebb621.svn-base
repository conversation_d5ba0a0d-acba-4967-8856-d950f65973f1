path="../../10-common/version/compileinfo/nvrkdvsend_his3519av100.txt"
date>>$path

cd ./prj_linux

echo ==============================================
echo =      nvr_kdvsend_linux for his3519av100    =
echo ==============================================

echo "============compile libnvrkdvsend his3519av100============">>../$path

make -j4 -e DEBUG=1 -f makefile_his3519av100 clean
make -j4 -e DEBUG=1 -f makefile_his3519av100 2>>../$path

#makefile install已经安装到指定目录了
#cp -L -r -f libnvrkdvsend.so ../../../10-common/lib/release/his3519av100/nvrkdvsend

cd ..
